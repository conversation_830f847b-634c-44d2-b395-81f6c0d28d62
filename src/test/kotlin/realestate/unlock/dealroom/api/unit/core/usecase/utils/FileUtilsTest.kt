package realestate.unlock.dealroom.api.unit.core.usecase.utils

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.kfile.KPathFile
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils.getParentFolder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Instant

class FileUtilsTest {

    @Test
    fun `should get deal base path`() {
        val id = anyId()
        val path = FileUtils.dealBasePath(id)
        Assertions.assertEquals("deal/$id/", path)
    }

    @Test
    fun `should build deal base path`() {
        val id = anyId()
        Assertions.assertEquals("deal/$id/", FileUtils.buildDealBasePath(id, ""))
        Assertions.assertEquals("deal/$id/manual", FileUtils.buildDealBasePath(id, "manual"))
        Assertions.assertEquals("deal/$id/manual/", FileUtils.buildDealBasePath(id, "manual/"))
        Assertions.assertEquals("deal/$id/manual", FileUtils.buildDealBasePath(id, "/manual"))
        Assertions.assertEquals("deal/$id/manual/", FileUtils.buildDealBasePath(id, "/manual/"))
        Assertions.assertEquals("deal/$id/manual/pepe.pdf", FileUtils.buildDealBasePath(id, "manual/pepe.pdf"))
        Assertions.assertEquals("deal/$id/manual/pepe.pdf", FileUtils.buildDealBasePath(id, "/manual/pepe.pdf"))
    }

    @Test
    fun `should build deal repository path`() {
        val id = anyId()
        Assertions.assertEquals("deal/$id/repository/", FileUtils.buildDealRepositoryPath(id, ""))
        Assertions.assertEquals("deal/$id/repository/manual", FileUtils.buildDealRepositoryPath(id, "manual"))
        Assertions.assertEquals("deal/$id/repository/manual/", FileUtils.buildDealRepositoryPath(id, "manual/"))
        Assertions.assertEquals("deal/$id/repository/manual", FileUtils.buildDealRepositoryPath(id, "/manual"))
        Assertions.assertEquals("deal/$id/repository/manual/", FileUtils.buildDealRepositoryPath(id, "/manual/"))
        Assertions.assertEquals("deal/$id/repository/manual/pepe.pdf", FileUtils.buildDealRepositoryPath(id, "manual/pepe.pdf"))
        Assertions.assertEquals("deal/$id/repository/manual/pepe.pdf", FileUtils.buildDealRepositoryPath(id, "/manual/pepe.pdf"))
    }

    @Test
    fun `should build deal history path`() {
        val id = anyId()
        Assertions.assertEquals("deal/$id/history/", FileUtils.buildDealHistoryPath(id, ""))
        Assertions.assertEquals("deal/$id/history/manual", FileUtils.buildDealHistoryPath(id, "manual"))
        Assertions.assertEquals("deal/$id/history/manual/", FileUtils.buildDealHistoryPath(id, "manual/"))
        Assertions.assertEquals("deal/$id/history/manual", FileUtils.buildDealHistoryPath(id, "/manual"))
        Assertions.assertEquals("deal/$id/history/manual/", FileUtils.buildDealHistoryPath(id, "/manual/"))
        Assertions.assertEquals("deal/$id/history/manual/pepe.pdf", FileUtils.buildDealHistoryPath(id, "manual/pepe.pdf"))
        Assertions.assertEquals("deal/$id/history/manual/pepe.pdf", FileUtils.buildDealHistoryPath(id, "/manual/pepe.pdf"))
    }

    @Test
    fun `should get parent folder`() {
        val file = KPathFile(
            uid = anyString(),
            name = "pepe.pdf",
            path = "manual/pepe.pdf",
            sizeInBytes = 12L,
            lastModified = Instant.now()
        )

        val folder = file.getParentFolder()

        Assertions.assertNotNull(folder)
        Assertions.assertEquals(folder?.name, "manual")
    }

    @Test
    fun `should get parent folder null because is deal root`() {
        val file = KPathFile(
            uid = anyString(),
            name = "pepe.pdf",
            path = "pepe.pdf",
            sizeInBytes = 12L,
            lastModified = Instant.now()
        )

        Assertions.assertNull(file.getParentFolder())
    }
}
