package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.hamcrest.core.IsNull
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.loi.MemberSign
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentSignRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.loi.GetLetterOfIntentSign
import realestate.unlock.dealroom.api.utils.entity.loi.LoiSignBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother

class GetMedicalLoiRoundSignTest {

    private val buyer = MemberObjectMother.buyer()
    private val seller = MemberObjectMother.seller()

    private val letterOfIntentSignRepository: LetterOfIntentSignRepository = mockk()
    private val memberRepository: MemberRepository = mockk {
        every { findById(buyer.id) } returns buyer
        every { findById(seller.id) } returns seller
    }
    private val target = GetLetterOfIntentSign(letterOfIntentSignRepository, memberRepository)

    @Test
    fun `gets loi sign`() {
        // given
        val givenLoi = LoiSignBuilder()
            .apply { sellerSign = MemberSign(seller, completedAt = null) }
            .apply { buyerSign = MemberSign(buyer, completedAt = null) }
            .build()
        every { letterOfIntentSignRepository.findByLoiId(givenLoi.loiId) } returns givenLoi

        // when
        val result = target.get(givenLoi.loiId)

        // then
        assertThat(result!!.loiId, IsEqual(givenLoi.loiId))
        assertThat(result.signingId, IsEqual(givenLoi.signingId))
        assertThat(result.status, IsEqual(givenLoi.status()))
        assertThat(result.buyerSign.id, IsEqual(givenLoi.buyerSign.id))
        assertThat(result.buyerSign.fullName, IsEqual(buyer.fullName))
        assertThat(result.buyerSign.completedAt, IsEqual(givenLoi.buyerSign.completedAt))
        assertThat(result.sellerSign.id, IsEqual(givenLoi.sellerSign.id))
        assertThat(result.sellerSign.fullName, IsEqual(seller.fullName))
        assertThat(result.sellerSign.completedAt, IsEqual(givenLoi.sellerSign.completedAt))
    }

    @Test
    fun `returns null`() {
        // given
        every { letterOfIntentSignRepository.findByLoiId(any()) } returns null

        // when
        val result = target.get(1)

        // then
        assertThat(result, IsNull())
    }
}
