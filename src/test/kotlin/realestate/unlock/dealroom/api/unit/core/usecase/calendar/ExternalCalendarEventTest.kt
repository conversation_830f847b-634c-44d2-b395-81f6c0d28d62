package realestate.unlock.dealroom.api.unit.core.usecase.calendar

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.ExternalDealCalendarEventInput
import realestate.unlock.dealroom.api.core.gateway.calendar.CalendarGateway
import realestate.unlock.dealroom.api.core.gateway.calendar.DealCalendarEvent
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalDealCalendarEventsActions
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.LocalDate

class ExternalCalendarEventTest {

    private lateinit var target: ExternalDealCalendarEventsActions
    private val calendarGateway: CalendarGateway = mockk(relaxed = true)
    private val dealRepository: DealRepository = mockk(relaxed = true)

    @BeforeEach
    fun setUp() {
        target = ExternalDealCalendarEventsActions(
            calendarGateway = calendarGateway,
            dealRepository = dealRepository
        )
    }

    private fun buildInput(
        calendarId: String? = anyString(),
        eventAlreadyCreated: Boolean = false
    ) =
        ExternalDealCalendarEventInput(
            dealId = anyId(),
            referenceId = anyString(),
            referenceType = anyString(),
            title = anyString(),
            description = anyString(),
            date = LocalDate.now()
        ).also {
            every { dealRepository.getCalendarId(it.dealId) } returns calendarId
            every { calendarGateway.findEvent(any(), any()) } returns if (eventAlreadyCreated) {
                DealCalendarEvent(
                    id = "${it.referenceType}${it.referenceId}",
                    startDate = LocalDate.now(),
                    endDate = LocalDate.now(),
                    title = anyString(),
                    description = anyString(),
                    calendarId = calendarId!!
                )
            } else {
                null
            }
        }

    private fun createDealEvent(
        input: ExternalDealCalendarEventInput,
        calendarId: String = anyString()
    ) =
        DealCalendarEvent(
            id = "${input.referenceType}${input.referenceId}",
            startDate = input.date,
            endDate = input.date,
            title = input.title,
            description = input.description,
            calendarId = calendarId
        )

    @Test
    fun `should update a event`() {
        val calendarId = anyString()
        val input = buildInput(calendarId = calendarId, eventAlreadyCreated = true)
        target.createOrUpdate(input)
        verify(exactly = 0) {
            calendarGateway.createEvent(any())
        }
        verify(exactly = 1) {
            calendarGateway.updateEvent(createDealEvent(input, calendarId))
        }
    }

    @Test
    fun `should create event if not exist on update`() {
        val calendarId = anyString()
        val input = buildInput(calendarId = calendarId, eventAlreadyCreated = false)
        target.createOrUpdate(input)
        verify(exactly = 0) {
            calendarGateway.updateEvent(any())
        }
        verify(exactly = 1) {
            calendarGateway.createEvent(createDealEvent(input, calendarId))
        }
    }

    @Test
    fun `should not update a event if calendar doesn't exists`() {
        val input = buildInput(calendarId = null)
        target.createOrUpdate(input)
        verify(exactly = 0) {
            calendarGateway.createEvent(any())
            calendarGateway.updateEvent(any())
        }
    }

    @Test
    fun `should delete a event`() {
        val calendarId = anyString()
        val input = buildInput(calendarId = calendarId, eventAlreadyCreated = true)
        target.delete(dealId = input.dealId, referenceId = input.referenceId, referenceType = input.referenceType)
        verify(exactly = 1) {
            calendarGateway.deleteEvent(calendarId, "${input.referenceType}${input.referenceId}")
        }
    }

    @Test
    fun `should not delete a event if calendar doesn't exists`() {
        val input = buildInput(calendarId = null)
        target.delete(dealId = input.dealId, referenceId = input.referenceId, referenceType = input.referenceType)
        verify(exactly = 0) {
            calendarGateway.deleteEvent(any(), "${input.referenceType}${input.referenceId}")
        }
    }

    @Test
    fun `should not delete a event if event  doesn't exists`() {
        val calendarId = anyString()
        val input = buildInput(calendarId = calendarId, eventAlreadyCreated = false)
        target.delete(dealId = input.dealId, referenceId = input.referenceId, referenceType = input.referenceType)
        verify(exactly = 0) {
            calendarGateway.deleteEvent(calendarId, "${input.referenceType}${input.referenceId}")
        }
    }
}
