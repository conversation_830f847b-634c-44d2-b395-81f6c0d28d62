package realestate.unlock.dealroom.api.unit.core.usecase.deal.update

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.entity.property.PropertyCreationByAddressInput
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.deal.create.FindOrCreatePropertyAssetsProperty
import realestate.unlock.dealroom.api.core.usecase.deal.update.DealUpdater
import realestate.unlock.dealroom.api.core.usecase.deal.update.UpdateDealProperty
import realestate.unlock.dealroom.api.core.usecase.property.CreateProperty
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyAssetsPropertyBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder

@ExtendWith(MockKExtension::class)
class UpdateDealPropertyTest {

    @MockK
    private lateinit var dealUpdater: DealUpdater

    @MockK
    private lateinit var dealRepository: DealRepository

    @MockK
    private lateinit var createProperty: CreateProperty

    @MockK
    private lateinit var findOrCreatePropertyAssetsProperty: FindOrCreatePropertyAssetsProperty

    private lateinit var updateDealProperty: UpdateDealProperty

    @BeforeEach
    fun setUp() {
        updateDealProperty = UpdateDealProperty(
            createProperty = createProperty,
            dealRepository = dealRepository,
            dealUpdater = dealUpdater,
            findOrCreatePropertyAssetsProperty = findOrCreatePropertyAssetsProperty
        )
    }

    @Test
    fun `it updates the property id`() {
        val givenDealId = 123L
        val givenDeal = DealBuilder().withId(givenDealId).build()
        val givenPropertyToCreate = PropertyBuilder().withId(321L).build()
        val propertyCreationInput = givenPropertyInput(givenPropertyToCreate)
        val givenUpdatedDeal = givenDeal.copy(propertyId = givenPropertyToCreate.id)
        every { dealRepository.findById(givenDealId) } returns givenDeal
        every { dealUpdater.update(givenUpdatedDeal, shouldUpdateTask = false) } returns givenUpdatedDeal
        every { findOrCreatePropertyAssetsProperty(any<PropertyCreationByAddressInput>()) } returns PropertyAssetsPropertyBuilder().build()

        val result = updateDealProperty(dealId = givenDealId, input = propertyCreationInput)

        assertThat(result.id, equalTo(givenDealId))
        assertThat(result.propertyId, equalTo(givenPropertyToCreate.id))
    }

    private fun givenPropertyInput(property: Property) =
        PropertyCreationByAddressInput(
            keywayId = property.keywayId,
            street = property.address.street,
            apartment = property.address.apartment,
            city = property.address.city,
            state = property.address.state,
            zip = property.address.zip,
            yearBuilt = property.yearBuilt,
            squareFootage = property.squareFootage,
            askingPrice = property.askingPrice,
            latitude = property.address.coordinates?.latitude,
            longitude = property.address.coordinates?.longitude,
            name = property.name,
            type = PropertyType.MEDICAL,
            multifamilyData = null
        ).also {
            every { createProperty.create(it, PropertyAssetsPropertyBuilder().build()) } returns property
        }
}
