package realestate.unlock.dealroom.api.unit.core.usecase.property

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.event.deal.DealEventPublisher
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.usecase.property.PropertyUpdater
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder

@ExtendWith(MockKExtension::class)
class PropertyUpdaterTest {

    @MockK
    private lateinit var propertyRepository: PropertyRepository

    @MockK
    private lateinit var dealEventPublisher: DealEventPublisher

    private lateinit var propertyUpdater: PropertyUpdater

    @BeforeEach
    fun setUp() {
        propertyUpdater = PropertyUpdater(
            propertyRepository = propertyRepository,
            dealEventPublisher = dealEventPublisher
        )
        every { dealEventPublisher.publish(any<Property>()) } returns Unit
    }

    @Test
    fun `it updates the property and sends a sns notification`() {
        val givenPropertyToUpdate = PropertyBuilder().build()
        every { propertyRepository.update(givenPropertyToUpdate) } returns givenPropertyToUpdate

        propertyUpdater.update(property = givenPropertyToUpdate)

        verify { propertyRepository.update(givenPropertyToUpdate) }
        verify { dealEventPublisher.publish(givenPropertyToUpdate) }
    }

    @Test
    fun `it updates the property and does not send a sns notification`() {
        val givenPropertyToUpdate = PropertyBuilder().build()
        every { propertyRepository.update(givenPropertyToUpdate) } returns givenPropertyToUpdate

        propertyUpdater.update(property = givenPropertyToUpdate, shouldSendUpdateNotification = false)

        verify { propertyRepository.update(givenPropertyToUpdate) }
        verify(exactly = 0) { dealEventPublisher.publish(givenPropertyToUpdate) }
    }
}
