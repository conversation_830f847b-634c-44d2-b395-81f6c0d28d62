package realestate.unlock.dealroom.api.unit.core.usecase.lease

import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.RequestChange
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.InteractWithDocumentRoundTest
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class RequestChangeTest : InteractWithDocumentRoundTest() {

    companion object {
        private val now = Instant.now()
    }

    private lateinit var requestChange: RequestChange

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        requestChange = RequestChange(
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            clock = clock,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `can request changes to a lease`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val leaseId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = leaseId)
        givenDocumentWithCurrentRound(id = leaseId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.SELLER_REVIEW, documentType = DocumentType.LEASE)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "shared",
            member = user.member,
            documentType = DocumentType.LEASE
        )

        val leaseUpdated = requestChange.update(input)

        assertDocumentWasUpdated(leaseUpdated, DocumentStatus.BUYER_REVIEW)
        assertInteractionWasCreated(draftDocuments, input, Interaction.Type.REQUEST_CHANGES)
    }

    @Test
    fun `cannot request changes to a Lease if its status is not SELLER_REVIEW`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val leaseId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = leaseId)
        givenDocumentWithCurrentRound(id = leaseId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.LEASE)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "shared",
            member = user.member,
            documentType = DocumentType.LEASE
        )

        assertThrows<InvalidDocumentStatusException> { requestChange.update(input) }
    }
}
