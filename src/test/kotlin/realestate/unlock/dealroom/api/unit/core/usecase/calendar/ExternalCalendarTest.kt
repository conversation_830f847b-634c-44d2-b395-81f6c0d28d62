package realestate.unlock.dealroom.api.unit.core.usecase.calendar

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.gateway.calendar.CalendarGateway
import realestate.unlock.dealroom.api.core.gateway.calendar.CreateDealCalendarInput
import realestate.unlock.dealroom.api.core.gateway.calendar.DealCalendar
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalDealCalendarActions
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString

class ExternalCalendarTest {

    private lateinit var target: ExternalDealCalendarActions
    private val calendarGateway: CalendarGateway = mockk(relaxed = true)
    private val dealRepository: DealRepository = mockk(relaxed = true)

    @BeforeEach
    fun setUp() {
        target = ExternalDealCalendarActions(
            calendarGateway = calendarGateway,
            dealRepository = dealRepository
        )
    }

    @Test
    fun `should create a calendar and return id`() {
        val deal = DealBuilder().build()
        val property = PropertyBuilder().build()
        val buyer = MemberObjectMother.buyer()
        val members = setOf(buyer, MemberObjectMother.seller())

        val calendarId = anyString()
        every {
            calendarGateway.createCalendar(
                input =
                CreateDealCalendarInput(
                    description = property.address.fullAddress(),
                    summary = property.name!!,
                    members = setOf(buyer.email)
                )
            )
        } returns calendarId
        every { dealRepository.saveCalendarId(calendarId = calendarId, dealId = deal.id) } returns Unit

        target.create(
            deal = deal,
            property = property,
            members = members
        )

        verify(exactly = 1) {
            calendarGateway.createCalendar(any())
            dealRepository.saveCalendarId(any(), any())
        }
    }

    @Test
    fun `should create a calendar and sanitize emails`() {
        val deal = DealBuilder().build()
        val property = PropertyBuilder().build()
        val members = setOf(
            MemberBuilder().withBuyerType()
                .apply { email = "<EMAIL>" }
                .build(),
            MemberBuilder().withBuyerType()
                .apply { email = "<EMAIL>" }
                .build(),
            MemberBuilder().withBuyerType()
                .apply { email = "<EMAIL>" }
                .build()
        )

        val calendarId = anyString()
        every { calendarGateway.createCalendar(any()) } returns calendarId
        every { dealRepository.saveCalendarId(calendarId = calendarId, dealId = deal.id) } returns Unit

        target.create(
            deal = deal,
            property = property,
            members = members
        )

        verify(exactly = 1) {
            calendarGateway.createCalendar(
                CreateDealCalendarInput(
                    description = property.address.fullAddress(),
                    summary = property.name!!,
                    members = setOf("<EMAIL>")
                )
            )
            dealRepository.saveCalendarId(any(), any())
        }
    }

    @Test
    fun `should not create a calendar if already created`() {
        val deal = DealBuilder().apply { calendarId = anyString() }.build()
        val property = PropertyBuilder().build()
        val members = setOf(MemberObjectMother.buyer(), MemberObjectMother.seller())

        target.create(
            deal = deal,
            property = property,
            members = members
        )

        verify(exactly = 0) {
            calendarGateway.createCalendar(any())
            dealRepository.saveCalendarId(any(), any())
        }
    }

    @Test
    fun `should update a calendar`() {
        val deal = DealBuilder().apply { calendarId = anyString() }.build()
        val property = PropertyBuilder().build()
        val buyer = MemberObjectMother.buyer()
        val members = setOf(buyer, MemberObjectMother.seller())

        every {
            calendarGateway.updateCalendar(
                dealCalendar =
                DealCalendar(
                    id = deal.calendarId!!,
                    description = property.address.fullAddress(),
                    summary = property.name!!,
                    members = setOf(buyer.email)
                )
            )
        } returns Unit

        target.update(
            deal = deal,
            property = property,
            members = members
        )

        verify(exactly = 1) {
            calendarGateway.updateCalendar(any())
        }
    }

    @Test
    fun `should delete a calendar`() {
        val deal = DealBuilder().apply { calendarId = anyString() }.build()

        every {
            dealRepository.saveCalendarId(deal.id, null)
        } returns Unit
        every {
            calendarGateway.deleteCalendar(calendarId = deal.calendarId!!)
        } returns Unit

        target.delete(
            deal = deal
        )

        verify(exactly = 1) {
            calendarGateway.deleteCalendar(any())
            dealRepository.saveCalendarId(any(), any())
        }
    }
}
