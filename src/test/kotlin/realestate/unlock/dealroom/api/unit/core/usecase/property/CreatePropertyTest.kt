package realestate.unlock.dealroom.api.unit.core.usecase.property

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.property.*
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.usecase.property.CreateProperty
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.MultifamilyAdditionalDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.MultifamilyRealEstateDataResponse
import realestate.unlock.dealroom.api.utils.entity.property.PropertyAssetsPropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class CreatePropertyTest {

    private lateinit var createProperty: CreateProperty

    @MockK(relaxed = true)
    private lateinit var propertyRepository: PropertyRepository

    @BeforeEach
    fun setUp() {
        createProperty = CreateProperty(
            propertyRepository = propertyRepository
        )
    }

    @Test
    fun `should create a property`() {
        val expectedKeywayId = "expected-id"
        val property = getProperty(keywayId = null)
        val input = getCreatePropertyInput(property, expectedKeywayId)
        val propertyAdditionalData = MultifamilyAdditionalDataResponse(
            realEstateData = MultifamilyRealEstateDataResponse(
                source = "source",
                constructionYear = 1983,
                askingPrice = BigDecimal(5123)
            ),
            listings = null,
            sales = null,
            mortgages = null
        )
        val propertyAssetsResponse = PropertyAssetsPropertyBuilder()
            .withId(expectedKeywayId)
            .withAdditionalData(propertyAdditionalData)
            .build()

        createProperty.create(input, propertyAssetsResponse)
        verify {
            propertyRepository.save(
                input.copy(
                    latitude = propertyAssetsResponse.location.latitude,
                    longitude = propertyAssetsResponse.location.longitude,
                    yearBuilt = property.yearBuilt,
                    squareFootage = property.squareFootage,
                    askingPrice = property.askingPrice,
                    multifamilyData = MultifamilyDataInput(
                        brokerFirm = null,
                        units = propertyAdditionalData.realEstateData?.units?.quantity?.toLong(),
                        averageSquareFootage = null,
                        occupancy = propertyAdditionalData.realEstateData?.occupancyPercentage,
                        parkingSpots = null,
                        parkingRatio = propertyAdditionalData.realEstateData?.parkingRatio,
                        owner = propertyAdditionalData.realEstateData?.owner,
                        propertyManager = propertyAdditionalData.realEstateData?.managerCompany,
                        unitsMix = listOf()
                    )
                ).toPropertyToSave()
            )
        }
    }

    @Test
    fun `creating a property providing a KeywayID that does not match with the ID of property assets should fail`() {
        val givenKeywayId = anyString()
        val property = getProperty(keywayId = givenKeywayId)
        val input = getCreatePropertyInput(property, keywayId = "another-keyway-id")
        val propertyAssetsProperty = PropertyAssetsPropertyBuilder().withId(givenKeywayId).build()

        assertThrows<BadRequestException> { createProperty.create(input, propertyAssetsProperty) }
    }

    private fun getProperty(keywayId: String? = anyString()) = Property(
        id = anyId(),
        name = anyString(),
        keywayId = keywayId,
        address = Address(
            street = "Test Address",
            apartment = "1 B",
            city = "New York",
            state = "New York",
            zip = "331331",
            coordinates = Coordinates(
                latitude = BigDecimal.ONE,
                longitude = BigDecimal.ONE
            )
        ),
        yearBuilt = 2005,
        squareFootage = BigDecimal.TEN,
        askingPrice = BigDecimal.TEN,
        mainPhotoId = null,
        mainPhoto = null,
        interiorPhotoId = null,
        interiorPhoto = null,
        createdAt = OffsetDateTime.now(),
        updatedAt = OffsetDateTime.now(),
        type = PropertyType.MULTIFAMILY,
        multifamilyData = null
    )

    private fun getCreatePropertyInput(property: Property, keywayId: String? = null) =
        PropertyCreationByAddressInput(
            name = property.name,
            keywayId = keywayId ?: property.keywayId,
            street = property.address.street,
            apartment = property.address.apartment,
            city = property.address.city,
            state = property.address.state,
            zip = property.address.zip,
            yearBuilt = property.yearBuilt,
            squareFootage = property.squareFootage,
            askingPrice = property.askingPrice,
            latitude = property.address.coordinates?.latitude,
            longitude = property.address.coordinates?.longitude,
            type = property.type,
            multifamilyData = null
        ).also {
            every { propertyRepository.save(it.toPropertyToSave()) } returns property
        }
}
