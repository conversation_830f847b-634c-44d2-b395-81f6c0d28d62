package realestate.unlock.dealroom.api.unit.core.usecase.task.file

import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.task.UpdateTaskInput
import realestate.unlock.dealroom.api.core.entity.task.file.TaskFile
import realestate.unlock.dealroom.api.core.entity.task.file.TaskFileToSave
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.repository.task.file.TaskFileRepository
import realestate.unlock.dealroom.api.core.usecase.task.file.KFileParser
import realestate.unlock.dealroom.api.core.usecase.task.file.SaveTaskFile
import realestate.unlock.dealroom.api.core.usecase.task.file.TaskFilePathGenerator
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.task.file.TaskFileObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class SaveTaskFileTest {
    private val kFileParser: KFileParser = mockk()
    private val taskFileRepository: TaskFileRepository = mockk(relaxed = true)
    private val taskFilePathGenerator: TaskFilePathGenerator = mockk()
    private val fileGateway: FileGateway = mockk(relaxed = true)

    private val target = SaveTaskFile(kFileParser, taskFileRepository, taskFilePathGenerator, fileGateway)

    @Test
    fun `save task files - all new`() {
        // Given
        val givenTask = TaskBuilder().withId(1).build()
        val member = MemberObjectMother.buyer()
        val givenUpdateTaskInput = givenUpdateTaskInput(givenTask, member)
        val givenKFile = KFile("uid", "name.pdf")
        val taskFilePath = anyString()
        every { kFileParser.parse(any(), any()) } returns listOf(givenKFile)
        every { taskFileRepository.findByTaskId(any()) } returns listOf()
        every { taskFilePathGenerator(any()) } returns taskFilePath

        // When
        target.save(givenTask, givenUpdateTaskInput)

        // Then
        val expectedTaskFileToSave = TaskFileToSave(givenKFile.name, taskFilePath, givenKFile.uid, givenTask.id, member.id)
        verify(exactly = 1) { taskFileRepository.save(eq(expectedTaskFileToSave)) }
    }

    @Test
    fun `save task files - all existing`() {
        // Given
        val givenTask = TaskBuilder().withId(1).build()
        val member = MemberObjectMother.buyer()
        val givenUpdateTaskInput = givenUpdateTaskInput(givenTask, member)
        val givenKFile = KFile("uid", "name.pdf")
        val givenTaskFile = TaskFile(
            id = 1,
            name = givenKFile.name,
            path = "some/path",
            kFileId = givenKFile.uid,
            taskId = givenTask.id,
            memberId = member.id,
            createdAt = OffsetDateTime.MAX
        )
        every { kFileParser.parse(any(), any()) } returns listOf(givenKFile)
        every { taskFileRepository.findByTaskId(any()) } returns listOf(givenTaskFile)

        // When
        target.save(givenTask, givenUpdateTaskInput)

        // Then
        verify(exactly = 0) { taskFileRepository.save(any()) }
    }

    @Test
    fun `save task files - empty input`() {
        // Given
        val givenTask = TaskBuilder().withId(1).build()
        val member = MemberObjectMother.buyer()
        val givenUpdateTaskInput = givenUpdateTaskInput(givenTask, member)
        val givenTaskFile = TaskFileObjectMother.taskFile(taskId = givenTask.id)
        every { kFileParser.parse(any(), any()) } returns listOf()
        every { taskFileRepository.findByTaskId(any()) } returns listOf(givenTaskFile)

        // When
        target.save(givenTask, givenUpdateTaskInput)

        // Then
        verify(exactly = 0) { taskFileRepository.save(any()) }
    }

    @Test
    fun `save task files - empty input & no existing files`() {
        // Given
        val givenTask = TaskBuilder().withId(1).build()
        val member = MemberObjectMother.buyer()
        val givenUpdateTaskInput = givenUpdateTaskInput(givenTask, member)
        every { kFileParser.parse(any(), any()) } returns listOf()
        every { taskFileRepository.findByTaskId(any()) } returns listOf()

        // When
        target.save(givenTask, givenUpdateTaskInput)

        // Then
        verify(exactly = 0) { taskFileRepository.save(any()) }
    }

    private fun givenUpdateTaskInput(
        givenTask: Task,
        member: Member
    ) = UpdateTaskInput(
        id = givenTask.id,
        status = givenTask.statusKey,
        dueDate = null,
        attachedForm = emptyMap(), // it does not matter here because the kfiles will be mocked in the parser
        assignedBuyerId = givenTask.assignedBuyer.id,
        assignedTeam = givenTask.assignedTeam,
        member = member,
        rejectionReason = null,
        enabled = null
    )
}
