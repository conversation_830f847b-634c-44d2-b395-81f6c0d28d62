package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.repository.document.DocumentInteractionRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentRoundInteractions
import realestate.unlock.dealroom.api.core.usecase.deal.documents.GetDocument
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.InteractionBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId

@ExtendWith(MockKExtension::class)
class FindDocumentRoundsInteractionsTest {

    @MockK
    private lateinit var documentInteractionRepository: DocumentInteractionRepository

    @MockK
    private lateinit var getDocument: GetDocument
    private lateinit var findDocumentRoundInteractions: FindDocumentRoundInteractions

    @BeforeEach
    fun setUp() {
        findDocumentRoundInteractions = FindDocumentRoundInteractions(
            getDocument = getDocument,
            documentInteractionRepository = documentInteractionRepository
        )
    }

    @Test
    fun `can get psa interactions`() {
        val documentId = anyId()
        val document = givenPSA()
        val interactions = givenInteractionsFor(document, documentId)

        val result = findDocumentRoundInteractions(FindDocumentRoundInteractions.Input(document.dealId, documentId, document.type))

        assertEquals(interactions, result)
    }

    private fun givenInteractionsFor(document: Document, documentId: Long) =
        listOf(
            InteractionBuilder().build(),
            InteractionBuilder().build(),
            InteractionBuilder().build()
        ).also { interactions ->
            every { documentInteractionRepository.findByRoundIdAndDocumentId(documentId, document.id) } returns interactions
        }

    private fun givenPSA() = DocumentBuilder().build().also {
        every { getDocument.byDealIdAndDocumentType(it.dealId, it.type) } returns it
    }
}
