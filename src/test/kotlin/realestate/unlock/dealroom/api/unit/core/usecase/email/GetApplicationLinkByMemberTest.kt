package realestate.unlock.dealroom.api.unit.core.usecase.email

import io.mockk.junit5.MockKExtension
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.usecase.email.GetApplicationLinkByMember
import realestate.unlock.dealroom.api.infrastructure.configuration.model.WebAppsConfig
import realestate.unlock.dealroom.api.utils.entity.member.MemberBuilder

@ExtendWith(MockKExtension::class)
class GetApplicationLinkByMemberTest {

    private val webAppsConfig = WebAppsConfig(
        dealRoom = "dealRoom",
        dealRoomAdmin = "dealRoomAdmin",
        kos = "kos"
    )

    private val getApplicationLinkByMember = GetApplicationLinkByMember(
        webApps = webAppsConfig
    )

    @Test
    fun `When member belongs to the seller group should generate url with deal room base path`() {
        // Given
        val member = MemberBuilder().withSellerBrokerType().build()

        // When
        val result = getApplicationLinkByMember.get(member)

        // then
        assertThat(result, equalTo(webAppsConfig.dealRoom))
    }

    @Test
    fun `When member belongs to the buyer group should generate url with deal room admin base path`() {
        // Given
        val member = MemberBuilder().withBuyerCounselType().build()

        // When
        val result = getApplicationLinkByMember.get(member)

        // then
        assertThat(result, equalTo(webAppsConfig.dealRoomAdmin))
    }
}
