package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.document.*
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarDocumentEvent
import realestate.unlock.dealroom.api.core.usecase.deal.documents.CreateDocumentInteraction
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentAndCurrentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import java.time.Clock
import java.time.OffsetDateTime

abstract class InteractWithDocumentRoundTest {

    @MockK(relaxed = true)
    protected lateinit var documentRepository: DocumentRepository

    @MockK(relaxed = true)
    protected lateinit var createDocumentInteraction: CreateDocumentInteraction

    @MockK
    protected lateinit var findDocumentAndCurrentRound: FindDocumentAndCurrentRound

    @MockK
    protected lateinit var externalCalendarDocumentEvent: ExternalCalendarDocumentEvent

    protected lateinit var clock: Clock

    protected fun assertInteractionWasCreated(documentRound: DocumentRound, input: InteractWithDocumentRound.Input, type: Interaction.Type) {
        verify {
            createDocumentInteraction(
                input.member,
                input.comment,
                documentRound,
                type,
                input.files
            )
        }
    }

    protected fun assertDocumentWasUpdated(documentUpdated: Document, status: DocumentStatus) {
        Assertions.assertEquals(status, documentUpdated.status)
        Assertions.assertEquals(OffsetDateTime.now(clock), documentUpdated.updatedAt)
    }

    protected fun givenDocumentWithCurrentRound(id: Long, deal: Deal, currentRound: DocumentRound, status: DocumentStatus, documentType: DocumentType) = DocumentBuilder().apply {
        this.id = id
        this.dealId = deal.id
        this.status = status
        this.currentRoundId = currentRound.id
        this.type = documentType
    }.build().also {
        every { findDocumentAndCurrentRound.byDealIdAndDocumentType<DocumentRound>(deal.id, documentType) } returns Pair(it, currentRound)
        every { externalCalendarDocumentEvent.impactOnCalendar(any()) } returns Unit
    }
}
