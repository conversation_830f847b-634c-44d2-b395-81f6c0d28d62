package realestate.unlock.dealroom.api.unit.entrypoint.rest.contract.paginated.filters

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.search.NumberOperator
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableNumber

class SearchableNumberTest {

    private fun createSearchableNumber(operator: NumberOperator, values: List<Int>, mustFail: <PERSON><PERSON>an = true) {
        try {
            SearchableNumber(operator, values)
            Assertions.assertFalse(mustFail)
        } catch (e: Exception) {
            print(e)
            Assertions.assertTrue(mustFail)
        }
    }

    @Test
    fun `can create datetime search with 1 value`() {
        NumberOperator.values().filter { it != NumberOperator.BETWEEN }.forEach {
            createSearchableNumber(it, listOf(1), mustFail = false)
        }
    }

    @Test
    fun `can create datetime search with 2 value`() {
        createSearchableNumber(NumberOperator.BETWEEN, listOf(1, 2), mustFail = false)
        createSearchableNumber(NumberOperator.IN, listOf(1, 2), mustFail = false)
    }
}
