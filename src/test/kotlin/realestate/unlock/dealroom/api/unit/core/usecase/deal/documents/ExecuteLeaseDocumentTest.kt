package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.*
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.kfile.FileToStaging
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.sign.DocumentOutput
import realestate.unlock.dealroom.api.core.gateway.sign.RetrieveDocumentInput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentSignRepository
import realestate.unlock.dealroom.api.core.repository.file.FileRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarDocumentEvent
import realestate.unlock.dealroom.api.core.usecase.deal.documents.*
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.ExecuteDocumentImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SendFileToProcess
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.document.DocumentSignerBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.io.ByteArrayInputStream
import java.time.Clock
import java.time.LocalDate
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class ExecuteLeaseDocumentTest {
    @MockK(relaxed = true)
    private lateinit var documentRepository: DocumentRepository

    @MockK(relaxed = true)
    private lateinit var signGateway: SignGateway

    @MockK(relaxed = true)
    private lateinit var fileGateway: FileGateway
    private val clock: Clock = Clock.systemDefaultZone()

    @MockK(relaxed = true)
    private lateinit var confirmExecutedDocument: ConfirmExecutedDocument

    @MockK(relaxed = true)
    private lateinit var documentSignRepository: DocumentSignRepository

    @MockK(relaxed = true)
    private lateinit var executeDocumentImpactOnTask: ExecuteDocumentImpactOnTask

    @MockK(relaxed = true)
    private lateinit var memberRepository: MemberRepository

    @MockK(relaxed = true)
    private lateinit var findDocumentAndCurrentRound: FindDocumentAndCurrentRound

    @MockK(relaxed = true)
    private lateinit var createDocumentInteraction: CreateDocumentInteraction

    @MockK(relaxed = true)
    private lateinit var getOrCreateDocument: GetOrCreateDocument

    @MockK
    private lateinit var externalCalendarDocumentEvent: ExternalCalendarDocumentEvent

    @MockK
    private lateinit var sendFileToProcess: SendFileToProcess

    @MockK
    private lateinit var fileRepository: FileRepository

    private lateinit var executeLease: ExecuteLease

    @BeforeEach
    fun setUp() {
        TransactionManager.initialize(mockk(relaxed = true))
        executeLease = ExecuteLease(
            documentRepository = documentRepository,
            signGateway = signGateway,
            fileGateway = fileGateway,
            clock = clock,
            confirmExecutedDocument = confirmExecutedDocument,
            documentSignRepository = documentSignRepository,
            executeDocumentImpactOnTask = executeDocumentImpactOnTask,
            memberRepository = memberRepository,
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            createDocumentInteraction = createDocumentInteraction,
            getOrCreateDocument = getOrCreateDocument,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent,
            sendFileToProcess = sendFileToProcess,
            fileRepository = fileRepository
        )
    }

    @Test
    fun `can execute lease with file from docusign`() {
        val envelopId = anyString()
        val leaseId = anyId()
        val documentId = anyString()
        getSignersByEnvelope(envelopId, leaseId, documentId)
        val (lease, round) = getLeaseAndRound(leaseId)
        val documentContent = retrieveDocument(envelopId, documentId)
        val kFile = uploadFile(lease, documentContent)
        val executedLeaseDocument = confirmFile(lease, kFile)
        val member = getBuyer(round.memberId)
        val file = File(id = executedLeaseDocument.fileId, name = anyString(), kFileId = anyString(), memberId = null, createdAt = OffsetDateTime.now())
        val authToken = anyString()
        val documentUpdateInputSlot = slot<Document>()
        every { documentRepository.update(capture(documentUpdateInputSlot)) } returns Unit
        every { executeDocumentImpactOnTask(lease, member) } returns Unit
        every { externalCalendarDocumentEvent.impactOnCalendar(any()) } returns Unit
        every { fileRepository.findById(executedLeaseDocument.fileId) } returns file
        every { sendFileToProcess(any()) } returns Unit

        executeLease.execute(ExecuteLease.SignInput(envelopId, documentId, authToken = authToken))

        Assertions.assertEquals(executedLeaseDocument.id, documentUpdateInputSlot.captured.currentRoundId)
        Assertions.assertEquals(DocumentStatus.EXECUTED, documentUpdateInputSlot.captured.status)
        Assertions.assertNotNull(documentUpdateInputSlot.captured.executedAt)
        Assertions.assertNotEquals(documentUpdateInputSlot.captured.updatedAt, lease.updatedAt)
        Assertions.assertNotEquals(documentUpdateInputSlot.captured.executedAt, lease.executedAt)

        verify(exactly = 1) {
            createDocumentInteraction(
                member = null,
                comment = null,
                documentRound = executedLeaseDocument,
                interactionType = Interaction.Type.EXECUTED
            )
        }
        verify(exactly = 1) {
            sendFileToProcess(
                SendFileToProcess.Input(
                    fileId = file.kFileId,
                    fileType = TokenFileType.LEASE,
                    dealId = lease.dealId,
                    authToken = authToken
                )
            )
        }
    }

    @Test
    fun `can execute lease with file from external source`() {
        val leaseId = anyId()
        val round = DocumentRoundObjectMother.finalDocument(documentId = leaseId)
        val lease = DocumentBuilder().apply { id = leaseId; currentRoundId = round.id }.build()
        every { getOrCreateDocument.byDealIdAndDocumentType(dealId = lease.dealId, documentType = DocumentType.LEASE) } returns lease
        val member = getBuyer(round.memberId)
        val fileInput = FileInput(uid = anyString(), name = anyString())
        val executedLeaseDocument = confirmFile(lease, KFile(name = fileInput.name, uid = fileInput.uid))
        val documentUpdateInputSlot = slot<Document>()
        val file = File(id = anyId(), name = fileInput.name, kFileId = fileInput.uid, memberId = null, createdAt = OffsetDateTime.now())
        val authToken = anyString()

        every { documentRepository.update(capture(documentUpdateInputSlot)) } returns Unit
        every { executeDocumentImpactOnTask(lease, member) } returns Unit
        every { externalCalendarDocumentEvent.impactOnCalendar(any()) } returns Unit
        every { fileRepository.findById(executedLeaseDocument.fileId) } returns file
        every { sendFileToProcess(any()) } returns Unit

        executeLease.execute(
            ExecuteLease.ExternalInput(
                dealId = lease.dealId,
                comment = "this is a comment",
                file = fileInput,
                member = member,
                executedAt = LocalDate.now(),
                authToken = authToken
            )
        )

        Assertions.assertEquals(executedLeaseDocument.id, documentUpdateInputSlot.captured.currentRoundId)
        Assertions.assertEquals(DocumentStatus.EXECUTED, documentUpdateInputSlot.captured.status)
        Assertions.assertNotNull(documentUpdateInputSlot.captured.executedAt)
        Assertions.assertNotEquals(documentUpdateInputSlot.captured.updatedAt, lease.updatedAt)
        Assertions.assertNotEquals(documentUpdateInputSlot.captured.executedAt, lease.executedAt)

        verify(exactly = 1) {
            createDocumentInteraction(
                member = null,
                comment = null,
                documentRound = executedLeaseDocument,
                interactionType = Interaction.Type.EXECUTED
            )
        }
        verify(exactly = 1) {
            sendFileToProcess(
                SendFileToProcess.Input(
                    fileId = file.kFileId,
                    fileType = TokenFileType.LEASE,
                    dealId = lease.dealId,
                    authToken = authToken
                )
            )
        }
    }

    private fun getLeaseAndRound(leaseIdInput: Long): Pair<Document, FinalDocument> {
        val round = DocumentRoundObjectMother.finalDocument(documentId = leaseIdInput)
        val lease = DocumentBuilder().apply { id = leaseIdInput; currentRoundId = round.id }.build()
        every { findDocumentAndCurrentRound.byDocumentId<FinalDocument>(leaseIdInput) } returns Pair(lease, round)
        return Pair(lease, round)
    }

    private fun getSignersByEnvelope(envelopeIdInput: String, leaseIdInput: Long, documentIdInput: String) =
        listOf(
            DocumentSignerBuilder().setSignCompleted(true).apply {
                envelopeId = envelopeIdInput
                documentId = leaseIdInput
                docusignDocumentId = documentIdInput
            }.build(),
            DocumentSignerBuilder().setSignCompleted(true).setBuyerMemberTeam().apply {
                envelopeId = envelopeIdInput
                documentId = leaseIdInput
                docusignDocumentId = documentIdInput
            }.build()
        ).also { signers -> every { documentSignRepository.findByEnvelopeId(envelopeIdInput) } returns signers }

    private fun retrieveDocument(envelopeIdInput: String, documentIdInput: String): DocumentOutput =
        DocumentOutput(
            documentIdInput,
            ByteArrayInputStream.nullInputStream()
        ).also {
            every { signGateway.retrieveDocument(RetrieveDocumentInput(envelopeIdInput, documentIdInput)) } returns it
        }

    private fun uploadFile(document: Document, documentOutput: DocumentOutput): KFile =
        KFile(uid = anyString(), name = DocumentFileHelper.fileName(document, FileType.EXECUTED_VERSION, ".pdf")).also {
            every {
                fileGateway.uploadStagedFile(
                    FileToStaging(
                        content = documentOutput.content,
                        contentType = "application/pdf",
                        filename = DocumentFileHelper.fileName(document, FileType.EXECUTED_VERSION, ".pdf")
                    )
                )
            } returns it
        }

    private fun confirmFile(document: Document, file: KFile): ExecutedDocument =
        ExecutedDocument(
            fileId = anyId(),
            documentId = document.id,
            id = anyId()
        ).also {
            every {
                confirmExecutedDocument(
                    input = ConfirmExecutedDocument.Input(
                        dealId = document.dealId,
                        executedVersionFile = FileInput(uid = file.uid, name = file.name)
                    ),
                    document = document
                )
            } returns it
        }

    private fun getBuyer(memberId: Long): Member =
        MemberObjectMother.buyer(memberId).also {
            every { memberRepository.findById(memberId) } returns it
        }
}
