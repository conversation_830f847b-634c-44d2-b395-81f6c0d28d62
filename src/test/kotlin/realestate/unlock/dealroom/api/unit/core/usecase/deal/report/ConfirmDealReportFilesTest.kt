package realestate.unlock.dealroom.api.unit.core.usecase.deal.report

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.usecase.deal.reports.ConfirmDealReportFiles
import realestate.unlock.dealroom.api.core.usecase.file.ConfirmStagedFile
import realestate.unlock.dealroom.api.core.usecase.file.ConfirmStagedFile.FileToConfirm
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SendFileToProcess
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.file.FileBuilder

@ExtendWith(MockKExtension::class)
class ConfirmDealReportFilesTest {

    @MockK(relaxed = true)
    private lateinit var confirmStagedFile: ConfirmStagedFile

    @MockK(relaxed = true)
    private lateinit var sendFileToProcess: SendFileToProcess

    private lateinit var confirmDealReportFiles: ConfirmDealReportFiles

    @BeforeEach
    fun setUp() {
        confirmDealReportFiles = ConfirmDealReportFiles(confirmStagedFile, sendFileToProcess)
    }

    @Test
    fun `It confirms every file`() {
        // given
        val filesToConfirm = listOf(
            FileInput(uid = "first-file", name = "first-file.pdf"),
            FileInput(uid = "second-file", name = "second-file.pdf")
        )
        val givenDealId = 12L
        val givenReportId = 13L
        val givenMember = mockk<Member>()

        every { confirmStagedFile.invoke(any()) } returns filesToConfirm.map {
            FileBuilder().apply {
                kFileId = it.uid
                name = it.name
            }.build()
        }

        // when
        confirmDealReportFiles(
            files = filesToConfirm,
            dealId = givenDealId,
            reportId = givenReportId,
            member = givenMember,
            authToken = anyString()
        )

        // then
        verifyConfirmStagedFileCall(filesToConfirm, givenMember, givenDealId, givenReportId)
        verifyFilesSentToChatGpt(filesToConfirm, givenDealId)
    }

    private fun verifyFilesSentToChatGpt(filesToConfirm: List<FileInput>, dealId: Long) {
        filesToConfirm.onEach {
            verify(exactly = 0) {
                sendFileToProcess(SendFileToProcess.Input(it.uid, TokenFileType.REPORT, dealId = dealId, authToken = anyString()))
            }
        }
    }

    private fun verifyConfirmStagedFileCall(
        filesToConfirm: List<FileInput>,
        givenMember: Member,
        givenDealId: Long,
        givenReportId: Long
    ) {
        verify {
            confirmStagedFile(
                filesToConfirm.map {
                    FileToConfirm(
                        name = it.name,
                        kFileId = it.uid,
                        member = givenMember,
                        path = FileUtils.buildDealRepositoryPath(givenDealId, "/reports")
                    )
                },
                linkedTo = ConfirmStagedFile.LinkedTo(
                    dealId = givenDealId,
                    entityType = LinkedFileEntityType.REPORT,
                    entityId = givenReportId.toString()
                )
            )
        }
    }
}
