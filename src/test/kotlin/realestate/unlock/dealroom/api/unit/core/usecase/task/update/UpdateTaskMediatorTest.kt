package realestate.unlock.dealroom.api.unit.core.usecase.task.update

import io.mockk.Called
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.exception.task.TaskDisabledException
import realestate.unlock.dealroom.api.core.usecase.task.TaskTransition
import realestate.unlock.dealroom.api.core.usecase.task.update.UpdateTask
import realestate.unlock.dealroom.api.core.usecase.task.update.UpdateTaskMediator
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother

@ExtendWith(MockKExtension::class)
class UpdateTaskMediatorTest {

    @MockK(relaxed = true)
    private lateinit var updateTask: UpdateTask

    @MockK(relaxed = true)
    private lateinit var taskTransition: TaskTransition

    @MockK
    private lateinit var taskRepository: TaskRepository

    private lateinit var updateTaskMediator: UpdateTaskMediator

    @BeforeEach
    fun setUp() {
        updateTaskMediator = UpdateTaskMediator(
            updateTask = updateTask,
            taskTransition = taskTransition,
            taskRepository = taskRepository
        )
    }

    @Test
    fun `when status is modified, runs the transition`() {
        val task = givenTaskInStatus(TaskStatus.TO_DO, TaskType.THIRD_PARTY_VISITS)
        val input = givenUpdateInput(task, TaskStatus.IN_REVIEW)

        updateTaskMediator.update(input)

        verifyTransitionExecuted(task, input)
    }

    @Test
    fun `when status is modified and is B&F, runs the update`() {
        val task = givenTaskInStatus(TaskStatus.TO_DO, TaskType.DOCUMENT_BACK_AND_FORTH)
        val input = givenUpdateInput(task, TaskStatus.IN_REVIEW)

        updateTaskMediator.update(input)

        verifyUpdateExecuted(task, input.toUpdateTaskInput(task))
    }

    @Test
    fun `when status does not change, performs update`() {
        val task = givenTaskInStatus(TaskStatus.IN_REVIEW, TaskType.THIRD_PARTY_VISITS)
        val input = givenUpdateInput(task, TaskStatus.IN_REVIEW)

        updateTaskMediator.update(input)

        verifyUpdateExecuted(task, input.toUpdateTaskInput(task))
    }

    @Test
    fun `when title change in a non custom task, throws exception`() {
        val task = givenTaskInStatus(TaskStatus.IN_REVIEW, TaskType.THIRD_PARTY_VISITS)
        val input = UpdateTaskMediator.Input(
            title = "new title",
            id = task.id,
            status = null,
            dueDate = null,
            attachedForm = null,
            assignedBuyerId = null,
            member = CompleteUserObjectMother.buyer().member
        )

        assertThrows<BadRequestException> {
            updateTaskMediator.update(input)
        }
    }

    @Test
    fun `when status does not change but input has old value, performs update `() {
        val task = givenTaskInStatus(TaskStatus.IN_REVIEW, TaskType.THIRD_PARTY_VISITS)
        val input = givenUpdateInput(task, TaskStatus.TO_DO)

        updateTaskMediator.update(input)

        verifyUpdateExecuted(task, input.toUpdateTaskInput(task))
    }

    @Test
    fun `when status is ON_HOLD, performs update`() {
        val task = givenTaskInStatus(TaskStatus.ON_HOLD, TaskType.PSA)
        val input = givenUpdateInput(task, TaskStatus.TO_DO)

        updateTaskMediator.update(input)

        verifyUpdateExecuted(task, input.toUpdateTaskInput(task))
    }

    @Test
    fun `when task is PSA and status is not ON_HOLD, runs the transition`() {
        val task = givenTaskInStatus(TaskStatus.TO_DO, TaskType.PSA)
        val input = givenUpdateInput(task, TaskStatus.IN_REVIEW)

        updateTaskMediator.update(input)

        verifyTransitionExecuted(task, input)
    }

    @Test
    fun `cannot update disabled tasks`() {
        val task = givenTaskInStatus(TaskStatus.TO_DO, TaskType.PSA)
        disableTask(task)

        assertThrows<TaskDisabledException> {
            updateTaskMediator.update(givenUpdateInput(task, TaskStatus.IN_REVIEW))
        }
    }

    private fun verifyUpdateExecuted(
        task: Task,
        input: UpdateTaskInput
    ) {
        verify {
            (updateTask.update(input, task))
        }
        verify { taskTransition wasNot Called }
    }

    private fun verifyTransitionExecuted(
        task: Task,
        input: UpdateTaskMediator.Input
    ) {
        val expectedTransition = expectedTransitionInput(task, input.toUpdateTaskInput(task))

        verify {
            taskTransition(expectedTransition)
        }
        verify { updateTask wasNot Called }
    }

    private fun expectedTransitionInput(
        task: Task,
        input: UpdateTaskInput
    ) = TaskTransitionInput(
        taskId = task.id,
        member = input.member,
        rejectReason = input.rejectionReason,
        transition = TaskStatusTransitions.REVIEW,
        dueDate = input.dueDate,
        attachedForm = input.attachedForm
    )

    private fun givenUpdateInput(task: Task, newStatus: TaskStatus) = UpdateTaskMediator.Input(
        id = task.id,
        status = newStatus,
        dueDate = null,
        attachedForm = emptyMap(),
        assignedBuyerId = null,
        member = CompleteUserObjectMother.seller().member,
        rejectionReason = null
    )

    private fun givenTaskInStatus(taskStatus: TaskStatus, taskType: TaskType) =
        TaskBuilder().withStatus(taskStatus).withTypeKey(taskType.key).build().also {
            every { taskRepository.findById(it.id) } returns it
        }

    private fun disableTask(task: Task) =
        every { taskRepository.findById(task.id) } returns task.copy(enabled = false)
}
