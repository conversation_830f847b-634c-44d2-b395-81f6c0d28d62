package realestate.unlock.dealroom.api.unit.entrypoint.rest.contract.paginated.filters

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.search.DateTimeOperator
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableDateTime
import java.time.LocalDate

class SearchableDateTimeTest {

    private fun createSearchableDateTime(operator: DateTimeOperator, values: List<LocalDate>, mustFail: Boolean = true) {
        try {
            SearchableDateTime(operator, values)
            Assertions.assertFalse(mustFail)
        } catch (e: Exception) {
            print(e)
            Assertions.assertTrue(mustFail)
        }
    }

    @Test
    fun `can create datetime search with 1 value`() {
        DateTimeOperator.values().filter { it != DateTimeOperator.BETWEEN }.forEach {
            createSearchableDateTime(it, listOf(LocalDate.now()), mustFail = false)
        }
    }

    @Test
    fun `can create datetime search with 2 value`() {
        createSearchableDateTime(DateTimeOperator.BETWEEN, listOf(LocalDate.now(), LocalDate.now()), mustFail = false)
    }
}
