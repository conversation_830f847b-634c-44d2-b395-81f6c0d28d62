package realestate.unlock.dealroom.api.unit.core.usecase.calendar

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.ExternalDealCalendarEventInput
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarTaskEvent
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalDealCalendarEventsActions
import realestate.unlock.dealroom.api.utils.entity.deal.DealCategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import java.time.LocalDate

class ExternalCalendarTaskEventTest {

    private lateinit var target: ExternalCalendarTaskEvent

    @MockK
    private val eventsActions: ExternalDealCalendarEventsActions = mockk()

    @MockK
    private val dealCategoryRepository: DealCategoryRepository = mockk()

    @BeforeEach
    fun setUp() {
        target = ExternalCalendarTaskEvent(
            eventsActions = eventsActions,
            dealCategoryRepository = dealCategoryRepository
        )
    }

    @Test
    fun `should createOrUpdate a calendar event if task has date`() {
        val category = DealCategoryBuilder().build()
        val task = TaskBuilder().apply {
            dueDate = LocalDate.now().plusDays(3)
            dealCategoryId = category.id
        }.build()

        every { eventsActions.createOrUpdate(any()) } returns Unit
        every { dealCategoryRepository.findById(task.dealCategoryId) } returns category
        target.impactOnCalendar(task)

        verify(exactly = 1) {
            eventsActions.createOrUpdate(
                input = ExternalDealCalendarEventInput(
                    dealId = category.dealId,
                    referenceId = task.id.toString(),
                    referenceType = "task",
                    title = task.title,
                    description = task.description,
                    date = task.dueDate!!
                )
            )
        }
    }

    @Test
    fun `should delete a calendar event if task hasn't date`() {
        val category = DealCategoryBuilder().build()
        val task = TaskBuilder().apply {
            dueDate = null
            dealCategoryId = category.id
        }.build()

        every { dealCategoryRepository.findById(task.dealCategoryId) } returns category
        every { eventsActions.delete(any(), any(), any()) } returns Unit

        target.impactOnCalendar(task)

        verify(exactly = 1) {
            eventsActions.delete(
                dealId = category.dealId,
                referenceId = task.id.toString(),
                referenceType = "task"
            )
        }
    }

    @Test
    fun `should delete a calendar event if task is done`() {
        val category = DealCategoryBuilder().build()
        val task = TaskBuilder().apply {
            dueDate = LocalDate.now().plusDays(3)
            statusKey = TaskStatus.DONE
            dealCategoryId = category.id
        }.build()

        every { dealCategoryRepository.findById(task.dealCategoryId) } returns category
        every { eventsActions.delete(any(), any(), any()) } returns Unit

        target.impactOnCalendar(task)

        verify(exactly = 1) {
            eventsActions.delete(
                dealId = category.dealId,
                referenceId = task.id.toString(),
                referenceType = "task"
            )
        }
    }
}
