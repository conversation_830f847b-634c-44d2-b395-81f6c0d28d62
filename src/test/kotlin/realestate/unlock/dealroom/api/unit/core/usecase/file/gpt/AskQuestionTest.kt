package realestate.unlock.dealroom.api.unit.core.usecase.file.gpt

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.file.gpt.*
import realestate.unlock.dealroom.api.core.gateway.gpt.ChatGptGateway
import realestate.unlock.dealroom.api.core.gateway.gpt.ChatGptGateway.AskQuestionInput
import realestate.unlock.dealroom.api.core.repository.file.FileQuestionHistoryRepository
import realestate.unlock.dealroom.api.core.repository.file.FileTokenRepository
import realestate.unlock.dealroom.api.core.usecase.file.gpt.AskQuestion
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.NotFoundException
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.Instant
import java.time.ZoneOffset

@ExtendWith(MockKExtension::class)
class AskQuestionTest {

    @MockK
    private lateinit var fileTokenRepository: FileTokenRepository

    @MockK
    private lateinit var chatGptGateway: ChatGptGateway

    @MockK
    private lateinit var fileQuestionHistoryRepository: FileQuestionHistoryRepository

    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)

    private lateinit var askQuestion: AskQuestion

    companion object {
        const val DEFAULT_QUESTION = "are you ready?"
        const val GIVEN_MEMBER_ID = 2L
    }

    @BeforeEach
    fun setUp() {
        askQuestion = AskQuestion(
            fileTokenRepository = fileTokenRepository,
            chatGptGateway = chatGptGateway,
            fileQuestionHistoryRepository = fileQuestionHistoryRepository,
            clock = clock
        )
    }

    @Test
    fun `if the file wasn't uploaded to be processed it should throw an exception`() {
        val givenFileId = anyString()
        every { fileTokenRepository.findByFileId(givenFileId) } returns null

        assertThrows<NotFoundException> {
            askQuestion(
                AskQuestion.Input(
                    fileId = givenFileId,
                    question = DEFAULT_QUESTION,
                    memberId = GIVEN_MEMBER_ID,
                    prompt = "any prompt",
                    authToken = anyString()
                )
            )
        }
    }

    @Test
    fun `if the file is ready it can ask questions successfully`() {
        val givenFileId = anyString()
        val givenToken = anyString()
        val givenDealId = anyId()
        val givenFileToken = FileToken(kFileId = givenFileId, token = givenToken, status = FileTokenStatus.READY, fileType = TokenFileType.REPORT, dealId = givenDealId)
        val givenQuestionAnswer = anyString()
        val authToken = anyString()

        every { fileTokenRepository.findByFileId(givenFileId) } returns givenFileToken
        every { chatGptGateway.askQuestion(AskQuestionInput(givenToken, DEFAULT_QUESTION, "lala", authToken)) } returns givenQuestionAnswer
        every { fileQuestionHistoryRepository.save(any()) } returns Unit

        val got = askQuestion(
            AskQuestion.Input(
                fileId = givenFileId,
                question = DEFAULT_QUESTION,
                memberId = null,
                prompt = "lala",
                authToken = authToken
            )
        )

        assertThat(got, equalTo(givenQuestionAnswer))
        verify { chatGptGateway.askQuestion(AskQuestionInput(givenToken, DEFAULT_QUESTION, "lala", authToken)) }
        verify {
            fileQuestionHistoryRepository.save(
                FileQuestionHistory(
                    kFileId = givenFileId,
                    questionType = FileQuestionType.GENERAL,
                    question = DEFAULT_QUESTION,
                    answer = givenQuestionAnswer,
                    memberId = null,
                    createdAt = clock.instant()
                )
            )
        }
    }
}
