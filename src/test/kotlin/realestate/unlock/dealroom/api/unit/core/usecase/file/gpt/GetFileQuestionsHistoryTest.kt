package realestate.unlock.dealroom.api.unit.core.usecase.file.gpt

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileQuestionHistory
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileQuestionType
import realestate.unlock.dealroom.api.core.repository.file.FileQuestionHistoryRepository
import realestate.unlock.dealroom.api.core.usecase.file.gpt.GetFileQuestionsHistory
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Instant

@ExtendWith(MockKExtension::class)
class GetFileQuestionsHistoryTest {

    @MockK
    private lateinit var fileQuestionHistoryRepository: FileQuestionHistoryRepository

    private lateinit var getFileQuestionsHistory: GetFileQuestionsHistory

    @BeforeEach
    fun setUp() {
        getFileQuestionsHistory = GetFileQuestionsHistory(fileQuestionHistoryRepository)
    }

    @Test
    fun `it retrieves the history successfully`() {
        val givenFileId = anyString()
        val givenMemberId = 2L
        val givenFirstQuestion = givenQuestion(FileQuestionType.GENERAL)
        val givenSecondQuestion = givenQuestion(FileQuestionType.FINDINGS)

        val givenHistory = listOf(givenFirstQuestion, givenSecondQuestion)

        every { fileQuestionHistoryRepository.get(givenFileId, givenMemberId) } returns givenHistory

        val got = getFileQuestionsHistory(fileId = givenFileId, memberId = givenMemberId)

        assertThat(got, hasSize(1))
        assertThat(got[0], equalTo(givenFirstQuestion))
    }

    private fun givenQuestion(fileQuestionType: FileQuestionType) = FileQuestionHistory(
        kFileId = anyString(),
        questionType = fileQuestionType,
        question = anyString(),
        answer = anyString(),
        memberId = null,
        createdAt = Instant.now()
    )
}
