package realestate.unlock.dealroom.api.unit.core.usecase.deal.schema

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.exception.InvalidJsonSchemaException
import realestate.unlock.dealroom.api.core.usecase.deal.schema.ValidateJsonSchema

class ValidateJsonSchemaTest {

    private lateinit var validateJsonSchema: ValidateJsonSchema
    private val schema = """
            {
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string"
                    },
                    "birthday": {
                        "type": "string",
                        "format": "date"
                    },
                    "age": {
                        "description": "Age in years",
                        "type": "integer",
                        "minimum": 0
                    }
                }
            }
    """.trimIndent()

    @BeforeEach
    fun setUp() {
        validateJsonSchema = ValidateJsonSchema()
    }

    @Test
    fun `valid json should pass`() {
        val json = """
            {
                "name": "<PERSON>",
                "birthday": "1990-01-01",
                "age": 21
            }
        """.trimIndent()

        validateJsonSchema.validateSchema(schema, json)
    }

    @Test
    fun `invalid date format should fail`() {
        val json = """
            {
                "birthday": "sa ra sa"
            }
        """.trimIndent()

        assertThrows(InvalidJsonSchemaException::class.java) {
            validateJsonSchema.validateSchema(schema, json)
        }.also {
            assertEquals("Json not valid for schema: [#/birthday Value fails format check \"date\", was \"sa ra sa\"]", it.message)
        }
    }

    @Test
    fun `invalid string in integer should fail`() {
        val json = """
            {
                "age": "12"
            }
        """.trimIndent()

        assertThrows(InvalidJsonSchemaException::class.java) {
            validateJsonSchema.validateSchema(schema, json)
        }.also {
            assertEquals("Json not valid for schema: [#/age Incorrect type, expected integer]", it.message)
        }
    }

    @Test
    fun `invalid minimum in integer should fail`() {
        val json = """
            {
                "age": -1
            }
        """.trimIndent()

        assertThrows(InvalidJsonSchemaException::class.java) {
            validateJsonSchema.validateSchema(schema, json)
        }.also {
            assertEquals("Json not valid for schema: [#/age Number fails check: minimum 0, was -1]", it.message)
        }
    }

    @Test
    fun `multiples errors should fail`() {
        val json = """
            {
                "name": true,
                "birthday": "sa ra sa",
                "age": -1
            }
        """.trimIndent()

        assertThrows(InvalidJsonSchemaException::class.java) {
            validateJsonSchema.validateSchema(schema, json)
        }.also {
            assertEquals(
                "Json not valid for schema: [#/name Incorrect type, expected string, #/birthday Value fails format check \"date\", was \"sa ra sa\", #/age Number fails check: minimum 0, was -1]",
                it.message
            )
        }
    }
}
