package realestate.unlock.dealroom.api.unit.core.usecase.file.gpt

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.DealData
import realestate.unlock.dealroom.api.core.entity.deal.findings.EntityType
import realestate.unlock.dealroom.api.core.entity.deal.findings.FindingQuestion
import realestate.unlock.dealroom.api.core.entity.deal.findings.FindingQuestionResponse
import realestate.unlock.dealroom.api.core.entity.deal.findings.FindingStatus
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileQuestionType
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileToken
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileTokenStatus
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.repository.deal.finding.FindingQuestionsRepository
import realestate.unlock.dealroom.api.core.repository.deal.report.DealReportRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealData
import realestate.unlock.dealroom.api.core.usecase.deal.findings.CreateFinding
import realestate.unlock.dealroom.api.core.usecase.file.gpt.AskQuestion
import realestate.unlock.dealroom.api.core.usecase.file.gpt.RunFindings
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@ExtendWith(MockKExtension::class)
class RunFindingsTest {

    @MockK
    private lateinit var findingQuestionRepository: FindingQuestionsRepository

    @MockK
    private lateinit var createFinding: CreateFinding

    @MockK
    private lateinit var askQuestion: AskQuestion

    @MockK
    private lateinit var dealReportRepository: DealReportRepository

    @MockK
    private lateinit var taskRepository: TaskRepository

    @MockK
    private lateinit var getDealData: GetDealData

    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)

    private lateinit var runFindings: RunFindings

    @BeforeEach
    fun setUp() {
        runFindings = RunFindings(
            findingQuestionsRepository = findingQuestionRepository,
            createFinding = createFinding,
            askQuestion = askQuestion,
            getDealData = getDealData,
            dealReportRepository = dealReportRepository,
            taskRepository = taskRepository,
            clock = clock
        )
    }

    @Test
    fun `it runs findings validations`() {
        val givenFileId = anyString()
        val givenDealId = 412L

        val fileToken = givenFileToken(givenFileId, givenDealId)
        val givenDeal = DealBuilder().apply {
            this.id = givenDealId
            this.offerPrice = BigDecimal(51231)
        }.build()
        val givenProperty = PropertyBuilder().apply {
            this.id = givenDeal.propertyId
            this.type = PropertyType.MULTIFAMILY
        }.build()
        val firstFindingQuestion = "check if the property name is \$_property.name_"
        val secondFindingQuestion = "check if the offer price is \$_deal.offerPrice_"
        val defaultPrompt = "this is the default prompt"
        val authToken = anyString()
        every { getDealData.get(givenDealId) } returns DealData(deal = givenDeal, property = givenProperty)
        every {
            findingQuestionRepository.findByEntityAndType(entityType = EntityType.DOCUMENT, type = "LOI", propertyType = PropertyType.MULTIFAMILY)
        } returns listOf(
            givenFindingQuestion(query = firstFindingQuestion, defaultPrompt),
            givenFindingQuestion(query = secondFindingQuestion, defaultPrompt)
        )
        val firstQuestionMock = mockGptCall(givenFileId, firstFindingQuestion, "\$_property.name_", givenProperty.name!!, defaultPrompt, authToken)
        val secondQuestionMock = mockGptCall(givenFileId, secondFindingQuestion, "\$_deal.offerPrice_", givenDeal.offerPrice!!.toString(), defaultPrompt, authToken)

        every { createFinding(any()) } returns Unit

        runFindings(fileToken, authToken = authToken)

        verify(exactly = 1) {
            createFinding(
                CreateFinding.Input(
                    dealId = givenDealId,
                    entity = EntityType.DOCUMENT,
                    entityType = "LOI",
                    kFileId = givenFileId,
                    message = firstQuestionMock.explanation,
                    status = FindingStatus.OPEN,
                    createdAt = OffsetDateTime.now(clock)
                )
            )
        }

        verify(exactly = 1) {
            createFinding(
                CreateFinding.Input(
                    dealId = givenDealId,
                    entity = EntityType.DOCUMENT,
                    entityType = "LOI",
                    kFileId = givenFileId,
                    message = secondQuestionMock.explanation,
                    status = FindingStatus.OPEN,
                    createdAt = OffsetDateTime.now(clock)
                )
            )
        }
    }

    private fun givenFileToken(fileId: String, dealId: Long) = FileToken(
        dealId = dealId,
        kFileId = fileId,
        token = anyString(),
        status = FileTokenStatus.READY,
        fileType = TokenFileType.LOI
    )

    private fun givenFindingQuestion(query: String, prompt: String) =
        FindingQuestion(
            id = anyId(),
            entity = EntityType.DOCUMENT,
            entityType = "LOI",
            query = query,
            prompt = prompt
        )

    private fun mockGptCall(fileId: String, question: String, variable: String, value: String, prompt: String, authToken: String) =
        FindingQuestionResponse(
            errorFound = true,
            explanation = anyString()
        ).also {
            every {
                askQuestion(
                    AskQuestion.Input(
                        fileId = fileId,
                        question = question.replace(variable, value),
                        prompt = prompt,
                        memberId = null,
                        FileQuestionType.FINDINGS,
                        authToken = authToken
                    )
                )
            } returns JsonMapper.encode(it)
        }
}
