package realestate.unlock.dealroom.api.unit.infrastructure.utils.file

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.utils.file.SanitizedFilename

class SanitizedFilenameTest {

    @Test
    fun `keeps valid chars`() {
        // given
        val filename = "asd1234_-.ext"

        // when
        val result = SanitizedFilename(filename)

        // then
        assertThat(result.value, IsEqual(filename))
    }

    @Test
    fun `replaces invalid chars with underscore if they are in the middle`() {
        // given
        val filename = "!@#$%^&*()some t3x!@#%^&*()t.ext"

        // when
        val result = SanitizedFilename(filename)

        // then
        assertThat(result.value, IsEqual("some_t3x_t.ext"))
    }

    @Test
    fun `removes invalid chars if they are at the start`() {
        // given
        val filename = "!@#$%^&*()some_t3xt.ext"

        // when
        val result = SanitizedFilename(filename)

        // then
        assertThat(result.value, IsEqual("some_t3xt.ext"))
    }

    @Test
    fun `removes invalid chars if they are at the end`() {
        // given
        val filename = "some_t3xt.ext!@#$%^&*()"

        // when
        val result = SanitizedFilename(filename)

        // then
        assertThat(result.value, IsEqual("some_t3xt.ext"))
    }
}
