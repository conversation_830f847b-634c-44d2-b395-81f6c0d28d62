package realestate.unlock.dealroom.api.unit.core.usecase.dashboard.calendar

import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventType
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput
import realestate.unlock.dealroom.api.core.entity.calendar.model.DocumentCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.repository.calendar.CalendarEventRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.GetCalendarDocumentEvents
import java.time.OffsetDateTime

class GetCalendarDocumentEventsTest {

    private val calendarEventRepository: CalendarEventRepository = mockk()

    private lateinit var getCalendarDocumentEvents: GetCalendarDocumentEvents

    @BeforeEach
    fun setUp() {
        getCalendarDocumentEvents = GetCalendarDocumentEvents(calendarEventRepository)
    }

    @Test
    fun `should return document events`() {
        val document: DocumentCalendarEventModel = mockk()
        every { document.id } returns 1
        every { document.type } returns DocumentType.LEASE
        every { document.dealId } returns 2L
        every { document.expectedBy } returns OffsetDateTime.now()
        every { document.propertyTitle() } returns "property name"

        every { calendarEventRepository.findDocumentsEvents(any()) } returns listOf(document)

        val events = getCalendarDocumentEvents.get(CalendarEventsInput(untilDays = 5, dealId = 2, organizationId = "AB1"))

        assertEquals(1, events.size)
        assertEquals(CalendarEventType.DOCUMENT_LEASE, events[0].type)
        assertEquals("Lease", events[0].title)
    }
}
