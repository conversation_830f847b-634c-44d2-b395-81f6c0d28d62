package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealStage
import realestate.unlock.dealroom.api.core.usecase.deal.update.DealUpdater
import realestate.unlock.dealroom.api.core.usecase.deal.update.UpdateDealStage
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class UpdateDealStageTest {

    @MockK
    private lateinit var dealUpdater: DealUpdater

    @MockK
    private lateinit var changeDealStage: ChangeDealStage

    private lateinit var updateDealStage: UpdateDealStage

    @BeforeEach
    fun setUp() {
        updateDealStage = UpdateDealStage(dealUpdater, changeDealStage)
    }

    @Test
    fun `It updates the deal stage and its dates as expected`() {
        val givenDeal = DealBuilder().build()
        val givenDealId = 123L
        val givenInput = UpdateDealStage.Input(
            dealId = givenDealId,
            newStage = Stage.POST_CLOSING,
            evaluationDueDate = LocalDate.now().minusDays(10),
            underwritingDueDate = LocalDate.now().minusDays(9),
            offerDueDate = LocalDate.now().minusDays(8),
            negotiationDueDate = LocalDate.now().minusDays(7),
            diligenceDueDate = LocalDate.now().minusDays(6),
            closingDueDate = LocalDate.now().minusDays(5),
            postClosingDueDate = LocalDate.now().minusDays(4)
        )
        val updatedDeal = mockk<Deal>()
        every { changeDealStage.invoke(givenDealId, givenInput.newStage, null) } returns givenDeal
        every { dealUpdater.update(any()) } returns mockk()

        updateDealStage(givenInput)

        verify {
            dealUpdater.update(
                givenDeal.copy(
                    evaluationDueDate = givenInput.evaluationDueDate,
                    underwritingDueDate = givenInput.underwritingDueDate,
                    loiExecutedDate = givenInput.offerDueDate,
                    diligenceExpirationDate = givenInput.diligenceDueDate,
                    initialClosingDate = givenInput.closingDueDate,
                    outsideClosingDate = givenInput.postClosingDueDate,
                    contractExecutedDate = givenInput.negotiationDueDate
                )
            )
        }
    }
}
