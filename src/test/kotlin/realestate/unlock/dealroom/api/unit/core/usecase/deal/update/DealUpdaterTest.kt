package realestate.unlock.dealroom.api.unit.core.usecase.deal.update

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.event.deal.DealEventPublisher
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarStageEvent
import realestate.unlock.dealroom.api.core.usecase.deal.update.DealUpdater
import realestate.unlock.dealroom.api.core.usecase.task.UpdateTaskDueDates
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder

@ExtendWith(MockKExtension::class)
class DealUpdaterTest {

    @MockK
    private lateinit var dealRepository: DealRepository

    @MockK
    private lateinit var dealEventPublisher: DealEventPublisher

    @MockK
    private lateinit var updateTaskDueDates: UpdateTaskDueDates

    @MockK
    private lateinit var externalCalendarStageEvent: ExternalCalendarStageEvent

    private lateinit var dealUpdater: DealUpdater

    @BeforeEach
    fun setUp() {
        every { dealEventPublisher.publish(any<Deal>()) } returns Unit
        every { updateTaskDueDates.update(any<Deal>()) } returns Unit
        every { externalCalendarStageEvent.impactOnCalendar(dealId = any(), date = any(), stage = any()) } returns Unit
        dealUpdater = DealUpdater(
            dealRepository = dealRepository,
            dealEventPublisher = dealEventPublisher,
            updateTaskDueDates = updateTaskDueDates,
            externalCalendarStageEvent = externalCalendarStageEvent
        )
    }

    @Test
    fun `it updates the deal`() {
        val givenDeal = DealBuilder().build()
        every { dealRepository.update(givenDeal) } returns givenDeal

        dealUpdater.update(givenDeal)

        verify { dealRepository.update(givenDeal) }
    }

    @Test
    fun `it updates the schema`() {
        val givenDeal = DealBuilder().build()
        every { dealRepository.updateDealSchemaData(dealId = givenDeal.id, dealSchemaData = givenDeal.schemaData) } returns mockk()

        dealUpdater.updateSchemaData(givenDeal)

        verify { dealRepository.updateDealSchemaData(givenDeal.id, givenDeal.schemaData) }
    }

    @Test
    fun `it send a sns notification when updating a deal`() {
        val givenDeal = DealBuilder().build()
        every { dealRepository.update(givenDeal) } returns givenDeal

        dealUpdater.update(givenDeal)

        verify {
            dealEventPublisher.publish(givenDeal)
        }
    }

    @Test
    fun `do not send a sns notification when updating a deal schema data if flag is false`() {
        val givenDeal = DealBuilder().build()
        every { dealRepository.updateDealSchemaData(dealId = givenDeal.id, dealSchemaData = givenDeal.schemaData) } returns mockk()

        dealUpdater.updateSchemaData(givenDeal, triggerDealEvent = false)

        verify(exactly = 0) {
            dealEventPublisher.publish(givenDeal)
        }
    }

    @Test
    fun `do not send a calendar event if update is schema`() {
        val givenDeal = DealBuilder().build()
        every { dealRepository.updateDealSchemaData(dealId = givenDeal.id, dealSchemaData = givenDeal.schemaData) } returns mockk()

        dealUpdater.updateSchemaData(givenDeal, triggerDealEvent = false)

        verify(exactly = 0) {
            externalCalendarStageEvent.impactOnCalendar(
                dealId = any(),
                date = any(),
                stage = any()
            )
        }
    }

    @Test
    fun `send a calendar event if update`() {
        val givenDeal = DealBuilder().build()
        every { dealRepository.update(givenDeal) } returns givenDeal

        dealUpdater.update(givenDeal)

        verify(exactly = 2) {
            externalCalendarStageEvent.impactOnCalendar(
                dealId = any(),
                date = any(),
                stage = any()
            )
        }
    }
}
