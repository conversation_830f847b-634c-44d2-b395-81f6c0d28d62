package realestate.unlock.dealroom.api.unit.core.usecase.member

import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.MemberSearch
import realestate.unlock.dealroom.api.core.entity.member.type.MemberType
import realestate.unlock.dealroom.api.core.usecase.member.filter.FilterMemberByEnabled
import realestate.unlock.dealroom.api.core.usecase.member.filter.FilterMemberByMemberType

class FilterMemberTest {

    @Test
    fun `should create filter by member type`() {
        val memberType = "seller"
        var search = MemberSearch(memberType, null)

        val filter = FilterMemberByMemberType(
            mockk {
                every { findByKey(any()) } returns MemberType(1, memberType, memberType)
            }
        )

        val result = filter.get(search)

        assertNotNull(result)
        assertEquals(result!!.first, "AND type_key = ?")
        assertEquals(result.second, memberType)

        assertNull(filter.get(MemberSearch(null, null)))
    }

    @Test
    fun `should create filter by member enabled`() {
        val filter = FilterMemberByEnabled()
        var result = filter.get(MemberSearch(null, true))

        assertNotNull(result)
        assertEquals(result!!.first, "AND enabled = ?")
        assertEquals(result.second, true)

        result = filter.get(MemberSearch(null, false))
        assertNotNull(result)
        assertEquals(result!!.first, "AND enabled = ?")
        assertEquals(result.second, false)

        assertNull(filter.get(MemberSearch(null, null)))
    }
}
