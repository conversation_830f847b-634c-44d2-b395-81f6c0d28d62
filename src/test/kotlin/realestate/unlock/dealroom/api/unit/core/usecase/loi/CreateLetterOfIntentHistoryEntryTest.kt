package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntry
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntryMember
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntryType
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentHistoryEntryRepository
import realestate.unlock.dealroom.api.core.usecase.loi.CreateLetterOfIntentHistoryEntry
import realestate.unlock.dealroom.api.core.usecase.loi.CreateLetterOfIntentHistoryEntryInput
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

class CreateLetterOfIntentHistoryEntryTest {

    private val letterOfIntentHistoryEntryRepository: LetterOfIntentHistoryEntryRepository = mockk(relaxed = true)
    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)

    private val target = CreateLetterOfIntentHistoryEntry(letterOfIntentHistoryEntryRepository, clock)

    @Test
    fun `saves input into repository adding the date`() {
        // given
        val givenKFile = KFile(anyString(), anyString())
        val givenMember = MemberObjectMother.buyer()
        val givenInput = givenInput(givenKFile, givenMember)

        // when
        target.execute(givenInput)

        // then
        verify {
            letterOfIntentHistoryEntryRepository.save(
                expectedHistoryEntry(givenInput, givenKFile, givenMember)
            )
        }
    }

    @Test
    fun `saves input without file repository adding the date`() {
        // given
        val givenMember = MemberObjectMother.buyer()
        val givenInput = givenInput(member = givenMember)

        // when
        target.execute(givenInput)

        // then
        verify {
            letterOfIntentHistoryEntryRepository.save(
                expectedHistoryEntry(givenInput, member = givenMember)
            )
        }
    }

    @Test
    fun `saves input without member repository adding the date`() {
        // given
        val givenKFile = KFile(anyString(), anyString())
        val givenInput = givenInput(kFile = givenKFile)

        // when
        target.execute(givenInput)

        // then
        verify {
            letterOfIntentHistoryEntryRepository.save(
                expectedHistoryEntry(givenInput, kFile = givenKFile)
            )
        }
    }

    private fun givenInput(
        kFile: KFile? = null,
        member: Member? = null
    ) = CreateLetterOfIntentHistoryEntryInput(
        dealId = 1,
        loiId = 1,
        type = LetterOfIntentHistoryEntryType.OFFER,
        description = "some description",
        kFile = kFile,
        member = member
    )

    private fun expectedHistoryEntry(
        givenInput: CreateLetterOfIntentHistoryEntryInput,
        kFile: KFile? = null,
        member: Member? = null
    ) = LetterOfIntentHistoryEntry(
        id = 0,
        dealId = givenInput.dealId,
        loiId = givenInput.loiId,
        type = givenInput.type,
        description = givenInput.description,
        createdAt = OffsetDateTime.ofInstant(clock.instant(), ZoneOffset.UTC),
        kFile = kFile,
        member = member?.let {
            LetterOfIntentHistoryEntryMember(member.fullName, member.typeKey)
        }
    )
}
