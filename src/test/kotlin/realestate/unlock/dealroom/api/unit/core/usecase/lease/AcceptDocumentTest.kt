package realestate.unlock.dealroom.api.unit.core.usecase.lease

import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.usecase.deal.documents.AcceptDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.InteractWithDocumentRoundTest
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class AcceptDocumentTest : InteractWithDocumentRoundTest() {

    companion object {
        private val now = Instant.now()
    }

    private lateinit var acceptDocument: AcceptDocument

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        acceptDocument = AcceptDocument(
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            clock = clock,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `can accept a lease`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val leaseId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = leaseId)
        givenDocumentWithCurrentRound(id = leaseId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.SELLER_REVIEW, documentType = DocumentType.LEASE)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "shared",
            member = user.member,
            documentType = DocumentType.LEASE
        )

        val leaseUpdated = acceptDocument.update(input)

        assertDocumentWasUpdated(leaseUpdated, DocumentStatus.PENDING_FINAL_VERSION)
        assertInteractionWasCreated(draftDocuments, input, Interaction.Type.APPROVE)
    }

    @Test
    fun `cannot accept a Lease if its status is not SELLER_REVIEW`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val leaseId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = leaseId)
        givenDocumentWithCurrentRound(id = leaseId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.LEASE)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "shared",
            member = user.member,
            documentType = DocumentType.LEASE
        )

        assertThrows<InvalidDocumentStatusException> { acceptDocument.update(input) }
    }
}
