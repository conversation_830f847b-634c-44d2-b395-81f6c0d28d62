package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.usecase.deal.documents.DocumentFileHelper
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FileType
import java.time.OffsetDateTime

class DocumentFileHelperTest {

    @Test
    fun `get psa file path`() {
        val document = Document(
            id = 1,
            dealId = 2,
            status = DocumentStatus.NOT_STARTED,
            currentRoundId = 10,
            executedAt = null,
            draftSubmittedAt = null,
            expectedBy = null,
            updatedAt = OffsetDateTime.now(),
            createdAt = OffsetDateTime.now(),
            type = DocumentType.PSA
        )
        val fileType = FileType.CLEAN_VERSION

        val path = DocumentFileHelper.filePath(document, fileType)

        Assertions.assertEquals("deal/2/psa/1/draft/10_CLEAN_VERSION", path)
    }
}
