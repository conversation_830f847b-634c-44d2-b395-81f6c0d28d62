package realestate.unlock.dealroom.api.unit.core.usecase.loi

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.usecase.loi.GetLoiEmailParams
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.MemberSignBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder

class GetLoiEmailParamsTest {
    private val target = GetLoiEmailParams()

    @Test
    fun `builds buyer email params`() {
        // given
        val property = PropertyBuilder().build()
        val givenLoi = LoiMedicalRoundBuilder().build()
        val givenBuyer = MemberObjectMother.buyer()

        // when
        val result = target.getBuyerTemplate(givenBuyer, givenLoi, property)

        // then
        assertThat(result.subject, IsEqual("Letter of Intent for ${property.address.fullAddress()}"))
        assertThat(result.body, IsEqual(""))
    }

    @Test
    fun `builds seller email params`() {
        // given
        val property = PropertyBuilder().build()
        val givenLoi = LoiMedicalRoundBuilder().build()
        val givenBuyer = MemberObjectMother.buyer()
        val givenSeller = MemberSignBuilder().build()

        // when
        val result = target.getSellerTemplate(givenSeller, givenLoi, givenBuyer, property)

        // then
        assertThat(result.subject, IsEqual("Letter of Intent for ${property.address.fullAddress()}"))
        assertThat(
            result.body,
            IsEqual(
                """
                    Please see and sign the final offer for ${property.address.fullAddress()} below.
                """.trimIndent()
            )
        )
    }

    @Test
    fun `builds carbon copy email params`() {
        // given
        val property = PropertyBuilder().build()
        val givenLoi = LoiMedicalRoundBuilder().build()
        val givenBuyer = MemberObjectMother.buyer()
        val givenSellerCounsel = MemberObjectMother.sellerCounsel()

        // when
        val result = target.getCarbonCopyTemplate(givenSellerCounsel, givenLoi, givenBuyer, property)

        // then
        assertThat(result.subject, IsEqual("Letter of Intent for ${property.address.fullAddress()}"))
        assertThat(
            result.body,
            IsEqual(
                """
            Hi ${givenSellerCounsel.fullName}, 
    
            Please see the attached LOI for ${property.address.fullAddress()}. 
            
            Happy to answer any questions.
            
            Best,
            ${givenBuyer.fullName}
                """.trimIndent()
            )
        )
    }
}
