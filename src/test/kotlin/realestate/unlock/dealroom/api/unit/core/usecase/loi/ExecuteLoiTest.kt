package realestate.unlock.dealroom.api.unit.core.usecase.loi

import org.hamcrest.MatcherAssert
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.loi.ExecutedLetterOfIntentRepository
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealStage
import realestate.unlock.dealroom.api.core.usecase.deal.update.DealUpdater
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SendFileToProcess
import realestate.unlock.dealroom.api.core.usecase.loi.CreateLetterOfIntentHistoryEntry
import realestate.unlock.dealroom.api.core.usecase.loi.ExecuteLoi
import realestate.unlock.dealroom.api.core.usecase.loi.GetLetterOfIntentRound
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.PostLoiRequest
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.loi.CreateLoiMediator
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.PostMultifamilyLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.Clock

class ExecuteLoiTest : BaseFunctionalWithoutRestTest() {
    private val letterOfIntentRoundRepository: LetterOfIntentRoundRepository = Context.injector.getInstance(LetterOfIntentRoundRepository::class.java)
    private val dealRepository: DealRepository = Context.injector.getInstance(DealRepository::class.java)
    private val executedLetterOfIntentRepository: ExecutedLetterOfIntentRepository = Context.injector.getInstance(ExecutedLetterOfIntentRepository::class.java)
    private val createLetterOfIntentHistoryEntry: CreateLetterOfIntentHistoryEntry = Context.injector.getInstance(CreateLetterOfIntentHistoryEntry::class.java)
    private val clock: Clock = Context.injector.getInstance(Clock::class.java)
    private val changeDealStage: ChangeDealStage = Context.injector.getInstance(ChangeDealStage::class.java)
    private val dealUpdater: DealUpdater = Context.injector.getInstance(DealUpdater::class.java)
    private val sendFileToProcess: SendFileToProcess = Context.injector.getInstance(SendFileToProcess::class.java)
    private lateinit var executeLoi: ExecuteLoi

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        executeLoi = ExecuteLoi(
            letterOfIntentRoundRepository,
            dealRepository,
            executedLetterOfIntentRepository,
            createLetterOfIntentHistoryEntry,
            clock,
            changeDealStage,
            dealUpdater,
            sendFileToProcess
        )
    }

    @Test
    fun `it doesn't update status if is after NEGOTIATION`() {
        executeLoi(
            PostMultifamilyLoiRequestBuilder().build(),
            CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY },
            Stage.DILIGENCE,
            Stage.DILIGENCE
        )
    }

    @Test
    fun `can create a multifamily executed loi`() {
        executeLoi(
            PostMultifamilyLoiRequestBuilder().build(),
            CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY }
        )
    }

    @Test
    fun `can create a medical executed loi`() {
        executeLoi(
            PostMedicalLoiRequestBuilder().build(),
            CreateDealInputBuilder().apply { propertyType = PropertyType.MEDICAL }
        )
    }

    private fun executeLoi(request: PostLoiRequest, dealCreationRequestBuilder: CreateDealInputBuilder, initialStage: Stage = Stage.OFFER, finalStage: Stage = Stage.NEGOTIATION) {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = dealCreationRequestBuilder)
        val mediator: CreateLoiMediator = Context.injector.getInstance(CreateLoiMediator::class.java)
        val loi = mediator.invoke(
            request,
            deal.id,
            deal.members.first { member: Member -> member.isTeam(MemberDealTeam.BUYER) }.id,
            "buyer"
        )

        checkCurrentLoiRound(deal.id, LetterOfIntentRoundStatus.IN_NEGOTIATION)
        dealUpdater.update(dealRepository.findById(deal.id).copy(stage = initialStage))

        // When
        val loiExecuted = executeLoi.execute(
            loi,
            loi.getOfferFiles()
                .first().let { file -> KFile(name = file.name, uid = file.kFileId) },
            authToken = anyString()
        )

        Assertions.assertNotNull(letterOfIntentRoundRepository.getDealId(loi.id))
        Assertions.assertNotNull(executedLetterOfIntentRepository.findByDeal(deal.id))

        // Then
        checkCurrentLoiRound(deal.id, LetterOfIntentRoundStatus.EXECUTED)
        dealRepository.findById(deal.id).also {
            MatcherAssert.assertThat(it.stage, IsEqual(finalStage))
        }
    }

    private fun checkCurrentLoiRound(dealId: Long, status: LetterOfIntentRoundStatus?) {
        val getCurrentLoi: GetLetterOfIntentRound = Context.injector.getInstance(GetLetterOfIntentRound::class.java)
        val loi = getCurrentLoi.getByDealId(dealId)
        MatcherAssert.assertThat(loi?.status, IsEqual(status))
    }
}
