package realestate.unlock.dealroom.api.unit.core.entity.task

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.task.Priority
import realestate.unlock.dealroom.api.core.entity.task.PriorityValue

class PriorityTest {

    companion object {
        val highOne = Priority(PriorityValue.HIGH, 1)
        val highTwo = Priority(PriorityValue.HIGH, 2)
        val lowOne = Priority(PriorityValue.LOW, 1)
        val mediumThree = Priority(PriorityValue.MEDIUM, 3)
        val mediumThreeClone = Priority(PriorityValue.MEDIUM, 3)
    }

    @Test
    fun `can determinate that a priority is higher than other`() {
        Assertions.assertTrue(highOne > lowOne)
        Assertions.assertTrue(highOne > highTwo)
        Assertions.assertTrue(highOne > mediumThree)
        Assertions.assertTrue(highTwo > lowOne)
        Assertions.assertTrue(mediumThree > lowOne)
    }

    @Test
    fun `can determinate that a priority is equal than other`() {
        Assertions.assertTrue(mediumThree == mediumThreeClone)
    }

    @Test
    fun `can determinate that a priority is lower than other`() {
        Assertions.assertTrue(lowOne < highTwo)
        Assertions.assertTrue(lowOne < mediumThree)
    }
}
