package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.*
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.exception.DocumentSignerAlreadySignedException
import realestate.unlock.dealroom.api.core.exception.DocumentSignerNotFoundException
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentSignRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.CreateDocumentInteraction
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentAndCurrentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.SignDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.SellerSignDocumentImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.document.DocumentSignerBuilder
import realestate.unlock.dealroom.api.utils.entity.document.InteractionBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class SignDocumentTest {

    @MockK(relaxed = true)
    private lateinit var documentRepository: DocumentRepository

    @MockK(relaxed = true)
    private lateinit var createDocumentInteraction: CreateDocumentInteraction

    @MockK(relaxed = true)
    private lateinit var findDocumentAndCurrentRound: FindDocumentAndCurrentRound

    @MockK(relaxed = true)
    private lateinit var memberRepository: MemberRepository
    private val clock = Clock.systemDefaultZone()

    @MockK(relaxed = true)
    private lateinit var documentSignRepository: DocumentSignRepository

    @MockK(relaxed = true)
    private lateinit var sellerSignDocumentImpactOnTask: SellerSignDocumentImpactOnTask

    private lateinit var signDocument: SignDocument

    @BeforeEach
    fun setUp() {
        TransactionManager.initialize(mockk(relaxed = true))
        signDocument = SignDocument(
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            memberRepository = memberRepository,
            clock = clock,
            documentSignRepository = documentSignRepository,
            sellerSignDocumentImpactOnTask = sellerSignDocumentImpactOnTask
        )
    }

    @Test
    fun `seller can sign psa`() {
        val envelopeId = anyString()
        val psa = DocumentBuilder().apply { status = DocumentStatus.PENDING_SIGN }.build()
        val completeUser = CompleteUserObjectMother.seller()
        val signers = listOf(
            DocumentSigner(psa.id, completeUser.member, OffsetDateTime.now()),
            DocumentSignerBuilder().setSignCompleted(true).setBuyerMemberTeam().build()
        )
        val round = DocumentRoundObjectMother.finalDocument(documentId = psa.id)
        mockkCalls(envelopeId, signers, psa, completeUser.member, round)
        mockGetMember(round)
        val interaction = slot<CreateDocumentInteraction.Input>()
        every { createDocumentInteraction(capture(interaction)) } returns InteractionBuilder().build()
        val signer = slot<DocumentSigner>()
        every { documentSignRepository.update(capture(signer)) } returns Unit

        signDocument.sign(input = SignDocument.Input(envelopeId = envelopeId, recipientId = completeUser.member.id.toString()))

        Assertions.assertEquals(interaction.captured.interactionType, Interaction.Type.SELLER_SIGN)
        Assertions.assertNull(interaction.captured.comment)
        Assertions.assertEquals(interaction.captured.member, completeUser.member)

        Assertions.assertEquals(signer.captured.signerEmail, completeUser.member.email)
        Assertions.assertNotNull(signer.captured.signCompletedAt)
    }

    @Test
    fun `seller can sign psa and impact on task`() {
        val envelopeId = anyString()
        val psa = DocumentBuilder().apply { status = DocumentStatus.PENDING_SIGN }.build()
        val seller = MemberObjectMother.seller()
        val signers = listOf(
            DocumentSigner(psa.id, seller, OffsetDateTime.now()),
            DocumentSignerBuilder().setSignCompleted(false).setBuyerMemberTeam().build()
        )
        val round = DocumentRoundObjectMother.finalDocument(documentId = psa.id)
        mockkCalls(envelopeId, signers, psa, seller, round)
        mockGetMember(round)
        val interaction = slot<CreateDocumentInteraction.Input>()
        every { createDocumentInteraction(capture(interaction)) } returns InteractionBuilder().build()
        val signer = slot<DocumentSigner>()
        every { documentSignRepository.update(capture(signer)) } returns Unit

        val slot = slot<Document>()
        every { documentRepository.update(capture(slot)) } returns Unit
        signDocument.sign(input = SignDocument.Input(envelopeId = envelopeId, recipientId = seller.id.toString()))
        every { sellerSignDocumentImpactOnTask.invoke(any(), seller) } returns Unit

        Assertions.assertEquals(interaction.captured.interactionType, Interaction.Type.SELLER_SIGN)
        Assertions.assertNull(interaction.captured.comment)
        Assertions.assertEquals(interaction.captured.member, seller)

        Assertions.assertEquals(signer.captured.signerEmail, seller.email)
        Assertions.assertNotNull(signer.captured.signCompletedAt)

        verify(exactly = 1) {
            sellerSignDocumentImpactOnTask.invoke(slot.captured, seller)
        }
    }

    @Test
    fun `seller can sign psa and impact on task with no member`() {
        val envelopeId = anyString()
        val psa = DocumentBuilder().apply { status = DocumentStatus.PENDING_SIGN }.build()
        val signerData = DocumentSignerBuilder().apply { documentId = psa.id }.build()
        val signers = listOf(
            signerData,
            DocumentSignerBuilder().setSignCompleted(false).setBuyerMemberTeam().build()
        )
        val adminMember = MemberObjectMother.buyer()
        val round = DocumentRoundObjectMother.finalDocument(documentId = psa.id, memberId = adminMember.id)
        mockkCalls(envelopeId, signers, psa, null, round)
        mockGetMember(round)
        val interaction = slot<CreateDocumentInteraction.Input>()
        every { createDocumentInteraction(capture(interaction)) } returns InteractionBuilder().build()
        every { documentSignRepository.update(any()) } returns Unit

        val slot = slot<Document>()
        every { documentRepository.update(capture(slot)) } returns Unit
        every { sellerSignDocumentImpactOnTask.invoke(any(), adminMember) } returns Unit

        signDocument.sign(input = SignDocument.Input(envelopeId = envelopeId, recipientId = signerData.signerRecipientId))

        Assertions.assertEquals(interaction.captured.interactionType, Interaction.Type.SELLER_SIGN)

        verify(exactly = 1) {
            sellerSignDocumentImpactOnTask.invoke(any(), any())
        }
    }

    @Test
    fun `seller can sign psa without member`() {
        val envelopeId = anyString()
        val psa = DocumentBuilder().apply { status = DocumentStatus.PENDING_SIGN }.build()
        val signerData = DocumentSignerBuilder().apply { documentId = psa.id }.build()
        val signers = listOf(
            signerData,
            DocumentSignerBuilder().setSignCompleted(true).setBuyerMemberTeam().build()
        )
        val round = DocumentRoundObjectMother.finalDocument(documentId = psa.id)
        mockkCalls(envelopeId, signers, psa, null, round)
        mockGetMember(round)
        val interaction = slot<CreateDocumentInteraction.Input>()
        every { createDocumentInteraction(capture(interaction)) } returns InteractionBuilder().build()
        val signer = slot<DocumentSigner>()
        every { documentSignRepository.update(capture(signer)) } returns Unit

        signDocument.sign(input = SignDocument.Input(envelopeId = envelopeId, recipientId = signerData.signerRecipientId))

        Assertions.assertEquals(interaction.captured.interactionType, Interaction.Type.SELLER_SIGN)
        Assertions.assertEquals(interaction.captured.comment, "Signed by Email: ${signerData.signerEmail} Name: ${signerData.fullName}")
        Assertions.assertNull(interaction.captured.member)

        Assertions.assertEquals(signer.captured.signerEmail, signerData.signerEmail)
        Assertions.assertNotNull(signer.captured.signCompletedAt)
    }

    @Test
    fun `buyer can sign psa`() {
        val envelopeId = anyString()
        val psa = DocumentBuilder().apply { status = DocumentStatus.PENDING_SIGN }.build()
        val completeUser = CompleteUserObjectMother.buyer()
        val signers = listOf(
            DocumentSigner(psa.id, completeUser.member, OffsetDateTime.now()),
            DocumentSignerBuilder().setSignCompleted(true).build()
        )

        val round = DocumentRoundObjectMother.finalDocument(documentId = psa.id)
        mockkCalls(envelopeId, signers, psa, completeUser.member, round)
        mockGetMember(round)

        val interaction = slot<CreateDocumentInteraction.Input>()
        every { createDocumentInteraction(capture(interaction)) } returns InteractionBuilder().build()
        val signer = slot<DocumentSigner>()
        every { documentSignRepository.update(capture(signer)) } returns Unit

        signDocument.sign(input = SignDocument.Input(envelopeId = envelopeId, recipientId = completeUser.member.id.toString()))

        Assertions.assertEquals(interaction.captured.interactionType, Interaction.Type.BUYER_SIGN)
        Assertions.assertNull(interaction.captured.comment)
        Assertions.assertEquals(interaction.captured.member, completeUser.member)

        Assertions.assertEquals(signer.captured.signerEmail, completeUser.member.email)
        Assertions.assertNotNull(signer.captured.signCompletedAt)
    }

    @Test
    fun `status must change to pending buyer sign`() {
        val envelopeId = anyString()
        val psa = DocumentBuilder().apply { status = DocumentStatus.PENDING_SIGN }.build()
        val completeUser = CompleteUserObjectMother.seller()
        val initSigners = listOf(
            DocumentSigner(psa.id, completeUser.member, OffsetDateTime.now()),
            DocumentSignerBuilder().setBuyerMemberTeam().build()
        )
        val finalSigners = listOf(
            DocumentSigner(psa.id, completeUser.member, OffsetDateTime.now()).copy(signCompletedAt = OffsetDateTime.now()),
            DocumentSignerBuilder().setBuyerMemberTeam().build()
        )
        mockkCalls(
            envelopeId = envelopeId,
            initSigners = initSigners,
            finalSigners = finalSigners,
            document = psa,
            member = completeUser.member
        )
        every { createDocumentInteraction(any()) } returns InteractionBuilder().build()
        every { documentSignRepository.update(any()) } returns Unit
        val slot = slot<Document>()
        every { documentRepository.update(capture(slot)) } returns Unit
        signDocument.sign(input = SignDocument.Input(envelopeId = envelopeId, recipientId = completeUser.member.id.toString()))

        Assertions.assertEquals(slot.captured.status, DocumentStatus.PENDING_BUYER_SIGN)
    }

    @Test
    fun `status must change to pending seller sign`() {
        val envelopeId = anyString()
        val psa = DocumentBuilder().apply { status = DocumentStatus.PENDING_SIGN }.build()
        val completeUser = CompleteUserObjectMother.buyer()
        val initSigners = listOf(
            DocumentSigner(psa.id, completeUser.member, OffsetDateTime.now()),
            DocumentSignerBuilder().build()
        )
        val finalSigners = listOf(
            DocumentSigner(psa.id, completeUser.member, OffsetDateTime.now()).copy(signCompletedAt = OffsetDateTime.now()),
            DocumentSignerBuilder().build()
        )
        mockkCalls(
            envelopeId = envelopeId,
            initSigners = initSigners,
            finalSigners = finalSigners,
            document = psa,
            member = completeUser.member
        )
        every { createDocumentInteraction(any()) } returns InteractionBuilder().build()
        every { documentSignRepository.update(any()) } returns Unit
        val slot = slot<Document>()
        every { documentRepository.update(capture(slot)) } returns Unit
        signDocument.sign(input = SignDocument.Input(envelopeId = envelopeId, recipientId = completeUser.member.id.toString()))

        Assertions.assertEquals(slot.captured.status, DocumentStatus.PENDING_SELLER_SIGN)
    }

    @Test
    fun `sign is in invalid psa status`() {
        val envelopeId = anyString()
        val psa = DocumentBuilder().build()
        mockkPsaSigners(envelopeId, getListOfSigners(sellerSignComplete = true))
        val completeUser = CompleteUserObjectMother.buyer()
        val signers = listOf(
            DocumentSigner(psa.id, completeUser.member, OffsetDateTime.now()),
            DocumentSignerBuilder().setSignCompleted(true).build()
        )

        val round = DocumentRoundObjectMother.finalDocument(documentId = psa.id)
        mockkCalls(envelopeId, signers, psa, completeUser.member, round)
        mockGetMember(round)

        val interaction = slot<CreateDocumentInteraction.Input>()
        every { createDocumentInteraction(capture(interaction)) } returns InteractionBuilder().build()
        val signer = slot<DocumentSigner>()
        every { documentSignRepository.update(capture(signer)) } returns Unit

        assertThrows<InvalidDocumentStatusException> {
            signDocument.sign(
                input =
                SignDocument.Input(envelopeId = envelopeId, recipientId = completeUser.member.id.toString())
            )
        }
    }

    private fun mockkCalls(
        envelopeId: String,
        signers: List<DocumentSigner>,
        document: Document,
        member: Member?,
        finalDocument: FinalDocument
    ) {
        mockkCalls(
            envelopeId = envelopeId,
            initSigners = signers,
            finalSigners = signers,
            document = document,
            member = member,
            finalDocument = finalDocument
        )
    }

    private fun mockkCalls(
        envelopeId: String,
        initSigners: List<DocumentSigner>,
        document: Document,
        member: Member?,
        finalSigners: List<DocumentSigner>,
        finalDocument: FinalDocument = DocumentRoundObjectMother.finalDocument(documentId = document.id)
    ) {
        mockkPsaSigners(envelopeId, initSigners)
        mockPsaSignersByPSA(document.id, finalSigners)
        every { findDocumentAndCurrentRound.byDocumentId<FinalDocument>(document.id) } returns Pair(document, finalDocument)
        if (member != null) every { memberRepository.findByEmail(member.email) } returns member
        initSigners.filter { it.signerEmail != member?.email }.forEach {
            every { memberRepository.findByEmail(it.signerEmail!!) } returns null
        }
    }

    @Test
    fun `signer not fount`() {
        val envelopeId = anyString()
        mockkPsaSigners(envelopeId, getListOfSigners())

        assertThrows<DocumentSignerNotFoundException> {
            signDocument.sign(
                SignDocument.Input(envelopeId = envelopeId, recipientId = anyString())
            )
        }
    }

    @Test
    fun `signer already sign`() {
        val envelopeId = anyString()
        val signers = getListOfSigners(sellerSignComplete = true, buyerSignComplete = true)
        mockkPsaSigners(
            envelopeId,
            signers
        )

        assertThrows<DocumentSignerAlreadySignedException> {
            signDocument.sign(
                SignDocument.Input(
                    envelopeId = envelopeId,
                    recipientId = signers.first().signerRecipientId
                )
            )
        }
    }

    private fun getListOfSigners(
        buyerSignComplete: Boolean = false,
        sellerSignComplete: Boolean = false
    ) = listOf(
        DocumentSignerBuilder().setSignCompleted(sellerSignComplete).build(),
        DocumentSignerBuilder().setSignCompleted(buyerSignComplete).setBuyerMemberTeam().build()
    )

    private fun mockkPsaSigners(envelopeId: String, signers: List<DocumentSigner>) {
        every { documentSignRepository.findByEnvelopeId(envelopeId) } returns signers
    }

    private fun mockPsaSignersByPSA(psaId: Long, signers: List<DocumentSigner>) {
        every { documentSignRepository.findByDocumentId(psaId) } returns signers
    }

    private fun mockGetMember(documentRound: FinalDocument) {
        every { memberRepository.findById(documentRound.memberId) } returns MemberObjectMother.buyer(documentRound.memberId)
    }
}
