package realestate.unlock.dealroom.api.unit.core.entity

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.core.entity.loi.LoiSignStatus
import realestate.unlock.dealroom.api.core.entity.loi.MemberSign
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.utils.entity.loi.LoiSignBuilder
import java.time.OffsetDateTime

class LetterOfIntentSignTest {

    @Test
    fun `can be signed by seller`() {
        val loiSign = LoiSignBuilder().build()
        val now = OffsetDateTime.now()
        val expectedSellerSign = loiSign.sellerSign.copy(completedAt = now)

        val loiSigned = loiSign.singBySeller(now)

        assertEquals(loiSign.loiId, loiSigned.loiId)
        assertEquals(loiSign.signingId, loiSigned.signingId)
        assertEquals(loiSign.buyerSign, loiSigned.buyerSign)
        assertEquals(expectedSellerSign, loiSigned.sellerSign)
    }

    @Test
    fun `seller cannot sign two times`() {
        val loiSign = LoiSignBuilder().build()
        val loiSignedBySeller = loiSign.singBySeller(OffsetDateTime.now())

        assertThrows<RuntimeException> { loiSignedBySeller.singBySeller(OffsetDateTime.now()) }
    }

    @Test
    fun `can be signed by buyer`() {
        val loiSign = LoiSignBuilder().build()
        val now = OffsetDateTime.now()
        val expectedBuyerSign = loiSign.buyerSign.copy(completedAt = now)

        val loiSigned = loiSign.singByBuyer(now)

        assertEquals(loiSign.loiId, loiSigned.loiId)
        assertEquals(loiSign.signingId, loiSigned.signingId)
        assertEquals(loiSign.sellerSign, loiSigned.sellerSign)
        assertEquals(expectedBuyerSign, loiSigned.buyerSign)
    }

    @Test
    fun `buyer cannot sign two times`() {
        val loiSign = LoiSignBuilder().build()
        val loiSignedByBuyer = loiSign.singByBuyer(OffsetDateTime.now())

        assertThrows<RuntimeException> { loiSignedByBuyer.singByBuyer(OffsetDateTime.now()) }
    }

    @Test
    fun `loi sign without signs is in status PENDING`() {
        val loiSign = LoiSignBuilder().apply {
            sellerSign = MemberSign(id = "1", completedAt = null, memberType = MemberTypeEnum.SELLER)
            buyerSign = MemberSign(id = "2", completedAt = null, memberType = MemberTypeEnum.BUYER)
        }.build()

        assertEquals(LoiSignStatus.PENDING, loiSign.status())
    }

    @Test
    fun `loi sign  signed only by buyer is in status SELLER_SIGN_PENDING`() {
        val loiSign = LoiSignBuilder().apply {
            sellerSign = MemberSign(id = "1", completedAt = null, memberType = MemberTypeEnum.SELLER)
            buyerSign = MemberSign(id = "2", completedAt = OffsetDateTime.now(), memberType = MemberTypeEnum.BUYER)
        }.build()

        assertEquals(LoiSignStatus.SELLER_SIGN_PENDING, loiSign.status())
    }

    @Test
    fun `loi sign  signed only by seller is in status BUYER_SIGN_PENDING`() {
        val loiSign = LoiSignBuilder().apply {
            sellerSign = MemberSign(id = "1", completedAt = OffsetDateTime.now(), memberType = MemberTypeEnum.SELLER)
            buyerSign = MemberSign(id = "2", completedAt = null, memberType = MemberTypeEnum.BUYER)
        }.build()

        assertEquals(LoiSignStatus.BUYER_SIGN_PENDING, loiSign.status())
    }

    @Test
    fun `loi sign signed by seller and buyer is in status COMPLETED`() {
        val loiSign = LoiSignBuilder().apply {
            sellerSign = MemberSign(id = "1", completedAt = OffsetDateTime.now(), memberType = MemberTypeEnum.SELLER)
            buyerSign = MemberSign(id = "2", completedAt = OffsetDateTime.now(), memberType = MemberTypeEnum.BUYER)
        }.build()

        assertEquals(LoiSignStatus.COMPLETED, loiSign.status())
    }
}
