package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.CreateDealSupportMemberInput
import realestate.unlock.dealroom.api.core.entity.deal.DealMemberRelation
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.MemberCreationInput
import realestate.unlock.dealroom.api.core.usecase.deal.CreatePointOfContact
import realestate.unlock.dealroom.api.core.usecase.deal.team.AddDealTeamMember
import realestate.unlock.dealroom.api.core.usecase.member.CreateMember

@ExtendWith(MockKExtension::class)
class CreateLeasebackDealSupportMemberTest {

    @MockK
    private lateinit var createMember: CreateMember

    @MockK
    private lateinit var addDealTeamMember: AddDealTeamMember

    private lateinit var createPointOfContact: CreatePointOfContact

    @BeforeEach
    fun setup() {
        createPointOfContact = CreatePointOfContact(createMember = createMember, addDealTeamMember = addDealTeamMember)
    }

    @Test
    fun `It creates the seller_support member correctly and adds it to the deal team`() {
        // given
        val givenDealId = 11L
        val givenMemberId = 12L
        val firstName = "pepe"
        val lastName = "pipi"
        val companyName = "Keyway"
        val phoneNumber = "1122334455"
        val email = "<EMAIL>"

        val givenMember = mockk<Member>()
        val givenMemberCreationInput = MemberCreationInput(
            memberType = "seller_support",
            firstName = firstName,
            lastName = lastName,
            companyName = companyName,
            address = null,
            phoneNumber = phoneNumber,
            email = email,
            needUser = false,
            organizationId = "org-id"
        )
        every { givenMember.id } returns givenMemberId
        every { createMember.create(givenMemberCreationInput) } returns givenMember
        every { addDealTeamMember.add(listOf(DealMemberRelation(dealId = givenDealId, memberId = givenMemberId))) } returns Unit

        val input = CreateDealSupportMemberInput(
            dealId = givenDealId,
            firstName = firstName,
            lastName = lastName,
            phoneNumber = phoneNumber,
            email = email,
            companyName = companyName,
            organizationId = "org-id"
        )

        // when
        createPointOfContact.create(input)

        // then
        verify {
            addDealTeamMember.add(listOf(DealMemberRelation(dealId = givenDealId, memberId = givenMemberId)))
        }
    }
}
