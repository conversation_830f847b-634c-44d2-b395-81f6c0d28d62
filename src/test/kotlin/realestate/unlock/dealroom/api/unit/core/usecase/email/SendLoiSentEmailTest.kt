package realestate.unlock.dealroom.api.unit.core.usecase.email

import io.mockk.CapturingSlot
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.kfile.KFileWithFile
import realestate.unlock.dealroom.api.core.entity.loi.input.LoiEmailOptionsInput
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.gateway.email.EmailGateway
import realestate.unlock.dealroom.api.core.gateway.email.LeaseEmailParams
import realestate.unlock.dealroom.api.core.gateway.email.OfferEmailParams
import realestate.unlock.dealroom.api.core.gateway.email.TemplatedEmailData
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.usecase.deal.team.GetDealMembers
import realestate.unlock.dealroom.api.core.usecase.email.loi.SendLoiSentEmail
import realestate.unlock.dealroom.api.infrastructure.configuration.model.LoiSentEmailConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.WebAppsConfig
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.creator.FileCreator
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMultifamilyRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class SendLoiSentEmailTest {

    private val givenDeal = DealBuilder().build()
    private val givenProperty = PropertyBuilder()
        .apply { id = givenDeal.propertyId }
        .build()
    private val kFile = KFileWithFile(anyString(), FileCreator.create())
    private val seller = MemberObjectMother.seller(1)
    private val buyer = MemberObjectMother.buyer(12)
    private val broker1 = MemberObjectMother.sellerBroker(123)
        .copy(email = "<EMAIL>", lastName = "broker1")
    private val broker2 = MemberObjectMother.sellerBroker(1234)
        .copy(email = "<EMAIL>", lastName = "broker2")
    private val givenDealTeam = listOf(seller, broker1, broker2, buyer)

    private val propertyRepository: PropertyRepository = mockk {
        every { findById(givenProperty.id) } returns givenProperty
    }

    private val getDealMembers: GetDealMembers = mockk {
        every { get(givenDeal.id) } returns givenDealTeam
    }
    private val emailGateway: EmailGateway = mockk(relaxed = true)
    private val fileGateway: FileGateway = mockk {
        every { getFile(kFile.kFileId, kFile.file.name) } returns kFile
    }

    private val loiSentEmailConfigFromEmail = "<EMAIL>"
    private val loiSentEmailConfig: LoiSentEmailConfig = mockk {
        every { emailFrom } returns loiSentEmailConfigFromEmail
        every { carbonCopiesMap } returns mapOf()
        every { backgroundCarbonCopies } returns setOf()
    }

    private val target = SendLoiSentEmail(
        propertyRepository = propertyRepository,
        getDealMembers = getDealMembers,
        emailGateway = emailGateway,
        fileGateway = fileGateway,
        loiSentEmailConfig = loiSentEmailConfig,
        webApps = WebAppsConfig("dealRoom", "dealRoomAdmin", "kos")
    )

    private fun captureEmailParams(): CapturingSlot<TemplatedEmailData> {
        val slot = slot<TemplatedEmailData>()
        every { emailGateway.sendTemplated(capture(slot)) } returns Unit
        return slot
    }

    @Test
    fun `If no emails are sent to broker nor seller it should be sent to the buyer who created the loi`() {
        // given
        val givenLoiRound = givenMedicalLoi()
        val emailOptions = givenEmailOptions(sendToBroker = false, sendToSeller = false)

        val slot = captureEmailParams()
        // when
        target.send(givenDeal, givenLoiRound, emailOptions)

        // then
        assertionMails(slot.captured.toEmailAddress, setOf(givenLoiRound.offer.createdBy))
    }

    @Test
    fun `do not send email to brokers when emailOptions are not specified`() {
        // given
        val givenLoiRound = givenMedicalLoi()
        val slot = captureEmailParams()
        // when
        target.send(givenDeal, givenLoiRound, emailOptions = null)
        assertionMails(slot.captured.toEmailAddress, setOf(buyer))
    }

    private fun assertionMails(emailAddresses: Set<String>, members: Set<Member>) {
        Assertions.assertEquals(emailAddresses.size, members.size)
        members.forEach { member ->
            Assertions.assertTrue(emailAddresses.any { it == member.email })
        }
    }

    @Test
    fun `sends email to all brokers`() {
        // given
        val givenLoiRound = givenMedicalLoi()
        val emailOptions = givenEmailOptions(sendToBroker = true)
        val slot = captureEmailParams()

        // when
        target.send(givenDeal, givenLoiRound, emailOptions)

        val brokers = setOf(broker1, broker2)
        // then
        assertionMails(slot.captured.toEmailAddress, brokers)
    }

    @Test
    fun `sends email to sellers`() {
        // given
        val givenLoiRound = givenMedicalLoi()
        val emailOptions = givenEmailOptions(sendToSeller = true)
        val slot = captureEmailParams()

        // when
        target.send(givenDeal, givenLoiRound, emailOptions)

        val sellers = setOf(seller)
        // then
        assertionMails(slot.captured.toEmailAddress, sellers)
    }

    @Test
    fun `sends email to configured carbon copies`() {
        // given
        val givenLoiRound = givenMedicalLoi()
        val emailOptions = givenEmailOptions(sendToBroker = true)
        val carbonCopiesMap = mapOf(givenLoiRound.offer.createdBy.email to setOf("<EMAIL>"))
        every { loiSentEmailConfig.carbonCopiesMap } returns carbonCopiesMap
        val slot = captureEmailParams()
        // when
        target.send(givenDeal, givenLoiRound, emailOptions)

        // then
        carbonCopiesMap[givenLoiRound.offer.createdBy.email]?.forEach {
            Assertions.assertTrue(
                slot.captured.carbonCopies.contains(it)
            )
        }
    }

    @Test
    fun `sends email to configured background carbon copies`() {
        // given
        val givenLoiRound = givenMedicalLoi()
        val emailOptions = givenEmailOptions(sendToBroker = true)
        val backgroundCarbonCopies = setOf("<EMAIL>")
        every { loiSentEmailConfig.backgroundCarbonCopies } returns backgroundCarbonCopies
        val slot = captureEmailParams()

        // when
        target.send(givenDeal, givenLoiRound, emailOptions)

        // then

        backgroundCarbonCopies.forEach {
            Assertions.assertTrue(
                slot.captured.backgroundCarbonCopies.contains(it)
            )
        }
    }

    @Test
    fun `can build medical emails param correctly`() {
        val givenLoiRound = givenMedicalLoi()
        val emailOptions = givenEmailOptions(sendToBroker = true)
        val slot = captureEmailParams()
        // when
        target.send(givenDeal, givenLoiRound, emailOptions)

        val templateParams = slot.captured.templateParams
        Assertions.assertTrue(templateParams.contains("offer"))
        Assertions.assertTrue(templateParams.contains("lease"))

        // check offer data
        val offerParams = JsonMapper.decode(JsonMapper.encode(templateParams["offer"]!!), OfferEmailParams::class.java)

        Assertions.assertEquals(offerParams.price, givenLoiRound.offer.salesPrice.formatNumber())
        Assertions.assertEquals(offerParams.address, givenProperty.address.street)
        Assertions.assertEquals(offerParams.tenant, givenLoiRound.tenantName)
        Assertions.assertEquals(offerParams.diligencePeriod, givenLoiRound.offer.dueDiligence.number)
        Assertions.assertEquals(offerParams.closingPeriod, givenLoiRound.offer.closing.period)
        Assertions.assertEquals(offerParams.extensionPeriod, givenLoiRound.offer.closing.periodExtension)
        Assertions.assertEquals(offerParams.expire, givenLoiRound.offer.contractTermination.formatDate())

        // check lease data
        val leaseParams = JsonMapper.decode(JsonMapper.encode(templateParams["lease"]!!), LeaseEmailParams::class.java)

        Assertions.assertEquals(leaseParams.guarantee, givenLoiRound.guaranteeType.name)
        Assertions.assertEquals(leaseParams.earnestDeposit, givenLoiRound.offer.earnestMoneyDeposit.formatNumber())
        Assertions.assertEquals(leaseParams.closingDeposit, givenLoiRound.offer.closing.extensionDeposit.formatNumber())
        Assertions.assertEquals(leaseParams.rent, givenLoiRound.offer.lease.rent.formatNumber())
        Assertions.assertEquals(leaseParams.terms, givenLoiRound.offer.lease.length)
    }

    @Test
    fun `can build multifamily emails param correctly`() {
        val givenLoiRound = givenMultifamilyLoi()
        val emailOptions = givenEmailOptions(sendToBroker = true)
        val slot = captureEmailParams()
        // when
        target.send(givenDeal, givenLoiRound, emailOptions)

        val templateParams = slot.captured.templateParams

        // check offer data
        Assertions.assertEquals(templateParams["price"], givenLoiRound.offer.price.formatNumber())
        Assertions.assertEquals(templateParams["expire"], (givenLoiRound.dueDate?.formatDate() ?: LocalDate.now().plusDays(7).formatDate()))
        Assertions.assertEquals(templateParams["address"], givenProperty.address.street)
        Assertions.assertEquals(templateParams["tenant"], givenLoiRound.legalEntity)
        Assertions.assertEquals(templateParams["diligence_period"], givenLoiRound.dueDiligencePeriod)
        Assertions.assertEquals(templateParams["closing_period"], givenLoiRound.closingPeriod)
        Assertions.assertEquals(templateParams["extension_period"], givenLoiRound.closingExtensionPeriod)
        Assertions.assertEquals(templateParams["earnest_deposit"], givenLoiRound.offer.deposit.formatNumber())
        Assertions.assertEquals(templateParams["closing_deposit"], givenLoiRound.offer.closingExtensionDeposit.formatNumber())
    }

    private fun Number.formatNumber() = String.format("%,.0f", this)
    private fun LocalDate.formatDate() = this.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))

    private fun givenMedicalLoi(comments: String? = null) = LoiMedicalRoundBuilder()
        .apply { dealId = givenDeal.id }
        .apply {
            offer = offer.copy(
                comments = comments,
                files = offer.files.map { it.copy(kFileId = kFile.kFileId, name = kFile.file.name) }
            )
        }.build()

    private fun givenMultifamilyLoi(comments: String? = null) = LoiMultifamilyRoundBuilder()
        .apply { dealId = givenDeal.id }
        .apply {
            offer = offer.copy(
                files = offer.files.map { it.copy(kFileId = kFile.kFileId, name = kFile.file.name) }
            )
        }.build()

    private fun givenEmailOptions(sendToBroker: Boolean = false, sendToSeller: Boolean = false) =
        LoiEmailOptionsInput(sendToBroker = sendToBroker, sendToSeller = sendToSeller)
}
