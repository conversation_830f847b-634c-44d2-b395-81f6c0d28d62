package realestate.unlock.dealroom.api.unit.core.usecase.task.update

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.DocumentSigner
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.entity.task.UpdateTaskInput
import realestate.unlock.dealroom.api.core.extension.convertToMap
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.core.repository.task.file.TaskFileRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.AcceptDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.RequestChange
import realestate.unlock.dealroom.api.core.usecase.deal.documents.UpsertDocumentSigner
import realestate.unlock.dealroom.api.core.usecase.file.SaveDocumentTaskFile
import realestate.unlock.dealroom.api.core.usecase.task.file.TaskFilePathGenerator
import realestate.unlock.dealroom.api.core.usecase.task.update.TaskResponseImpactOnDocument
import realestate.unlock.dealroom.api.utils.entity.deal.DealCategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.task.file.TaskFileObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.file.FileBuilder
import java.time.Clock

@ExtendWith(MockKExtension::class)
class GetTaskResponseImpactOnDocumentTest {

    @MockK(relaxed = true)
    private lateinit var acceptDocument: AcceptDocument

    @MockK(relaxed = true)
    private lateinit var requestChange: RequestChange

    @MockK
    private lateinit var dealCategoryRepository: DealCategoryRepository

    @MockK(relaxed = true)
    private lateinit var saveDocumentTaskFile: SaveDocumentTaskFile

    @MockK
    private lateinit var taskFilePathGenerator: TaskFilePathGenerator

    @MockK
    private lateinit var taskFileRepository: TaskFileRepository

    @MockK
    private lateinit var upsertDocumentSigner: UpsertDocumentSigner

    private lateinit var taskResponseImpactOnDocument: TaskResponseImpactOnDocument

    private val clock = Clock.systemDefaultZone()

    @BeforeEach
    fun setUp() {
        taskResponseImpactOnDocument = TaskResponseImpactOnDocument(
            acceptDocument = acceptDocument,
            requestChange = requestChange,
            dealCategoryRepository = dealCategoryRepository,
            saveDocumentTaskFile = saveDocumentTaskFile,
            taskFileRepository = taskFileRepository,
            taskFilePathGenerator = taskFilePathGenerator,
            upsertDocumentSigner = upsertDocumentSigner,
            clock = clock
        )
        every { taskFilePathGenerator(any()) } returns anyString()
    }

    @Test
    fun `seller accept psa, then document is updated`() {
        val dealId = anyId()
        val acceptDocumentInput = TaskResponseImpactOnDocument.AcceptDocumentInput(
            firstName = anyString(),
            lastName = anyString(),
            email = anyString(),
            comment = anyString()
        )
        val (task, input) = givenTaskAndUpdateInputForAccept(dealId, acceptDocumentInput)
        val psa = acceptPSAInteractionMock(dealId, acceptDocumentInput.comment, input.member)
        val slot = slot<DocumentSigner>()
        every { upsertDocumentSigner.invoke(capture(slot)) } returns Unit

        taskResponseImpactOnDocument(input, task)

        assertPSAAccepted(dealId, input, acceptDocumentInput)

        Assertions.assertEquals(slot.captured.documentId, psa.id)
        Assertions.assertEquals(slot.captured.signerEmail, acceptDocumentInput.email)
        Assertions.assertEquals(slot.captured.signerFirstName, acceptDocumentInput.firstName)
        Assertions.assertEquals(slot.captured.signerLastName, acceptDocumentInput.lastName)
        Assertions.assertEquals(slot.captured.signerTeamType, MemberDealTeam.SELLER)
    }

    @Test
    fun `seller request changes on psa, then document is updated`() {
        val dealId = anyId()
        val rejectDocumentInput = TaskResponseImpactOnDocument.RejectDocumentInput(
            comment = anyString(),
            file = FileInput(uid = anyString(), name = anyString())
        )
        val (task, input) = givenTaskAndUpdateInputForRequestChanges(dealId, rejectDocumentInput)
        val taskFile = TaskFileObjectMother.taskFile()
        every { taskFileRepository.save(any()) } returns taskFile
        val file = FileBuilder().build()
        every { saveDocumentTaskFile.save(taskFile) } returns file

        taskResponseImpactOnDocument(input, task)

        assertPSAChangesRequested(dealId, input, rejectDocumentInput, file)
    }

    private fun givenTaskAndUpdateInputForAccept(dealId: Long, acceptDocumentInput: TaskResponseImpactOnDocument.AcceptDocumentInput): Pair<Task, UpdateTaskInput> {
        val data = acceptDocumentInput.convertToMap<String, Any>()
        val task = givenCompleteTask(
            dealId,
            data
        )
        val updateInput = updateTaskInputFor(task, TaskStatus.ACCEPTED)
        return Pair(task, updateInput)
    }

    private fun givenTaskAndUpdateInputForRequestChanges(dealId: Long, rejectDocumentInput: TaskResponseImpactOnDocument.RejectDocumentInput): Pair<Task, UpdateTaskInput> {
        val data = rejectDocumentInput.convertToMap<String, Any>()
        val task = givenCompleteTask(
            dealId,
            data
        )
        val updateInput = updateTaskInputFor(task, TaskStatus.IN_REVIEW)
        return Pair(task, updateInput)
    }

    private fun updateTaskInputFor(task: Task, taskStatus: TaskStatus) = UpdateTaskInput(
        id = task.id,
        status = taskStatus,
        dueDate = null,
        attachedForm = emptyMap(),
        member = CompleteUserObjectMother.seller().member,
        assignedBuyerId = null,
        assignedTeam = null,
        enabled = null,
        rejectionReason = null
    )

    private fun givenCompleteTask(dealId: Long, data: Map<String, Any>) = TaskBuilder().withTypeKey("psa").withData(data).build().also {
        every { dealCategoryRepository.findById(it.dealCategoryId) } returns DealCategoryBuilder().apply {
            this.dealId = dealId
        }.build()
    }

    private fun acceptPSAInteractionMock(dealIdForTest: Long, comment: String?, member: Member): Document =
        DocumentBuilder().apply { dealId = dealIdForTest }.build().also { psa ->
            every {
                acceptDocument.update(
                    InteractWithDocumentRound.Input(
                        dealId = dealIdForTest,
                        comment = comment,
                        member = member,
                        files = listOf(),
                        documentType = DocumentType.PSA
                    )
                )
            } returns psa
        }

    private fun assertPSAAccepted(dealId: Long, input: UpdateTaskInput, acceptDocumentInput: TaskResponseImpactOnDocument.AcceptDocumentInput) {
        verify {
            acceptDocument.update(
                InteractWithDocumentRound.Input(
                    dealId = dealId,
                    comment = acceptDocumentInput.comment,
                    member = input.member,
                    files = listOf(),
                    documentType = DocumentType.PSA
                )
            )
        }

        verify(exactly = 0) { saveDocumentTaskFile.save(any()) }
    }

    private fun assertPSAChangesRequested(dealId: Long, input: UpdateTaskInput, rejectDocumentInput: TaskResponseImpactOnDocument.RejectDocumentInput, file: File) {
        verify {
            requestChange.update(
                InteractWithDocumentRound.Input(
                    dealId = dealId,
                    comment = rejectDocumentInput.comment,
                    member = input.member,
                    files = listOf(file),
                    documentType = DocumentType.PSA
                )
            )
        }
    }
}
