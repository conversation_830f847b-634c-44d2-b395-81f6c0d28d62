package realestate.unlock.dealroom.api.unit.core.usecase.calendar

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventType
import realestate.unlock.dealroom.api.core.entity.calendar.ExternalDealCalendarEventInput
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarStageEvent
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalDealCalendarEventsActions
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.LocalDate

class ExternalCalendarStageEvenTest {

    private lateinit var target: ExternalCalendarStageEvent

    @MockK
    private val eventsActions: ExternalDealCalendarEventsActions = mockk()

    @BeforeEach
    fun setUp() {
        target = ExternalCalendarStageEvent(eventsActions = eventsActions)
    }

    @Test
    fun `should createOrUpdate a calendar event if report has date`() {
        val dealId = anyId()
        val date = LocalDate.now().plusDays(3)
        val eventType = CalendarEventType.DUE_DILIGENCE
        every { eventsActions.createOrUpdate(any()) } returns Unit

        target.impactOnCalendar(dealId, date, eventType)

        verify(exactly = 1) {
            eventsActions.createOrUpdate(
                input = ExternalDealCalendarEventInput(
                    dealId = dealId,
                    referenceId = dealId.toString(),
                    referenceType = "stageduediligence",
                    title = "DUE DILIGENCE",
                    description = "DUE DILIGENCE",
                    date = date
                )
            )
        }
    }

    @Test
    fun `should delete a calendar event if report hasn't date`() {
        val dealId = anyId()
        val date = null
        val eventType = CalendarEventType.DUE_DILIGENCE

        every { eventsActions.delete(any(), any(), any()) } returns Unit

        target.impactOnCalendar(dealId, date, eventType)

        verify(exactly = 1) {
            eventsActions.delete(
                dealId = dealId,
                referenceId = dealId.toString(),
                referenceType = "stageduediligence"
            )
        }
    }
}
