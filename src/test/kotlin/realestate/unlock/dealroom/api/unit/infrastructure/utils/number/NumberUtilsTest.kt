package realestate.unlock.dealroom.api.unit.infrastructure.utils.number

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.utils.number.NumberUtils.decimalToPercentage
import java.math.BigDecimal

class NumberUtilsTest {

    @Test
    fun `It multiplies the decimal by 100`() {
        val givenDecimal = BigDecimal.valueOf(0.03)

        val percentage = decimalToPercentage(givenDecimal)

        assertThat(percentage, equalTo(givenDecimal * BigDecimal(100)))
    }
}
