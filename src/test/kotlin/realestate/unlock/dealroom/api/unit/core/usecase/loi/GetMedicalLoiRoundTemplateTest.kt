package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.hamcrest.core.IsNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRound
import realestate.unlock.dealroom.api.core.entity.loi.MedicalLoiRound
import realestate.unlock.dealroom.api.core.entity.loi.draft.LetterOfIntentDraft
import realestate.unlock.dealroom.api.core.entity.loi.template.MedicalLoiTemplate
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentDraftRepository
import realestate.unlock.dealroom.api.core.repository.loi.MedicalLetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.usecase.loi.GetLoiMedicalTemplate
import realestate.unlock.dealroom.api.utils.entity.deal.CompleteDealBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import java.time.LocalDate

class GetMedicalLoiRoundTemplateTest {
    private val dealId: Long = 1
    private val dueDiligenceNumberOfDays: Long = 2
    private val closingPeriodValue: Long = 2
    private val closingExtensionPeriod: Long = 5
    private val letterOfIntentRoundRepository: MedicalLetterOfIntentRoundRepository = mockk()
    private val letterOfIntentDraftRepository: LetterOfIntentDraftRepository = mockk()
    private val target = GetLoiMedicalTemplate(letterOfIntentRoundRepository, letterOfIntentDraftRepository)

    @BeforeEach
    fun init() {
        every { letterOfIntentDraftRepository.getByDealId(dealId) } returns null
    }

    @Test
    fun `returns deal based template when no letter of intent offer exists`() {
        // given
        val givenDeal = givenCompleteDeal()
        every { letterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns null

        // when
        val result = target.get(givenDeal)

        // then
        assertLoiTemplateFromDeal(result, givenDeal)
    }

    @Test
    fun `returns deal based template (with null data) when no letter of intent offer exists`() {
        // given
        val givenDeal = givenCompleteDeal(withDates = false)
        every { letterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns null

        // when
        val result = target.get(givenDeal)

        // then
        assertLoiTemplateFromDeal(result, givenDeal)
    }

    @Test
    fun `returns loi based template`() {
        // given
        val givenLoi = LoiMedicalRoundBuilder().build()
        val givenDeal = givenCompleteDeal()
        every { letterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns givenLoi

        // when
        val result = target.get(givenDeal)

        // then
        assertLoiTemplateFromLoi(result, givenLoi, givenDeal)
    }

    @Test
    fun `If a LOI draft is found it should not use the deal based template nor loi based template`() {
        val givenDeal = givenCompleteDeal(withDates = false)
        val mockedLoiDraft = mockk<LetterOfIntentDraft>()
        every { mockedLoiDraft.toLetterOfIntentTemplate(givenDeal.property) } returns mockk()
        every { letterOfIntentDraftRepository.getByDealId(givenDeal.id) } returns mockedLoiDraft

        target.get(givenDeal)

        verify(exactly = 0) { letterOfIntentRoundRepository.findByDealIdAndStatus(givenDeal.id, LetterOfIntentRound.ALL_STATES) }
    }

    private fun givenCompleteDeal(withDates: Boolean = true) = CompleteDealBuilder()
        .apply { contractExecutedDate = LocalDate.now().takeIf { withDates } }
        .apply { diligenceExpirationDate = contractExecutedDate.takeIf { withDates }?.plusDays(dueDiligenceNumberOfDays) }
        .apply { initialClosingDate = diligenceExpirationDate.takeIf { withDates }?.plusDays(closingPeriodValue) }
        .apply { outsideClosingDate = initialClosingDate.takeIf { withDates }?.plusDays(closingExtensionPeriod) }
        .build()

    private fun assertLoiTemplateFromDeal(
        response: MedicalLoiTemplate,
        givenDeal: CompleteDeal
    ) {
        assertThat(response.tenantName, IsEqual(givenDeal.tenantName))
        assertThat(response.propertySquareFootage, IsEqual(givenDeal.property.squareFootage))
        assertThat(response.propertyAddress, IsEqual(givenDeal.property.address))
        assertThat(response.vertical, IsEqual(givenDeal.vertical))
        assertThat(response.broker?.name, IsNull())
        assertThat(response.broker?.companyName, IsNull())
        assertThat(response.offer.offerPrice, IsEqual(givenDeal.offerPrice))
        assertThat(response.offer.lease.rent, IsEqual(givenDeal.lease.rent))
        assertThat(response.offer.lease.type, IsEqual(givenDeal.lease.type))
        assertThat(response.offer.lease.condition, IsEqual(givenDeal.type?.getLeaseCondition()))
        assertThat(response.offer.lease.rentIncrease, IsEqual(givenDeal.lease.rentIncrease))
        assertThat(response.offer.lease.increaseEveryYear, IsEqual(givenDeal.lease.increaseEveryYear))
        assertThat(response.offer.lease.length, IsEqual(givenDeal.lease.length))
        assertThat(response.offer.lease.expirationYear, IsEqual(givenDeal.lease.expirationYear))
        assertThat(response.offer.lease.numberOfOptions, IsEqual(givenDeal.lease.numberOfOptions))
        assertThat(response.offer.lease.optionLengths, IsEqual(givenDeal.lease.optionLengths))
        assertThat(response.offer.lease.rentCpi, IsEqual(givenDeal.lease.rentCpi))
        assertThat(response.offer.lease.rentStepType, IsEqual(givenDeal.lease.rentStepType))
        assertThat(response.offer.closing.period, IsEqual(givenDeal.lease.closingPeriod))
        assertThat(response.offer.closing.periodExtension, IsEqual(givenDeal.lease.closingPeriodExtension))
        assertThat(response.offer.closing.extensionDeposit, IsEqual(givenDeal.extensionDeposit))
        assertThat(response.offer.contractTermination, IsNull())
        assertThat(response.offer.earnestMoneyDeposit, IsEqual(givenDeal.earnestMoneyDeposit))
        assertThat(response.offer.dueDiligence.number, IsEqual(givenDeal.lease.dueDiligenceNumber))
    }

    private fun assertLoiTemplateFromLoi(
        result: MedicalLoiTemplate,
        givenLoi: MedicalLoiRound,
        givenDeal: CompleteDeal
    ) {
        assertThat(result.tenantName, IsEqual(givenLoi.tenantName))
        assertThat(result.propertySquareFootage, IsEqual(givenLoi.propertySquareFootage))
        assertThat(result.propertyAddress, IsEqual(givenDeal.property.address))
        assertThat(result.vertical, IsEqual(givenLoi.vertical))
        assertThat(result.broker?.name, IsEqual(givenLoi.broker.name))
        assertThat(result.broker?.companyName, IsEqual(givenLoi.broker.companyName))
        assertThat(result.offer.offerPrice, IsEqual(givenLoi.offer.salesPrice))
        assertThat(result.offer.lease.rent, IsEqual(givenLoi.offer.lease.rent))
        assertThat(result.offer.lease.type, IsEqual(givenLoi.offer.lease.type))
        assertThat(result.offer.lease.condition, IsEqual(givenLoi.offer.lease.condition))
        assertThat(result.offer.lease.rentIncrease, IsEqual(givenLoi.offer.lease.rentIncrease))
        assertThat(result.offer.lease.increaseEveryYear, IsEqual(givenLoi.offer.lease.increaseEveryYear))
        assertThat(result.offer.lease.length, IsEqual(givenLoi.offer.lease.length))
        assertThat(result.offer.lease.expirationYear, IsEqual(givenLoi.offer.lease.expirationYear))
        assertThat(result.offer.lease.numberOfOptions, IsEqual(givenLoi.offer.lease.numberOfOptions))
        assertThat(result.offer.lease.optionLengths, IsEqual(givenLoi.offer.lease.optionLengths))
        assertThat(result.offer.lease.rentCpi, IsEqual(givenLoi.offer.lease.rentCpi))
        assertThat(result.offer.lease.rentStepType, IsEqual(givenLoi.offer.lease.rentStepType))
        assertThat(result.offer.closing.period, IsEqual(givenLoi.offer.closing.period))
        assertThat(result.offer.closing.periodExtension, IsEqual(givenLoi.offer.closing.periodExtension))
        assertThat(result.offer.closing.extensionDeposit, IsEqual(givenLoi.offer.closing.extensionDeposit))
        assertThat(result.offer.contractTermination, IsEqual(givenLoi.offer.contractTermination))
        assertThat(result.offer.earnestMoneyDeposit, IsEqual(givenLoi.offer.earnestMoneyDeposit))
        assertThat(result.offer.dueDiligence.number, IsEqual(givenLoi.offer.dueDiligence.number))
        assertThat(result.offer.customSections!!.sections.first().title, IsEqual(givenLoi.offer.customSections!!.sections.first().title))
        assertThat(result.offer.customSections!!.sections.first().content, IsEqual(givenLoi.offer.customSections!!.sections.first().content))
    }
}
