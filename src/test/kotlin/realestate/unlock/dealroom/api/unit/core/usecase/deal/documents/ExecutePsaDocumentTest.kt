package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.document.*
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.kfile.FileToStaging
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.sign.DocumentOutput
import realestate.unlock.dealroom.api.core.gateway.sign.RetrieveDocumentInput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentSignRepository
import realestate.unlock.dealroom.api.core.repository.file.FileRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarDocumentEvent
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealStage
import realestate.unlock.dealroom.api.core.usecase.deal.documents.*
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.ExecuteDocumentImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.deal.update.UpdateDealDatesFromPsaExecuted
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SendFileToProcess
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.document.DocumentSignerBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.io.ByteArrayInputStream
import java.time.Clock
import java.time.LocalDate
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class ExecutePsaDocumentTest {
    @MockK(relaxed = true)
    private lateinit var documentRepository: DocumentRepository

    @MockK(relaxed = true)
    private lateinit var signGateway: SignGateway

    @MockK(relaxed = true)
    private lateinit var fileGateway: FileGateway
    private val clock: Clock = Clock.systemDefaultZone()

    @MockK(relaxed = true)
    private lateinit var confirmExecutedDocument: ConfirmExecutedDocument

    @MockK(relaxed = true)
    private lateinit var documentSignRepository: DocumentSignRepository

    @MockK(relaxed = true)
    private lateinit var executeDocumentImpactOnTask: ExecuteDocumentImpactOnTask

    @MockK(relaxed = true)
    private lateinit var findDocumentAndCurrentRound: FindDocumentAndCurrentRound

    @MockK(relaxed = true)
    private lateinit var createDocumentInteraction: CreateDocumentInteraction

    @MockK(relaxed = true)
    private lateinit var updateDealDatesFromPsaExecuted: UpdateDealDatesFromPsaExecuted

    @MockK(relaxed = true)
    private lateinit var getOrCreateDocument: GetOrCreateDocument

    @MockK(relaxed = true)
    private lateinit var dealRepository: DealRepository

    @MockK(relaxed = true)
    private lateinit var changeDealStage: ChangeDealStage

    @MockK(relaxed = true)
    private lateinit var memberRepository: MemberRepository

    @MockK
    private lateinit var sendFileToProcess: SendFileToProcess

    @MockK
    private lateinit var fileRepository: FileRepository

    @MockK
    private lateinit var externalCalendarDocumentEvent: ExternalCalendarDocumentEvent

    private lateinit var executePsa: ExecutePsa

    @BeforeEach
    fun setUp() {
        TransactionManager.initialize(mockk(relaxed = true))
        executePsa = ExecutePsa(
            documentRepository = documentRepository,
            signGateway = signGateway,
            fileGateway = fileGateway,
            clock = clock,
            confirmExecutedDocument = confirmExecutedDocument,
            documentSignRepository = documentSignRepository,
            executeDocumentImpactOnTask = executeDocumentImpactOnTask,
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            createDocumentInteraction = createDocumentInteraction,
            updateDealDatesFromPsaExecuted = updateDealDatesFromPsaExecuted,
            getOrCreateDocument = getOrCreateDocument,
            changeDealStage = changeDealStage,
            dealRepository = dealRepository,
            memberRepository = memberRepository,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent,
            sendFileToProcess = sendFileToProcess,
            fileRepository = fileRepository
        )
    }

    @Test
    fun `can execute psa with file from docusign`() {
        val envelopId = anyString()
        val psaId = anyId()
        val documentId = anyString()
        getSignersByEnvelope(envelopId, psaId, documentId)
        val (psa, round) = getPsaAndRound(psaId)
        val documentContent = retrieveDocument(envelopId, documentId)
        val kFile = uploadFile(psa, documentContent)
        val executedPSADocument = confirmFile(psa, kFile)
        val member = getBuyer(round.memberId)
        givenDeal(psa.dealId)
        val file = File(id = executedPSADocument.fileId, kFileId = kFile.uid, name = kFile.name, memberId = null, createdAt = OffsetDateTime.now())
        val authToken = anyString()
        val documentUpdateInputSlot = slot<Document>()
        every { documentRepository.update(capture(documentUpdateInputSlot)) } returns Unit
        every { executeDocumentImpactOnTask(psa, member) } returns Unit
        every { externalCalendarDocumentEvent.impactOnCalendar(any()) } returns Unit
        every { fileRepository.findById(executedPSADocument.fileId) } returns file
        every { sendFileToProcess(any()) } returns Unit

        executePsa.execute(ExecutePsa.SignInput(envelopId, documentId, authToken = authToken))

        Assertions.assertEquals(executedPSADocument.id, documentUpdateInputSlot.captured.currentRoundId)
        Assertions.assertEquals(DocumentStatus.EXECUTED, documentUpdateInputSlot.captured.status)
        Assertions.assertNotNull(documentUpdateInputSlot.captured.executedAt)
        Assertions.assertNotEquals(documentUpdateInputSlot.captured.updatedAt, psa.updatedAt)
        Assertions.assertNotEquals(documentUpdateInputSlot.captured.executedAt, psa.executedAt)

        verify(exactly = 1) {
            createDocumentInteraction(
                member = null,
                comment = null,
                documentRound = executedPSADocument,
                interactionType = Interaction.Type.EXECUTED
            )
        }
        verify(exactly = 1) {
            sendFileToProcess(
                SendFileToProcess.Input(
                    fileId = file.kFileId,
                    fileType = TokenFileType.PSA,
                    dealId = psa.dealId,
                    authToken = authToken
                )
            )
        }
    }

    @Test
    fun `can execute psa and impact on deal`() {
        val envelopId = anyString()
        val psaId = anyId()
        val documentId = anyString()
        getSignersByEnvelope(envelopId, psaId, documentId)
        val (psa, round) = getPsaAndRound(psaId)
        val documentContent = retrieveDocument(envelopId, documentId)
        val kFile = uploadFile(psa, documentContent)
        val confirmedFile = confirmFile(psa, kFile)
        val member = getBuyer(round.memberId)
        val deal = givenDeal(psa.dealId)
        val updatedDeal = deal.copy(stage = Stage.NEGOTIATION)
        val file = File(id = confirmedFile.fileId, kFileId = kFile.uid, name = kFile.name, memberId = null, createdAt = OffsetDateTime.now())
        val authToken = anyString()
        val documentUpdateInputSlot = slot<Document>()
        every { documentRepository.update(capture(documentUpdateInputSlot)) } returns Unit
        every { executeDocumentImpactOnTask(psa, member) } returns Unit
        every { dealRepository.findById(deal.id) } returns updatedDeal
        every { changeDealStage.invoke(dealId = deal.id, newStage = Stage.DILIGENCE) } returns mockk()
        every { externalCalendarDocumentEvent.impactOnCalendar(any()) } returns Unit
        every { fileRepository.findById(confirmedFile.fileId) } returns file
        every { sendFileToProcess(any()) } returns Unit

        executePsa.execute(ExecutePsa.SignInput(envelopId, documentId, authToken = authToken))

        verify(exactly = 1) {
            changeDealStage.invoke(dealId = deal.id, newStage = Stage.DILIGENCE)
        }

        verify(exactly = 1) {
            updateDealDatesFromPsaExecuted.update(updatedDeal, OffsetDateTime.now(clock).toLocalDate())
        }
        verify(exactly = 1) {
            sendFileToProcess(
                SendFileToProcess.Input(
                    fileId = kFile.uid,
                    fileType = TokenFileType.PSA,
                    dealId = psa.dealId,
                    authToken = authToken
                )
            )
        }
    }

    @Test
    fun `it doesn't change the status if it is DILIGENCE or a later status`() {
        val envelopId = anyString()
        val psaId = anyId()
        val documentId = anyString()
        getSignersByEnvelope(envelopId, psaId, documentId)
        val (psa, round) = getPsaAndRound(psaId)
        val documentContent = retrieveDocument(envelopId, documentId)
        val kFile = uploadFile(psa, documentContent)
        val confirmedFile = confirmFile(psa, kFile)
        val member = getBuyer(round.memberId)
        val deal = givenDeal(psa.dealId)
        val updatedDeal = deal.copy(stage = Stage.CLOSING)
        val file = File(id = confirmedFile.fileId, kFileId = kFile.uid, name = kFile.name, memberId = null, createdAt = OffsetDateTime.now())

        val documentUpdateInputSlot = slot<Document>()
        every { documentRepository.update(capture(documentUpdateInputSlot)) } returns Unit
        every { executeDocumentImpactOnTask(psa, member) } returns Unit
        every { dealRepository.findById(deal.id) } returns updatedDeal
        every { externalCalendarDocumentEvent.impactOnCalendar(any()) } returns Unit
        every { fileRepository.findById(confirmedFile.fileId) } returns file
        every { sendFileToProcess(any()) } returns Unit

        executePsa.execute(ExecutePsa.SignInput(envelopId, documentId, authToken = anyString()))

        verify(exactly = 0) {
            changeDealStage.invoke(any(), any())
        }

        verify(exactly = 1) {
            updateDealDatesFromPsaExecuted.update(updatedDeal, OffsetDateTime.now(clock).toLocalDate())
        }
    }

    @Test
    fun `can execute psa with file from external`() {
        val psaId = anyId()
        val round = DocumentRoundObjectMother.finalDocument(documentId = psaId)
        val psa = DocumentBuilder().apply { id = psaId; currentRoundId = round.id }.build()
        val fileInput = FileInput(uid = anyString(), name = anyString())
        val executedPSADocument = confirmFile(psa, KFile(uid = fileInput.uid, name = fileInput.name))
        val member = getBuyer(round.memberId)
        val deal = givenDeal(psa.dealId)
        val file = File(id = executedPSADocument.fileId, kFileId = fileInput.uid, name = fileInput.name, memberId = null, createdAt = OffsetDateTime.now())
        val authToken = anyString()
        every { getOrCreateDocument.byDealIdAndDocumentType(dealId = deal.id, DocumentType.PSA) } returns psa
        val documentUpdateInputSlot = slot<Document>()
        every { documentRepository.update(capture(documentUpdateInputSlot)) } returns Unit
        every { executeDocumentImpactOnTask(psa, member) } returns Unit
        every { externalCalendarDocumentEvent.impactOnCalendar(any()) } returns Unit
        every { fileRepository.findById(executedPSADocument.fileId) } returns file
        every { sendFileToProcess(any()) } returns Unit

        executePsa.execute(
            ExecutePsa.ExternalInput(
                dealId = deal.id,
                comment = "comment",
                file = fileInput,
                member = member,
                executedAt = LocalDate.now(),
                authToken = authToken
            )
        )

        Assertions.assertEquals(executedPSADocument.id, documentUpdateInputSlot.captured.currentRoundId)
        Assertions.assertEquals(DocumentStatus.EXECUTED, documentUpdateInputSlot.captured.status)
        Assertions.assertNotNull(documentUpdateInputSlot.captured.executedAt)
        Assertions.assertNotEquals(documentUpdateInputSlot.captured.updatedAt, psa.updatedAt)
        Assertions.assertNotEquals(documentUpdateInputSlot.captured.executedAt, psa.executedAt)

        verify(exactly = 1) {
            createDocumentInteraction(
                member = null,
                comment = null,
                documentRound = executedPSADocument,
                interactionType = Interaction.Type.EXECUTED
            )
        }

        verify(exactly = 1) {
            sendFileToProcess(
                SendFileToProcess.Input(
                    fileId = fileInput.uid,
                    fileType = TokenFileType.PSA,
                    dealId = psa.dealId,
                    authToken = authToken
                )
            )
        }
    }

    private fun getPsaAndRound(psaIdInput: Long): Pair<Document, FinalDocument> {
        val round = DocumentRoundObjectMother.finalDocument(documentId = psaIdInput)
        val psa = DocumentBuilder().apply { id = psaIdInput; currentRoundId = round.id }.build()
        every { findDocumentAndCurrentRound.byDocumentId<FinalDocument>(psaIdInput) } returns Pair(psa, round)
        return Pair(psa, round)
    }

    private fun getSignersByEnvelope(envelopeIdInput: String, psaIdInput: Long, documentIdInput: String) =
        listOf(
            DocumentSignerBuilder().setSignCompleted(true).apply {
                envelopeId = envelopeIdInput
                documentId = psaIdInput
                docusignDocumentId = documentIdInput
            }.build(),
            DocumentSignerBuilder().setSignCompleted(true).setBuyerMemberTeam().apply {
                envelopeId = envelopeIdInput
                documentId = psaIdInput
                docusignDocumentId = documentIdInput
            }.build()
        ).also { signers -> every { documentSignRepository.findByEnvelopeId(envelopeIdInput) } returns signers }

    private fun retrieveDocument(envelopeIdInput: String, documentIdInput: String): DocumentOutput =
        DocumentOutput(
            documentIdInput,
            ByteArrayInputStream.nullInputStream()
        ).also {
            every { signGateway.retrieveDocument(RetrieveDocumentInput(envelopeIdInput, documentIdInput)) } returns it
        }

    private fun uploadFile(document: Document, documentOutput: DocumentOutput): KFile =
        KFile(uid = anyString(), name = DocumentFileHelper.fileName(document, FileType.EXECUTED_VERSION, ".pdf")).also {
            every {
                fileGateway.uploadStagedFile(
                    FileToStaging(
                        content = documentOutput.content,
                        contentType = "application/pdf",
                        filename = DocumentFileHelper.fileName(document, FileType.EXECUTED_VERSION, ".pdf")
                    )
                )
            } returns it
        }

    private fun confirmFile(document: Document, file: KFile): ExecutedDocument =
        ExecutedDocument(
            fileId = anyId(),
            documentId = document.id,
            id = anyId()
        ).also {
            every {
                confirmExecutedDocument(
                    input = ConfirmExecutedDocument.Input(
                        dealId = document.dealId,
                        executedVersionFile = FileInput(uid = file.uid, name = file.name)
                    ),
                    document = document
                )
            } returns it
        }

    private fun getBuyer(memberId: Long): Member =
        MemberObjectMother.buyer(memberId).also {
            every { memberRepository.findById(memberId) } returns it
        }

    private fun givenDeal(dealId: Long): Deal =
        DealBuilder().withId(dealId).build().also {
            every { dealRepository.findById(dealId) } returns it
        }
}
