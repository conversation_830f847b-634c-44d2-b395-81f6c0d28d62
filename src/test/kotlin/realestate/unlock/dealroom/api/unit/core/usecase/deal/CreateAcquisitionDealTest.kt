package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.*
import realestate.unlock.dealroom.api.core.entity.deal.inputs.ContractExecutionInput
import realestate.unlock.dealroom.api.core.entity.deal.inputs.DealDataInput
import realestate.unlock.dealroom.api.core.entity.deal.inputs.MembersInput
import realestate.unlock.dealroom.api.core.entity.deal.schema.DealSchema
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.entity.property.PropertyCreationByAddressInput
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.event.deal.DealEventPublisher
import realestate.unlock.dealroom.api.core.event.deal.EventType
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.repository.deal.DealMemberRelationRepository
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.deal.SaveDealData
import realestate.unlock.dealroom.api.core.repository.deal.schema.DealSchemaRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalDealCalendarActions
import realestate.unlock.dealroom.api.core.usecase.deal.category.CreateCategoriesAndTasksForDeal
import realestate.unlock.dealroom.api.core.usecase.deal.create.CreateAcquisitionDeal
import realestate.unlock.dealroom.api.core.usecase.deal.create.CreateDeal
import realestate.unlock.dealroom.api.core.usecase.deal.create.FindOrCreatePropertyAssetsProperty
import realestate.unlock.dealroom.api.core.usecase.deal.team.ValidateMembersOnDeal
import realestate.unlock.dealroom.api.core.usecase.property.CreateProperty
import realestate.unlock.dealroom.api.core.usecase.property.UploadPropertyPhotoToKFile
import realestate.unlock.dealroom.api.core.usecase.tag.ValidateAndCreateTags
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.images.PropertyImagesResponse
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyAssetsPropertyBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.stub.FeatureFlagsStub
import java.math.BigDecimal
import java.time.Clock
import java.time.OffsetDateTime

class CreateAcquisitionDealTest {

    private lateinit var createAcquisitionDeal: CreateAcquisitionDeal
    private val dealRepository: DealRepository = mockk(relaxed = true)
    private val dealSchemaRepository: DealSchemaRepository = mockk(relaxed = true)
    private val clock: Clock = mockk(relaxed = true)
    private val createProperty: CreateProperty = mockk(relaxed = true)
    private val memberRepository: MemberRepository = mockk(relaxed = true)
    private val uploadPropertyPhotoToKFile: UploadPropertyPhotoToKFile = mockk(relaxed = true)
    private val saveDealMemberRelationRepository: DealMemberRelationRepository = mockk(relaxed = true)
    private val createCategoriesAndTasksForDeal: CreateCategoriesAndTasksForDeal = mockk(relaxed = true)
    private val validateAndCreateTags: ValidateAndCreateTags = mockk(relaxed = true)
    private val validateMembersOnDeal: ValidateMembersOnDeal = mockk(relaxed = true)
    private val dealEventPublisher: DealEventPublisher = mockk()
    private val externalDealCalendarActions: ExternalDealCalendarActions = mockk()
    private val featureFlags = FeatureFlagsStub(mutableMapOf(Feature.ORG_CAN_CREATE_NNN to true))
    private val findOrCreatePropertyAssetsProperty: FindOrCreatePropertyAssetsProperty = mockk(relaxed = true)
    private val givenPropertyAssetsResponse = PropertyAssetsPropertyBuilder().build()

    @BeforeEach
    fun setUp() {
        TransactionManager.initialize(mockk(relaxed = true))
        createAcquisitionDeal = CreateAcquisitionDeal(
            CreateDeal(
                dealRepository,
                dealSchemaRepository,
                clock,
                createProperty,
                memberRepository,
                uploadPropertyPhotoToKFile,
                saveDealMemberRelationRepository,
                createCategoriesAndTasksForDeal,
                validateAndCreateTags,
                validateMembersOnDeal,
                dealEventPublisher,
                externalDealCalendarActions,
                featureFlags,
                findOrCreatePropertyAssetsProperty
            )
        )
        every { findOrCreatePropertyAssetsProperty.invoke(any<PropertyCreationByAddressInput>()) } returns givenPropertyAssetsResponse
    }

    @Test
    fun `creates property`() {
        // given
        val input = givenInput()
        every { dealEventPublisher.publish(EventType.NEW_DEAL, any(), any()) } returns Unit

        // when
        createAcquisitionDeal.create(input)

        // then
        verify {
            createProperty.create(input.property, givenPropertyAssetsResponse)
        }
    }

    @Test
    fun `creates deal`() {
        // given
        val givenDealDataInput = givenDealDataInput().copy(
            buyerCompanyName = "buyer company name",
            brokerCompanyName = "broker company name",
            sellerCompanyName = "seller company name"
        )
        val givenProperty = PropertyBuilder().build()
        val input = givenInput(dealDataInput = givenDealDataInput)
        every { createProperty.create(input.property, givenPropertyAssetsResponse) } returns givenProperty
        val givenDeal = DealBuilder().apply { propertyId = givenProperty.id }.build()
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit
        // when
        val deal = createAcquisitionDeal.create(input)

        // then
        Assertions.assertNotNull(deal)
        Assertions.assertEquals(Stage.EVALUATION, deal.stage)
        Assertions.assertEquals(deal.propertyId, givenProperty.id)
        Assertions.assertEquals(givenDealDataInput.buyerCompanyName, deal.buyerCompanyName)
        Assertions.assertEquals(givenDealDataInput.brokerCompanyName, deal.brokerCompanyName)
        Assertions.assertEquals(givenDealDataInput.sellerCompanyName, deal.sellerCompanyName)

        verify { dealEventPublisher.publish(EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) }
    }

    @Test
    fun `do not creates executed loi when no loi executed date`() {
        // given
        val givenProperty = PropertyBuilder().build()
        val givenDeal = DealBuilder().build()
        val input = givenInput()
        every { createProperty.create(input.property, givenPropertyAssetsResponse) } returns givenProperty
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit

        val completeDeal = givenDeal.toCompleteDeal(givenProperty)
        // when
        val deal = createAcquisitionDeal.create(input)

        // then
        Assertions.assertEquals(Stage.EVALUATION, deal.stage)
    }

    @Test
    fun `should create a property and dowload photo`() {
        val input = givenInput()
        val givenProperty = PropertyBuilder().build()
        val givenDeal = DealBuilder().withPropertyId(givenProperty.id).build()
        val givenPropertyPhotoUrl = anyString()
        val givenPropertyAssetsImages = PropertyImagesResponse(urls = listOf(givenPropertyPhotoUrl))
        val givenPropertyAssetsResponse = givenPropertyAssetsResponse.copy(images = givenPropertyAssetsImages)

        every { findOrCreatePropertyAssetsProperty(input.property) } returns givenPropertyAssetsResponse
        every { createProperty.create(input.property, givenPropertyAssetsResponse) } returns givenProperty
        every { uploadPropertyPhotoToKFile.invoke(givenProperty.id, givenDeal.id, givenPropertyPhotoUrl) } returns KFile(anyString(), anyString())
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit

        val deal = createAcquisitionDeal.create(input)

        Assertions.assertEquals(deal.propertyId, givenProperty.id)
        verify {
            uploadPropertyPhotoToKFile.invoke(givenProperty.id, givenDeal.id, givenPropertyPhotoUrl)
        }
    }

    @Test
    fun `download photo fails but dont affect flow`() {
        val input = givenInput()
        val givenProperty = PropertyBuilder().build()
        val givenDeal = DealBuilder()
            .apply { propertyId = givenProperty.id }
            .build()

        val givenPropertyPhotoUrl = anyString()
        val givenPropertyAssetsImages = PropertyImagesResponse(urls = listOf(givenPropertyPhotoUrl))
        val givenPropertyAssetsProperty = givenPropertyAssetsResponse.copy(images = givenPropertyAssetsImages)

        every { findOrCreatePropertyAssetsProperty(input.property) } returns givenPropertyAssetsProperty
        every { createProperty.create(input.property, givenPropertyAssetsProperty) } returns givenProperty
        every { dealRepository.save(any()) } returns givenDeal
        every { uploadPropertyPhotoToKFile.invoke(givenProperty.id, givenDeal.id, givenPropertyPhotoUrl) }.throws(Exception("NO"))
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit

        val deal = createAcquisitionDeal.create(input)

        Assertions.assertEquals(deal.propertyId, givenProperty.id)
        verify {
            uploadPropertyPhotoToKFile.invoke(givenProperty.id, givenDeal.id, givenPropertyPhotoUrl)
        }
    }

    @Test
    fun `creating a deal with a property with no images should not fail`() {
        val input = givenInput()
        val givenProperty = PropertyBuilder().build()
        val givenDeal = DealBuilder()
            .apply { propertyId = givenProperty.id }
            .build()

        val givenPropertyAssetsProperty = givenPropertyAssetsResponse.copy(images = PropertyImagesResponse(urls = listOf()))
        every { findOrCreatePropertyAssetsProperty(input.property) } returns givenPropertyAssetsProperty
        every { createProperty.create(input.property, givenPropertyAssetsProperty) } returns givenProperty
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit

        val deal = createAcquisitionDeal.create(input)

        Assertions.assertEquals(deal.propertyId, givenProperty.id)
        verify(exactly = 0) {
            uploadPropertyPhotoToKFile.invoke(givenProperty.id, givenDeal.id, any())
        }
    }

    @Test
    fun `saves deal data in repositories`() {
        // given
        val givenInput = givenInput()
        val givenProperty = PropertyBuilder().build()
        val givenDeal = DealBuilder().withPropertyId(givenProperty.id).build()
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = any()) } returns Unit

        val slot = slot<Iterable<DealMemberRelation>>()
        every { saveDealMemberRelationRepository.save(capture(slot)) } returns Unit
        val dealSchema: DealSchema = mockk(relaxed = true)
        every { dealSchema.id } returns 123
        every { dealSchemaRepository.findLastByPropertyType(any()) } returns dealSchema

        // when
        createAcquisitionDeal.create(givenInput)

        val expectedSaveDealData = expectedSaveDealData(givenInput, givenProperty, 123)
        // then
        verify { dealRepository.save(expectedSaveDealData) }
        givenInput.members.dealMembers.forEach {
            Assertions.assertTrue(slot.captured.any { captured -> captured.memberId == it })
        }
    }

    @Test
    fun `creates categories and tasks for deal`() {
        // given
        val givenDeal = DealBuilder().build()
        val members = setOf(seller(), buyer())
        val input = givenInput(membersInput = givenMemberInput(members))
        val givenProperty = mockProperty()
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit
        // when
        createAcquisitionDeal.create(input)

        // then
        assertCategoriesWereCreated(givenDeal, members, givenProperty)
    }

    @Test
    fun `creates categories and tasks for deal with seller broker only`() {
        // given )
        val members = setOf(sellerBroker(), buyer())
        val property = PropertyBuilder().build()
        val input = givenInput(
            membersInput = givenMemberInput(dealMembers = members),
            propertyInput = givenPropertyInput(property)
        )
        val givenDeal = DealBuilder().build()

        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = property) } returns Unit
        val slot = slot<Iterable<DealMemberRelation>>()
        every { saveDealMemberRelationRepository.save(capture(slot)) } returns Unit
        // when
        createAcquisitionDeal.create(input)

        // then
        assertCategoriesWereCreated(givenDeal, members, property)
        input.members.dealMembers.forEach {
            Assertions.assertTrue(slot.captured.any { captured -> captured.memberId == it })
        }
    }

    @Test
    fun `creates categories and tasks for deal with 2 sellers`() {
        // given
        val members = setOf(buyer(), seller(), sellerBroker())
        val property = PropertyBuilder().build()
        val input = givenInput(
            membersInput = givenMemberInput(dealMembers = members),
            propertyInput = givenPropertyInput(property)
        )
        val givenDeal = DealBuilder().build()

        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = property) } returns Unit
        val slot = slot<Iterable<DealMemberRelation>>()
        every { saveDealMemberRelationRepository.save(capture(slot)) } returns Unit
        // when
        createAcquisitionDeal.create(input)

        // then
        assertCategoriesWereCreated(givenDeal, members, property)
        input.members.dealMembers.forEach {
            Assertions.assertTrue(slot.captured.any { captured -> captured.memberId == it })
        }
    }

    private fun assertCategoriesWereCreated(
        givenDeal: Deal,
        members: Set<Member>,
        property: Property
    ) {
        verify {
            createCategoriesAndTasksForDeal.create(
                deal = givenDeal.toCompleteDeal(property)
                    .copy(members = members),
                dealTemplateKey = if (property.type == PropertyType.MEDICAL) "default" else "multifamily"
            )
        }
    }

    private fun seller(id: Long = 1): Member = MemberBuilder()
        .withId(id)
        .withSellerType()
        .build()

    private fun buyer(id: Long = 2): Member = MemberBuilder()
        .withId(id)
        .withBuyerType()
        .build()

    private fun sellerBroker(id: Long = 3): Member = MemberBuilder()
        .withId(id)
        .withSellerBrokerType()
        .build()

    private fun mockProperty(property: Property = PropertyBuilder().build()) =
        property.also { every { createProperty.create(any<PropertyCreationByAddressInput>(), any()) } returns it }

    private fun givenInput(
        dealDataInput: DealDataInput = givenDealDataInput(),
        propertyInput: PropertyCreationByAddressInput = givenPropertyInput(),
        contractExecutionInput: ContractExecutionInput = givenContractExecutionInput(),
        membersInput: MembersInput = givenMemberInput()
    ): CreateAcquisitionDeal.Input =
        CreateAcquisitionDeal.Input(
            property = propertyInput,
            dealData = dealDataInput,
            members = membersInput,
            contractExecution = contractExecutionInput,
            organizationId = "org-id"
        )

    private fun givenMemberInput(
        dealMembers: Set<Member> = setOf(seller(), sellerBroker(), buyer()),
        leaderId: Long = dealMembers.first { it.type == MemberTypeEnum.BUYER }.id
    ): MembersInput =
        MembersInput(
            dealMembers = dealMembers.map { it.id }.toSet(),
            tenantName = "tenantName",
            leaderId = leaderId
        ).also {
            every { memberRepository.findByMemberIds(it.dealMembers) } returns dealMembers.toList()
        }

    private fun givenDealDataInput() = DealDataInput(
        vertical = null,
        guaranteeType = null,
        type = DealType.SALE_LEASEBACK,
        estimateId = null,
        offerPrice = BigDecimal.ONE,
        earnestMoneyDeposit = BigDecimal.valueOf(2),
        extensionDeposit = BigDecimal.valueOf(3)
    )

    private fun givenPropertyInput(property: Property = PropertyBuilder().build()) =
        PropertyCreationByAddressInput(
            keywayId = property.keywayId,
            street = property.address.street,
            apartment = property.address.apartment,
            city = property.address.city,
            state = property.address.state,
            zip = property.address.zip,
            yearBuilt = property.yearBuilt,
            squareFootage = property.squareFootage,
            askingPrice = property.askingPrice,
            latitude = property.address.coordinates?.latitude,
            longitude = property.address.coordinates?.longitude,
            name = property.name,
            type = PropertyType.MEDICAL,
            multifamilyData = null
        ).also {
            every { createProperty.create(it, givenPropertyAssetsResponse) } returns property
        }

    private fun givenContractExecutionInput() = ContractExecutionInput()

    private fun expectedSaveDealData(input: CreateAcquisitionDeal.Input, property: Property, dealSchemaId: Long): SaveDealData {
        return SaveDealData(
            propertyId = property.id,
            status = DealStatus.ACTIVE,
            stage = Stage.EVALUATION,
            loiExecutedDate = null,
            contractExecutedDate = null,
            diligenceExpirationDate = null,
            initialClosingDate = null,
            outsideClosingDate = null,
            evaluationDueDate = null,
            underwritingDueDate = null,
            updatedAt = OffsetDateTime.now(clock),
            createdAt = OffsetDateTime.now(clock),
            dealType = input.dealData.type,
            tenantName = input.members.tenantName,
            vertical = input.dealData.vertical,
            leaseRent = null,
            leaseType = null,
            leaseRentIncrease = null,
            leaseIncreaseEveryYear = null,
            leaseLength = null,
            leaseExpirationYear = null,
            leaseNumberOfOptions = null,
            leaseOptionLengths = null,
            leaseRentCpi = null,
            leaseRentStepType = null,
            guaranteeType = input.dealData.guaranteeType,
            dueDiligenceNumber = input.contractExecution.dueDiligenceNumber,
            closingPeriod = input.contractExecution.closingPeriod,
            closingPeriodExtension = input.contractExecution.closingPeriodExtension,
            dealSchemaId = dealSchemaId,
            tags = emptySet(),
            leaderId = input.members.leaderId,
            offerPrice = input.dealData.offerPrice,
            earnestMoneyDeposit = input.dealData.earnestMoneyDeposit,
            extensionDeposit = input.dealData.extensionDeposit,
            organizationId = input.organizationId,
            buyerCompanyName = input.dealData.buyerCompanyName,
            brokerCompanyName = input.dealData.brokerCompanyName,
            sellerCompanyName = input.dealData.sellerCompanyName,
            leaseDate = null,
            firstPassId = null
        )
    }
}
