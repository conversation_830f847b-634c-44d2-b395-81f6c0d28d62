package realestate.unlock.dealroom.api.unit.core.usecase.task

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.DealData
import realestate.unlock.dealroom.api.core.entity.deal.DealType
import realestate.unlock.dealroom.api.core.entity.task.TaskExternalDataDefinition
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.field.DealDataFieldParser
import realestate.unlock.dealroom.api.core.usecase.task.FillTaskWithDealData.fillWithDealData
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import java.math.BigDecimal
import java.time.LocalDate

class FillTaskWithDealDataTest {

    @Test
    fun `must return task dueDate form deal with value`() {
        val task = TaskBuilder().apply {
            this.taskExternalDataDefinition = TaskExternalDataDefinition("deal.loiExecutedDate", emptySet())
        }.build()

        val deal = DealBuilder().apply {
            loiExecutedDate = LocalDate.now()
        }.build()

        val filledTask = task.fillWithDealData(
            dealData =
            DealData(deal = deal, property = PropertyBuilder().build())
        )

        Assertions.assertEquals(task.id, filledTask.id)
        Assertions.assertEquals(task.attachedFormData, filledTask.attachedFormData)
        Assertions.assertEquals(filledTask.dueDate, deal.loiExecutedDate)
    }

    @Test
    fun `must return task dueDate form deal without value`() {
        val task = TaskBuilder().apply {
            this.taskExternalDataDefinition = TaskExternalDataDefinition("deal.loiExecutedDate", emptySet())
        }.build()

        val deal = DealBuilder().apply {
            loiExecutedDate = null
        }.build()

        val filledTask = task.fillWithDealData(
            dealData =
            DealData(deal = deal, property = PropertyBuilder().build())
        )

        Assertions.assertEquals(task.id, filledTask.id)
        Assertions.assertEquals(task.attachedFormData, filledTask.attachedFormData)
        Assertions.assertEquals(filledTask.dueDate, deal.loiExecutedDate)
    }

    @Test
    fun `must return task attached form data with deal data`() {
        val fields = setOf(
            "deal.loiExecutedDate",
            "deal.type",
            "deal.offerPrice",
            "deal.schemaData.data.marketReno",
            "deal.propertyId",
            "property.name"
        )
        val task = TaskBuilder().apply {
            this.taskExternalDataDefinition = TaskExternalDataDefinition(null, fields)
        }.build()
        val deal = DealBuilder().apply {
            this.loiExecutedDate = LocalDate.now()
            this.dealType = DealType.SALE_LEASEBACK
            this.offerPrice = BigDecimal.TEN
            this.schemaData = mapOf("marketReno" to "yes")
            this.propertyId = 123
        }.build()
        val property = PropertyBuilder().apply { this.name = "Nombre" }.build()
        val dealData = DealData(deal = deal, property = property)
        val filledTask = task.fillWithDealData(dealData = dealData)
        Assertions.assertEquals(task.id, filledTask.id)
        fields.forEach {
            Assertions.assertEquals(
                filledTask.attachedFormData?.get(it),
                DealDataFieldParser.getValue(it, dealData)
            )
        }
    }

    @Test
    fun `must return task attached form data with deal data and merge with current`() {
        val fields = setOf(
            "deal.loiExecutedDate",
            "deal.type"
        )
        val formData = mapOf("pepe" to 123)
        val task = TaskBuilder().apply {
            this.attachedFormData = formData
            this.taskExternalDataDefinition = TaskExternalDataDefinition(null, fields)
        }.build()
        val deal = DealBuilder().apply {
            this.loiExecutedDate = LocalDate.now()
            this.dealType = DealType.SALE_LEASEBACK
            this.offerPrice = BigDecimal.TEN
            this.schemaData = mapOf("marketReno" to "yes")
            this.propertyId = 123
        }.build()
        val property = PropertyBuilder().apply { this.name = "Nombre" }.build()
        val dealData = DealData(deal = deal, property = property)
        val filledTask = task.fillWithDealData(dealData = dealData)
        Assertions.assertEquals(task.id, filledTask.id)
        Assertions.assertEquals(formData.size + fields.size, filledTask.attachedFormData?.size)
        fields.forEach {
            Assertions.assertEquals(
                filledTask.attachedFormData?.get(it),
                DealDataFieldParser.getValue(it, dealData)
            )
        }
        formData.forEach { (key, value) ->
            Assertions.assertEquals(
                filledTask.attachedFormData?.get(key),
                value
            )
        }
    }
}
