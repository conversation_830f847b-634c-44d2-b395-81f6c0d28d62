package realestate.unlock.dealroom.api.unit.entrypoint.rest.security.filter.deal

import io.javalin.http.Context
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.usecase.member.GetMembersByDealId
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ForbiddenException
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.security.anyUserToken

class VerifyDealMembershipByDealIdPathParamSecurityFilterTest {

    private val member = MemberObjectMother.buyer()
    private val givenAuthenticatedUser = AuthenticatedUser(member, anyUserToken(), authToken = anyString())
    private val ctx: Context = mockk(relaxed = true)

    private val getMembersByDealId: GetMembersByDealId = mockk()
    private val filter = VerifyDealMembershipByDealIdPathParamSecurityFilter(getMembersByDealId)

    @Test
    fun `throws bad request on missing deal-id path param`() {
        // when
        val action = { filter.apply(ctx, givenAuthenticatedUser) }

        // then
        assertThrows<BadRequestException>(action)
    }

    @Test
    fun `throws forbidden when member is not in deal members`() {
        // given
        givenDealPathParam()
        every { getMembersByDealId.get(any()) } returns setOf()

        // when
        val action = { filter.apply(ctx, givenAuthenticatedUser) }

        // then
        assertThrows<ForbiddenException>(action)
    }

    @Test
    fun `authorizes user`() {
        // given
        givenDealPathParam()
        val givenDeal = DealBuilder().apply { organizationId = member.organizationId }.build()
        every { getMembersByDealId.get(any()) } returns setOf(member)

        // when
        val action = { filter.apply(ctx, givenAuthenticatedUser) }

        // then
        assertDoesNotThrow(action)
    }

    private fun givenDealPathParam() {
        every { ctx.pathParamAsClass<Long>(eq("deal-id")).get() } returns 1
    }
}
