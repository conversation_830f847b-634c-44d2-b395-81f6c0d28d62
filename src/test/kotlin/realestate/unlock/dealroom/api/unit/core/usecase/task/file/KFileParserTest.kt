package realestate.unlock.dealroom.api.unit.core.usecase.task.file

import com.fasterxml.jackson.core.type.TypeReference
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.usecase.task.file.KFileParser
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper

class KFileParserTest {

    private lateinit var kFileParser: KFileParser

    @BeforeEach
    fun setUP() {
        kFileParser = KFileParser()
    }

    @Test
    fun `can extract id from data form with file field`() {
        // given
        val taskTemplate = givenTaskTemplate(getSchemaWithFile())
        val fileKey = "file"
        val kFile = KFile(uid = "aa", name = "file.pdf")
        val fileValue = """[{"uid":"${kFile.uid}","name":"${kFile.name}"}]"""

        // when
        val result = kFileParser.parse(taskTemplate, mapOf(fileKey to fileValue))

        // then
        assertThat(result, IsEqual(listOf(kFile)))
    }

    @Test
    fun `can extract id from data form with file field on dependency`() {
        // given
        val taskTemplate = givenTaskTemplate(getSchemaWithFileOnDependencies())
        val fileKey = "file"
        val kFile = KFile(uid = "99", name = "file2.pdf")
        val fileValue = """[{"uid":"${kFile.uid}","name":"${kFile.name}"}]"""

        // when
        val result = kFileParser.parse(taskTemplate, mapOf(fileKey to fileValue))

        // then
        assertThat(result, IsEqual(listOf(kFile)))
    }

    @Test
    fun `returns empty for a schema without file`() {
        // given
        val taskTemplate = givenTaskTemplate(getSchemaWithoutFiles())

        // when
        val result = kFileParser.parse(taskTemplate, mapOf("name" to "name"))

        // then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `can handle more that one file`() {
        // given
        val taskTemplate = givenTaskTemplate(getSchemaWithFile())
        val fileKey = "file"
        val kFile1 = KFile(uid = "ss", name = "resume.pdf")
        val kFile2 = KFile(uid = "pp", name = "photos.pdf")

        val fileValue = """[{"uid":"${kFile1.uid}","name":"${kFile1.name}"},{"uid":"${kFile2.uid}","name":"${kFile2.name}"}]"""

        // when
        val result = kFileParser.parse(taskTemplate, mapOf(fileKey to fileValue))

        // then
        assertThat(result, IsEqual(listOf(kFile1, kFile2)))
    }

    @Test
    fun `can extract file from psa or lease document`() {
        // given
        val kFile = KFile(uid = "b0e1507a-4ca5-4212-ba18-96b58049168e", name = "file.pdf")
        val fileValue = JsonMapper.decode(
            """{
                            "file": {
                                        "uid": "${kFile.uid}",
                                        "name": "${kFile.name}"
                                    },
                            "comment": "See attached for a revised version"
                            }""",
            (object : TypeReference<Map<String, Any>>() {})
        )

        // when
        val result = kFileParser.parseDocumentFile(fileValue)

        // then
        assertThat(result, IsEqual(listOf(kFile)))
    }

    @Test
    fun `can handle psa or lease without document`() {
        // when
        val nullResult = kFileParser.parseDocumentFile(null)
        val emptyResult = kFileParser.parseDocumentFile(mapOf())

        // then
        assertTrue(nullResult.isEmpty())
        assertTrue(emptyResult.isEmpty())
    }

    private fun givenTaskTemplate(schema: String) = JsonMapper.decode(schema, (object : TypeReference<Map<String, Any>>() {}))

    private fun getSchemaWithFile() = """
        {
          "type": "object",
          "required": [
            "file"
          ],
          "properties": {
            "file": {
              "type": "string",
              "title": "Please upload any related document",
              "format": "data-url"
            }
          }
        }
    """.trimIndent()

    private fun getSchemaWithFileOnDependencies() = """
        {
          "type": "object",
          "required": [
            "serviceContracts"
          ],
          "properties": {
            "serviceContracts": {
              "enum": [
                "",
                "Yes",
                "No"
              ],
              "type": "string",
              "title": "Are there any service contracts at the landlord level?"
            }
          },
          "dependencies": {
            "serviceContracts": {
              "oneOf": [
                {
                  "required": [
                    "file"
                  ],
                  "properties": {
                    "file": {
                      "type": "string",
                      "title": "Please upload any service contracts",
                      "format": "data-url"
                    },
                    "serviceContracts": {
                      "enum": [
                        "Yes"
                      ]
                    }
                  }
                }
              ]
            }
          }
        }
    """.trimIndent()

    private fun getSchemaWithoutFiles() = """
        {
          "type": "object",
          "required": [
            "visitOn",
            "name",
            "phone",
            "email"
          ],
          "properties": {
            "name": {
              "type": "string",
              "title": "Name"
            },
            "email": {
              "type": "string",
              "title": "Email",
              "format": "email"
            },
            "phone": {
              "type": "string",
              "title": "Phone",
              "format": "phone"
            },
            "visitOn": {
              "type": "string",
              "title": "Keyway scheduled a site visit for",
              "format": "date"
            }
          }
        }
    """.trimIndent()
}
