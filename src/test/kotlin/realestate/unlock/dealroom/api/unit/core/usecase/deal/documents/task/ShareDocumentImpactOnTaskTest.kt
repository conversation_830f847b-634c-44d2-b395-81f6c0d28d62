package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.task

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.entity.task.TaskStatusTransitions
import realestate.unlock.dealroom.api.core.entity.task.TaskTransitionInput
import realestate.unlock.dealroom.api.core.entity.task.file.TaskFileToSave
import realestate.unlock.dealroom.api.core.repository.document.DocumentRoundRepository
import realestate.unlock.dealroom.api.core.repository.file.FileRepository
import realestate.unlock.dealroom.api.core.repository.task.file.TaskFileRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.ShareDocumentImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.task.GetDocumentTask
import realestate.unlock.dealroom.api.core.usecase.task.TaskTransition
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class ShareDocumentImpactOnTaskTest {

    @MockK
    private lateinit var getDocumentTask: GetDocumentTask

    @MockK
    private lateinit var documentRoundRepository: DocumentRoundRepository

    @MockK
    private lateinit var fileRepository: FileRepository

    @MockK(relaxed = true)
    private lateinit var taskTransition: TaskTransition

    @MockK(relaxed = true)
    private lateinit var taskFileRepository: TaskFileRepository

    private lateinit var shareDocumentImpactOnTask: ShareDocumentImpactOnTask

    @BeforeEach
    fun setUp() {
        shareDocumentImpactOnTask = ShareDocumentImpactOnTask(
            getDocumentTask = getDocumentTask,
            documentRoundRepository = documentRoundRepository,
            fileRepository = fileRepository,
            taskFileRepository = taskFileRepository,
            taskTransition = taskTransition
        )
    }

    @Test
    fun `It updates the PSA task and stores the task file`() {
        val givenRoundId = 12L
        val givenFileId = 15L
        val user = CompleteUserObjectMother.buyer()
        val givenPsa = DocumentBuilder().apply { this.currentRoundId = givenRoundId }.build()
        val givenPsaTask = TaskBuilder().withTemplateKey("purchase_and_sale_contract").build()
        val givenFile = File(id = givenFileId, name = "file-name", kFileId = anyString(), memberId = 4L, createdAt = OffsetDateTime.now())
        every { getDocumentTask(givenPsa) } returns givenPsaTask
        every { fileRepository.findById(givenFileId) } returns givenFile
        every { documentRoundRepository.findById(givenRoundId) } returns DocumentRoundObjectMother.draftDocuments(cleanVersionFileId = givenFileId)

        shareDocumentImpactOnTask(givenPsa, user.member)

        verify {
            taskFileRepository.save(
                TaskFileToSave(
                    name = givenFile.name,
                    path = "deal/${givenPsa.dealId}/psa/${givenPsa.id}/draft/${givenPsa.currentRoundId}_CLEAN_VERSION",
                    kFileId = givenFile.kFileId,
                    taskId = givenPsaTask.id,
                    memberId = user.memberId
                )
            )
        }

        verify {
            taskTransition.invoke(
                TaskTransitionInput(
                    taskId = givenPsaTask.id,
                    member = user.member,
                    transition = TaskStatusTransitions.SHARE
                )
            )
        }
    }
}
