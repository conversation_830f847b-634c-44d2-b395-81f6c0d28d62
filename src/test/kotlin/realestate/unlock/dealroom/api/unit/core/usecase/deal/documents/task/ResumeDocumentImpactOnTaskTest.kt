package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.task

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.OnHoldPreviousStatus
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.ResumeDocumentImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.task.GetDocumentTask
import realestate.unlock.dealroom.api.core.usecase.task.update.UpdateTaskMediator
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother

@ExtendWith(MockKExtension::class)
class ResumeDocumentImpactOnTaskTest {

    @MockK
    private lateinit var getDocumentTask: GetDocumentTask

    @MockK(relaxed = true)
    private lateinit var updateTaskMediator: UpdateTaskMediator
    private lateinit var resumeDocumentImpactOnTask: ResumeDocumentImpactOnTask

    @BeforeEach
    fun setUp() {
        resumeDocumentImpactOnTask = ResumeDocumentImpactOnTask(
            getDocumentTask = getDocumentTask,
            updateTaskMediator = updateTaskMediator
        )
    }

    @Test
    fun `resume psa impact on task`() {
        val psa = DocumentBuilder().build()
        val task = givenTask(psa)
        val user = CompleteUserObjectMother.buyer()

        val onHoldPreviousStatus = OnHoldPreviousStatus(psa.id, DocumentStatus.PENDING_SIGN, TaskStatus.TO_DO)

        resumeDocumentImpactOnTask(psa, onHoldPreviousStatus, user.member)

        verify {
            updateTaskMediator.update(
                UpdateTaskMediator.Input(
                    id = task.id,
                    dueDate = task.dueDate,
                    status = onHoldPreviousStatus.taskStatus,
                    attachedForm = null,
                    assignedBuyerId = task.assignedBuyer.id,
                    member = user.member,
                    rejectionReason = task.rejectionReason,
                    assignedTeam = task.assignedTeam
                )
            )
        }
    }

    private fun givenTask(document: Document) = TaskBuilder().build().also {
        every { getDocumentTask(document) } returns it
    }
}
