package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.RequestBusinessReview
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class RequestBusinessReviewTest : InteractWithDocumentRoundTest() {

    companion object {
        private val now = Instant.now()
    }

    private lateinit var requestBusinessReview: RequestBusinessReview

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        requestBusinessReview = RequestBusinessReview(
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            clock = clock,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `cannot ask for business review if psa is not on legal review`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId)
        val psa = givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.EXECUTED, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = psa.dealId,
            comment = "reviewed version",
            member = user.member,
            documentType = DocumentType.PSA
        )

        assertThrows<InvalidDocumentStatusException> { requestBusinessReview.update(input) }
    }

    @Test
    fun `can ask for business review`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId)
        val psa = givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.LEGAL_REVIEW, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = psa.dealId,
            comment = "reviewed version",
            member = user.member,
            documentType = DocumentType.PSA
        )

        val psaUpdated = requestBusinessReview.update(input)

        assertDocumentWasUpdated(psaUpdated, DocumentStatus.BUYER_REVIEW)
        assertInteractionWasCreated(draftDocuments, input, Interaction.Type.REQUEST_BUSINESS_REVIEW)
    }
}
