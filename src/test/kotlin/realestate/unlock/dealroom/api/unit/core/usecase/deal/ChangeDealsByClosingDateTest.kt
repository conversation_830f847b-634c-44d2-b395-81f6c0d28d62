package realestate.unlock.dealroom.api.unit.core.usecase.deal

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealStage
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealsByClosingDate
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.Clock
import java.time.LocalDate

class ChangeDealsByClosingDateTest : BaseFunctionalWithoutRestTest() {

    private lateinit var changeDealsByClosingDate: ChangeDealsByClosingDate
    private lateinit var buyer: Member
    private lateinit var seller: Member
    private var dealRepository = Context.injector.getInstance(DealRepository::class.java)
    private var changeDealStage = Context.injector.getInstance(ChangeDealStage::class.java)

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        changeDealsByClosingDate = ChangeDealsByClosingDate(
            dealRepository,
            changeDealStage,
            Clock.systemDefaultZone()
        )
    }

    @Test
    fun `can find closing expired deals and update to closing`() {
        val deals = mutableMapOf<Int, CompleteDeal>()
        for (i in 1..4) {
            deals[i] = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        }
        val updatedDeals = mutableMapOf<Int, Deal>()

        for (i in 1..2) {
            updatedDeals[i] = deals[i]?.let { deal ->
                dealRepository.findById(deal.id).let {
                    dealRepository.update(
                        it.copy(
                            stage = Stage.DILIGENCE,
                            initialClosingDate = LocalDate.now().plusDays(-1)
                        )
                    )
                }
            }!!
        }

        val nonChangeDiligence = dealRepository.findById(deals[3]!!.id).let {
            dealRepository.update(
                it.copy(
                    stage = Stage.DILIGENCE,
                    initialClosingDate = LocalDate.now().plusDays(1)
                )
            )
        }

        val nonChangeClosed = dealRepository.findById(deals[4]!!.id).let {
            dealRepository.update(
                it.copy(
                    stage = Stage.NEGOTIATION,
                    initialClosingDate = LocalDate.now().plusDays(10)
                )
            )
        }

        // when
        changeDealsByClosingDate.findAndUpdate()

        for (i in 1..2) {
            Assertions.assertEquals(dealRepository.findById(deals[i]!!.id).stage, Stage.CLOSING)
        }
        Assertions.assertEquals(dealRepository.findById(nonChangeClosed.id).stage, nonChangeClosed.stage)
        Assertions.assertEquals(dealRepository.findById(nonChangeDiligence.id).stage, nonChangeDiligence.stage)
    }
}
