package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.loi.draft.*
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentDraftRepository
import realestate.unlock.dealroom.api.core.usecase.exception.deal.InvalidDealStageException
import realestate.unlock.dealroom.api.core.usecase.loi.SaveLetterOfIntentDraft
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder

class SaveLetterOfIntentDraftTest {

    private val dealRepository: DealRepository = mockk()
    private val letterOfIntentDraftRepository: LetterOfIntentDraftRepository = mockk()
    private val saveLetterOfIntentDraft = SaveLetterOfIntentDraft(dealRepository, letterOfIntentDraftRepository)

    @Test
    fun `Cannot save a LOI draft if deal is in contract negotiation`() {
        val givenDeal = givenDealInStage(Stage.NEGOTIATION)

        assertThrows<InvalidDealStageException> {
            saveLetterOfIntentDraft.save(LetterOfIntentDraftInput(dealId = givenDeal.id))
        }
    }

    @Test
    fun `Given a deal with status NEW it should save the LOI draft successfully)`() {
        val givenDeal = givenDealInStage(Stage.EVALUATION)
        val givenLoiDraft = loiDraft(givenDeal.id)
        every { letterOfIntentDraftRepository.save(any()) } returns givenLoiDraft
        val givenLoiDraftInput = LetterOfIntentDraftInput(
            dealId = givenDeal.id,
            tenantName = "Mr tenant",
            vertical = "Medical",
            brokerName = "Broker name"
        )

        val result = saveLetterOfIntentDraft.save(givenLoiDraftInput)

        assertThat(result.dealId, equalTo(givenLoiDraftInput.dealId))
        assertThat(result.tenantName, equalTo(givenLoiDraftInput.tenantName))
        assertThat(result.vertical, equalTo(givenLoiDraftInput.vertical))
        assertThat(result.broker.name, equalTo(givenLoiDraftInput.brokerName))
    }

    private fun givenDealInStage(stage: Stage): Deal {
        val givenDeal = DealBuilder().withStage(stage).build()
        every { dealRepository.findById(givenDeal.id) } returns givenDeal

        return givenDeal
    }

    private fun loiDraft(dealId: Long): LetterOfIntentDraft = LetterOfIntentDraft(
        id = 123,
        dealId = dealId,
        tenantName = "Mr tenant",
        vertical = "Medical",
        broker = BrokerDraft(name = "Broker name"),
        offer = OfferDraft(
            lease = LeaseDraft(),
            closing = ClosingDraft(),
            dueDiligence = DueDiligenceDraft()
        )
    )
}
