package realestate.unlock.dealroom.api.unit.core.usecase.file.gpt

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.DealData
import realestate.unlock.dealroom.api.core.usecase.file.gpt.FindingQuestionParser
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal

class FindingQuestionParserTest {

    @Test
    fun `it parses the questions correctly`() {
        val givenOfferPrice = BigDecimal(12312)
        val givenPropertyName = anyString()
        val givenDeal = DealBuilder().apply { offerPrice = givenOfferPrice }.build()
        val givenProperty = PropertyBuilder().apply { name = givenPropertyName }.build()
        val dealData = DealData(deal = givenDeal, property = givenProperty)

        val givenQuestion = "propertyName: \$_property.name_ offerPrice: \$_deal.offerPrice_"
        val questionParsed = FindingQuestionParser.parse(givenQuestion, dealData)

        assertThat(questionParsed, equalTo("propertyName: $givenPropertyName offerPrice: $givenOfferPrice"))
    }
}
