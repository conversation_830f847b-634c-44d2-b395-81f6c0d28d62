package realestate.unlock.dealroom.api.unit.core.usecase.task.update.process

import com.fasterxml.jackson.core.type.TypeReference
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus.*
import realestate.unlock.dealroom.api.core.usecase.task.file.KFileParser
import realestate.unlock.dealroom.api.core.usecase.task.update.process.ProcessDocumentBackAndForthTaskChanges
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother.buyer
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class ProcessDocumentBackAndForthTaskChangesTest {

    @MockK
    private lateinit var processDocumentBackAndForthTaskChanges: ProcessDocumentBackAndForthTaskChanges

    @BeforeEach
    fun setUp() {
        processDocumentBackAndForthTaskChanges = ProcessDocumentBackAndForthTaskChanges(
            kFileParser = KFileParser()
        )
    }

    @Test
    fun `does not process if user is not uploading a new file`() {
        // Given
        val taskId = 99L
        val completeTask = givenCompleteTask(taskId = taskId)
        val updateInput = givenUpdateTaskInput(task = completeTask, file = null)

        // When
        val result = processDocumentBackAndForthTaskChanges.process(completeTask, updateInput)

        // Then
        val expectedNoProcessResult = ProcessedTaskUpdate(updateInput, completeTask.data)
        assertThat(result, equalTo(expectedNoProcessResult))
    }

    @Test
    fun `fails if member is null`() {
        // Given
        val taskId = 106L
        val completeTask = givenCompleteTask(taskId = taskId)
        val updateInput = givenUpdateTaskInput(task = completeTask, statusKey = DONE)

        // When
        val result = processDocumentBackAndForthTaskChanges.process(completeTask, updateInput)

        // Then
        val expectedNoProcessResult = ProcessedTaskUpdate(updateInput, completeTask.data)
        assertThat(result, equalTo(expectedNoProcessResult))
    }

    @Test
    fun `when process task assigned to seller team, assigned team has been updated to buyer team`() {
        // Given
        val taskId = 320L
        val completeTask = givenCompleteTask(taskId = taskId, assignedTeam = MemberDealTeam.SELLER)
        val updateInput = givenUpdateTaskInput(task = completeTask)
        val buyer = buyer()

        // When
        val result = processDocumentBackAndForthTaskChanges.process(completeTask, updateInput)

        // Then
        val expectedProcessResult = ProcessedTaskUpdate(updateInput, completeTask.data).copy(assignedBuyerId = buyer.id, statusKey = IN_REVIEW.key, assignedTeam = MemberDealTeam.BUYER)
        assertThat(result, equalTo(expectedProcessResult))
    }

    @Test
    fun `when process task assigned to buyer team, assigned team has been updated to seller team`() {
        // Given
        val taskId = 320L
        val completeTask = givenCompleteTask(taskId = taskId, assignedTeam = MemberDealTeam.BUYER)
        val updateInput = givenUpdateTaskInput(task = completeTask)
        val buyer = buyer()

        // When
        val result = processDocumentBackAndForthTaskChanges.process(completeTask, updateInput)

        // Then
        val expectedProcessResult = ProcessedTaskUpdate(updateInput, completeTask.data).copy(assignedBuyerId = buyer.id, statusKey = IN_REVIEW.key, assignedTeam = MemberDealTeam.SELLER)
        assertThat(result, equalTo(expectedProcessResult))
    }

    private fun givenCompleteTask(taskId: Long, assignedBuyer: Member = buyer(), assignedTeam: MemberDealTeam = MemberDealTeam.SELLER) = TaskBuilder()
        .withId(taskId)
        .withDealCategoryId(88)
        .withTemplateKey("form")
        .withStatus(TO_DO)
        .withTypeKey("document_back_and_forth")
        .withFormSchema(backAndForthSchema())
        .withAssignedBuyer(assignedBuyer)
        .withAssignedTeam(assignedTeam)
        .build()

    private fun backAndForthSchema(): Map<String, Any> {
        return JsonMapper.decode(
            """ {
                    "type": "object",
                    "required": ["file"],
                    "properties": {
                                    "file": {
                                                "type": "string",
                                                 "title": "Please upload any related document",
                                                 "format": "data-url"
                                               }
                                   }
                    }""",
            object : TypeReference<Map<String, Any>>() {}
        )
    }

    private fun givenUpdateTaskInput(
        task: Task,
        dueDate: LocalDate? = null,
        statusKey: TaskStatus = TO_DO,
        file: KFile? = KFile(uid = "99", name = "file2.pdf"),
        member: Member = buyer(),
        assignedTeam: MemberDealTeam? = null
    ): UpdateTaskInput {
        val attachedForm = file?.let { mapOf("file" to """[{"uid":"${file.uid}","name":"${file.name}"}]""") } ?: mapOf()
        return UpdateTaskInput(
            id = task.id,
            status = statusKey,
            dueDate = dueDate,
            attachedForm = attachedForm,
            member = member,
            assignedBuyerId = task.assignedBuyer.id,
            assignedTeam = assignedTeam,
            rejectionReason = null,
            enabled = null
        )
    }
}
