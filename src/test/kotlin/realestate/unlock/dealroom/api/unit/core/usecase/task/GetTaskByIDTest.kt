package realestate.unlock.dealroom.api.unit.core.usecase.task

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealData
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.field.DealDataFieldParser
import realestate.unlock.dealroom.api.core.usecase.task.GetTaskById
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.toJsonPGObject
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class GetTaskByIDTest : BaseFunctionalWithoutRestTest() {

    private val getTaskById = Context.injector.getInstance(GetTaskById::class.java)
    private val sqlClient = Context.injector.getInstance(SqlClient::class.java)
    private val dealRepository = Context.injector.getInstance(DealRepository::class.java)
    private val getDealData = Context.injector.getInstance(GetDealData::class.java)

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    @Test
    fun `can get task by Id`() {
        val completeDeal = DealCreator.createDealByRest()
        val oneTask = completeDeal.findOneTask()
        val foundedTask = getTaskById.get(taskId = oneTask.id)
        Assertions.assertEquals(foundedTask.id, oneTask.id)
        Assertions.assertEquals(foundedTask.statusKey, oneTask.statusKey)
        Assertions.assertEquals(foundedTask.attachedFormData, oneTask.attachedFormData)
        Assertions.assertEquals(foundedTask.dueDate, oneTask.dueDate)
    }

    @Test
    fun `can get task by id with deal data in dueDate`() {
        val dueDateField = "deal.loiExecutedDate"
        val completeDeal = DealCreator.createDealByRest()
        val oneTask = completeDeal.findOneTask()
        sqlClient.update(
            query = "UPDATE task SET due_date_external_data_definition = '$dueDateField' WHERE id = ${oneTask.id}",
            params = emptyList()
        )
        val deal = dealRepository.update(
            deal = dealRepository.findById(completeDeal.id)
                .copy(loiExecutedDate = LocalDate.now())
        )

        val foundedTask = getTaskById.get(taskId = oneTask.id)
        Assertions.assertEquals(foundedTask.id, oneTask.id)
        Assertions.assertEquals(foundedTask.statusKey, oneTask.statusKey)
        Assertions.assertEquals(foundedTask.attachedFormData, oneTask.attachedFormData)
        Assertions.assertEquals(foundedTask.dueDate, deal.loiExecutedDate)
        Assertions.assertEquals(foundedTask.externalDataDefinition?.dueDate, dueDateField)
    }

    @Test
    fun `can get task by id with deal data in attached form`() {
        val fields = setOf(
            "deal.loiExecutedDate",
            "deal.type",
            "deal.offerPrice",
            "deal.schemaData.data.marketReno",
            "deal.propertyId",
            "property.id"
        )
        val completeDeal = DealCreator.createDealByRest()
        val oneTask = completeDeal.findOneTask()
        dealRepository.update(
            deal = dealRepository.findById(completeDeal.id)
                .copy(loiExecutedDate = LocalDate.now())
        )
        sqlClient.update(
            query = """UPDATE task SET form_external_data_definition = ? WHERE id = ${oneTask.id}""",
            params = listOf(fields.toJsonPGObject())
        )
        val foundedTask = getTaskById.get(taskId = oneTask.id)
        Assertions.assertEquals(foundedTask.id, oneTask.id)
        Assertions.assertEquals(foundedTask.statusKey, oneTask.statusKey)
        Assertions.assertNotEquals(foundedTask.attachedFormData, oneTask.attachedFormData)
        val dealData = getDealData.get(completeDeal.id)
        fields.forEach {
            Assertions.assertEquals(
                foundedTask.attachedFormData?.get(it),
                DealDataFieldParser.getValue(it, dealData)
            )
        }
    }
}
