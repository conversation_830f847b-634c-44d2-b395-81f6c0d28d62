package realestate.unlock.dealroom.api.unit.core.usecase.file

import io.mockk.called
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.CreateLinkedFileInput
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.repository.file.FileRepository
import realestate.unlock.dealroom.api.core.usecase.deal.files.linked.CreateLinkedFile
import realestate.unlock.dealroom.api.core.usecase.file.ConfirmStagedFile
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class ConfirmStagedFileTest {

    companion object {
        val now: Instant = Instant.now()
    }

    @MockK(relaxed = true)
    private lateinit var fileRepository: FileRepository

    @MockK(relaxed = true)
    private lateinit var createLinkedFile: CreateLinkedFile

    @MockK(relaxed = true)
    private lateinit var fileGateway: FileGateway
    private lateinit var confirmStagedFile: ConfirmStagedFile

    @BeforeEach
    fun setUp() {
        every { fileRepository.nextId() } answers { anyId() }
        confirmStagedFile = ConfirmStagedFile(
            fileGateway = fileGateway,
            clock = Clock.fixed(now, ZoneId.systemDefault()),
            fileRepository = fileRepository,
            createLinkedFile = createLinkedFile
        )
    }

    @Test
    fun `can confirm staged files`() {
        val filesToConfirm = givenFilesToConfirm()

        val filesConfirmed = confirmStagedFile(filesToConfirm)

        assertEquals(filesConfirmed.size, filesConfirmed.size)
        filesToConfirm.forEach { fileToConfirm ->
            val fileConfirmed = filesConfirmed.first { it.kFileId == fileToConfirm.kFileId }
            assertEquals(now.epochSecond, fileConfirmed.createdAt.toEpochSecond())
            assertEquals(fileToConfirm.member?.id, fileConfirmed.memberId)
            assertEquals(fileToConfirm.name, fileConfirmed.name)
            verify { fileRepository.save(fileConfirmed) }
        }
    }

    @Test
    fun `can confirm staged files and link file`() {
        val filesToConfirm = givenFilesToConfirm()
        val linkedTo = ConfirmStagedFile.LinkedTo(
            dealId = anyId(),
            entityId = anyString(),
            entityType = LinkedFileEntityType.REPORT
        )

        every { createLinkedFile.execute(dealId = linkedTo.dealId, input = any()) } returns mockk()

        val filesConfirmed = confirmStagedFile(filesToConfirm, linkedTo = linkedTo)

        assertEquals(filesConfirmed.size, filesConfirmed.size)
        filesToConfirm.forEach { fileToConfirm ->
            val fileConfirmed = filesConfirmed.first { it.kFileId == fileToConfirm.kFileId }
            assertEquals(now.epochSecond, fileConfirmed.createdAt.toEpochSecond())
            assertEquals(fileToConfirm.member?.id, fileConfirmed.memberId)
            assertEquals(fileToConfirm.name, fileConfirmed.name)
            verify { fileRepository.save(fileConfirmed) }
            verify {
                createLinkedFile.execute(
                    dealId = linkedTo.dealId,
                    input = CreateLinkedFileInput(
                        fileId = fileToConfirm.kFileId,
                        entityType = LinkedFileEntityType.REPORT,
                        entityId = linkedTo.entityId,
                        filePath = null
                    ),
                    triggerEntityAction = false
                )
            }
        }
    }

    @Test
    fun `confirming an empty list should not attempt to confirm anything`() {
        confirmStagedFile(listOf())

        verify { fileGateway wasNot called }
        verify { fileRepository wasNot called }
    }

    private fun givenFilesToConfirm() =
        MemberObjectMother.buyer().let {
            listOf(
                givenFileToConfirm(it),
                givenFileToConfirm(it)
            )
        }

    private fun givenFileToConfirm(member: Member) = ConfirmStagedFile.FileToConfirm(
        name = anyString(),
        path = anyString(),
        kFileId = anyString(),
        member = member
    )
}
