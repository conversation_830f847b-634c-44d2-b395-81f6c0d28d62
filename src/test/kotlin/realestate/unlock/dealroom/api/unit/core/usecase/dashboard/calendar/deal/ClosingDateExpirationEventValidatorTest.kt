package realestate.unlock.dealroom.api.unit.core.usecase.dashboard.calendar.deal

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventType
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput
import realestate.unlock.dealroom.api.core.entity.calendar.model.DealCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.usecase.calendar.eventfinder.ClosingDateExpirationCalendarEventFinder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

class ClosingDateExpirationEventValidatorTest {

    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)
    private var closingDateExpirationEventValidator = ClosingDateExpirationCalendarEventFinder(clock)

    @Test
    fun `it should create an event report if closing date is within given days`() {
        val givenDays = 7
        val givenDealEvent = DealCalendarEventModel(
            dealId = anyId(), stage = Stage.EVALUATION, diligenceExpirationDate = null, initialClosingDate = LocalDate.now(clock).plusDays(4),
            propertyName = "any", addressStreet = "any", addressApartment = "any", addressCity = "any", addressState = "any", addressZip = "any"
        )
        val result = closingDateExpirationEventValidator.find(givenDealEvent, CalendarEventsInput(untilDays = givenDays, organizationId = "org-id"))

        assertThat(result, notNullValue())
        assertThat(result!!.dealId, equalTo(givenDealEvent.dealId))
        assertThat(result.type, equalTo(CalendarEventType.CLOSING_DATE))
    }

    @Test
    fun `it shouldn't create an event report if closing date is more than the given days`() {
        val givenDays = 7
        val givenDealEvent = DealCalendarEventModel(
            dealId = anyId(), stage = Stage.EVALUATION, diligenceExpirationDate = null, initialClosingDate = LocalDate.now(clock).plusDays(8),
            propertyName = "any", addressStreet = "any", addressApartment = "any", addressCity = "any", addressState = "any", addressZip = "any"
        )
        val result = closingDateExpirationEventValidator.find(givenDealEvent, CalendarEventsInput(untilDays = givenDays, organizationId = "org-id"))

        assertThat(result, nullValue())
    }

    @Test
    fun `it should create an event report if closing date was yesterday`() {
        val givenDealEvent = DealCalendarEventModel(
            dealId = anyId(), stage = Stage.EVALUATION, diligenceExpirationDate = null, initialClosingDate = LocalDate.now(clock).minusDays(1),
            propertyName = "any", addressStreet = "any", addressApartment = "any", addressCity = "any", addressState = "any", addressZip = "any"
        )
        val result = closingDateExpirationEventValidator.find(givenDealEvent, CalendarEventsInput(untilDays = 7, organizationId = "org-id"))

        assertThat(result, notNullValue())
    }
}
