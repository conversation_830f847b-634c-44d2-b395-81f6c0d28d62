package realestate.unlock.dealroom.api.unit.core.usecase.task.update

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.category.Category
import realestate.unlock.dealroom.api.core.entity.deal.category.DealCategory
import realestate.unlock.dealroom.api.core.entity.email.TaskChangedEmailToSend
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.repository.category.FindCategoryByKeyRepository
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.email.task.changed.SendTaskChangedEmail
import realestate.unlock.dealroom.api.core.usecase.task.update.NotifyMembersAboutTaskChanged
import realestate.unlock.dealroom.api.infrastructure.configuration.model.WebAppsConfig
import realestate.unlock.dealroom.api.utils.entity.CategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.deal.DealCategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother.buyer
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother.buyerCounsel
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother.seller
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString

@ExtendWith(MockKExtension::class)
class NotifyMembersAboutTaskChangedTest {

    @RelaxedMockK
    private lateinit var sendTaskChangedEmail: SendTaskChangedEmail

    @MockK
    private lateinit var taskRepository: TaskRepository

    @MockK
    private lateinit var memberRepository: MemberRepository

    @MockK
    private lateinit var dealCategoryRepository: DealCategoryRepository

    @MockK
    private lateinit var dealRepository: DealRepository

    @MockK
    private lateinit var findCategoryByKeyRepository: FindCategoryByKeyRepository

    @MockK
    private lateinit var propertyRepository: PropertyRepository

    private lateinit var webAppsConfig: WebAppsConfig

    private lateinit var notifyMembersAboutTaskChanged: NotifyMembersAboutTaskChanged

    @BeforeEach
    fun setUp() {
        webAppsConfig = WebAppsConfig(
            dealRoom = anyString(),
            dealRoomAdmin = "https://deal-room-admin-dev.vercel.app",
            kos = "kos"
        )
        notifyMembersAboutTaskChanged = NotifyMembersAboutTaskChanged(
            sendTaskChangedEmail = sendTaskChangedEmail,
            taskRepository = taskRepository,
            memberRepository = memberRepository,
            dealCategoryRepository = dealCategoryRepository,
            dealRepository = dealRepository,
            findCategoryByKeyRepository = findCategoryByKeyRepository,
            propertyRepository = propertyRepository,
            webAppsConfig = webAppsConfig
        )
    }

    @Test
    fun `can notify to members about task changes`() {
        // Given
        val taskId = anyId()
        val property = givenProperty()
        val (category, dealCategory) = givenDealCategory()
        val (task, members) = givenATask(taskId = taskId, category = category, dealCategory = dealCategory, property = property)

        // when
        notifyMembersAboutTaskChanged(taskId)

        // then
        assertBuyerMembersWereNotified(task, members, dealCategory, category, property)
    }

    private fun givenDealCategory(): Pair<Category, DealCategory> {
        val category = CategoryBuilder().build()
        val dealCategory = DealCategoryBuilder().apply {
            id = anyId()
            dealId = anyId()
            categoryKey = anyString()
        }.build()
        return Pair(category, dealCategory)
    }

    private fun givenProperty() = PropertyBuilder().withId(anyId()).build()

    private fun assertBuyerMembersWereNotified(task: Task, members: Set<Member>, dealCategory: DealCategory, category: Category, property: Property) {
        val expectedEmails = mailsFor(task, members, dealCategory, category, property)
        expectedEmails.forEach { email ->
            verify { sendTaskChangedEmail.send(email) }
        }
    }

    private fun mailsFor(task: Task, members: Set<Member>, dealCategory: DealCategory, category: Category, property: Property): List<TaskChangedEmailToSend> {
        val buyerMembers = members.filter { member ->
            member.typeKey.startsWith(prefix = "buyer", ignoreCase = true)
        }
        return buyerMembers.map { member ->
            TaskChangedEmailToSend(
                emailTo = member.email,
                fullName = member.fullName,
                taskStatus = task.statusKey.key,
                taskName = task.title,
                categoryName = category.name,
                propertyAddress = property.address.fullAddress(),
                memberAssigned = task.assignedBuyer.fullName,
                link = "${webAppsConfig.dealRoomAdmin}/deals/${dealCategory.dealId}/categories/${dealCategory.id}"
            )
        }
    }

    private fun givenATask(taskId: Long, dealCategory: DealCategory, category: Category, property: Property): Pair<Task, Set<Member>> {
        val assignedMember = seller()
        val deal = DealBuilder().withId(dealCategory.dealId).withPropertyId(456L).build()
        val members = setOf(assignedMember, buyer(), buyerCounsel())
        val task = TaskBuilder()
            .withId(taskId)
            .withDealCategoryId(dealCategory.id)
            .withAssignedTeam(assignedMember.getDealTeam())
            .withTypeKey("building_documents")
            .build()
            .also { task ->
                every { taskRepository.findById(task.id) } returns task
                every { memberRepository.findByDealCategoryId(task.dealCategoryId) } returns members
                every { dealCategoryRepository.findById(task.dealCategoryId) } returns dealCategory
                every { dealRepository.findById(deal.id) } returns deal
                every { findCategoryByKeyRepository.find(dealCategory.categoryKey) } returns category
                every { propertyRepository.findById(deal.propertyId) } returns property
            }
        return Pair(task, members)
    }
}
