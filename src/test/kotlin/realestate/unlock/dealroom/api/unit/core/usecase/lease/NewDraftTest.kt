package realestate.unlock.dealroom.api.unit.core.usecase.lease

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.document.*
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarDocumentEvent
import realestate.unlock.dealroom.api.core.usecase.deal.documents.*
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDraftFilesException
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class NewDraftTest {

    companion object {
        private val now = Instant.now()
    }

    @MockK
    private lateinit var createDocument: CreateDocument

    @MockK
    private lateinit var confirmDraftDocuments: ConfirmDraftDocuments

    @MockK(relaxed = true)
    private lateinit var documentRepository: DocumentRepository

    @MockK(relaxed = true)
    private lateinit var createDocumentInteraction: CreateDocumentInteraction

    @MockK
    private lateinit var externalCalendarDocumentEvent: ExternalCalendarDocumentEvent

    private lateinit var clock: Clock
    private lateinit var newDraft: NewDraft

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        newDraft = NewDraft(
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            confirmDraftDocuments = confirmDraftDocuments,
            createDocument = createDocument,
            clock = clock,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `cannot upload new lease draft when it is executed`() {
        val deal = DealBuilder().build()
        val leaseId = anyId()
        val roundId = anyId()
        givenLease(id = leaseId, deal = deal, currentRoundId = roundId, status = DocumentStatus.EXECUTED)

        val input = DocumentDraftInput(
            dealId = deal.id,
            redLineVersionFile = givenFileInput(),
            cleanVersionFile = null,
            comment = "legal review",
            member = MemberObjectMother.buyer(),
            documentType = DocumentType.LEASE
        )

        assertThrows<InvalidDocumentStatusException> { newDraft(input) }
    }

    @Test
    fun `cannot upload new lease draft without file`() {
        val deal = DealBuilder().build()
        val leaseId = anyId()
        val roundId = anyId()
        givenLease(id = leaseId, deal = deal, currentRoundId = roundId, status = DocumentStatus.NOT_STARTED)

        val input = DocumentDraftInput(
            dealId = deal.id,
            redLineVersionFile = null,
            cleanVersionFile = null,
            comment = "legal review",
            member = MemberObjectMother.buyer(),
            documentType = DocumentType.LEASE
        )

        assertThrows<InvalidDraftFilesException> { newDraft(input) }
    }

    @Test
    fun `can upload first lease draft`() {
        val deal = DealBuilder().build()
        val lease = givenLease(id = anyId(), deal = deal, currentRoundId = null, status = DocumentStatus.NOT_STARTED)
        val cleanVersionFile = givenFileInput()
        val readLineVersionFile = givenFileInput()
        val input = DocumentDraftInput(
            dealId = deal.id,
            cleanVersionFile = cleanVersionFile,
            redLineVersionFile = readLineVersionFile,
            comment = "initial version",
            member = MemberObjectMother.buyer(),
            documentType = DocumentType.LEASE
        )

        val expectedDraft = givenConfirmedDocuments(input, lease)

        val leaseUpdated = newDraft(input)

        assertLeaseWasUpdated(leaseUpdated, expectedDraft)
        assertInteractionWasCreated(expectedDraft, input)
    }

    private fun givenConfirmedDocuments(input: DocumentDraftInput, document: Document) = DocumentRoundObjectMother.draftDocuments(documentId = document.id).also {
        every { confirmDraftDocuments(input, document) } returns it
    }

    private fun givenLease(id: Long, deal: Deal, currentRoundId: Long?, status: DocumentStatus) = DocumentBuilder().apply {
        this.id = id
        this.dealId = deal.id
        this.status = status
        this.currentRoundId = currentRoundId
    }.build().also {
        every { documentRepository.findByDealIdAndDocumentType(deal.id, DocumentType.LEASE) } returns it
        every { externalCalendarDocumentEvent.impactOnCalendar(any()) } returns Unit
    }

    private fun assertLeaseWasUpdated(documentUpdated: Document, expectedDraft: DraftDocuments) {
        Assertions.assertEquals(DocumentStatus.BUYER_REVIEW, documentUpdated.status)
        Assertions.assertEquals(expectedDraft.id, documentUpdated.currentRoundId)
        Assertions.assertEquals(OffsetDateTime.now(clock), documentUpdated.updatedAt)
        Assertions.assertEquals(OffsetDateTime.now(clock), documentUpdated.draftSubmittedAt)
    }

    private fun givenFileInput() = FileInput(anyString(), anyString())

    private fun assertInteractionWasCreated(documentRound: DocumentRound, input: DocumentDraftInput) {
        verify {
            createDocumentInteraction(
                member = input.member,
                comment = input.comment,
                documentRound = documentRound,
                interactionType = Interaction.Type.NEW_DRAFT
            )
        }
    }
}
