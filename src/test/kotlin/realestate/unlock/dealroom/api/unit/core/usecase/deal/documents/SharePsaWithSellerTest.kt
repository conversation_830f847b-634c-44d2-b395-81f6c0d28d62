package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.SharePsaWithSeller
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.ShareDocumentImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class SharePsaWithSellerTest : InteractWithDocumentRoundTest() {

    companion object {
        private val now = Instant.now()
    }

    @MockK
    val shareDocumentImpactOnTask: ShareDocumentImpactOnTask = mockk()

    @MockK
    val dealRepository: DealRepository = mockk()

    private lateinit var sharePsaWithSeller: SharePsaWithSeller

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        every { shareDocumentImpactOnTask(any(), any()) } returns Unit
        sharePsaWithSeller = SharePsaWithSeller(
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            clock = clock,
            shareDocumentImpactOnTask = shareDocumentImpactOnTask,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `can share a psa`() {
        val deal = givenDeal(Stage.NEGOTIATION)
        val user = CompleteUserObjectMother.buyer()
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId)
        val psa = givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "shared",
            member = user.member,
            documentType = DocumentType.PSA
        )

        val psaUpdated = sharePsaWithSeller.update(input)

        assertDocumentWasUpdated(psaUpdated, DocumentStatus.SELLER_REVIEW)
        assertInteractionWasCreated(draftDocuments, input, Interaction.Type.SHARE)
        verify { shareDocumentImpactOnTask(document = psa, member = user.member) }
    }

    @Test
    fun `cannot shared psa that is not in BUYER_REVIEW or LEGAL_REVIEW`() {
        val deal = givenDeal(Stage.NEGOTIATION)
        val user = CompleteUserObjectMother.buyer()
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId)
        givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.EXECUTED, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "shared",
            member = user.member,
            documentType = DocumentType.PSA
        )

        assertThrows<InvalidDocumentStatusException> { sharePsaWithSeller.update(input) }
    }

    @Test
    fun `cannot shared psa without clean version`() {
        val deal = givenDeal(Stage.NEGOTIATION)
        val user = CompleteUserObjectMother.buyer()
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId, cleanVersionFileId = null)
        givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "shared",
            member = user.member,
            documentType = DocumentType.PSA
        )

        assertThrows<InvalidDocumentStatusException> { sharePsaWithSeller.update(input) }
    }

    @Test
    fun `can share a psa and change deal status`() {
        val deal = givenDeal(Stage.NEGOTIATION)
        val user = CompleteUserObjectMother.buyer()
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId)
        val psa = givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "shared",
            member = user.member,
            documentType = DocumentType.PSA
        )

        val psaUpdated = sharePsaWithSeller.update(input)

        assertDocumentWasUpdated(psaUpdated, DocumentStatus.SELLER_REVIEW)
        assertInteractionWasCreated(draftDocuments, input, Interaction.Type.SHARE)
    }

    private fun givenDeal(stage: Stage): Deal =
        DealBuilder().withStage(stage).build().also {
            every { dealRepository.findById(it.id) } returns it
            every { dealRepository.update(deal = it.copy(stage = Stage.NEGOTIATION)) } returns it.copy(stage = Stage.NEGOTIATION)
        }
}
