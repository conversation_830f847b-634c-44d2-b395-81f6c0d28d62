package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.property.Coordinates
import realestate.unlock.dealroom.api.core.gateway.property.PropertyDetails
import realestate.unlock.dealroom.api.core.gateway.property.PropertyDetailsGateway
import realestate.unlock.dealroom.api.core.usecase.deal.template.GetDealCreationTemplate
import realestate.unlock.dealroom.api.core.usecase.deal.template.TemplateSource
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal

@ExtendWith(MockKExtension::class)
class GetDealCreationTemplateTest {

    @MockK
    lateinit var propertyDetailsGateway: PropertyDetailsGateway
    lateinit var getDealCreationTemplate: GetDealCreationTemplate

    @BeforeEach
    fun setUp() {
        getDealCreationTemplate = GetDealCreationTemplate(propertyDetailsGateway)
    }

    @Test
    fun `can get deal creation template from property sage`() {
        val propertyKeywayId = anyString()
        val expectedPropertyDetails = givenPropertySagePropertyDetails(propertyKeywayId)

        val result = getDealCreationTemplate.get(propertyKeywayId, TemplateSource.PROPERTY_SAGE)

        assertEquals(expectedPropertyDetails, result)
        verify(exactly = 1) { propertyDetailsGateway.findByPropertyKeywayId(propertyKeywayId) }
    }

    private fun givenPropertySagePropertyDetails(propertyKeywayId: String) =
        givenPropertyDetails(propertyKeywayId)
            .apply {
                every { propertyDetailsGateway.findByPropertyKeywayId(propertyKeywayId) } returns this
            }

    private fun givenPropertyDetails(propertyKeywayId: String?) = PropertyDetails(
        keywayId = propertyKeywayId,
        name = anyString(),
        address = anyString(),
        city = anyString(),
        state = anyString(),
        squareFootage = BigDecimal.ONE,
        constructionYear = 1988,
        coordinates = Coordinates(BigDecimal.TEN, BigDecimal.TEN),
        zipCode = anyString(),
        tenant = anyString(),
        vertical = "Other",
        units = 12,
        askingPrice = BigDecimal(41231)
    )
}
