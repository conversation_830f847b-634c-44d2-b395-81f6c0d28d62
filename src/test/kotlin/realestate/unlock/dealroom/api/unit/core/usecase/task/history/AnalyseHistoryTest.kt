package realestate.unlock.dealroom.api.unit.core.usecase.task.history

import com.fasterxml.jackson.core.type.TypeReference
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.entity.task.file.TaskFile
import realestate.unlock.dealroom.api.core.entity.task.history.CreateTaskHistoryInput
import realestate.unlock.dealroom.api.core.entity.task.history.HistoryAnalysisResult
import realestate.unlock.dealroom.api.core.entity.task.history.TaskHistory
import realestate.unlock.dealroom.api.core.entity.task.history.TaskHistoryTaskUpdates
import realestate.unlock.dealroom.api.core.entity.task.history.snapshot.TaskHistorySnapshot
import realestate.unlock.dealroom.api.core.usecase.task.history.AnalyseHistory.analyse
import realestate.unlock.dealroom.api.core.usecase.task.history.AnalyseHistory.getAddedFiles
import realestate.unlock.dealroom.api.core.usecase.task.history.AnalyseHistory.getRemovedFiles
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entity.member.MemberBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.LocalDate
import java.time.OffsetDateTime

class AnalyseHistoryTest {

    @Test
    fun `can get added files from task history`() {
        val addedFile = getTaskFile()
        val from = TaskBuilder().build()
        val to = from.toTaskWithFilesAndHistory(files = listOf(addedFile))

        val addedFiles = getTaskHistory(
            CreateTaskHistoryInput(
                currentStatus = from.toTaskWithFilesAndHistory(),
                updatedStatus = to,
                updates = TaskHistoryTaskUpdates(
                    id = from.id,
                    statusKey = from.statusKey.key,
                    attachedForm = from.attachedFormData,
                    dueDate = from.dueDate,
                    assignedBuyerId = anyId(),
                    assignedTeam = MemberDealTeam.BUYER,
                    files = listOf(addedFile)
                ),
                member = CompleteUserObjectMother.admin().member
            )
        ).getAddedFiles()

        Assertions.assertEquals(1, addedFiles.size)
        Assertions.assertEquals(addedFile.kFileId, addedFiles.first().uid)
    }

    @Test
    fun `can get removed files from task history`() {
        val addedFileFrom = getTaskFile()
        val addedFileTo = getTaskFile()
        val baseTask = TaskBuilder().withFormSchema(givenFormSchema()).build()
        val from = baseTask.copy(
            attachedFormData = buildFileAttached(addedFileFrom)
        ).toTaskWithFilesAndHistory(files = listOf(addedFileFrom))
        val to = baseTask.copy(
            attachedFormData = buildFileAttached(addedFileTo)
        ).toTaskWithFilesAndHistory(files = listOf(addedFileTo))

        val history = getTaskHistory(
            CreateTaskHistoryInput(
                currentStatus = from,
                updatedStatus = to,
                updates = TaskHistoryTaskUpdates(
                    id = from.task.id,
                    statusKey = from.task.statusKey.key,
                    attachedForm = from.task.attachedFormData,
                    dueDate = from.task.dueDate,
                    assignedBuyerId = anyId(),
                    assignedTeam = MemberDealTeam.BUYER,
                    files = listOf(addedFileTo)
                ),
                member = CompleteUserObjectMother.admin().member
            )
        )

        Assertions.assertEquals(1, history.getAddedFiles().size)
        Assertions.assertEquals(addedFileTo.kFileId, history.getAddedFiles().first().uid)

        Assertions.assertEquals(1, history.getRemovedFiles().size)
        Assertions.assertEquals(addedFileFrom.kFileId, history.getRemovedFiles().first().uid)
    }

    @Test
    fun `can get analysis from task history`() {
        val addedFile = getTaskFile()
        val baseTask = TaskBuilder().build()
        val from = baseTask.toTaskWithFilesAndHistory()
        val to = baseTask.copy(
            dueDate = LocalDate.now().plusDays(10),
            statusKey = TaskStatus.DONE,
            assignedBuyer = MemberBuilder().withId(anyId()).build()
        ).toTaskWithFilesAndHistory()

        val analyseResult = getTaskHistory(
            CreateTaskHistoryInput(
                currentStatus = from,
                updatedStatus = to,
                updates = TaskHistoryTaskUpdates(
                    id = from.task.id,
                    statusKey = to.task.statusKey.key,
                    attachedForm = from.task.attachedFormData,
                    dueDate = to.task.dueDate,
                    assignedBuyerId = anyId(),
                    assignedTeam = MemberDealTeam.BUYER,
                    files = listOf(addedFile)
                ),
                member = CompleteUserObjectMother.admin().member
            )
        ).analyse()

        Assertions.assertTrue(analyseResult.contains(HistoryAnalysisResult.CHANGE_STATUS))
        Assertions.assertTrue(analyseResult.contains(HistoryAnalysisResult.UPDATE_DUE_DATE))
        Assertions.assertTrue(analyseResult.contains(HistoryAnalysisResult.CHANGE_ASSIGNEE))
    }

    private fun getSchemaWithFile() = """
        {
          "type": "object",
          "required": [
            "file"
          ],
          "properties": {
            "file": {
              "type": "string",
              "title": "Please upload any related document",
              "format": "data-url"
            }
          }
        }
    """.trimIndent()

    private fun givenFormSchema() = JsonMapper.decode(getSchemaWithFile(), (object : TypeReference<Map<String, Any>>() {}))

    private fun buildFileAttached(file: TaskFile) =
        mapOf("file" to """[{"uid":"${file.kFileId}","name":"${file.name}"}]""")

    private fun getTaskFile() =
        TaskFile(
            taskId = anyId(),
            id = anyId(),
            kFileId = anyString(),
            name = anyString(),
            path = anyString(),
            createdAt = OffsetDateTime.now(),
            memberId = anyId()
        )

    private fun getTaskHistory(input: CreateTaskHistoryInput) =
        TaskHistory(
            id = anyId(),
            taskId = anyId(),
            memberId = anyId(),
            createdAt = OffsetDateTime.now(),
            snapshot = TaskHistorySnapshot(input)
        )
}
