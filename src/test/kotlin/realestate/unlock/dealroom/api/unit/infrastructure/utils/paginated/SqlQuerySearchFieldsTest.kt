package realestate.unlock.dealroom.api.unit.infrastructure.utils.paginated

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.search.*
import realestate.unlock.dealroom.api.repository.database.getSqlQuery
import java.time.LocalDate

class SqlQuerySearchFieldsTest {

    @Test
    fun `Datetime Search must have all correct operators`() {
        val field = "field"
        val expectedValues = mapOf(
            DateTimeOperator.EQ to "$field = ? ",
            DateTimeOperator.BETWEEN to "$field BETWEEN ? AND ? ",
            DateTimeOperator.GT to "$field > ? ",
            DateTimeOperator.GTE to "$field >= ? ",
            DateTimeOperator.LT to "$field < ? ",
            DateTimeOperator.LTE to "$field <= ? "
        )

        DateTimeOperator.values().map {
            SearchDateTime<LocalDate>(it, values = listOf(LocalDate.MAX))
        }.forEach {
            Assertions.assertEquals(expectedValues[it.operator], it.getSqlQuery(field))
        }
    }

    @Test
    fun `String Search must have all correct operators`() {
        val field = "field"
        val expectedValues = mapOf(
            StringOperator.EQ to "$field = ? ",
            StringOperator.LIKE to "$field ILIKE ? ",
            StringOperator.IN to "$field IN (?) "
        )

        StringOperator.values().map {
            SearchString(it, values = listOf("Search"))
        }.forEach {
            Assertions.assertEquals(expectedValues[it.operator], it.getSqlQuery(field))
        }
    }

    @Test
    fun `Number Search must have all correct operators`() {
        val field = "field"
        val expectedValues = mapOf(
            NumberOperator.EQ to "$field = ? ",
            NumberOperator.BETWEEN to "$field BETWEEN ? AND ? ",
            NumberOperator.GT to "$field > ? ",
            NumberOperator.GTE to "$field >= ? ",
            NumberOperator.LT to "$field < ? ",
            NumberOperator.LTE to "$field <= ? ",
            NumberOperator.IN to "$field IN (?) "
        )

        NumberOperator.values().map {
            SearchNumber<Long>(it, values = listOf(Long.MIN_VALUE))
        }.forEach {
            Assertions.assertEquals(expectedValues[it.operator], it.getSqlQuery(field))
        }
    }

    @Test
    fun `JsonArray Search must have all correct operators`() {
        val field = "field"
        val expectedValues = mapOf(
            JsonArrayOperator.IN to "jsonb_exists_any($field, array[?,?]) "
        )

        JsonArrayOperator.values().map {
            SearchJsonArray(it, values = listOf("Search1", "Search2"))
        }.forEach {
            Assertions.assertEquals(expectedValues[it.operator], it.getSqlQuery(field))
        }
    }
}
