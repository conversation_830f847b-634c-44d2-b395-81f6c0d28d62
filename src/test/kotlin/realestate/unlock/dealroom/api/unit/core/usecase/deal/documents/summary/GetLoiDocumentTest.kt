package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.summary

import io.mockk.every
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.document.summary.LoiDocumentStatus
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.loi.MedicalLoiRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.summary.GetLoiDocument
import realestate.unlock.dealroom.api.core.usecase.loi.GetLetterOfIntentRound
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder

class GetLoiDocumentTest {

    private val getLetterOfIntentRound: GetLetterOfIntentRound = mockk()

    private val getLoiDocument = GetLoiDocument(getLetterOfIntentRound = getLetterOfIntentRound)

    @Test
    fun `Given a deal with status NEW it should return a LoiDocument with status TO_DO`() {
        val givenDeal = DealBuilder().withStage(Stage.EVALUATION).build()
        every { getLetterOfIntentRound.getByDealId(givenDeal.id) } returns null
        val loiDocument = getLoiDocument.get(givenDeal)
        assertThat(loiDocument.status, equalTo(LoiDocumentStatus.TO_DO))
    }

    @Test
    fun `Given a deal with status ARCHIVED it should return a LoiDocument with status TO_DO`() {
        val givenDeal = DealBuilder().archived().build()
        every { getLetterOfIntentRound.getByDealId(givenDeal.id) } returns null
        val loiDocument = getLoiDocument.get(givenDeal)
        assertThat(loiDocument.status, equalTo(LoiDocumentStatus.TO_DO))
    }

    fun `Given a deal with loi in negotiation it should return the expected status`() {
        val (givenDeal, givenLoiRound) = givenDealWithLoiRoundStatus(
            stage = Stage.OFFER,
            loiRoundStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION
        )

        val loiDocument = getLoiDocument.get(givenDeal)

        assertThat(loiDocument.status, equalTo(LoiDocumentStatus.IN_NEGOTIATION))
        assertThat(loiDocument.createdAt, equalTo(givenLoiRound.date))
    }

    @Test
    fun `Given a deal with loi aborted it should return the expected status`() {
        val (givenDeal, givenLoiRound) = givenDealWithLoiRoundStatus(
            stage = Stage.OFFER,
            loiRoundStatus = LetterOfIntentRoundStatus.ABORTED
        )

        val loiDocument = getLoiDocument.get(givenDeal)

        assertThat(loiDocument.status, equalTo(LoiDocumentStatus.ABORTED))
        assertThat(loiDocument.createdAt, equalTo(givenLoiRound.date))
    }

    @Test
    fun `Given a deal with loi accepted it should return the expected status`() {
        val (givenDeal, givenLoiRound) = givenDealWithLoiRoundStatus(
            stage = Stage.OFFER,
            loiRoundStatus = LetterOfIntentRoundStatus.ACCEPTED
        )

        val loiDocument = getLoiDocument.get(givenDeal)

        assertThat(loiDocument.status, equalTo(LoiDocumentStatus.ACCEPTED))
        assertThat(loiDocument.createdAt, equalTo(givenLoiRound.date))
    }

    @Test
    fun `Given a deal with loi executed it should return the expected status`() {
        val (givenDeal, givenLoiRound) = givenDealWithLoiRoundStatus(
            stage = Stage.DILIGENCE,
            loiRoundStatus = LetterOfIntentRoundStatus.EXECUTED
        )

        val loiDocument = getLoiDocument.get(givenDeal)

        assertThat(loiDocument.status, equalTo(LoiDocumentStatus.EXECUTED))
        assertThat(loiDocument.createdAt, equalTo(givenLoiRound.date))
    }

    @Test
    fun `Given a deal with loi rejected it should return the expected status`() {
        val (givenDeal, givenLoiRound) = givenDealWithLoiRoundStatus(
            stage = Stage.OFFER,
            loiRoundStatus = LetterOfIntentRoundStatus.REJECTED
        )

        val loiDocument = getLoiDocument.get(givenDeal)

        assertThat(loiDocument.status, equalTo(LoiDocumentStatus.REJECTED))
        assertThat(loiDocument.createdAt, equalTo(givenLoiRound.date))
    }

    private fun givenDealWithLoiRoundStatus(stage: Stage, loiRoundStatus: LetterOfIntentRoundStatus): Pair<Deal, MedicalLoiRound> {
        val givenDeal = DealBuilder().withStage(stage).build()
        val givenLoiRound = LoiMedicalRoundBuilder().withStatus(loiRoundStatus).build()
        every { getLetterOfIntentRound.getByDealId(givenDeal.id) } returns givenLoiRound

        return Pair(givenDeal, givenLoiRound)
    }
}
