package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.task

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.task.TaskStatusTransitions
import realestate.unlock.dealroom.api.core.entity.task.TaskTransitionInput
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.ReadyToSignImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.task.GetDocumentTask
import realestate.unlock.dealroom.api.core.usecase.task.TaskTransition
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder

@ExtendWith(MockKExtension::class)
class ReadyToSignImpactOnTaskTest {

    @MockK
    private lateinit var getDocumentTask: GetDocumentTask

    @MockK(relaxed = true)
    private lateinit var taskTransition: TaskTransition

    private lateinit var readyToSignImpactOnTask: ReadyToSignImpactOnTask

    @BeforeEach
    fun setUp() {
        readyToSignImpactOnTask = ReadyToSignImpactOnTask(
            getDocumentTask = getDocumentTask,
            taskTransition = taskTransition
        )
    }

    @Test
    fun `impact on task`() {
        val member = MemberObjectMother.buyer()
        val psa = DocumentBuilder().build()
        val psaTask = TaskBuilder().withTemplateKey("purchase_and_sale_contract").build()
        every { getDocumentTask(psa) } returns psaTask

        readyToSignImpactOnTask(psa, member)

        verify {
            taskTransition(
                TaskTransitionInput(
                    taskId = psaTask.id,
                    member = member,
                    transition = TaskStatusTransitions.UPLOAD_FOR_SIGNATURE
                )
            )
        }
    }
}
