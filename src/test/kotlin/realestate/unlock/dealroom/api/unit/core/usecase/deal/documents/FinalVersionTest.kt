package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.document.*
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarDocumentEvent
import realestate.unlock.dealroom.api.core.usecase.deal.documents.*
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class FinalVersionTest {

    companion object {
        private val now = Instant.now()
    }

    @MockK
    private lateinit var confirmFinalDocument: ConfirmFinalDocument

    @MockK(relaxed = true)
    private lateinit var documentRepository: DocumentRepository

    @MockK(relaxed = true)
    private lateinit var getDocument: GetDocument

    @MockK(relaxed = true)
    private lateinit var createDocumentInteraction: CreateDocumentInteraction

    @MockK(relaxed = true)
    private lateinit var createDocumentSigningDraft: CreateDocumentSigningDraft

    @MockK
    private lateinit var externalCalendarDocumentEvent: ExternalCalendarDocumentEvent

    private lateinit var clock: Clock
    private lateinit var finalVersion: FinalVersion

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        finalVersion = FinalVersion(
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            confirmFinalDocument = confirmFinalDocument,
            createDocumentSigningDraft = createDocumentSigningDraft,
            clock = clock,
            getDocument = getDocument,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `cannot upload final version, invalid status`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val psaId = anyId()
        val roundId = anyId()
        givenPSA(id = psaId, deal = deal, currentRoundId = roundId, status = DocumentStatus.LEGAL_REVIEW)

        val input = FinalDocumentInput(
            dealId = deal.id,
            finalVersionFile = givenFileInput(),
            comment = "legal review",
            member = MemberObjectMother.buyer(),
            createSignDraft = true,
            documentType = DocumentType.PSA
        )

        assertThrows<InvalidDocumentStatusException> { finalVersion(input) }
    }

    @Test
    fun `can upload final version and create sign draft`() {
        val deal = DealBuilder().build()
        val member = MemberObjectMother.buyer()
        val document = givenPSA(id = anyId(), deal = deal, currentRoundId = 1, status = DocumentStatus.PENDING_FINAL_VERSION)
        val input = FinalDocumentInput(
            dealId = deal.id,
            finalVersionFile = givenFileInput(),
            comment = "initial version",
            member = member,
            createSignDraft = true,
            documentType = DocumentType.PSA
        )

        val expectedDraft = givenConfirmedDocuments(input, document)

        val psaUpdated = finalVersion(input)

        assertPsaWasUpdated(psaUpdated, expectedDraft)
        assertInteractionWasCreated(expectedDraft, input)
        verify(exactly = 1) { createDocumentSigningDraft(document.dealId, member, document.type) }
    }

    @Test
    fun `can upload final version and dont create sign draft`() {
        val deal = DealBuilder().build()
        val member = MemberObjectMother.buyer()
        val document = givenPSA(id = anyId(), deal = deal, currentRoundId = 1, status = DocumentStatus.PENDING_FINAL_VERSION)
        val input = FinalDocumentInput(
            dealId = deal.id,
            finalVersionFile = givenFileInput(),
            comment = "initial version",
            member = member,
            createSignDraft = false,
            documentType = DocumentType.PSA
        )

        val expectedDraft = givenConfirmedDocuments(input, document)

        val psaUpdated = finalVersion(input)

        assertPsaWasUpdated(psaUpdated, expectedDraft)
        assertInteractionWasCreated(expectedDraft, input)
        verify(exactly = 0) { createDocumentSigningDraft(document.dealId, member, document.type) }
    }

    private fun givenConfirmedDocuments(input: FinalDocumentInput, document: Document): FinalDocument =
        DocumentRoundObjectMother.finalDocument(documentId = document.id).also {
            every { confirmFinalDocument(input, document) } returns it
            every { externalCalendarDocumentEvent.impactOnCalendar(any()) } returns Unit
        }

    private fun givenPSA(id: Long, deal: Deal, currentRoundId: Long?, status: DocumentStatus) = DocumentBuilder().apply {
        this.id = id
        this.dealId = deal.id
        this.status = status
        this.currentRoundId = currentRoundId
    }.build().also {
        every { getDocument.byDealIdAndDocumentType(deal.id, DocumentType.PSA) } returns it
    }

    private fun assertPsaWasUpdated(documentUpdated: Document, expectedDraft: FinalDocument) {
        Assertions.assertEquals(DocumentStatus.PREPARE_SIGN_VERSION, documentUpdated.status)
        Assertions.assertEquals(expectedDraft.id, documentUpdated.currentRoundId)
        Assertions.assertEquals(OffsetDateTime.now(clock), documentUpdated.updatedAt)
    }

    private fun givenFileInput() = FileInput(anyString(), anyString())

    private fun assertInteractionWasCreated(documentRound: DocumentRound, input: FinalDocumentInput) {
        verify {
            createDocumentInteraction(
                input.member,
                input.comment,
                documentRound,
                Interaction.Type.FINAL_VERSION
            )
        }
    }
}
