package realestate.unlock.dealroom.api.unit.entrypoint.rest.contract.paginated.filters

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableEnum

class SearchableEnumTest {

    enum class TestEnum {
        UNO,
        DOS,
        TRES,
        CATORCE
    }

    private fun createSearchableEnum(operator: EnumOperator, values: List<TestEnum>, mustFail: Boolean = true) {
        try {
            SearchableEnum(operator, values)
            Assertions.assertFalse(mustFail)
        } catch (e: Exception) {
            Assertions.assertTrue(mustFail)
        }
    }

    @Test
    fun `cannot create enum search without values`() {
        createSearchableEnum(EnumOperator.EQ, listOf(), mustFail = true)
    }

    @Test
    fun `can create enum search with eq`() {
        createSearchableEnum(EnumOperator.EQ, listOf(TestEnum.UNO), mustFail = false)
    }

    @Test
    fun `cannot create enum search with eq with more than one value`() {
        createSearchableEnum(EnumOperator.EQ, listOf(TestEnum.UNO, TestEnum.DOS), mustFail = true)
    }

    @Test
    fun `can create enum search with in with one value`() {
        createSearchableEnum(EnumOperator.IN, listOf(TestEnum.UNO), mustFail = false)
    }

    @Test
    fun `can create enum search with in with more thar one value`() {
        createSearchableEnum(EnumOperator.IN, listOf(TestEnum.UNO, TestEnum.DOS), mustFail = false)
    }
}
