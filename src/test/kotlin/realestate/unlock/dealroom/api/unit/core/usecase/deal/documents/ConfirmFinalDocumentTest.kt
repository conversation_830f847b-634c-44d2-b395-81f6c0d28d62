package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.FinalDocument
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.repository.document.DocumentRoundRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.ConfirmFinalDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FileType
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FinalDocumentInput
import realestate.unlock.dealroom.api.core.usecase.file.ConfirmStagedFile
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class ConfirmFinalDocumentTest {

    @MockK
    private lateinit var confirmStagedFile: ConfirmStagedFile

    @MockK(relaxed = true)
    private lateinit var documentRoundRepository: DocumentRoundRepository

    private lateinit var confirmDraftDocuments: ConfirmFinalDocument

    @BeforeEach
    fun setUp() {
        confirmDraftDocuments = ConfirmFinalDocument(
            confirmStagedFile = confirmStagedFile,
            documentRoundRepository = documentRoundRepository
        )
    }

    @Test
    fun `can confirm final document`() {
        val nextId = nextRoundId()
        val psa = DocumentBuilder().build()
        val psaNextId = psa.copy(currentRoundId = nextId)
        val input = FinalDocumentInput(
            dealId = anyId(),
            finalVersionFile = givenFileInput(),
            comment = "initial version",
            member = MemberObjectMother.buyer(),
            createSignDraft = false,
            documentType = DocumentType.PSA
        )

        val file = givenFilesConfirmed(psaNextId, input)

        val documentsConfirmed = confirmDraftDocuments(input, psa)

        assertEquals(psa.id, documentsConfirmed.documentId)
        assertEquals(input.comment, documentsConfirmed.comment)
        assertEquals(input.member.id, documentsConfirmed.memberId)
        assertEquals(file.first().id, documentsConfirmed.fileId)
        assertNewDocumentWasSaved(documentsConfirmed)
    }

    private fun nextRoundId(): Long =
        anyId().also {
            every { documentRoundRepository.nextId() } returns it
        }

    private fun givenFileInput() = FileInput(anyString(), anyString())

    private fun assertNewDocumentWasSaved(expected: FinalDocument) {
        verify {
            documentRoundRepository.save(expected)
        }
    }

    private fun givenFilesConfirmed(document: Document, input: FinalDocumentInput): List<File> {
        val filesToConfirm = listOfNotNull(fileToConfirm(input.finalVersionFile, document, input))
        return filesToConfirm.map {
            File(
                id = anyId(),
                name = it.name,
                kFileId = it.kFileId,
                memberId = input.member.id,
                createdAt = OffsetDateTime.now(Clock.systemUTC())
            )
        }.also {
            every { confirmStagedFile(filesToConfirm) } returns it
        }
    }

    private fun fileToConfirm(
        it: FileInput,
        document: Document,
        input: FinalDocumentInput
    ) = ConfirmStagedFile.FileToConfirm(
        name = it.name,
        kFileId = it.uid,
        path = "deal/${document.dealId}/psa/${document.id}/final/${document.currentRoundId}_${FileType.FINAL_VERSION.name}",
        member = input.member

    )
}
