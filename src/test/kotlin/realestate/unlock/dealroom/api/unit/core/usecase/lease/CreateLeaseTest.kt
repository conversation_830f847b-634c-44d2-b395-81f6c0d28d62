package realestate.unlock.dealroom.api.unit.core.usecase.lease

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealById
import realestate.unlock.dealroom.api.core.usecase.deal.documents.CreateDocument
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class CreateLeaseTest {

    companion object {
        private val now = Instant.now()
    }

    @MockK(relaxed = true)
    private lateinit var getDealById: GetDealById

    @MockK(relaxed = true)
    private lateinit var documentRepository: DocumentRepository

    private lateinit var createDocument: CreateDocument

    @BeforeEach
    fun setUp() {
        createDocument = CreateDocument(
            getDealById = getDealById,
            documentRepository = documentRepository,
            clock = Clock.fixed(now, ZoneId.systemDefault())
        )
    }

    @Test
    fun `can create a lease for a deal`() {
        val deal = DealBuilder().build().also {
            every {
                getDealById.get(it.id)
            } returns it
        }
        val leaseId = anyId().also {
            every { documentRepository.nextId() } returns it
        }

        val lease = createDocument(deal.id, DocumentType.LEASE)

        verify { documentRepository.save(lease) }
        assertEquals(lease.id, leaseId)
        assertEquals(lease.dealId, deal.id)
        assertEquals(lease.status, DocumentStatus.NOT_STARTED)
        assertEquals(lease.type, DocumentType.LEASE)
        assertNull(lease.currentRoundId)
        assertNull(lease.executedAt)
        assertNull(lease.draftSubmittedAt)
        assertNull(lease.expectedBy)
        assertEquals(lease.createdAt.toEpochSecond(), now.epochSecond)
        assertEquals(lease.updatedAt.toEpochSecond(), now.epochSecond)
    }
}
