package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.summary

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.task.TaskType
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.summary.GetLeaseDocument
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder

@ExtendWith(MockKExtension::class)
class GetLeaseDocumentTest {

    @MockK
    private lateinit var documentRepository: DocumentRepository

    @MockK
    private lateinit var taskRepository: TaskRepository

    @MockK
    private lateinit var propertyRepository: PropertyRepository

    private lateinit var getLeaseDocument: GetLeaseDocument

    @BeforeEach
    fun setUp() {
        getLeaseDocument = GetLeaseDocument(
            documentRepository = documentRepository,
            taskRepository = taskRepository,
            propertyRepository = propertyRepository
        )
    }

    @Test
    fun `given no lease for deal with new lease flow, returns ToDo`() {
        val deal = givenDeal(newLeaseFlow = true)
        every { taskRepository.findByDealIdAndTypeKey(dealId = deal.id, typeKey = DocumentType.LEASE.taskTypeKey) } returns TaskBuilder().withTypeKey(TaskType.LEASE.key).build()
        every { documentRepository.findByDealIdAndDocumentType(deal.id, DocumentType.LEASE) } returns null
        every { propertyRepository.findById(deal.propertyId) } returns PropertyBuilder().build()

        val document = getLeaseDocument(deal)

        checkNotNull(document)
        assertNull(document.expectedBy)
        assertNull(document.createdAt)
        assertEquals(DocumentStatus.NOT_STARTED, document.status)
    }

    @Test
    fun `given lease for deal with new Lease flow, returns it`() {
        val deal = givenDeal(newLeaseFlow = true)
        val lease = DocumentBuilder().apply { dealId = deal.id }.build()
        every { taskRepository.findByDealIdAndTypeKey(dealId = deal.id, typeKey = DocumentType.LEASE.taskTypeKey) } returns TaskBuilder().withTypeKey(TaskType.LEASE.key).build()
        every { documentRepository.findByDealIdAndDocumentType(deal.id, DocumentType.LEASE) } returns lease
        every { propertyRepository.findById(deal.propertyId) } returns PropertyBuilder().build()

        val document = getLeaseDocument(deal)

        checkNotNull(document)
        assertEquals(lease.expectedBy?.toLocalDate(), document.expectedBy)
        assertEquals(lease.createdAt.toLocalDate(), document.createdAt)
        assertEquals(lease.status, document.status)
    }

    @Test
    fun `given deal with old lease flow, returns null`() {
        val deal = givenDeal(newLeaseFlow = false)
        every { taskRepository.findByDealIdAndTypeKey(dealId = deal.id, typeKey = DocumentType.LEASE.taskTypeKey) } returns null
        every { propertyRepository.findById(deal.propertyId) } returns PropertyBuilder().build()

        val document = getLeaseDocument(deal)

        assertNull(document)
    }

    @Test
    fun `given deal with multifamily property, returns null`() {
        val deal = givenDeal(newLeaseFlow = false)
        every { taskRepository.findByDealIdAndTypeKey(dealId = deal.id, typeKey = DocumentType.LEASE.taskTypeKey) } returns TaskBuilder().withTypeKey(TaskType.LEASE.key).build()
        every { propertyRepository.findById(deal.propertyId) } returns PropertyBuilder().apply { this.type = PropertyType.MULTIFAMILY }.build()

        val document = getLeaseDocument(deal)

        assertNull(document)
    }

    private fun givenDeal(newLeaseFlow: Boolean): Deal {
        val deal = DealBuilder().build()
        val newLeaseTask = if (newLeaseFlow) TaskBuilder().build() else null
        every { taskRepository.findByDealIdAndTypeKey(deal.id, "lease") } returns newLeaseTask
        return deal
    }
}
