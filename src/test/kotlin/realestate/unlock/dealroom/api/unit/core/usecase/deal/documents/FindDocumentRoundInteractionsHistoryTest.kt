package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentRound
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.ExecutedDocument
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentAndCurrentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentRoundInteractions
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentRoundInteractions.Input
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentRoundInteractionsHistory
import realestate.unlock.dealroom.api.utils.entity.document.InteractionBuilder

@ExtendWith(MockKExtension::class)
class FindDocumentRoundInteractionsHistoryTest {

    @MockK
    private lateinit var findDocumentRoundInteractions: FindDocumentRoundInteractions

    @MockK
    private lateinit var findDocumentAndCurrentRound: FindDocumentAndCurrentRound

    private lateinit var findDocumentRoundInteractionsHistory: FindDocumentRoundInteractionsHistory

    @BeforeEach
    fun setUp() {
        findDocumentRoundInteractionsHistory = FindDocumentRoundInteractionsHistory(
            findDocumentRoundInteractions = findDocumentRoundInteractions,
            findDocumentAndCurrentRound = findDocumentAndCurrentRound
        )
    }

    @Test
    fun `it retrieves only the filtered interaction`() {
        // given
        val givenDealId = 12L
        val givenPsaRoundId = 23L
        val givenDocumentRound = ExecutedDocument(id = givenPsaRoundId, documentId = 1, fileId = 1)

        every { findDocumentAndCurrentRound.byDealIdAndDocumentType<DocumentRound>(givenDealId, DocumentType.PSA) } returns Pair(mockk(), givenDocumentRound)
        mockInteractionsForDealAndPsaRoundId(givenDealId, givenPsaRoundId)

        // when
        val filteredInteractions = findDocumentRoundInteractionsHistory(dealId = givenDealId, documentType = DocumentType.PSA)

        // then
        assertThat(filteredInteractions, hasSize(6))
        assertTrue(filteredInteractions.any { it.type == Interaction.Type.APPROVE })
        assertTrue(filteredInteractions.any { it.type == Interaction.Type.SELLER_SIGN })
        assertTrue(filteredInteractions.any { it.type == Interaction.Type.BUYER_SIGN })
        assertTrue(filteredInteractions.any { it.type == Interaction.Type.REQUEST_CHANGES })
        assertTrue(filteredInteractions.any { it.type == Interaction.Type.SHARE })
        assertTrue(filteredInteractions.any { it.type == Interaction.Type.FINAL_VERSION })
    }

    private fun mockInteractionsForDealAndPsaRoundId(dealId: Long, psaRoundId: Long) =
        listOf(
            InteractionBuilder().apply { this.type = Interaction.Type.APPROVE }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.REQUEST_CHANGES }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.REQUEST_LEGAL_REVIEW }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.REQUEST_BUSINESS_REVIEW }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.SELLER_SIGN }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.ON_HOLD }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.RESUME }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.BUYER_SIGN }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.SHARE }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.COMMENT }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.NEW_DRAFT }.build(),
            InteractionBuilder().apply { this.type = Interaction.Type.FINAL_VERSION }.build()
        ).let { interactions ->
            every { findDocumentRoundInteractions(Input(dealId = dealId, roundId = psaRoundId, documentType = DocumentType.PSA)) } returns interactions
        }
}
