package realestate.unlock.dealroom.api.unit.core.usecase.deal.category

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.category.DealCategory
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.task.TaskWithFilesAndHistory
import realestate.unlock.dealroom.api.core.repository.category.FindCategoryByKeyRepository
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.repository.task.file.TaskFileRepository
import realestate.unlock.dealroom.api.core.usecase.deal.category.GetCompleteDealCategory
import realestate.unlock.dealroom.api.utils.entity.CategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.deal.DealCategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder

@ExtendWith(MockKExtension::class)
class GetCompleteDealCategoryTest {

    private lateinit var getCompleteDealCategory: GetCompleteDealCategory

    @MockK
    private lateinit var dealCategoryRepository: DealCategoryRepository

    @MockK
    private lateinit var findCategoryByKeyRepository: FindCategoryByKeyRepository

    @MockK
    private lateinit var taskRepository: TaskRepository

    @RelaxedMockK
    private lateinit var taskFileRepository: TaskFileRepository

    @BeforeEach
    fun setUp() {
        getCompleteDealCategory = GetCompleteDealCategory(
            dealCategoryRepository = dealCategoryRepository,
            findCategoryByKeyRepository = findCategoryByKeyRepository,
            taskRepository = taskRepository,
            taskFileRepository = taskFileRepository
        )
    }

    @Test
    fun `when seller ask for deal category, only returns task with seller visibility`() {
        val dealCategory = givenDealCategory()
        val seller = MemberObjectMother.seller()
        val tasks = givenTaskFor(dealCategory, seller)

        val completeDealCategory = getCompleteDealCategory.get(dealCategoryId = dealCategory.id, member = seller)

        assertEquals(2, completeDealCategory.tasksWithFilesAndHistory.size)
        assert(
            completeDealCategory.tasksWithFilesAndHistory.map(TaskWithFilesAndHistory::task)
                .contains(tasks.first { it.visibility == Task.Visibility.ALL })
        )
        assert(
            completeDealCategory.tasksWithFilesAndHistory.map(TaskWithFilesAndHistory::task)
                .contains(tasks.first { it.visibility == Task.Visibility.SELLER_TEAM })
        )
    }

    @Test
    fun `when buyer ask for deal category, only returns task with buyer visibility`() {
        val dealCategory = givenDealCategory()
        val buyer = MemberObjectMother.buyer()
        val tasks = givenTaskFor(dealCategory, buyer)

        val completeDealCategory = getCompleteDealCategory.get(dealCategoryId = dealCategory.id, member = buyer)

        assertEquals(2, completeDealCategory.tasksWithFilesAndHistory.size)
        assert(
            completeDealCategory.tasksWithFilesAndHistory.map(TaskWithFilesAndHistory::task)
                .contains(tasks.first { it.visibility == Task.Visibility.ALL })
        )
        assert(
            completeDealCategory.tasksWithFilesAndHistory.map(TaskWithFilesAndHistory::task)
                .contains(tasks.first { it.visibility == Task.Visibility.BUYER_TEAM })
        )
    }

    private fun givenTaskFor(dealCategory: DealCategory, member: Member): List<Task> {
        val sellerTask = TaskBuilder().withVisibility(Task.Visibility.SELLER_TEAM).withDealCategoryId(dealCategory.id).build()
        val buyerTask = TaskBuilder().withVisibility(Task.Visibility.BUYER_TEAM).withDealCategoryId(dealCategory.id).build()
        val buyerAndSellerTask = TaskBuilder().withVisibility(Task.Visibility.ALL).withDealCategoryId(dealCategory.id).build()
        return listOf(sellerTask, buyerTask, buyerAndSellerTask).also {
            every { taskRepository.findByDealCategories(listOf(dealCategory.id)) } returns it
        }
    }

    private fun givenDealCategory() = DealCategoryBuilder().build().also { dealCategory ->
        every { dealCategoryRepository.findById(dealCategory.id) } returns dealCategory
        every { findCategoryByKeyRepository.find(dealCategory.categoryKey) } returns CategoryBuilder().build()
    }
}
