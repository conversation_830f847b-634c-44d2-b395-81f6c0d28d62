package realestate.unlock.dealroom.api.unit.core.usecase.deal.file

import com.fasterxml.jackson.core.type.TypeReference
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.kfile.KPathFile
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.deal.files.GetFilesAndFolderInPath
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DealFilesAndFoldersConfig
import realestate.unlock.dealroom.api.infrastructure.gateway.files.KFileServiceFileGateway
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Instant

class GetFilesAndFolderInPathTest {

    @MockK
    val fileGateway = mockk<FileGateway>()

    private val dealFilesAndFoldersConfig = DealFilesAndFoldersConfig("__folder__.txt")

    val target: GetFilesAndFolderInPath = GetFilesAndFolderInPath(fileGateway, dealFilesAndFoldersConfig)

    @Test
    fun `can get folders and files`() {
        val path = "uno/"
        val dealId = anyId()
        val finalPath = "deal/$dealId/repository/$path"
        val file = fakeKFile("${path}pepe.txt")
        val listOfKFiles = listOf(
            file,
            fakeKFile("${finalPath}${dealFilesAndFoldersConfig.dealRoomPixelFolder}"),
            fakeKFile("${finalPath}dos/pepe.txt"),
            fakeKFile("${finalPath}dos/tres/pepe.txt")
        )

        every { fileGateway.getFiles(finalPath) } returns listOfKFiles

        val output = target.invoke(dealId, path)

        Assertions.assertEquals(output.files.size, 1)
        Assertions.assertEquals(output.folders.size, 1)
        Assertions.assertEquals(output.files.first(), file)
        Assertions.assertEquals(output.folders.first().name, "dos")
    }

    @Test
    fun `can get folders and files with different paths`() {
        val path = ""
        val dealId = 1010L
        every { fileGateway.getFiles("deal/$dealId/repository/") } returns realKFileResponse()

        val output = target.invoke(dealId, path)

        Assertions.assertEquals(1, output.files.size)
        Assertions.assertEquals(1, output.folders.size)
        Assertions.assertEquals(
            "f19bd41e-f484-40f0-ab84-bed01c0c8ef7-modelo_de_contrato_de_locacion.docx",
            output.files.first().name
        )
        Assertions.assertEquals(
            "category",
            output.folders.first().name
        )
        Assertions.assertEquals(
            "category",
            output.folders.first().path
        )
    }

    @Test
    fun `can get folders and files with differents paths with childs count`() {
        val path = "category/13821/task/"
        val dealId = 1010L
        every { fileGateway.getFiles("deal/$dealId/repository/category/13821/task/") } returns realKFileResponse()
            .filter { it.path.startsWith("deal/$dealId/repository/category/13821/task/") }

        val output = target.invoke(dealId, path)

        Assertions.assertEquals(0, output.files.size)
        Assertions.assertEquals(2, output.folders.size)
        Assertions.assertEquals(1, output.folders.first().childCount())
    }

    private fun fakeKFile(path: String) =
        KPathFile(
            uid = anyString(),
            name = anyString(),
            path = path,
            lastModified = Instant.now(),
            sizeInBytes = 0
        )

    private fun realKFileResponse() = JsonMapper.decode(
        """
    [
{
"id": "deal-room/deal/1010/repository/category/13821/task/48793/3958ab17-0f18-4a12-a82a-df268cd5c928-modelo_de_contrato_de_locacion.docx",
"name": "3958ab17-0f18-4a12-a82a-df268cd5c928-modelo_de_contrato_de_locacion.docx",
"size_in_bytes": 23265,
"last_modified": "2023-03-10T16:03:52Z",
"path": "deal/1010/repository/category/13821/task/48793/3958ab17-0f18-4a12-a82a-df268cd5c928-modelo_de_contrato_de_locacion.docx"
},
{
"id": "deal-room/deal/1010/repository/category/13821/task/48794/6eb34fb7-9914-4f80-b25c-1951d91efd82-Screen Shot 2022-12-21 at 10.32.58.png",
"name": "6eb34fb7-9914-4f80-b25c-1951d91efd82-Screen Shot 2022-12-21 at 10.32.58.png",
"size_in_bytes": 79263,
"last_modified": "2023-03-20T20:17:46Z",
"path": "deal/1010/repository/category/13821/task/48794/6eb34fb7-9914-4f80-b25c-1951d91efd82-Screen Shot 2022-12-21 at 10.32.58.png"
},
{
"id": "deal-room/deal/1010/repository/category/13829/task/48874/0e2d5ec1-97c3-4c4f-8d52-2b59b00a775d-Screen Shot 2022-12-21 at 10.32.58.png",
"name": "0e2d5ec1-97c3-4c4f-8d52-2b59b00a775d-Screen Shot 2022-12-21 at 10.32.58.png",
"size_in_bytes": 79263,
"last_modified": "2023-03-09T13:29:06Z",
"path": "deal/1010/repository/category/13829/task/48874/0e2d5ec1-97c3-4c4f-8d52-2b59b00a775d-Screen Shot 2022-12-21 at 10.32.58.png"
},
{
"id": "deal-room/deal/1010/repository/category/13829/task/48874/28305a8a-c8f5-4d02-a516-f49a8c2f1bdf-Screen Shot 2022-12-22 at 16.45.17.png",
"name": "28305a8a-c8f5-4d02-a516-f49a8c2f1bdf-Screen Shot 2022-12-22 at 16.45.17.png",
"size_in_bytes": 71758,
"last_modified": "2023-03-09T14:03:33Z",
"path": "deal/1010/repository/category/13829/task/48874/28305a8a-c8f5-4d02-a516-f49a8c2f1bdf-Screen Shot 2022-12-22 at 16.45.17.png"
},
{
"id": "deal-room/deal/1010/repository/category/13829/task/48874/7d7a5833-38ab-4b48-b241-094e86c7399f-Screen Shot 2022-12-22 at 16.45.17.png",
"name": "7d7a5833-38ab-4b48-b241-094e86c7399f-Screen Shot 2022-12-22 at 16.45.17.png",
"size_in_bytes": 71758,
"last_modified": "2023-03-09T14:03:33Z",
"path": "deal/1010/repository/category/13829/task/48874/7d7a5833-38ab-4b48-b241-094e86c7399f-Screen Shot 2022-12-22 at 16.45.17.png"
},
{
"id": "deal-room/deal/1010/repository/category/13829/task/48874/c32fef0a-3d36-4737-aabc-6548321bb73b-Screen Shot 2022-12-23 at 09.29.48.png",
"name": "c32fef0a-3d36-4737-aabc-6548321bb73b-Screen Shot 2022-12-23 at 09.29.48.png",
"size_in_bytes": 414895,
"last_modified": "2023-03-09T14:03:33Z",
"path": "deal/1010/repository/category/13829/task/48874/c32fef0a-3d36-4737-aabc-6548321bb73b-Screen Shot 2022-12-23 at 09.29.48.png"
},
{
"id": "deal-room/deal/1010/repository/category/13829/task/48874/e9cbb0f7-f360-4f43-a6bb-590b1f832522-Screen Shot 2022-12-21 at 10.32.58.png",
"name": "e9cbb0f7-f360-4f43-a6bb-590b1f832522-Screen Shot 2022-12-21 at 10.32.58.png",
"size_in_bytes": 79263,
"last_modified": "2023-03-09T13:29:06Z",
"path": "deal/1010/repository/category/13829/task/48874/e9cbb0f7-f360-4f43-a6bb-590b1f832522-Screen Shot 2022-12-21 at 10.32.58.png"
},
{
"id": "deal-room/deal/1010/repository/category/13829/task/48874/fa61e07e-0309-4325-9b9c-2366abed0652-Screen Shot 2022-12-21 at 10.32.58.png",
"name": "fa61e07e-0309-4325-9b9c-2366abed0652-Screen Shot 2022-12-21 at 10.32.58.png",
"size_in_bytes": 79263,
"last_modified": "2023-03-09T13:29:06Z",
"path": "deal/1010/repository/category/13829/task/48874/fa61e07e-0309-4325-9b9c-2366abed0652-Screen Shot 2022-12-21 at 10.32.58.png"
},
{
"id": "deal-room/deal/1010/repository/f19bd41e-f484-40f0-ab84-bed01c0c8ef7-modelo_de_contrato_de_locacion.docx",
"name": "f19bd41e-f484-40f0-ab84-bed01c0c8ef7-modelo_de_contrato_de_locacion.docx",
"size_in_bytes": 23265,
"last_modified": "2023-03-17T18:06:27Z",
"path": "deal/1010/repository/f19bd41e-f484-40f0-ab84-bed01c0c8ef7-modelo_de_contrato_de_locacion.docx"
}
]
""",
        object : TypeReference<List<KFileServiceFileGateway.KFileResponse>>() {}
    ).map {
        KPathFile(
            uid = it.id,
            name = it.name,
            path = it.path,
            sizeInBytes = it.sizeInBytes,
            lastModified = it.lastModified
        )
    }
}
