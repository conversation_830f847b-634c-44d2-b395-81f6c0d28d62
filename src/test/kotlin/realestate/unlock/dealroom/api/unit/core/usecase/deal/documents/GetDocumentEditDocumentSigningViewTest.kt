package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.apache.http.client.utils.URIBuilder
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.gateway.sign.IntegrationSettings
import realestate.unlock.dealroom.api.core.gateway.sign.RetrieveEditEnvelopeViewUrlInput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.repository.document.DocumentSignRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.GetDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.GetDocumentEditDocumentSigningView
import realestate.unlock.dealroom.api.core.usecase.email.GetApplicationLinkByMember
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentSignerBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.net.URI
import java.time.Duration

@ExtendWith(MockKExtension::class)
class GetDocumentEditDocumentSigningViewTest {

    @MockK(relaxed = true)
    private lateinit var signGateway: SignGateway

    @MockK
    private lateinit var getApplicationLinkByMember: GetApplicationLinkByMember

    @MockK
    private lateinit var getDocument: GetDocument

    @MockK
    private lateinit var documentSignRepository: DocumentSignRepository

    private lateinit var getDocumentEditDocumentSigningView: GetDocumentEditDocumentSigningView

    @BeforeEach
    fun setUp() {
        getDocumentEditDocumentSigningView = GetDocumentEditDocumentSigningView(
            getDocument = getDocument,
            signGateway = signGateway,
            getApplicationLinkByMember = getApplicationLinkByMember,
            documentSignRepository = documentSignRepository
        )
    }

    @Test
    fun `can get edit url`() {
        val document = DocumentBuilder().build()
        val envelopeId = anyString()
        val returnPath = "http://ole.com"
        val application = "http://deal-room.com"
        val member = MemberObjectMother.buyer()
        val returnUrl = "http://altaSigner.com"
        every { getDocument.byDealIdAndDocumentType(document.dealId, document.type) } returns document
        every { documentSignRepository.findByDocumentId(document.id) } returns listOf(DocumentSignerBuilder().apply { this.envelopeId = envelopeId }.build())
        every { getApplicationLinkByMember.get(member) } returns "http://deal-room.com"
        val input = RetrieveEditEnvelopeViewUrlInput(
            envelopId = envelopeId,
            integrationSettings = IntegrationSettings(
                returnUrl = URIBuilder(application)
                    .apply { path = returnPath }
                    .build()
                    .normalize(),
                pingFrequency = Duration.ofSeconds(600).seconds,
                pingUrl = URI.create(application)
            )
        )
        every { signGateway.retrieveEditEnvelopeViewUrl(input) } returns URI.create(returnUrl)

        val url = getDocumentEditDocumentSigningView.invoke(
            GetDocumentEditDocumentSigningView.Input(
                dealId = document.dealId,
                returnPath = returnPath,
                member = member,
                documentType = document.type
            )
        )

        verify(exactly = 1) { signGateway.retrieveEditEnvelopeViewUrl(input) }
        Assertions.assertEquals(url.toString(), returnUrl)
    }
}
