package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.usecase.deal.documents.AddComment
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidCommentException
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class AddCommentTest : InteractWithDocumentRoundTest() {

    companion object {
        private val now = Instant.now()
    }

    private lateinit var addComment: AddComment

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        addComment = AddComment(
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            clock = clock,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `can comment a psa round`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId)
        val psa = givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "my comment",
            member = user.member,
            documentType = DocumentType.PSA
        )

        val psaUpdated = addComment.update(input)

        Assertions.assertEquals(psa.status, psaUpdated.status)
        Assertions.assertEquals(psa.currentRoundId, psaUpdated.currentRoundId)
        Assertions.assertEquals(OffsetDateTime.now(clock), psaUpdated.updatedAt)
        assertInteractionWasCreated(draftDocuments, input, Interaction.Type.COMMENT)
    }

    @Test
    fun `cannot add empty comment`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId, cleanVersionFileId = null)
        givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "",
            member = user.member,
            documentType = DocumentType.PSA
        )

        assertThrows<InvalidCommentException> { addComment.update(input) }
    }
}
