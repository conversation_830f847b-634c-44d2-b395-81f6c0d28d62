package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.task

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.entity.task.TaskStatusTransitions
import realestate.unlock.dealroom.api.core.entity.task.TaskTransitionInput
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.SellerSignDocumentImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.task.GetDocumentTask
import realestate.unlock.dealroom.api.core.usecase.task.TaskTransition
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder

@ExtendWith(MockKExtension::class)
class SellerSignDocumentImpactOnTaskTest {

    @MockK
    private lateinit var getDocumentTask: GetDocumentTask

    @MockK(relaxed = true)
    private lateinit var taskTransition: TaskTransition

    private lateinit var sellerSignDocumentImpactOnTask: SellerSignDocumentImpactOnTask

    @BeforeEach
    fun setUp() {
        sellerSignDocumentImpactOnTask = SellerSignDocumentImpactOnTask(
            getDocumentTask = getDocumentTask,
            taskTransition = taskTransition
        )
    }

    @Test
    fun `impact on task`() {
        val buyer = MemberObjectMother.buyer()
        val psa = DocumentBuilder().build()
        val psaTask = TaskBuilder().withTemplateKey("purchase_and_sale_contract")
            .withStatus(TaskStatus.WAITING_FOR_SIGNATURE).build()
        every { getDocumentTask(psa) } returns psaTask

        sellerSignDocumentImpactOnTask(psa, buyer)

        verify {
            taskTransition(
                TaskTransitionInput(
                    taskId = psaTask.id,
                    member = buyer,
                    transition = TaskStatusTransitions.SELLER_SIGN
                )
            )
        }
    }

    @Test
    fun `don't impact on task if status isn't waiting for signature`() {
        val buyer = MemberObjectMother.buyer()
        val psa = DocumentBuilder().build()
        val psaTask = TaskBuilder().withTemplateKey("purchase_and_sale_contract")
            .withStatus(TaskStatus.DONE).build()
        every { getDocumentTask(psa) } returns psaTask

        sellerSignDocumentImpactOnTask(psa, buyer)

        verify(exactly = 0) {
            taskTransition(
                TaskTransitionInput(
                    taskId = psaTask.id,
                    member = buyer,
                    transition = TaskStatusTransitions.SELLER_SIGN
                )
            )
        }
    }
}
