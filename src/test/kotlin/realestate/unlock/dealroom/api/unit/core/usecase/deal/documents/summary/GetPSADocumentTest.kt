package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.summary

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.summary.GetPSADocument
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder

@ExtendWith(MockKExtension::class)
class GetPSADocumentTest {

    @MockK
    private lateinit var documentRepository: DocumentRepository

    @MockK
    private lateinit var taskRepository: TaskRepository

    private lateinit var getPSADocument: GetPSADocument

    @BeforeEach
    fun setUp() {
        getPSADocument = GetPSADocument(documentRepository = documentRepository, taskRepository = taskRepository)
    }

    @Test
    fun `given no psa for deal with new PSA flow, returns ToDo`() {
        val deal = givenDeal(newPsaFlow = true)
        every { documentRepository.findByDealIdAndDocumentType(deal.id, DocumentType.PSA) } returns null

        val document = getPSADocument(deal)

        checkNotNull(document)
        assertNull(document.expectedBy)
        assertNull(document.createdAt)
        assertEquals(DocumentStatus.NOT_STARTED, document.status)
    }

    @Test
    fun `given psa for deal with new PSA flow, returns it`() {
        val deal = givenDeal(newPsaFlow = true)
        val psa = DocumentBuilder().apply { dealId = deal.id }.build()
        every { documentRepository.findByDealIdAndDocumentType(deal.id, DocumentType.PSA) } returns psa

        val document = getPSADocument(deal)

        checkNotNull(document)
        assertEquals(psa.expectedBy?.toLocalDate(), document.expectedBy)
        assertEquals(psa.createdAt.toLocalDate(), document.createdAt)
        assertEquals(psa.status, document.status)
    }

    @Test
    fun `given deal with new PSA flow, returns null`() {
        val deal = givenDeal(newPsaFlow = false)

        val document = getPSADocument(deal)

        assertNull(document)
    }

    private fun givenDeal(newPsaFlow: Boolean): Deal {
        val deal = DealBuilder().build()
        val newPsaTask = if (newPsaFlow) TaskBuilder().build() else null
        every { taskRepository.findByDealIdAndTypeKey(deal.id, "psa") } returns newPsaTask
        return deal
    }
}
