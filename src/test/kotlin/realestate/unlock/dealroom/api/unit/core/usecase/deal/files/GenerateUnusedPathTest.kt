package realestate.unlock.dealroom.api.unit.core.usecase.deal.files

import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.kfile.KPathFile
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.deal.files.GenerateUnusedPath
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Instant

class GenerateUnusedPathTest {

    private val fileGateway: FileGateway = mockk()
    private val target = GenerateUnusedPath(fileGateway)

    @Test
    fun `can get upload file url`() {
        // Given
        val input = "deal/123/some-folder/pepe.pdf"
        mockGetFiles(emptyList())

        // When
        val response = target.get(input)

        // Then
        assertEquals("deal/123/some-folder/pepe.pdf", response)
    }

    @Test
    fun `can get with repeated name`() {
        // Given
        val input = "deal/123/some-folder/pepe.pdf"
        mockGetFiles(
            listOf(
                "deal/123/some-folder/pepe.pdf"
            )
        )

        // When
        val response = target.get(input)

        // Then
        assertEquals("deal/123/some-folder/pepe (1).pdf", response)
    }

    @Test
    fun `can get with repeated name but different extension`() {
        // Given
        val input = "deal/123/some-folder/pepe.pdf"
        mockGetFiles(
            listOf(
                "deal/123/some-folder/pepe.docx"
            )
        )

        // When
        val response = target.get(input)

        // Then
        assertEquals("deal/123/some-folder/pepe.pdf", response)
    }

    @Test
    fun `can get with repeated name multiple times`() {
        // Given
        val input = "deal/123/some-folder/pepe.pdf"
        mockGetFiles(
            listOf(
                "deal/123/some-folder/pepe.pdf",
                "deal/123/some-folder/pepe (1).pdf",
                "deal/123/some-folder/pepe (2).pdf",
                "deal/123/some-folder/pepe (3).pdf"
            )
        )

        // When
        val response = target.get(input)

        // Then
        assertEquals("deal/123/some-folder/pepe (4).pdf", response)
    }

    @Test
    fun `can get with repeated name multiple times and missing one in the middle`() {
        // Given
        val input = "deal/123/some-folder/pepe.pdf"
        mockGetFiles(
            listOf(
                "deal/123/some-folder/pepe.pdf",
                "deal/123/some-folder/pepe (1).pdf",
                "deal/123/some-folder/pepe (3).pdf"
            )
        )

        // When
        val response = target.get(input)

        // Then
        assertEquals("deal/123/some-folder/pepe (2).pdf", response)
    }

    private fun mockGetFiles(paths: List<String>) {
        every { fileGateway.getFiles(any()) } returns paths.map {
            KPathFile(
                uid = anyString(),
                name = anyString(),
                sizeInBytes = 1,
                lastModified = Instant.now(),
                path = it
            )
        }
    }
}
