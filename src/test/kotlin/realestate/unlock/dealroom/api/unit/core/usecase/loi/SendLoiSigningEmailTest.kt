package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.gateway.email.EmailGateway
import realestate.unlock.dealroom.api.core.gateway.email.TemplatedEmailData
import realestate.unlock.dealroom.api.core.gateway.jwt.JWTGateway
import realestate.unlock.dealroom.api.core.gateway.sign.SignDocumentType
import realestate.unlock.dealroom.api.core.usecase.loi.SendLoiSigningEmail
import realestate.unlock.dealroom.api.infrastructure.configuration.model.JwtGatewayConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.LoiSentEmailConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.WebAppsConfig
import realestate.unlock.dealroom.api.infrastructure.gateway.jwt.JWTAuthGateway
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiSignBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import java.time.Clock
import java.time.LocalDate
import java.time.format.DateTimeFormatter

class SendLoiSigningEmailTest {

    private val jwtGateway: JWTGateway = mockk()
    private val emailGateway: EmailGateway = mockk()
    private val loiSentEmailConfig: LoiSentEmailConfig = LoiSentEmailConfig(
        emailFrom = "<EMAIL>",
        carbonCopiesMap = mapOf(),
        backgroundCarbonCopies = setOf()
    )
    private val webApps: WebAppsConfig = WebAppsConfig(
        dealRoom = "dealroom",
        dealRoomAdmin = "admin",
        kos = "kos"
    )

    private val sendLoiSigningEmail = SendLoiSigningEmail(
        jwtGateway = jwtGateway,
        emailGateway = emailGateway,
        loiSentEmailConfig = loiSentEmailConfig,
        webApps = webApps
    )

    @Test
    fun `can send sign email`() {
        val memberSign = LoiSignBuilder().build().sellerSign
        val property = PropertyBuilder().build()
        val loi = LoiMedicalRoundBuilder().build()

        val token = mapOf(
            SendLoiSigningEmail.MEMBER_ID to memberSign.id,
            SendLoiSigningEmail.LOI_ID to loi.id.toString(),
            SendLoiSigningEmail.DOC_TYPE to SignDocumentType.LOI.name,
            SendLoiSigningEmail.APP_LINK to webApps.dealRoom
        ).let {
            JWTAuthGateway(
                clock = Clock.systemUTC(),
                jwtGatewayConfig = JwtGatewayConfig(issuer = "issuer", secret = "secret", tokenExpirationInSeconds = 10000)
            )
                .generateToken(it)
        }

        every { jwtGateway.generateToken(any()) } returns token

        val emailDataSlot = slot<TemplatedEmailData>()
        every { emailGateway.sendTemplated(capture(emailDataSlot)) } returns Unit

        sendLoiSigningEmail.invoke(memberSign, loi, property)

        val emailData = emailDataSlot.captured
        Assertions.assertEquals(emailData.toEmailAddress.first(), memberSign.email)
        Assertions.assertEquals(emailData.fromEmailAddress, loiSentEmailConfig.emailFrom)
        Assertions.assertEquals(emailData.attachments.size, 0)
        Assertions.assertEquals(emailData.templateKey, "loi_with_lease_sign_email")
        Assertions.assertEquals(emailData.templateParams["price"], loi.offer.salesPrice.formatNumber())
        Assertions.assertEquals(emailData.templateParams["rent"], loi.offer.lease.rent.formatNumber())
        Assertions.assertEquals(emailData.templateParams["terms"], loi.offer.lease.length)
        Assertions.assertEquals(emailData.templateParams["expires"], loi.offer.contractTermination.formatDate())
        Assertions.assertEquals(emailData.templateParams["address"], property.address.street)
        Assertions.assertEquals(emailData.templateParams["tenant"], loi.tenantName)
        Assertions.assertEquals(emailData.templateParams["sign_url"], webApps.dealRoom.plus("""/sign-document-link?token=$token"""))
    }

    private fun Number.formatNumber() = String.format("%,.0f", this)
    private fun LocalDate.formatDate() = this.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))
}
