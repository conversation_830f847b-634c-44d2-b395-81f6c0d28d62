package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.kfile.FileToStaging
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.loi.*
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.kfileservice.StagingFileToConfirm
import realestate.unlock.dealroom.api.core.gateway.sign.DocumentOutput
import realestate.unlock.dealroom.api.core.gateway.sign.RetrieveDocumentInput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.loi.ExecutedLetterOfIntentRepository
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentSignRepository
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealStage
import realestate.unlock.dealroom.api.core.usecase.deal.update.DealUpdater
import realestate.unlock.dealroom.api.core.usecase.exception.deal.loi.InvalidLetterOfIntentSignStatusException
import realestate.unlock.dealroom.api.core.usecase.exception.deal.loi.LetterOfIntentSignNotFoundException
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SendFileToProcess
import realestate.unlock.dealroom.api.core.usecase.loi.*
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiSignBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.io.ByteArrayInputStream
import java.time.*

@ExtendWith(MockKExtension::class)
class ExecuteLoiRoundFromSignTest {

    companion object {
        private val now = Instant.now()
    }

    @MockK
    private lateinit var fileGateway: FileGateway

    @MockK
    private lateinit var signGateway: SignGateway

    @MockK(relaxed = true)
    private lateinit var dealRepository: DealRepository

    @MockK(relaxed = true)
    private lateinit var executedLetterOfIntentRepository: ExecutedLetterOfIntentRepository

    @MockK
    private lateinit var letterOfIntentSignRepository: LetterOfIntentSignRepository

    @MockK
    private lateinit var letterOfIntentRoundRepository: LetterOfIntentRoundRepository

    @MockK(relaxed = true)
    private lateinit var createLetterOfIntentHistoryEntry: CreateLetterOfIntentHistoryEntry

    private lateinit var clock: Clock

    private lateinit var executeLetterOfIntentFromSign: ExecuteLetterOfIntentFromSign

    @MockK
    private lateinit var getLetterOfIntentRound: GetLetterOfIntentRound

    @MockK
    private lateinit var changeDealStage: ChangeDealStage

    @MockK
    private lateinit var dealUpdater: DealUpdater

    @MockK
    private lateinit var sendFileToProcess: SendFileToProcess

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        executeLetterOfIntentFromSign = ExecuteLetterOfIntentFromSign(
            letterOfIntentSignRepository = letterOfIntentSignRepository,
            signGateway = signGateway,
            fileGateway = fileGateway,
            executeLoi = ExecuteLoi(
                clock = clock,
                createLetterOfIntentHistoryEntry = createLetterOfIntentHistoryEntry,
                letterOfIntentRoundRepository = letterOfIntentRoundRepository,
                dealRepository = dealRepository,
                executedLetterOfIntentRepository = executedLetterOfIntentRepository,
                changeDealStage = changeDealStage,
                dealUpdater = dealUpdater,
                sendFileToProcess = sendFileToProcess
            ),
            getLetterOfIntentRound = getLetterOfIntentRound
        )
        every { sendFileToProcess(any()) } returns Unit
    }

    @Test
    fun `if loiSing does not exist, it fails`() {
        givenNoSignLoi()
        val input = givenInput()

        assertThrows<LetterOfIntentSignNotFoundException> { executeLetterOfIntentFromSign(input, authToken = anyString()) }
    }

    @Test
    fun `if loiSing is not complete, it fails`() {
        val input = givenInput()
        val deal = givenDeal()
        givenSignLoi(completed = false, envelopeId = input.envelopeId, deal = deal)

        assertThrows<InvalidLetterOfIntentSignStatusException> { executeLetterOfIntentFromSign(input, authToken = anyString()) }
    }

    @Test
    fun `can execute a complete loi`() {
        // given
        val input = givenInput()
        val deal = givenDeal()
        val (loiSign, loi) = givenSignLoi(completed = true, envelopeId = input.envelopeId, deal = deal)
        val document = givenDocumentSigned(loiSign)
        val kFile = givenKFileUploadedSuccessful(loi, loiSign, document)
        val authToken = anyString()

        // when
        val result = executeLetterOfIntentFromSign(input, authToken = authToken)

        // then
        assertThatLoiIsExecuted(result, loi, deal, kFile)
        verify {
            sendFileToProcess(
                SendFileToProcess.Input(
                    fileId = kFile.uid,
                    fileType = TokenFileType.LOI,
                    dealId = deal.id,
                    authToken = authToken
                )
            )
        }
    }

    @Test
    fun `creates loi executed history entry`() {
        // given
        val input = givenInput()
        val deal = givenDeal()
        val (loiSign, loi) = givenSignLoi(completed = true, envelopeId = input.envelopeId, deal = deal)
        val document = givenDocumentSigned(loiSign)
        val kFile = givenKFileUploadedSuccessful(loi, loiSign, document)
        val authToken = anyString()
        // when
        executeLetterOfIntentFromSign(input, authToken = authToken)

        // then
        verify {
            createLetterOfIntentHistoryEntry.execute(
                CreateLetterOfIntentHistoryEntryInput(
                    dealId = loi.dealId,
                    loiId = loi.id,
                    type = LetterOfIntentHistoryEntryType.EXECUTION,
                    description = "Loi Executed",
                    kFile = kFile
                )
            )
        }

        verify {
            sendFileToProcess(
                SendFileToProcess.Input(
                    fileId = kFile.uid,
                    fileType = TokenFileType.LOI,
                    dealId = deal.id,
                    authToken = authToken
                )
            )
        }
    }

    private fun assertThatLoiIsExecuted(
        result: ExecutedLetterOfIntent,
        loi: MedicalLoiRound,
        deal: Deal,
        kFile: KFile
    ) {
        assertThat(result.loiId, IsEqual(loi.id))
        assertThat(result.dealId, IsEqual(deal.id))
        assertThat(result.executedAt, IsEqual(LocalDate.now(clock)))
        assertThat(result.letterOfIntentSigned!!.kFileId, IsEqual(kFile.uid))
        assertThat(result.letterOfIntentSigned!!.name, IsEqual(kFile.name))

        verify {
            dealUpdater.update(
                deal.copy(
                    id = deal.id,
                    stage = Stage.NEGOTIATION,
                    loiExecutedDate = result.executedAt,
                    offerPrice = loi.offer.salesPrice
                )
            )
        }
    }

    private fun givenDeal() = DealBuilder().build().also { deal ->
        every { dealRepository.findById(deal.id) } returns deal
        every { dealUpdater.update(any()) } returns deal
    }

    private fun givenSignLoi(completed: Boolean, envelopeId: String, deal: Deal): Pair<LetterOfIntentSign, MedicalLoiRound> {
        val loi = LoiMedicalRoundBuilder().apply {
            this.dealId = deal.id
            this.offerResponse = OfferResponse(
                files = listOf(),
                comments = anyString(),
                createdAt = LocalDate.now(),
                createdBy = MemberObjectMother.buyer()
            )
        }.build().also {
            every { changeDealStage.invoke(dealId = it.dealId, newStage = Stage.NEGOTIATION, comment = "Status change by LOI execution") } returns deal.copy(stage = Stage.NEGOTIATION)
            every { getLetterOfIntentRound.getById(it.id) } returns it
            every { letterOfIntentRoundRepository.getDealId(it.id) } returns it.dealId
            every { letterOfIntentRoundRepository.updateLoiStatusAndDueDate(it.id, status = LetterOfIntentRoundStatus.EXECUTED, null) } returns Unit
        }
        val completedAt = if (completed) OffsetDateTime.now() else null
        val loiSign = LoiSignBuilder()
            .apply {
                this.loiId = loi.id
                this.signingId = envelopeId
                this.sellerSign = MemberSign("1", completedAt, MemberTypeEnum.SELLER)
                this.buyerSign = MemberSign("2", completedAt, MemberTypeEnum.BUYER)
            }
            .build()
            .also { loiSing ->
                every { letterOfIntentSignRepository.findBySigningId(loiSing.signingId) } returns loiSing
            }
        return Pair(loiSign, loi)
    }

    private fun givenNoSignLoi() {
        every { letterOfIntentSignRepository.findBySigningId(any()) } returns null
    }

    private fun givenInput() = ExecuteLetterOfIntentInput(envelopeId = anyString())

    private fun givenDocumentSigned(loiSign: LetterOfIntentSign) = DocumentOutput(
        loiSign.documentId.toString(),
        ByteArrayInputStream.nullInputStream()
    ).apply {
        every {
            signGateway.retrieveDocument(
                RetrieveDocumentInput(
                    loiSign.signingId,
                    loiSign.documentId.toString()
                )
            )
        } returns this
    }

    private fun givenKFileUploadedSuccessful(loi: MedicalLoiRound, loiSign: LetterOfIntentSign, document: DocumentOutput): KFile {
        val kFile = KFile(uid = anyString(), name = "signed_loi_${loiSign.loiId}.pdf")
        every {
            fileGateway.uploadStagedFile(
                FileToStaging(
                    content = document.content,
                    contentType = "application/pdf",
                    filename = kFile.name
                )
            )
        } returns kFile
        every {
            fileGateway.confirmStagingFile(
                listOf(
                    StagingFileToConfirm(
                        kFileId = kFile.uid,
                        path = "deal/${loi.dealId}/loi/${loiSign.loiId}/executed"
                    )
                )
            )
        } returns Unit
        return kFile
    }
}
