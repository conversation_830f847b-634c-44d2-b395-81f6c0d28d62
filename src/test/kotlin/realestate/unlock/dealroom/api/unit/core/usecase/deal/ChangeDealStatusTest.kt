package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.DealHistory
import realestate.unlock.dealroom.api.core.entity.deal.DealStatus
import realestate.unlock.dealroom.api.core.repository.deal.DealHistoryRepository
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealStatus
import realestate.unlock.dealroom.api.core.usecase.deal.update.DealUpdater
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@ExtendWith(MockKExtension::class)
class ChangeDealStatusTest {

    @MockK
    private lateinit var dealRepository: DealRepository

    @MockK
    private lateinit var dealHistoryRepository: DealHistoryRepository

    @MockK
    private lateinit var dealUpdater: DealUpdater

    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)

    private lateinit var changeDealStatus: ChangeDealStatus

    @BeforeEach
    fun init() {
        TransactionManager.initialize(mockk(relaxed = true))
        changeDealStatus = ChangeDealStatus(
            dealRepository = dealRepository,
            dealHistoryRepository = dealHistoryRepository,
            clock = clock,
            dealUpdater = dealUpdater
        )
    }

    @Test
    fun `it updates the deal stage as expected`() {
        val currentStatus = DealStatus.ACTIVE
        val givenDeal = DealBuilder().withStatus(currentStatus).build()
        val givenNewStatus = DealStatus.ON_HOLD
        val givenUpdatedDeal = givenDeal.copy(status = givenNewStatus)
        val givenHistoryId = anyId()
        val givenComment = "an amazing comment"

        every { dealRepository.findById(givenDeal.id) } returns givenDeal
        every { dealUpdater.update(givenUpdatedDeal) } returns givenUpdatedDeal
        every { dealHistoryRepository.nextId() } returns givenHistoryId
        every { dealHistoryRepository.save(any()) } returns Unit

        changeDealStatus.invoke(
            ChangeDealStatus.Input(
                dealId = givenDeal.id,
                newStatus = givenNewStatus,
                comment = givenComment
            )
        )

        verify {
            dealUpdater.update(givenUpdatedDeal)
        }

        verify {
            dealHistoryRepository.save(
                DealHistory(
                    id = givenHistoryId,
                    dealId = givenDeal.id,
                    oldStage = givenDeal.stage,
                    newStage = givenDeal.stage,
                    oldStatus = currentStatus,
                    newStatus = givenNewStatus,
                    comment = givenComment,
                    memberId = null,
                    createdAt = OffsetDateTime.now(clock)
                )
            )
        }
    }
}
