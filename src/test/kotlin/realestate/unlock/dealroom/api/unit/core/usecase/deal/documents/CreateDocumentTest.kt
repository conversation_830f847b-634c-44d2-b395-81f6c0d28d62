package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealById
import realestate.unlock.dealroom.api.core.usecase.deal.documents.CreateDocument
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class CreateDocumentTest {

    companion object {
        private val now = Instant.now()
    }

    @MockK(relaxed = true)
    private lateinit var getDealById: GetDealById

    @MockK(relaxed = true)
    private lateinit var documentRepository: DocumentRepository

    private lateinit var createDocument: CreateDocument

    @BeforeEach
    fun setUp() {
        createDocument = CreateDocument(
            getDealById = getDealById,
            documentRepository = documentRepository,
            clock = Clock.fixed(now, ZoneId.systemDefault())
        )
    }

    @Test
    fun `can create a psa for a deal`() {
        val deal = DealBuilder().build().also {
            every {
                getDealById.get(it.id)
            } returns it
        }
        val psaId = anyId().also {
            every { documentRepository.nextId() } returns it
        }

        val psa = createDocument(deal.id, DocumentType.PSA)

        verify { documentRepository.save(psa) }
        assertEquals(psa.id, psaId)
        assertEquals(psa.dealId, deal.id)
        assertEquals(psa.status, DocumentStatus.NOT_STARTED)
        assertEquals(psa.type, DocumentType.PSA)
        assertNull(psa.currentRoundId)
        assertNull(psa.executedAt)
        assertNull(psa.draftSubmittedAt)
        assertNull(psa.expectedBy)
        assertEquals(psa.createdAt.toEpochSecond(), now.epochSecond)
        assertEquals(psa.updatedAt.toEpochSecond(), now.epochSecond)
    }
}
