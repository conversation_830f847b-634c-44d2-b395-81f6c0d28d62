package realestate.unlock.dealroom.api.unit.core.usecase.file.gpt

import com.keyway.kommons.http.exception.GenericErrorRequestException
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileToken
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileTokenStatus
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.kfile.KFileWithUrl
import realestate.unlock.dealroom.api.core.gateway.gpt.ChatGptGateway
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.repository.file.FileTokenRepository
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SendFileToProcess
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.client.SQSChatGptFileSentClient
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ConflictException
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString

@ExtendWith(MockKExtension::class)
class SendFileToProcessTest {

    @MockK
    private lateinit var fileGateway: FileGateway

    @MockK
    private lateinit var fileTokenRepository: FileTokenRepository

    @MockK
    private lateinit var chatGptGateway: ChatGptGateway

    @MockK
    private lateinit var sqsChatGptFileSentClient: SQSChatGptFileSentClient

    private lateinit var sendFileToProcess: SendFileToProcess

    @BeforeEach
    fun setUp() {
        sendFileToProcess = SendFileToProcess(
            fileGateway = fileGateway,
            fileTokenRepository = fileTokenRepository,
            chatGptGateway = chatGptGateway,
            sqsChatGptFileSentClient = sqsChatGptFileSentClient
        )
    }

    @Test
    fun `it sends a file to be processed`() {
        // given
        val givenFileId = anyString()
        val givenS3Url = anyString()
        val givenToken = anyString()
        val givenDealId = anyId()
        val authToken = anyString()

        every { fileTokenRepository.findByFileId(givenFileId) } returns null
        every { fileGateway.getFileWithUrl(givenFileId) } returns KFileWithUrl(kFileId = givenFileId, url = givenS3Url, name = anyString())
        every { chatGptGateway.uploadFileToProcess(givenS3Url, authToken = authToken) } returns givenToken
        every { fileTokenRepository.save(any()) } returns mockk()
        every { sqsChatGptFileSentClient.send(any()) } returns Unit

        // when
        sendFileToProcess(SendFileToProcess.Input(givenFileId, TokenFileType.REPORT, dealId = givenDealId, authToken = authToken))

        // then
        verify { chatGptGateway.uploadFileToProcess(givenS3Url, authToken = authToken) }
        verify { fileTokenRepository.save(FileToken(kFileId = givenFileId, token = givenToken, status = FileTokenStatus.PROCESSING, fileType = TokenFileType.REPORT, dealId = givenDealId)) }
        verify { sqsChatGptFileSentClient.send("""{"file_id":"$givenFileId","token":"$givenToken"}""") }
    }

    @Test
    fun `it does not send the file to process if it has already been processed`() {
        val givenFileId = anyString()
        val givenToken = givenTokenWithStatus(FileTokenStatus.PROCESSING, anyId())
        val authToken = anyString()

        every { fileTokenRepository.findByFileId(givenFileId) } returns givenToken

        assertThrows<ConflictException> { sendFileToProcess(SendFileToProcess.Input(givenFileId, TokenFileType.REPORT, anyId(), authToken = authToken)) }
    }

    @Test
    fun `if sending the file to process returns a status code 415 it should update the token status to NOT_PROCESSABLE`() {
        val givenFileId = anyString()
        val givenS3Url = anyString()
        val givenDealId = anyId()
        val authToken = anyString()

        val givenError = GenericErrorRequestException(statusCode = 415, message = "content not processable")
        every { fileTokenRepository.findByFileId(givenFileId) } returns null
        every { fileGateway.getFileWithUrl(givenFileId) } returns KFileWithUrl(kFileId = givenFileId, url = givenS3Url, name = anyString())
        every { chatGptGateway.uploadFileToProcess(givenS3Url, authToken = authToken) } throws givenError
        every { fileTokenRepository.save(any()) } returns mockk()

        assertThrows<GenericErrorRequestException> {
            sendFileToProcess(SendFileToProcess.Input(givenFileId, TokenFileType.REPORT, givenDealId, authToken = authToken))
        }

        verify {
            fileTokenRepository.save(
                FileToken(
                    kFileId = givenFileId,
                    token = "FILE_NOT_PROCESSABLE",
                    status = FileTokenStatus.CONTENT_NOT_PROCESSABLE,
                    fileType = TokenFileType.REPORT,
                    dealId = givenDealId
                )
            )
        }
    }

    private fun givenTokenWithStatus(status: FileTokenStatus, dealId: Long) = FileToken(
        kFileId = anyString(),
        token = anyString(),
        status = status,
        fileType = TokenFileType.REPORT,
        dealId = dealId
    )
}
