package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.called
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.loi.*
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.loi.MedicalLoiRound
import realestate.unlock.dealroom.api.core.entity.loi.OfferResponse
import realestate.unlock.dealroom.api.core.entity.loi.RespondLetterOfIntentInput
import realestate.unlock.dealroom.api.core.entity.loi.file.LetterOfIntentFile
import realestate.unlock.dealroom.api.core.repository.loi.MedicalLetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.exception.deal.loi.LetterOfIntentNotFoundException
import realestate.unlock.dealroom.api.core.usecase.loi.*
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.MemberSignBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class RespondMedicalLoiRoundTest {

    private val seller = MemberObjectMother.seller(1)

    @MockK
    private lateinit var letterOfIntentRoundRepository: MedicalLetterOfIntentRoundRepository

    @RelaxedMockK
    private lateinit var confirmLetterOfIntentFiles: ConfirmLetterOfIntentFiles

    @RelaxedMockK
    private lateinit var createLetterOfIntentHistoryEntry: CreateLetterOfIntentHistoryEntry

    @RelaxedMockK
    private lateinit var beginLetterOfIntentSigning: BeginLetterOfIntentSigning

    private val memberRepository: MemberRepository = mockk {
        every { findById(seller.id) } returns seller
    }

    private lateinit var respondLetterOfIntent: RespondLetterOfIntent

    @BeforeEach
    fun setUp() {
        respondLetterOfIntent = RespondLetterOfIntent(
            letterOfIntentRoundRepository = letterOfIntentRoundRepository,
            confirmLetterOfIntentFiles = confirmLetterOfIntentFiles,
            createLetterOfIntentHistoryEntry = createLetterOfIntentHistoryEntry,
            memberRepository = memberRepository,
            beginLetterOfIntentSigning = beginLetterOfIntentSigning
        )
    }

    @Test
    fun `Can accept a LOI in negotiation`() {
        // given
        val givenLOI = givenLOIWithStatus(LetterOfIntentRoundStatus.IN_NEGOTIATION)
        val input = givenRespondLetterOfIntentInput(loi = givenLOI, accepted = true)
        val offerResponse = OfferResponse(
            files = listOf(LetterOfIntentFile(id = 1L, memberId = input.memberId, name = input.files[0].name, kFileId = input.files[0].uid)),
            comments = input.comments,
            createdAt = LocalDate.now(),
            createdBy = seller
        )
        val updatedLoi = givenLOI.copy(status = LetterOfIntentRoundStatus.ACCEPTED, offerResponse = offerResponse)
        every { letterOfIntentRoundRepository.update(any()) } returns updatedLoi

        // when
        val acceptedLoi = respondLetterOfIntent.respond(input)

        // then
        assertThat(acceptedLoi.status, equalTo(LetterOfIntentRoundStatus.ACCEPTED))
        verify { confirmLetterOfIntentFiles.confirm(input.files, updatedLoi) }
    }

    @Test
    fun `Cannot accept a LOI with status rejected`() {
        val givenLOI = givenLOIWithStatus(LetterOfIntentRoundStatus.REJECTED)
        val input = givenRespondLetterOfIntentInput(loi = givenLOI, accepted = false)

        // when
        val error = assertThrows<RuntimeException> {
            respondLetterOfIntent.respond(input)
        }

        // then
        assertThat(error.message, IsEqual("Only letter of intents with status IN_NEGOTIATION can perform this operation"))
    }

    @Test
    fun `Cannot accept a LOI with status accepted`() {
        val givenLOI = givenLOIWithStatus(LetterOfIntentRoundStatus.ACCEPTED)
        val input = givenRespondLetterOfIntentInput(loi = givenLOI, accepted = true)

        // when
        val error = assertThrows<RuntimeException> {
            respondLetterOfIntent.respond(input)
        }

        // then
        assertThat(error.message, IsEqual("Only letter of intents with status IN_NEGOTIATION can perform this operation"))
    }

    @Test
    fun `Can reject a LOI in negotiation`() {
        // given
        val givenLOI = givenLOIWithStatus(LetterOfIntentRoundStatus.IN_NEGOTIATION)
        val input = givenRespondLetterOfIntentInput(loi = givenLOI, accepted = false)
        val offerResponse = OfferResponse(
            files = listOf(LetterOfIntentFile(id = 1L, memberId = input.memberId, name = input.files[0].name, kFileId = input.files[0].uid)),
            comments = input.comments,
            createdAt = LocalDate.now(),
            createdBy = seller
        )
        val updatedLoi = givenLOI.copy(status = LetterOfIntentRoundStatus.REJECTED, offerResponse = offerResponse)
        every { letterOfIntentRoundRepository.update(any()) } returns updatedLoi

        // when
        val rejectedLoi = respondLetterOfIntent.respond(input)

        // then
        assertThat(rejectedLoi.status, equalTo(LetterOfIntentRoundStatus.REJECTED))
        verify { confirmLetterOfIntentFiles.confirm(input.files, updatedLoi) }
    }

    @Test
    fun `Cannot reject a LOI with status rejected`() {
        val givenLOI = givenLOIWithStatus(LetterOfIntentRoundStatus.REJECTED)
        val input = givenRespondLetterOfIntentInput(loi = givenLOI, accepted = false)

        // when
        val error = assertThrows<RuntimeException> {
            respondLetterOfIntent.respond(input)
        }

        // then
        assertThat(error.message, IsEqual("Only letter of intents with status IN_NEGOTIATION can perform this operation"))
    }

    @Test
    fun `Cannot reject a LOI with status accepted`() {
        // given
        val givenLOI = givenLOIWithStatus(LetterOfIntentRoundStatus.ACCEPTED)
        val input = givenRespondLetterOfIntentInput(loi = givenLOI, accepted = false)

        // when
        val error = assertThrows<RuntimeException> {
            respondLetterOfIntent.respond(input)
        }

        // then
        assertThat(error.message, IsEqual("Only letter of intents with status IN_NEGOTIATION can perform this operation"))
    }

    @Test
    fun `It should throw exception if LOI was not found`() {
        // given
        val loiId = 2L
        val loi = LoiMedicalRoundBuilder().apply { id = loiId }.build()
        val input = givenRespondLetterOfIntentInput(loi = loi, accepted = true)
        every { letterOfIntentRoundRepository.findById(loiId) } returns null

        // when
        val error = assertThrows<LetterOfIntentNotFoundException> {
            respondLetterOfIntent.respond(input)
        }

        // then
        assertThat(error.message, IsEqual("Letter of intent with id [$loiId] not found"))
    }

    @Test
    fun `creates history entry when successfully updated`() {
        // given
        val givenLOI = givenLOIWithStatus(LetterOfIntentRoundStatus.IN_NEGOTIATION)
        val input = givenRespondLetterOfIntentInput(loi = givenLOI, accepted = true)
        val offerResponse = OfferResponse(
            files = listOf(LetterOfIntentFile(id = 1L, memberId = input.memberId, name = input.files[0].name, kFileId = input.files[0].uid)),
            comments = input.comments,
            createdAt = LocalDate.now(),
            createdBy = seller
        )
        val updatedLoi = givenLOI.copy(status = LetterOfIntentRoundStatus.ACCEPTED, offerResponse = offerResponse)
        every { letterOfIntentRoundRepository.update(any()) } returns updatedLoi

        // when
        val acceptedLoi = respondLetterOfIntent.respond(input)

        // then
        verify {
            createLetterOfIntentHistoryEntry.execute(
                CreateLetterOfIntentHistoryEntryInput(
                    dealId = acceptedLoi.dealId,
                    loiId = acceptedLoi.id,
                    type = LetterOfIntentHistoryEntryType.OFFER_RESPONSE,
                    description = acceptedLoi.offerResponse!!.comments,
                    kFile = null,
                    member = seller
                )
            )
        }
    }

    @Test
    fun `begins signing process when accepted a LOI`() {
        // given
        val givenLOI = givenLOIWithStatus(LetterOfIntentRoundStatus.IN_NEGOTIATION)
        val input = givenRespondLetterOfIntentInput(loi = givenLOI, accepted = true)
        val offerResponse = OfferResponse(
            files = listOf(LetterOfIntentFile(id = 1L, memberId = input.memberId, name = input.files[0].name, kFileId = input.files[0].uid)),
            comments = input.comments,
            createdAt = LocalDate.now(),
            createdBy = seller
        )
        val updatedLoi = givenLOI.copy(status = LetterOfIntentRoundStatus.ACCEPTED, offerResponse = offerResponse)
        every { letterOfIntentRoundRepository.update(any()) } returns updatedLoi

        // when
        respondLetterOfIntent.respond(input)

        // then
        verify { beginLetterOfIntentSigning.execute(updatedLoi, input.sellerSigner!!) }
    }

    @Test
    fun `do not begins signing process when rejected a LOI`() {
        // given
        val givenLOI = givenLOIWithStatus(LetterOfIntentRoundStatus.IN_NEGOTIATION)
        val input = givenRespondLetterOfIntentInput(loi = givenLOI, accepted = false)
        val offerResponse = OfferResponse(
            files = listOf(LetterOfIntentFile(id = 1L, memberId = input.memberId, name = input.files[0].name, kFileId = input.files[0].uid)),
            comments = input.comments,
            createdAt = LocalDate.now(),
            createdBy = seller
        )
        val updatedLoi = givenLOI.copy(status = LetterOfIntentRoundStatus.REJECTED, offerResponse = offerResponse)
        every { letterOfIntentRoundRepository.update(any()) } returns updatedLoi

        // when
        respondLetterOfIntent.respond(input)

        // then
        verify { beginLetterOfIntentSigning wasNot called }
    }

    private fun givenLOIWithStatus(status: LetterOfIntentRoundStatus): MedicalLoiRound =
        LoiMedicalRoundBuilder().apply {
            this.status = status
        }.build()
            .also { every { letterOfIntentRoundRepository.findById(it.id) } returns it }

    private fun givenRespondLetterOfIntentInput(loi: MedicalLoiRound, accepted: Boolean): RespondLetterOfIntentInput = RespondLetterOfIntentInput(
        memberId = seller.id,
        dealId = loi.dealId,
        loiId = loi.id,
        accepted = accepted,
        files = listOf(FileInput(uid = anyString(), name = anyString())),
        sellerSigner = MemberSignBuilder().build()
    )
}
