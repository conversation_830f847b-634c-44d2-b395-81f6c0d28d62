package realestate.unlock.dealroom.api.unit.entrypoint.rest.security.filter.deal

import io.javalin.http.Context
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealById
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ForbiddenException
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.security.anyUserToken

class VerifyDealBelongsToOrganizationSecurityFilterTest {

    private val member = MemberObjectMother.buyer()
    private val givenAuthenticatedUser = AuthenticatedUser(member, anyUserToken(), anyString())
    private val ctx: Context = mockk(relaxed = true)

    private val getDealById: GetDealById = mockk()
    private val filter = VerifyDealBelongsToOrganizationSecurityFilter(getDealById)

    @Test
    fun `throws bad request on missing deal-id path param`() {
        // when
        val action = { filter.apply(ctx, givenAuthenticatedUser) }

        // then
        assertThrows<BadRequestException>(action)
    }

    @Test
    fun `throws forbidden when member is not in the same organization as the deal`() {
        // given
        givenDealPathParam()
        val givenDeal = DealBuilder().apply { organizationId = "another-org-id" }.build()
        every { getDealById.get(any()) } returns givenDeal

        // when
        val action = { filter.apply(ctx, givenAuthenticatedUser) }

        // then
        assertThrows<ForbiddenException>(action)
    }

    @Test
    fun `authorizes user`() {
        givenDealPathParam()
        val givenDeal = DealBuilder().apply { organizationId = member.organizationId }.build()
        every { getDealById.get(any()) } returns givenDeal

        val action = { filter.apply(ctx, givenAuthenticatedUser) }

        assertDoesNotThrow(action)
    }

    private fun givenDealPathParam() {
        every { ctx.pathParamAsClass<Long>(eq("deal-id")).get() } returns 1
    }
}
