package realestate.unlock.dealroom.api.unit.core.usecase.deal

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.DealStatus
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.task.TaskExternalDataDefinition
import realestate.unlock.dealroom.api.core.entity.task.TaskWithFilesAndHistory
import realestate.unlock.dealroom.api.core.entity.task.file.TaskFile
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealData
import realestate.unlock.dealroom.api.core.usecase.deal.UpdateDealDataFromTask
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

class UpdateDealDataFromTaskTest : BaseFunctionalWithoutRestTest() {

    private val updateDealDataFromTask = Context.injector.getInstance(UpdateDealDataFromTask::class.java)
    private val getDealDealData = Context.injector.getInstance(GetDealData::class.java)

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    @Test
    fun `can update deal date from due date`() {
        val deal = DealCreator.createDealByRest()
        val dueDate = LocalDate.now()
        val task = deal.findOneTask().copy(externalDataDefinition = TaskExternalDataDefinition("deal.evaluationDueDate", emptySet()), dueDate = dueDate)
        updateDealDataFromTask.update(givenTaskWithFilesAndHistory(task), null)
        val dealData = getDealDealData.get(dealId = deal.id)
        Assertions.assertEquals(dueDate, dealData.deal.evaluationDueDate)
    }

    @Test
    fun `can update deal data from task form data`() {
        val deal = DealCreator.createDealByRest()
        val formData = mapOf(
            "deal.evaluationDueDate" to LocalDate.now(),
            "deal.offerPrice" to BigDecimal.valueOf(123L),
            "deal.schemaData.data.noi" to 123,
            "deal.status" to DealStatus.ON_HOLD,
            "property.name" to anyString()
        )
        val task = deal.findOneTask().copy(externalDataDefinition = TaskExternalDataDefinition(null, formData = formData.keys))
        updateDealDataFromTask.update(givenTaskWithFilesAndHistory(task), formData)
        val dealData = getDealDealData.get(dealId = deal.id)
        Assertions.assertEquals(formData["deal.evaluationDueDate"], dealData.deal.evaluationDueDate)
        Assertions.assertEquals(formData["deal.offerPrice"], dealData.deal.offerPrice)
        Assertions.assertEquals(formData["deal.schemaData.data.noi"].toString(), dealData.deal.schemaData.data["noi"].toString())
        Assertions.assertEquals(formData["deal.status"], dealData.deal.status)
        Assertions.assertEquals(formData["property.name"], dealData.property.name)
    }

    @Test
    fun `only update fields on definition`() {
        val deal = DealCreator.createDealByRest()
        val formData = mapOf(
            "deal.evaluationDueDate" to LocalDate.now(),
            "deal.offerPrice" to BigDecimal.valueOf(123L),
            "deal.schemaData.data.noi" to BigDecimal.valueOf(123L),
            "deal.status" to DealStatus.ON_HOLD,
            "property.name" to anyString()
        )
        val task = deal.findOneTask().copy(externalDataDefinition = TaskExternalDataDefinition(null, formData = setOf("deal.evaluationDueDate")))
        updateDealDataFromTask.update(givenTaskWithFilesAndHistory(task), formData)
        val dealData = getDealDealData.get(dealId = deal.id)
        Assertions.assertEquals(formData["deal.evaluationDueDate"], dealData.deal.evaluationDueDate)
        Assertions.assertNotEquals(formData["deal.offerPrice"], dealData.deal.offerPrice)
        Assertions.assertNotEquals(formData["deal.schemaData.data.noi"], dealData.deal.schemaData.data["noi"])
        Assertions.assertNotEquals(formData["deal.status"], dealData.deal.status)
        Assertions.assertNotEquals(formData["property.name"], dealData.property.name)
    }

    @Test
    fun `only update fields on definition with data`() {
        val deal = DealCreator.createDealByRest()
        val formData = mapOf(
            "deal.evaluationDueDate" to LocalDate.now(),
            "deal.offerPrice" to BigDecimal.valueOf(123L),
            "deal.schemaData.data.noi" to BigDecimal.valueOf(123L),
            "deal.status" to DealStatus.ON_HOLD,
            "property.name" to anyString()
        )
        val task = deal.findOneTask().copy(externalDataDefinition = TaskExternalDataDefinition(null, formData = formData.keys))
        updateDealDataFromTask.update(givenTaskWithFilesAndHistory(task), mapOf("otro_campo" to 123))
        val dealData = getDealDealData.get(dealId = deal.id)
        Assertions.assertNotEquals(formData["deal.evaluationDueDate"], dealData.deal.evaluationDueDate)
        Assertions.assertNotEquals(formData["deal.offerPrice"], dealData.deal.offerPrice)
        Assertions.assertNotEquals(formData["deal.schemaData.data.noi"], dealData.deal.schemaData.data["noi"])
        Assertions.assertNotEquals(formData["deal.status"], dealData.deal.status)
        Assertions.assertNotEquals(formData["property.name"], dealData.property.name)
    }

    @Test
    fun `it updates omFileId field`() {
        val deal = DealCreator.createDealByRest()
        val task = deal.findOneTask(templateKey = "nnn_upload_om")
        val givenTaskFile = TaskFile(
            taskId = anyId(),
            id = anyId(),
            kFileId = anyString(),
            name = anyString(),
            path = anyString(),
            createdAt = OffsetDateTime.now(),
            memberId = anyId()
        )

        updateDealDataFromTask.update(givenTaskWithFilesAndHistory(task = task, files = listOf(givenTaskFile)), mapOf())

        val dealData = getDealDealData.get(dealId = deal.id)

        assertThat(dealData.deal.omFileId, equalTo(givenTaskFile.kFileId))
    }

    private fun givenTaskWithFilesAndHistory(task: Task, files: List<TaskFile> = emptyList()) = TaskWithFilesAndHistory(
        task = task,
        files = files,
        history = emptyList()
    )
}
