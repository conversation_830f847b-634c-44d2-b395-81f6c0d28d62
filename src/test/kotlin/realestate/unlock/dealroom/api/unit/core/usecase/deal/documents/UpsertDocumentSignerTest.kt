package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.DocumentSigner
import realestate.unlock.dealroom.api.core.usecase.deal.documents.UpsertDocumentSigner
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.repository.database.document.DocumentSignDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.OffsetDateTime

class UpsertDocumentSignerTest : BaseFunctionalWithoutRestTest() {

    private lateinit var psaDatabaseSignRepository: DocumentSignDatabaseRepository
    private lateinit var upsertDocumentSigner: UpsertDocumentSigner

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        psaDatabaseSignRepository = Context.injector.getInstance(DocumentSignDatabaseRepository::class.java)
        upsertDocumentSigner = UpsertDocumentSigner(psaDatabaseSignRepository)
    }

    @Test
    fun `can save and update a PSA Signer`() {
        val signer = DocumentSigner(documentId = anyId(), member = MemberObjectMother.buyer(), createdAt = OffsetDateTime.now())
        upsertDocumentSigner(signer)
        comparePsaSigner(signer, psaDatabaseSignRepository.findByDocumentId(signer.documentId).first())
        val updateSigner = signer.copy(signCompletedAt = OffsetDateTime.now(), envelopeId = anyString(), docusignDocumentId = anyString())
        upsertDocumentSigner(updateSigner)
        comparePsaSigner(updateSigner, psaDatabaseSignRepository.findByDocumentId(signer.documentId).first())
    }

    private fun comparePsaSigner(
        one: DocumentSigner,
        two: DocumentSigner
    ) {
        Assertions.assertEquals(one.documentId, two.documentId)
        Assertions.assertEquals(one.envelopeId, two.envelopeId)
        Assertions.assertEquals(one.docusignDocumentId, two.docusignDocumentId)
        Assertions.assertEquals(one.signerRecipientId, two.signerRecipientId)
        Assertions.assertEquals(one.signerEmail, two.signerEmail)
        Assertions.assertEquals(one.signerFirstName, two.signerFirstName)
        Assertions.assertEquals(one.signerLastName, two.signerLastName)
        Assertions.assertEquals(one.signerTeamType, two.signerTeamType)
    }
}
