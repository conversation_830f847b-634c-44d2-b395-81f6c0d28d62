package realestate.unlock.dealroom.api.unit.core.usecase.deal.report

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.reports.Report
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportByTask
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportStatus
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.task.file.TaskFile
import realestate.unlock.dealroom.api.core.repository.deal.report.DealReportRepository
import realestate.unlock.dealroom.api.core.repository.deal.report.ReportByTaskRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.repository.task.file.TaskFileRepository
import realestate.unlock.dealroom.api.core.usecase.deal.reports.GetReportTaskFiles
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.math.BigDecimal
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class GetReportTaskFilesTest {

    @MockK
    private lateinit var taskFileRepository: TaskFileRepository

    @MockK
    private lateinit var dealReportRepository: DealReportRepository

    @MockK
    private lateinit var propertyRepository: PropertyRepository

    @MockK
    private lateinit var reportByTaskRepository: ReportByTaskRepository

    private lateinit var getReportTaskFiles: GetReportTaskFiles

    @BeforeEach
    fun setUp() {
        getReportTaskFiles = GetReportTaskFiles(
            taskFileRepository = taskFileRepository,
            dealReportRepository = dealReportRepository,
            propertyRepository = propertyRepository,
            reportByTaskRepository = reportByTaskRepository
        )
    }

    @Test
    fun `it retrieves the right files for report`() {
        val dealId = anyId()
        val reportType = "SURVEY"
        val propertyType = PropertyType.MEDICAL
        val taskTemplate = "survey"

        val report = givenReport(dealId, reportType)
        givenProperty(dealId = dealId, propertyType = propertyType)
        givenReportByTask(reportType = reportType, propertyType = propertyType, taskTemplate = taskTemplate)
        val files = givenTaskFiles(dealId = dealId, taskTemplate = taskTemplate)

        val taskFiles = getReportTaskFiles(dealId = dealId, reportId = report.id)

        assertEquals(files, taskFiles)
    }

    @Test
    fun `it returns empty list for report without tasks associated`() {
        val dealId = anyId()
        val reportType = "SURVEY"

        val report = givenReport(dealId, reportType)
        givenProperty(dealId = dealId, propertyType = PropertyType.MEDICAL)
        givenNoReportByTask()

        val taskFiles = getReportTaskFiles(dealId = dealId, reportId = report.id)

        assertTrue(taskFiles.isEmpty())
    }

    private fun givenTaskFiles(dealId: Long, taskTemplate: String) =
        listOf(mockk<TaskFile>()).also {
            every {
                taskFileRepository.findByDealIdAndTaskTemplate(dealId = dealId, templateKey = taskTemplate)
            } returns it
        }

    private fun givenReport(dealId: Long, type: String) = Report(
        id = anyId(),
        dealId = dealId,
        type = type,
        reportName = null,
        vendorKey = "EBI",
        vendorName = null,
        status = ReportStatus.ORDERED,
        expectedDate = LocalDate.now(),
        costEstimate = BigDecimal.TEN,
        cost = BigDecimal.valueOf(15),
        findings = null,
        tags = listOf(),
        documents = listOf()
    )
        .also {
            every { dealReportRepository.findById(it.id) } returns it
        }

    private fun givenProperty(dealId: Long, propertyType: PropertyType) = every {
        propertyRepository.findByDealId(dealId)
    } returns PropertyBuilder().apply {
        this.type = propertyType
    }.build()

    private fun givenReportByTask(reportType: String, propertyType: PropertyType, taskTemplate: String) = ReportByTask(
        reportType,
        taskTemplate,
        propertyType
    ).also { reportByTask ->
        every {
            reportByTaskRepository.findByReportTypeAndPropertyType(reportType, propertyType)
        } returns listOf(reportByTask)
    }

    private fun givenNoReportByTask() = every {
        reportByTaskRepository.findByReportTypeAndPropertyType(any(), any())
    } returns emptyList()
}
