package realestate.unlock.dealroom.api.unit.core.usecase.user

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.SetMemberWalkThroughDoneInput
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.member.SetMemberWalkThroughDone
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

class SetUserWalkThroughDoneTest {
    private val memberRepository: MemberRepository = mockk(relaxed = true)
    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)

    private val setUserWalkThroughDone = SetMemberWalkThroughDone(memberRepository, clock)

    @Test
    fun `sets user walk through to done`() {
        // given
        val member = MemberObjectMother.seller()
        every { memberRepository.findById(member.id) } returns member

        // when
        setUserWalkThroughDone.execute(SetMemberWalkThroughDoneInput(member.id))

        // then
        verify {
            memberRepository.update(
                member.copy(
                    walkThroughDone = true,
                    updatedAt = OffsetDateTime.now(clock)
                )
            )
        }
    }
}
