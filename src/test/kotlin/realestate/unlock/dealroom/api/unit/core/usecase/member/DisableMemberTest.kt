package realestate.unlock.dealroom.api.unit.core.usecase.member

import com.keyway.security.domain.user.User
import com.keyway.security.domain.user.UserSdk
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.member.DisableMember
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.Clock
import java.time.Instant
import java.time.ZoneOffset

class DisableMemberTest : BaseFunctionalWithoutRestTest() {
    private lateinit var disableMember: DisableMember
    private lateinit var clock: Clock
    private val memberRepository = Context.injector.getInstance(MemberRepository::class.java)

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)
        disableMember = DisableMember(
            memberRepository,
            clock
        )
    }

    @Test
    fun `should disable a member`() {
        val member = AuthMock.createMemberWithUser()
        disableMemberMockUserSkd(member)
        disableMember.disable(member.id)
        Assertions.assertTrue(!memberRepository.findById(memberId = member.id).enabled)
    }

    @Test
    fun `should disable a member if doesn't has auth`() {
        val member = AuthMock.createMemberWithUser(needUser = false)
        disableMember.disable(member.id)
        Assertions.assertTrue(!memberRepository.findById(memberId = member.id).enabled)
    }

    private fun disableMemberMockUserSkd(member: Member) {
        Context.injector.getInstance(UserSdk::class.java).let { userSdk ->
            val userMock = mockk<User>()
            every { userMock.id } returns member.authId!!
            every { userMock.email } returns member.email
            every { userMock.name } returns member.fullName
            every { userMock.emailVerified } returns true
            every { userMock.blocked } returns true
            every { userSdk.block(member.authId!!) } returns userMock
        }
    }
}
