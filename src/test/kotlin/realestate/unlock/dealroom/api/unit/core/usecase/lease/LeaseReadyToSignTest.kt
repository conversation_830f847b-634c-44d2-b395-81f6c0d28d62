package realestate.unlock.dealroom.api.unit.core.usecase.lease

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.FinalDocument
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentSignRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.DocumentReadyToSign
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentAndCurrentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.ReadyToSignImpactOnTask
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.document.DocumentSignerBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock

@ExtendWith(MockKExtension::class)
class LeaseReadyToSignTest {

    @MockK(relaxed = true)
    private lateinit var documentRepository: DocumentRepository

    @MockK
    private lateinit var findDocumentAndCurrentRound: FindDocumentAndCurrentRound

    @MockK
    private lateinit var documentSignRepository: DocumentSignRepository

    @MockK(relaxed = true)
    private lateinit var readyToSignImpactOnTask: ReadyToSignImpactOnTask

    @MockK
    private lateinit var memberRepository: MemberRepository
    private val clock = Clock.systemDefaultZone()
    private lateinit var documentReadyToSign: DocumentReadyToSign

    @BeforeEach
    fun setUp() {
        documentReadyToSign = DocumentReadyToSign(
            documentRepository = documentRepository,
            clock = clock,
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            documentSignRepository = documentSignRepository,
            readyToSignImpactOnTask = readyToSignImpactOnTask,
            memberRepository = memberRepository
        )
    }

    @Test
    fun `status must change to pending sign`() {
        val member = MemberObjectMother.buyer()
        val envelopeId = anyString()
        val leaseId = anyId()
        val finalDocument = DocumentRoundObjectMother.finalDocument(documentId = leaseId)
        val lease = DocumentBuilder().apply {
            type = DocumentType.LEASE
            id = leaseId
            this.status = DocumentStatus.PREPARE_SIGN_VERSION
        }.build()
        every { documentSignRepository.findByEnvelopeId(envelopeId) } returns listOf(
            DocumentSignerBuilder().apply { this.envelopeId = envelopeId; this.documentId = leaseId }.build(),
            DocumentSignerBuilder().apply { this.envelopeId = envelopeId; this.documentId = leaseId }.build()
        )
        every { findDocumentAndCurrentRound.byDocumentId<FinalDocument>(leaseId) } returns Pair(lease, finalDocument)

        every { memberRepository.findById(finalDocument.memberId) } returns member

        val leaseResult = documentReadyToSign.markAsSent(envelopeId)

        Assertions.assertEquals(DocumentStatus.PENDING_SIGN, leaseResult.status)
        verify { documentRepository.update(leaseResult) }
        verify { readyToSignImpactOnTask(document = lease, member = member) }
    }
}
