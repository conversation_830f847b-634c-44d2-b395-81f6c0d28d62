package realestate.unlock.dealroom.api.unit.core.usecase.calendar

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.ExternalDealCalendarEventInput
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarReportEvent
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalDealCalendarEventsActions
import realestate.unlock.dealroom.api.utils.entity.report.ReportBuilder
import java.time.LocalDate

class ExternalCalendarReportEvenTest {

    private lateinit var target: ExternalCalendarReportEvent

    @MockK
    private val eventsActions: ExternalDealCalendarEventsActions = mockk()

    @BeforeEach
    fun setUp() {
        target = ExternalCalendarReportEvent(eventsActions = eventsActions)
    }

    @Test
    fun `should createOrUpdate a calendar event if report has date`() {
        val report = ReportBuilder().apply {
            expectedDate = LocalDate.now().plusDays(10)
        }.build()

        every { eventsActions.createOrUpdate(any()) } returns Unit

        target.impactOnCalendar(report)

        verify(exactly = 1) {
            eventsActions.createOrUpdate(
                input = ExternalDealCalendarEventInput(
                    dealId = report.dealId,
                    referenceId = report.id.toString(),
                    referenceType = "report",
                    title = report.reportName ?: report.type,
                    description = report.vendorKey,
                    date = report.expectedDate!!
                )
            )
        }
    }

    @Test
    fun `should delete a calendar event if report hasn't date`() {
        val report = ReportBuilder().apply {
            expectedDate = null
        }.build()

        every { eventsActions.delete(any(), any(), any()) } returns Unit

        target.impactOnCalendar(report)

        verify(exactly = 1) {
            eventsActions.delete(
                dealId = report.dealId,
                referenceId = report.id.toString(),
                referenceType = "report"
            )
        }
    }
}
