package realestate.unlock.dealroom.api.unit.core.usecase.deal

import com.keyway.kommons.http.exception.BadRequestException
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.team.AddDealTeamMembersInput
import realestate.unlock.dealroom.api.core.repository.deal.DealMemberRelationRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.UpdateExternalCalendar
import realestate.unlock.dealroom.api.core.usecase.deal.AddDealTeamMembers
import realestate.unlock.dealroom.api.core.usecase.deal.team.AddDealTeamMember

class AddDealTeamMembersTest {

    private val addDealTeamMember: AddDealTeamMember = mockk(relaxed = true)
    private val memberRepository: MemberRepository = mockk(relaxed = true)
    private val getDealMemberRelationRepository: DealMemberRelationRepository = mockk(relaxed = true)
    private val updateExternalCalendar: UpdateExternalCalendar = mockk()

    private val instance = AddDealTeamMembers(
        addDealTeamMember,
        memberRepository,
        getDealMemberRelationRepository,
        updateExternalCalendar
    )

    @Test
    fun `Add a new member`() {
        // given
        val input = AddDealTeamMembersInput(1, listOf(2))

        every { memberRepository.findByMemberIds(any()) } returns listOf(mockk())

        every { getDealMemberRelationRepository.getByDealAndMember(any(), any()) } returns null

        every { updateExternalCalendar(input.dealId) } returns Unit
        // when
        instance.add(input)

        // then
        verify {
            addDealTeamMember.add(any())
        }
    }

    @Test
    fun `Add a member that is already in the deal`() {
        // given
        val input = AddDealTeamMembersInput(1, listOf(2))

        every { memberRepository.findByMemberIds(any()) } returns listOf(mockk())

        every { getDealMemberRelationRepository.getByDealAndMember(any(), any()) } returns mockk()

        every { updateExternalCalendar(input.dealId) } returns Unit
        // when
        val error = org.junit.jupiter.api.assertThrows<BadRequestException> {
            instance.add(input)
        }

        // then
        assertThat(error.message, equalTo("There is a member that already is in the deal"))
    }

    @Test
    fun `Add a member id that does not exists`() {
        // given
        val input = AddDealTeamMembersInput(1, listOf(2, 3))

        every { memberRepository.findByMemberIds(any()) } returns listOf(mockk())

        every { getDealMemberRelationRepository.getByDealAndMember(any(), any()) } returns mockk()

        every { updateExternalCalendar(input.dealId) } returns Unit
        // when
        val error = org.junit.jupiter.api.assertThrows<BadRequestException> {
            instance.add(input)
        }

        // then
        assertThat(error.message, equalTo("There is a member that not exist"))
    }
}
