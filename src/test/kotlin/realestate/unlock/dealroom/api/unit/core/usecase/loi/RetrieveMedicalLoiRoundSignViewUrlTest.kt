package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.core.entity.loi.MemberSign
import realestate.unlock.dealroom.api.core.entity.loi.RetrieveLetterOfIntentSignViewUrlInput
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.gateway.sign.RetrieveSignViewUrlOutput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentSignRepository
import realestate.unlock.dealroom.api.core.usecase.email.GetApplicationLinkByMember
import realestate.unlock.dealroom.api.core.usecase.exception.deal.loi.LetterOfIntentNotFoundException
import realestate.unlock.dealroom.api.core.usecase.loi.RetrieveLetterOfIntentSignViewUrl
import realestate.unlock.dealroom.api.utils.entity.loi.LoiSignBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.net.URI
import java.time.OffsetDateTime

class RetrieveMedicalLoiRoundSignViewUrlTest {
    private val loiId: Long = 1
    private val dealId: Long = 1
    private val returnPath: String = "/some/path"
    private val buyer = MemberObjectMother.buyer()
    private val seller = MemberObjectMother.seller()
    private val sellerBroker = MemberObjectMother.sellerBroker()
    private val expectedSignViewOutput =
        RetrieveSignViewUrlOutput(URI.create("http://www.some-sign-url.com"))
    private val expectedCarbonCopyViewOutput =
        RetrieveSignViewUrlOutput(URI.create("http://www.some-carbon-copy-url.com"))

    private val letterOfIntentSignRepository: LetterOfIntentSignRepository = mockk()
    private val getApplicationLinkByMember: GetApplicationLinkByMember = mockk()
    private val signGateway: SignGateway = mockk {
        every { retrieveSignerViewUrl(any()) } returns expectedSignViewOutput
        every { retrieveCarbonCopyViewUrl(any()) } returns expectedCarbonCopyViewOutput
    }
    private val target = RetrieveLetterOfIntentSignViewUrl(letterOfIntentSignRepository, getApplicationLinkByMember, signGateway)

    @Test
    fun `buyer member should retrieve carbon copy view url`() {
        // given
        val givenLoiSign = givenLoiSign()
        val givenApplicationLink = anyString()
        val givenInput = givenInput(buyer)
        every { letterOfIntentSignRepository.findByLoiId(eq(loiId)) } returns givenLoiSign
        every { getApplicationLinkByMember.get(eq(buyer)) } returns givenApplicationLink

        // when
        val result = target.execute(givenInput)

        // then
        assertThat(result, IsEqual(expectedCarbonCopyViewOutput.viewUrl))
    }

    @Test
    fun `seller signer can retrieve sign view url`() {
        // given
        val givenLoiSign = givenLoiSign()
        val givenApplicationLink = anyString()
        val givenInput = givenInput(seller)
        every { letterOfIntentSignRepository.findByLoiId(eq(loiId)) } returns givenLoiSign
        every { getApplicationLinkByMember.get(eq(seller)) } returns givenApplicationLink

        // when
        val result = target.execute(givenInput)

        // then
        assertThat(result, IsEqual(expectedSignViewOutput.viewUrl))
    }

    @Test
    fun `member can retrieve carbon copy url`() {
        // given
        val givenLoiSign = givenLoiSign()
        val givenApplicationLink = anyString()
        val givenInput = givenInput(sellerBroker)
        every { letterOfIntentSignRepository.findByLoiId(eq(loiId)) } returns givenLoiSign
        every { getApplicationLinkByMember.get(eq(sellerBroker)) } returns givenApplicationLink

        // when
        val result = target.execute(givenInput)

        // then
        assertThat(result, IsEqual(expectedCarbonCopyViewOutput.viewUrl))
    }

    @Test
    fun `throws LetterOfIntentNotFoundException`() {
        // given
        val givenInput = givenInput(buyer)
        every { letterOfIntentSignRepository.findByLoiId(eq(loiId)) } returns null

        // when
        val action = { target.execute(givenInput) }

        // then
        assertThrows<LetterOfIntentNotFoundException> { action() }
    }

    private fun givenInput(member: Member) = RetrieveLetterOfIntentSignViewUrlInput(loiId, dealId, member, returnPath)

    private fun givenLoiSign() = LoiSignBuilder().apply {
        buyerSign = MemberSign(buyer.id.toString(), OffsetDateTime.now(), MemberTypeEnum.BUYER)
        sellerSign = MemberSign(
            id = seller.id.toString(),
            completedAt = null,
            memberType = MemberTypeEnum.SELLER,
            email = seller.email,
            firstName = seller.firstName,
            lastName = seller.lastName
        )
    }.build()
}
