package realestate.unlock.dealroom.api.unit.core.usecase.lease

import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.usecase.deal.documents.AddComment
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidCommentException
import realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.InteractWithDocumentRoundTest
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class AddCommentTest : InteractWithDocumentRoundTest() {

    companion object {
        private val now = Instant.now()
    }

    private lateinit var addComment: AddComment

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        addComment = AddComment(
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            clock = clock,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `can comment a lease round`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val leaseId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = leaseId)
        val lease = givenDocumentWithCurrentRound(id = leaseId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.LEASE)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "my comment",
            member = user.member,
            documentType = DocumentType.LEASE
        )

        val leaseUpdated = addComment.update(input)

        Assertions.assertEquals(lease.status, leaseUpdated.status)
        Assertions.assertEquals(lease.currentRoundId, leaseUpdated.currentRoundId)
        Assertions.assertEquals(OffsetDateTime.now(clock), leaseUpdated.updatedAt)
        assertInteractionWasCreated(draftDocuments, input, Interaction.Type.COMMENT)
    }

    @Test
    fun `cannot add empty comment`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val leaseId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = leaseId, cleanVersionFileId = null)
        givenDocumentWithCurrentRound(id = leaseId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.LEASE)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "",
            member = user.member,
            documentType = DocumentType.LEASE
        )

        assertThrows<InvalidCommentException> { addComment.update(input) }
    }
}
