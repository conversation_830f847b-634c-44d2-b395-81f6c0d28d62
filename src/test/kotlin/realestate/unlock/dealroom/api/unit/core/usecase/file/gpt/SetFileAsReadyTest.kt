package realestate.unlock.dealroom.api.unit.core.usecase.file.gpt

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.reports.Report
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileToken
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileTokenStatus
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.keypilot.DocumentQuestion
import realestate.unlock.dealroom.api.core.repository.deal.report.DealReportRepository
import realestate.unlock.dealroom.api.core.repository.file.FileTokenRepository
import realestate.unlock.dealroom.api.core.repository.keypilot.DocumentSuggestedQuestionsRepository
import realestate.unlock.dealroom.api.core.repository.keypilot.DocumentTypePromptRepository
import realestate.unlock.dealroom.api.core.repository.keypilot.DocumentTypePromptRepository.Prompt
import realestate.unlock.dealroom.api.core.usecase.file.gpt.AskQuestion
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SetFileAsReady
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString

@ExtendWith(MockKExtension::class)
class SetFileAsReadyTest {

    companion object {
        const val ASK_FOR_SUMMARY_QUESTION = "Write a short summary, and then write the findings in bullet point format, and indicate what page you got each piece of information from"
    }

    @MockK
    private lateinit var askQuestion: AskQuestion

    @MockK
    private lateinit var fileTokenRepository: FileTokenRepository

    @MockK
    private lateinit var dealReportRepository: DealReportRepository

    @MockK
    private lateinit var documentSuggestedQuestionsRepository: DocumentSuggestedQuestionsRepository

    @MockK
    private lateinit var documentTypePromptRepository: DocumentTypePromptRepository

    private lateinit var setFileAsReady: SetFileAsReady

    @BeforeEach
    fun setUp() {
        setFileAsReady = SetFileAsReady(
            askQuestion = askQuestion,
            fileTokenRepository = fileTokenRepository,
            dealReportRepository = dealReportRepository,
            documentSuggestedQuestionsRepository = documentSuggestedQuestionsRepository,
            documentTypePromptRepository = documentTypePromptRepository
        )
    }

    @Test
    fun `it updates the file status to ready and asks for the summary`() {
        val slot = slot<FileToken>()
        val fileToken = FileToken(anyString(), anyString(), FileTokenStatus.PROCESSING, TokenFileType.TASK, anyId())
        val givenPrompt = Prompt(anyString())
        val authToken = anyString()

        every { fileTokenRepository.findByFileId(fileToken.kFileId) } returns fileToken
        every { fileTokenRepository.update(capture(slot)) } answers { slot.captured }
        every { documentTypePromptRepository.getPromptForTaskFile(fileToken.kFileId) } returns givenPrompt
        every { askQuestion(AskQuestion.Input(fileToken.kFileId, ASK_FOR_SUMMARY_QUESTION, givenPrompt.prompt, null, authToken = authToken)) } returns anyString()

        setFileAsReady(SetFileAsReady.Input(fileId = fileToken.kFileId, token = fileToken.token, authToken = authToken))

        verify { fileTokenRepository.update(fileToken.copy(status = FileTokenStatus.READY)) }
        verify {
            askQuestion(
                AskQuestion.Input(
                    fileId = fileToken.kFileId,
                    question = ASK_FOR_SUMMARY_QUESTION,
                    memberId = null,
                    prompt = givenPrompt.prompt,
                    authToken = authToken
                )
            )
        }
        verify { fileTokenRepository.update(fileToken.copy(status = FileTokenStatus.INITIAL_QUESTIONS_READY)) }
    }

    @Test
    fun `when file status is ready and it a report with questions to send automatically, question are sent`() {
        val reportType = anyString()
        val report = mockk<Report>().also { every { it.type } returns reportType }
        val questions = listOf(DocumentQuestion(anyString(), 1, false), DocumentQuestion(anyString(), 2, true))
        val fileToken = FileToken(anyString(), anyString(), FileTokenStatus.PROCESSING, TokenFileType.REPORT, anyId())
        val givenPrompt = Prompt(anyString())
        val authToken = anyString()
        every { fileTokenRepository.findByFileId(fileToken.kFileId) } returns fileToken
        every { documentTypePromptRepository.getPrompt(document = TokenFileType.REPORT.name, documentType = reportType) } returns givenPrompt
        val slot = slot<FileToken>()
        every { fileTokenRepository.update(capture(slot)) } answers { slot.captured }
        every { askQuestion(any()) } returns anyString()
        every { dealReportRepository.findByKFileId(fileToken.kFileId) } returns report
        every { documentSuggestedQuestionsRepository.find(document = "REPORT", documentType = reportType) } returns questions

        setFileAsReady(SetFileAsReady.Input(fileId = fileToken.kFileId, token = fileToken.token, authToken = authToken))

        verify { fileTokenRepository.update(fileToken.copy(status = FileTokenStatus.READY)) }
        verify {
            askQuestion(
                AskQuestion.Input(
                    fileId = fileToken.kFileId,
                    question = questions[1].question,
                    memberId = null,
                    prompt = givenPrompt.prompt,
                    authToken = authToken
                )
            )
        }
    }
}
