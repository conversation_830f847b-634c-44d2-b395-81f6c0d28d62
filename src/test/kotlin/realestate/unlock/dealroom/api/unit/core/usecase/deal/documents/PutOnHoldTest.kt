package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.*
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.repository.document.OnHoldPreviousStatusRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.PutOnHold
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.PutOnHoldImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class PutOnHoldTest : InteractWithDocumentRoundTest() {

    companion object {
        private val now = Instant.now()
    }

    @MockK
    private lateinit var putOnHoldImpactOnTask: PutOnHoldImpactOnTask

    @MockK(relaxed = true)
    private lateinit var onHoldPreviousStatusRepository: OnHoldPreviousStatusRepository

    private lateinit var putOnHold: PutOnHold

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        putOnHold = PutOnHold(
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            putOnHoldImpactOnTask = putOnHoldImpactOnTask,
            onHoldPreviousStatusRepository = onHoldPreviousStatusRepository,
            clock = clock,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `can put psa on hold`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val (psaId, draftDocuments) = pasInDraft()
        val psa = givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.PSA)
        val oldTaskStatus = TaskStatus.TO_DO
        every { putOnHoldImpactOnTask.invoke(psa, user.member) } returns oldTaskStatus

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "on hold",
            member = user.member,
            documentType = DocumentType.PSA
        )

        val psaUpdated = putOnHold.update(input)

        assertDocumentWasUpdated(psaUpdated, DocumentStatus.ON_HOLD)
        assertInteractionWasCreated(draftDocuments, input, Interaction.Type.ON_HOLD)
        verify { onHoldPreviousStatusRepository.save(OnHoldPreviousStatus(psaId, psa.status, oldTaskStatus)) }
    }

    @Test
    fun `cannot put psa on hold when status is invalid`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val (psaId, draftDocuments) = pasInDraft()
        givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.ON_HOLD, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "on hold",
            member = user.member,
            documentType = DocumentType.PSA
        )

        assertThrows<InvalidDocumentStatusException> { putOnHold.update(input) }
    }

    private fun pasInDraft(): Pair<Long, DraftDocuments> {
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId)
        return Pair(psaId, draftDocuments)
    }
}
