package realestate.unlock.dealroom.api.unit.core.usecase.lease

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.DraftDocuments
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.repository.document.DocumentRoundRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.ConfirmDraftDocuments
import realestate.unlock.dealroom.api.core.usecase.deal.documents.DocumentDraftInput
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FileType
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDraftFilesException
import realestate.unlock.dealroom.api.core.usecase.file.ConfirmStagedFile
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class ConfirmDraftDocumentsTest {

    @MockK
    private lateinit var confirmStagedFile: ConfirmStagedFile

    @MockK(relaxed = true)
    private lateinit var documentRoundRepository: DocumentRoundRepository

    private lateinit var confirmDraftDocuments: ConfirmDraftDocuments

    @BeforeEach
    fun setUp() {
        confirmDraftDocuments = ConfirmDraftDocuments(
            confirmStagedFile = confirmStagedFile,
            documentRoundRepository = documentRoundRepository
        )
    }

    @Test
    fun `cannot confirm draft documents without files`() {
        val lease = DocumentBuilder().apply { this.type = DocumentType.LEASE }.build()
        val input = DocumentDraftInput(
            dealId = anyId(),
            cleanVersionFile = null,
            redLineVersionFile = null,
            comment = "initial version",
            member = MemberObjectMother.buyer(),
            documentType = DocumentType.LEASE
        )

        assertThrows<InvalidDraftFilesException> { confirmDraftDocuments(input, lease) }
    }

    @Test
    fun `can confirm draft documents with both files`() {
        val nextId = nextRoundId()
        val lease = DocumentBuilder().apply { this.type = DocumentType.LEASE }.build()
        val leaseNextId = lease.copy(currentRoundId = nextId)
        val input = DocumentDraftInput(
            dealId = anyId(),
            cleanVersionFile = givenFileInput(),
            redLineVersionFile = givenFileInput(),
            comment = "initial version",
            member = MemberObjectMother.buyer(),
            documentType = DocumentType.LEASE
        )

        val files = givenFilesConfirmed(leaseNextId, input)

        val draftDocumentsConfirmed = confirmDraftDocuments(input, lease)

        assertDraftConfirmed(leaseNextId, input, files, draftDocumentsConfirmed)
        assertNewDocumentWasSaved(draftDocumentsConfirmed)
    }

    @Test
    fun `can confirm draft documents with only one files`() {
        val nextId = nextRoundId()
        val lease = DocumentBuilder().apply { this.type = DocumentType.LEASE }.build()
        val leaseNextId = lease.copy(currentRoundId = nextId)
        val input = DocumentDraftInput(
            dealId = anyId(),
            cleanVersionFile = null,
            redLineVersionFile = givenFileInput(),
            comment = "initial version",
            member = MemberObjectMother.buyer(),
            documentType = DocumentType.LEASE
        )

        val files = givenFilesConfirmed(leaseNextId, input)

        val draftDocumentsConfirmed = confirmDraftDocuments(input, lease)

        assertDraftConfirmed(leaseNextId, input, files, draftDocumentsConfirmed)
        assertNewDocumentWasSaved(draftDocumentsConfirmed)
    }

    private fun nextRoundId(): Long =
        anyId().also {
            every { documentRoundRepository.nextId() } returns it
        }

    private fun assertDraftConfirmed(document: Document, input: DocumentDraftInput, files: List<File>, draftDocumentsConfirmed: DraftDocuments) {
        assertEquals(document.id, draftDocumentsConfirmed.documentId)
        assertEquals(input.comment, draftDocumentsConfirmed.comment)
        assertEquals(input.member.id, draftDocumentsConfirmed.memberId)
        input.cleanVersionFile?.let { fileInput ->
            assertEquals(files.first { it.kFileId == fileInput.uid }.id, draftDocumentsConfirmed.cleanVersionFileId)
        }
        input.redLineVersionFile?.let { fileInput ->
            assertEquals(files.first { it.kFileId == fileInput.uid }.id, draftDocumentsConfirmed.redLineVersionFileId)
        }
    }

    private fun givenFileInput() = FileInput(anyString(), anyString())

    private fun assertNewDocumentWasSaved(expectedDraft: DraftDocuments) {
        verify {
            documentRoundRepository.save(expectedDraft)
        }
    }

    private fun givenFilesConfirmed(document: Document, input: DocumentDraftInput): List<File> {
        val cleanVersion = input.cleanVersionFile?.let {
            fileToConfirm(it, document, input, FileType.CLEAN_VERSION)
        }

        val readLineVersion = input.redLineVersionFile?.let {
            fileToConfirm(it, document, input, FileType.RED_LINE_VERSION)
        }

        val filesToConfirm = listOfNotNull(cleanVersion, readLineVersion)

        return filesToConfirm.map {
            File(
                id = anyId(),
                name = it.name,
                kFileId = it.kFileId,
                memberId = input.member.id,
                createdAt = OffsetDateTime.now(Clock.systemUTC())
            )
        }.also {
            every { confirmStagedFile(filesToConfirm) } returns it
        }
    }

    private fun fileToConfirm(
        it: FileInput,
        document: Document,
        input: DocumentDraftInput,
        type: FileType
    ) = ConfirmStagedFile.FileToConfirm(
        name = it.name,
        kFileId = it.uid,
        path = "deal/${document.dealId}/lease/${document.id}/draft/${document.currentRoundId}_${type.name}",
        member = input.member

    )
}
