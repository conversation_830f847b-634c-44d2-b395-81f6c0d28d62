package realestate.unlock.dealroom.api.unit.core.usecase.auth

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.gateway.auth.AuthorizationService
import realestate.unlock.dealroom.api.core.usecase.auth.ValidateToken
import realestate.unlock.dealroom.api.core.usecase.member.GetMemberByAuthId
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.UnauthorizedException
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.security.anyUserToken

@ExtendWith(MockKExtension::class)
class ValidateTokenTest {

    @MockK
    private lateinit var getMemberByAuthId: GetMemberByAuthId

    @MockK
    private lateinit var authorizationService: AuthorizationService
    private lateinit var validateToken: ValidateToken

    @BeforeEach
    fun setUp() {
        validateToken = ValidateToken(
            authorizationService = authorizationService,
            getMemberByAuthId = getMemberByAuthId
        )
    }

    @Test
    fun `returns an user when token is valid`() {
        val token = anyString()
        val userToken = anyUserToken()
        val member = MemberObjectMother.buyer()
        every { authorizationService.validate(token) } returns userToken
        every { getMemberByAuthId.get(userToken.id) } returns member

        val result = validateToken.validate(token)

        assertEquals(
            AuthenticatedUser(member, userToken, token),
            result
        )
    }

    @Test
    fun `throws UnauthorizedException when token is invalid`() {
        val token = anyString()
        val error = RuntimeException("invalid token")
        every { authorizationService.validate(token) } throws error

        val result = assertThrows<UnauthorizedException> { validateToken.validate(token) }

        assertEquals(error, result.cause)
    }
}
