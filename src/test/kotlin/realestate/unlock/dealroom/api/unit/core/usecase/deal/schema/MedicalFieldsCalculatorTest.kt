package realestate.unlock.dealroom.api.unit.core.usecase.deal.schema

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.usecase.deal.schema.FieldsCalculatorUtils.defaultScale
import realestate.unlock.dealroom.api.core.usecase.deal.schema.MedicalFieldsCalculator
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entity.deal.DealDataBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import java.math.BigDecimal

class MedicalFieldsCalculatorTest {

    @Test
    fun `can generate calculated fields`() {
        val deal = DealDataBuilder().apply {
            this.property = PropertyBuilder().apply {
                askingPrice = BigDecimal.valueOf(200)
                squareFootage = BigDecimal.valueOf(200)
            }.build()
        }.build()
        val data = mapOf(
            "offerNoi" to BigDecimal.valueOf(50).defaultScale(),
            "offerCapRate" to BigDecimal.valueOf(25).defaultScale(),
            "askingNoi" to BigDecimal.valueOf(10).defaultScale()
        )

        val result = JsonMapper.decode(JsonMapper.encode(data), MedicalFieldsCalculator::class.java).calculate(deal)

        val expectedData = mapOf(
            "askingCapRate" to BigDecimal.valueOf(5).defaultScale(),
            "offerNoiPsf" to BigDecimal.valueOf(0.250).defaultScale()
        )

        assertEquals(expectedData, result)
    }

    @Test
    fun `can handle value 0 on generate calculated fields`() {
        val deal = DealDataBuilder().apply { this.property = PropertyBuilder().apply { askingPrice = BigDecimal.ZERO }.build() }.build()
        val data = mapOf(
            "offerNoi" to BigDecimal.valueOf(0).defaultScale(),
            "offerCapRate" to BigDecimal.valueOf(0).defaultScale(),
            "askingNoi" to BigDecimal.valueOf(10).defaultScale()
        )

        val result = JsonMapper.decode(JsonMapper.encode(data), MedicalFieldsCalculator::class.java).calculate(deal)

        val expectedData = mapOf<String, Any?>(
            "askingCapRate" to null,
            "offerNoiPsf" to BigDecimal.ZERO.defaultScale()
        )

        assertEquals(expectedData, result)
    }

    @Test
    fun `can handle null values`() {
        val deal = DealDataBuilder().apply { this.property = PropertyBuilder().apply { askingPrice = BigDecimal.valueOf(200) }.build() }.build()
        val data = mapOf("askingCapRate" to BigDecimal.valueOf(0.05).defaultScale())

        val result = JsonMapper.decode(JsonMapper.encode(data), MedicalFieldsCalculator::class.java).calculate(deal)

        val expectedData = mapOf<String, Any?>(
            "askingCapRate" to null,
            "offerNoiPsf" to null
        )

        assertEquals(expectedData, result)
    }
}
