package realestate.unlock.dealroom.api.unit.core.usecase.deal.schema

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.usecase.deal.schema.FieldsCalculatorUtils.defaultScale
import realestate.unlock.dealroom.api.core.usecase.deal.schema.MultifamilyFieldsCalculator
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.deal.DealDataBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import java.math.BigDecimal

class MultifamilyFieldsCalculatorTest {

    @Test
    fun `can generate calculated fields`() {
        val deal = givenDeal(
            offerPrice = BigDecimal.valueOf(2000000),
            squareFootage = BigDecimal.valueOf(1000),
            askingPrice = BigDecimal.valueOf(2200000),
            units = 10
        )
        val data = mapOf(
            "stabilizedNoi" to BigDecimal.valueOf(500000).defaultScale(),
            "capexUnit" to BigDecimal.valueOf(8000).defaultScale(),
            "t12Noi" to BigDecimal.valueOf(400000).defaultScale()
        )

        val result = JsonMapper.decode(JsonMapper.encode(data), MultifamilyFieldsCalculator::class.java).calculate(deal)

        val expectedData = mapOf(
            "stabilizedYoc" to BigDecimal.valueOf(24).defaultScale(),
            "askingPriceUnit" to 220000L,
            "askingPriceSf" to 2200L,
            "offerPriceUnit" to 200000L,
            "offerPriceSf" to 2000L,
            "goingInCap" to BigDecimal("20").defaultScale()
        )

        Assertions.assertEquals(expectedData, result)
    }

    @Test
    fun `can handle value 0 on generate calculated fields`() {
        val deal = givenDeal(
            offerPrice = BigDecimal.valueOf(2000000),
            squareFootage = BigDecimal.ZERO,
            askingPrice = BigDecimal.valueOf(2200000),
            units = 10
        )
        val data = mapOf(
            "stabilizedNoi" to BigDecimal.valueOf(500000).defaultScale(),
            "capexUnit" to BigDecimal.valueOf(8000).defaultScale(),
            "t12Noi" to BigDecimal.valueOf(400000).defaultScale()
        )

        val result = JsonMapper.decode(JsonMapper.encode(data), MultifamilyFieldsCalculator::class.java).calculate(deal)

        val expectedData = mapOf(
            "stabilizedYoc" to BigDecimal.valueOf(24).defaultScale(),
            "askingPriceUnit" to 220000L,
            "askingPriceSf" to null,
            "offerPriceUnit" to 200000L,
            "offerPriceSf" to null,
            "goingInCap" to BigDecimal("20").defaultScale()
        )

        Assertions.assertEquals(expectedData, result)
    }

    @Test
    fun `can handle null values`() {
        val deal = givenDeal(
            offerPrice = BigDecimal.valueOf(2000000),
            squareFootage = null,
            askingPrice = BigDecimal.valueOf(2200000),
            units = 10
        )
        val data = mapOf(
            "stabilizedNoi" to BigDecimal.valueOf(500000).defaultScale(),
            "capexUnit" to BigDecimal.valueOf(8000).defaultScale(),
            "t12Noi" to BigDecimal.valueOf(400000).defaultScale()
        )

        val result = JsonMapper.decode(JsonMapper.encode(data), MultifamilyFieldsCalculator::class.java).calculate(deal)

        val expectedData = mapOf(
            "stabilizedYoc" to BigDecimal.valueOf(24).defaultScale(),
            "askingPriceUnit" to 220000L,
            "askingPriceSf" to null,
            "offerPriceUnit" to 200000L,
            "offerPriceSf" to null,
            "goingInCap" to BigDecimal("20").defaultScale()
        )

        Assertions.assertEquals(expectedData, result)
    }

    @Test
    fun `fix bug percentage going-cap`() {
        val deal = givenDeal(
            offerPrice = BigDecimal.valueOf(5500000),
            squareFootage = BigDecimal.ZERO,
            askingPrice = BigDecimal.valueOf(2200000),
            units = 10
        )
        val data = mapOf(
            "t12Noi" to BigDecimal.valueOf(146184).defaultScale()
        )

        val result = JsonMapper.decode(JsonMapper.encode(data), MultifamilyFieldsCalculator::class.java).calculate(deal)

        val expectedData = mapOf(
            "stabilizedYoc" to null,
            "askingPriceUnit" to 220000L,
            "askingPriceSf" to null,
            "offerPriceUnit" to 550000L,
            "offerPriceSf" to null,
            "goingInCap" to BigDecimal("2.7").defaultScale()
        )

        Assertions.assertEquals(expectedData, result)
    }

    private fun givenDeal(
        offerPrice: BigDecimal?,
        squareFootage: BigDecimal?,
        askingPrice: BigDecimal?,
        units: Long?
    ) =
        DealDataBuilder().apply {
            this.deal = DealBuilder().apply {
                this.offerPrice = offerPrice
            }.build()
            this.property = PropertyBuilder().apply {
                this.type = PropertyType.MULTIFAMILY
                this.squareFootage = squareFootage
                this.askingPrice = askingPrice
                this.units = units
            }.build()
        }.build()
}
