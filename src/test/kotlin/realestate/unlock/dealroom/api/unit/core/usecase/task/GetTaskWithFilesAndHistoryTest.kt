package realestate.unlock.dealroom.api.unit.core.usecase.task

import io.mockk.every
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.task.TaskWithFilesAndHistory
import realestate.unlock.dealroom.api.core.entity.task.file.TaskFile
import realestate.unlock.dealroom.api.core.entity.task.history.TaskHistory
import realestate.unlock.dealroom.api.core.repository.task.file.TaskFileRepository
import realestate.unlock.dealroom.api.core.repository.task.history.TaskHistoryRepository
import realestate.unlock.dealroom.api.core.usecase.task.GetCompleteTask
import realestate.unlock.dealroom.api.core.usecase.task.GetTaskById
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder

class GetTaskWithFilesAndHistoryTest {
    private val task = TaskBuilder().withId(1).build()
    private val taskHistory = listOf<TaskHistory>()
    private val taskFiles = listOf<TaskFile>()

    private val getTaskById: GetTaskById = mockk {
        every { get(task.id) } returns task
    }

    private val findTaskHistoryByTaskIdRepository: TaskHistoryRepository = mockk {
        every { findByTaskId(task.id) } returns taskHistory
    }
    private val taskFileRepository: TaskFileRepository = mockk {
        every { findByTaskId(task.id) } returns taskFiles
    }

    private val target = GetCompleteTask(
        getTaskById,
        findTaskHistoryByTaskIdRepository,
        taskFileRepository
    )

    @Test
    fun `gets a task by id with files, history and member`() {
        // given
        val expectedCompleteTask = completeTask()

        // when
        val result = target.get(task.id)

        // then
        assertThat(result, IsEqual(expectedCompleteTask))
    }

    private fun completeTask() = TaskWithFilesAndHistory(
        task = task,
        files = taskFiles,
        history = taskHistory
    )
}
