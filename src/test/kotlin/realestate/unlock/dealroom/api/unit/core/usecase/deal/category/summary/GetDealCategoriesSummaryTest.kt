package realestate.unlock.dealroom.api.unit.core.usecase.deal.category.summary

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.deal.category.DealCategory
import realestate.unlock.dealroom.api.core.entity.task.TaskStatusTranslator
import realestate.unlock.dealroom.api.core.repository.category.FindCategoryByKeyRepository
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.deal.category.summary.GetDealCategoriesSummary
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.utils.entity.CategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.deal.DealCategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId

@ExtendWith(MockKExtension::class)
class GetDealCategoriesSummaryTest {

    @MockK
    private lateinit var dealCategoryRepository: DealCategoryRepository

    @MockK(relaxed = true)
    private lateinit var findCategoryByKeyRepository: FindCategoryByKeyRepository

    @MockK
    private lateinit var taskRepository: TaskRepository

    private lateinit var getDealCategoriesSummary: GetDealCategoriesSummary

    @BeforeEach
    fun setUp() {
        getDealCategoriesSummary = GetDealCategoriesSummary(
            dealCategoryRepository = dealCategoryRepository,
            findCategoryByKeyRepository = findCategoryByKeyRepository,
            taskRepository = taskRepository
        )
    }

    @Test
    fun `when seller new has psa task, it is summarized`() {
        val deal = DealBuilder().build()
        val seller = MemberObjectMother.seller()
        val stageFilter: Stage? = null
        val dealCategory = givenDealCategory(deal, stageFilter)
        val task = givenTaskFor(typeKey = "psa", dealCategory = dealCategory)

        val result = getDealCategoriesSummary.get(dealId = deal.id, stageFilter = stageFilter, taskStatusTranslators = emptySet(), member = seller)

        assert(result.isNotEmpty())
    }

    @Test
    fun `when buyer has new psa task, it is not summarized`() {
        val deal = DealBuilder().build()
        val buyer = MemberObjectMother.buyer()
        val stageFilter: Stage? = null
        val dealCategory = givenDealCategory(deal, stageFilter)
        val task = givenTaskFor(typeKey = "psa", dealCategory = dealCategory)

        val result = getDealCategoriesSummary.get(dealId = deal.id, stageFilter = stageFilter, taskStatusTranslators = emptySet(), member = buyer)

        assert(result.isEmpty())
    }

    @Test
    fun `when buyer has old psa task, it is summarized`() {
        val deal = DealBuilder().build()
        val buyer = MemberObjectMother.buyer()
        val stageFilter: Stage = Stage.CLOSING
        val dealCategory = givenDealCategory(deal, stageFilter)
        val task = givenTaskFor(typeKey = "document_back_and_forth", dealCategory = dealCategory)

        val result = getDealCategoriesSummary.get(dealId = deal.id, stageFilter = stageFilter, taskStatusTranslators = emptySet(), member = buyer)

        assert(result.isNotEmpty())
    }

    @Test
    fun `params stage and statues are not allowed at the same time`() {
        val buyer = MemberObjectMother.buyer()

        assertThrows<BadRequestException> {
            getDealCategoriesSummary.get(dealId = anyId(), stageFilter = Stage.CLOSING, taskStatusTranslators = setOf(TaskStatusTranslator.DONE), member = buyer)
        }
    }

    private fun givenDealCategory(deal: Deal, stageFilter: Stage?) =
        DealCategoryBuilder().apply { dealId = deal.id }.build().also {
            if (stageFilter == null) {
                every { dealCategoryRepository.findByDealId(deal.id) } returns listOf(it)
            } else {
                every { dealCategoryRepository.findByDealIdAndStages(dealId = deal.id, Stage.values().filter { st -> st.ordinal <= stageFilter.ordinal }) } returns listOf(it)
            }

            every { findCategoryByKeyRepository.find(it.categoryKey) } returns CategoryBuilder().build()
        }

    private fun givenTaskFor(
        typeKey: String,
        dealCategory: DealCategory
    ) = TaskBuilder().withTypeKey(typeKey).build().also {
        every { taskRepository.findByDealCategories(listOf(dealCategory.id)) } returns listOf(it)
    }
}
