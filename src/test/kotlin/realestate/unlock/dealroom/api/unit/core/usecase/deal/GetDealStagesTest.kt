package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.category.Category
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.DealStage
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.deal.category.DealCategory
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags
import realestate.unlock.dealroom.api.core.repository.deal.category.FindDealStageByKeysRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealById
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealStages
import realestate.unlock.dealroom.api.core.usecase.deal.StagesFilterProvider
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class GetDealStagesTest {

    @MockK
    private lateinit var getDealById: GetDealById

    @MockK
    private lateinit var findDealStageByKeysRepository: FindDealStageByKeysRepository

    @MockK
    private lateinit var featureFlags: FeatureFlags

    private val clock = Clock.fixed(Instant.now(), ZoneId.systemDefault())

    private lateinit var target: GetDealStages

    @BeforeEach
    fun setUp() {
        target = GetDealStages(
            getDealById = getDealById,
            findDealStageByKeysRepository = findDealStageByKeysRepository,
            clock = clock,
            stagesFilterProvider = StagesFilterProvider(featureFlags)
        )
    }

    @Test
    fun `Given a new deal it should return empty values`() {
        // given
        val dealId = 2L
        val member = MemberObjectMother.seller(authId = anyString())
        givenDealInStage(id = dealId, stage = Stage.EVALUATION)
        givenClosingFeatureForUser(member, on = true)

        // when
        val result = target.get(dealId, member)

        // then
        assertThat(result, IsEqual(listOf()))
    }

    @Test
    fun `Given a deal in contract negotiation return empty values`() {
        // given
        val dealId = 2L
        val member = MemberObjectMother.seller(authId = anyString())
        givenDealInStage(id = dealId, stage = Stage.OFFER)
        givenClosingFeatureForUser(member, on = true)

        // when
        val result = target.get(dealId, member)

        // then
        assertThat(result, IsEqual(listOf()))
    }

    @Test
    fun `Given a deal archived negotiation return empty values`() {
        // given
        val dealId = 2L
        val member = MemberObjectMother.seller(authId = anyString())
        givenDealInStage(id = dealId, stage = Stage.NEGOTIATION, isArchived = true)
        givenClosingFeatureForUser(member, on = true)

        // when
        val result = target.get(dealId, member)

        // then
        assertThat(result, IsEqual(listOf()))
    }

    @Test
    fun `Given a deal in contract negotiation it should return the due diligence stage only`() {
        // given
        val dealId = 2L
        val member = MemberObjectMother.seller(authId = anyString())
        givenDealInStage(id = dealId, stage = Stage.NEGOTIATION)
        givenClosingFeatureForUser(member, on = true)
        createCategoriesForDeal()

        // when
        val result = target.get(dealId, member)

        // then
        assertThat(result, IsEqual(listOf(DealStage(key = "diligence", name = "Due diligence"))))
    }

    @Test
    fun `Given a deal in contract negotiation that is close to closing it should return the due diligence and closing stage`() {
        // given
        val dealId = 2L
        val member = MemberObjectMother.seller(authId = anyString())
        givenDealInStage(id = dealId, stage = Stage.NEGOTIATION, initialClosingDate = LocalDate.now(clock).plusDays(1))
        givenClosingFeatureForUser(member, on = true)
        createCategoriesForDeal()

        // when
        val result = target.get(dealId, member)

        // then
        assertThat(
            result,
            IsEqual(
                listOf(
                    DealStage(key = "diligence", name = "Due diligence"),
                    DealStage(key = "closing", name = "Closing")
                )
            )
        )
    }

    @Test
    fun `Given a deal in contract negotiation that is close to closing but feature is off, it should not closing stage`() {
        // given
        val dealId = 2L
        val member = MemberObjectMother.seller(authId = anyString())
        givenDealInStage(id = dealId, stage = Stage.NEGOTIATION, initialClosingDate = LocalDate.now(clock).plusDays(1))
        givenClosingFeatureForUser(member, on = false)
        createCategoriesForDeal()

        // when
        val result = target.get(dealId, member)

        // then
        assertThat(
            result,
            IsEqual(
                listOf(
                    DealStage(key = "diligence", name = "Due diligence")
                )
            )
        )
    }

    private fun givenDealInStage(id: Long, stage: Stage, initialClosingDate: LocalDate? = null, isArchived: Boolean = false): Deal =
        DealBuilder()
            .apply { this.id = id }
            .apply { this.propertyId = 2L }
            .apply { this.stage = stage }
            .apply { this.initialClosingDate = initialClosingDate }
            .also { if (isArchived) it.archived() }
            .build().also {
                every { getDealById.get(id) } returns it
            }

    private fun givenClosingFeatureForUser(member: Member, on: Boolean) {
        every { featureFlags.isOn(Feature.CLOSING, member.authId!!) } returns on
    }

    private fun createCategoriesForDeal(categoryKey: String = "insurance") {
        val dealCategoryId = 12L
        val dueDiligenceStage = DealStage(key = "diligence", name = "Due diligence")
        val closingStage = DealStage(key = "closing", name = "Closing")
        val dealCategoryKey = "insurance"
        val dealCategory = mockk<DealCategory>()
        val category = mockk<Category>()

        every { category.key } returns categoryKey
        every { dealCategory.id } returns dealCategoryId
        every { dealCategory.categoryKey } returns dealCategoryKey
        every { findDealStageByKeysRepository.find(listOf(Stage.DILIGENCE)) } returns listOf(dueDiligenceStage)
        every { findDealStageByKeysRepository.find(listOf(Stage.DILIGENCE, Stage.CLOSING)) } returns listOf(dueDiligenceStage, closingStage)
    }
}
