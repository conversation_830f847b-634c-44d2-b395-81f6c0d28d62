package realestate.unlock.dealroom.api.unit.core.entity.deal.category.summary

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.nullValue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.category.summary.DealCategoryTaskStatusSummary
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import java.time.LocalDate
import java.time.OffsetDateTime

class DealCategoryTaskStatusSummaryTest {

    @Test
    fun `It creates the object properly`() {
        val member = MemberObjectMother.buyer()
        val firstDoneTask = TaskBuilder().withStatus(TaskStatus.DONE).build()
        val secondDoneTask = TaskBuilder().withStatus(TaskStatus.DONE).build()
        val inReviewTask = TaskBuilder().withStatus(TaskStatus.IN_REVIEW).withAssignedTeam(MemberDealTeam.SELLER).build()
        val inBuyerReviewTask = TaskBuilder().withStatus(TaskStatus.IN_REVIEW).withAssignedTeam(MemberDealTeam.BUYER).build()
        val toDoTask = TaskBuilder().withStatus(TaskStatus.TO_DO).build()
        val rejectedTask = TaskBuilder().withStatus(TaskStatus.TO_DO)
            .withRejectionReason("rejection reason")
            .build()
        val rejectTaskNew = TaskBuilder().withStatus(TaskStatus.REJECTED).build()

        val taskList = listOf(
            toDoTask,
            firstDoneTask,
            secondDoneTask,
            inReviewTask,
            rejectedTask,
            inBuyerReviewTask,
            rejectTaskNew
        )

        var tasksStatusSummary = DealCategoryTaskStatusSummary.getSummaryFor(taskList, member)

        assertThat(tasksStatusSummary.toDo, equalTo(1))
        assertThat(tasksStatusSummary.done, equalTo(2))
        assertThat(tasksStatusSummary.inReview, equalTo(2)) // TODO: revisar
        assertThat(tasksStatusSummary.rejected, equalTo(2))
        assertThat(tasksStatusSummary.total, equalTo(taskList.size))

        tasksStatusSummary = DealCategoryTaskStatusSummary.getSummaryFor(taskList, MemberObjectMother.seller())

        assertThat(tasksStatusSummary.toDo, equalTo(4))
        assertThat(tasksStatusSummary.done, equalTo(2))
        assertThat(tasksStatusSummary.inReview, equalTo(1))
        assertThat(tasksStatusSummary.rejected, equalTo(2))
        assertThat(tasksStatusSummary.total, equalTo(taskList.size))
    }

    @Test
    fun `It find next due date`() {
        val now = OffsetDateTime.now()
        val firstDoneTask = TaskBuilder().withStatus(TaskStatus.DONE).updatedAt(now.minusMinutes(100)).build()
        val secondDoneTask = TaskBuilder().withStatus(TaskStatus.DONE).updatedAt(now.minusMinutes(80)).build()
        val inReviewTask = TaskBuilder().withStatus(TaskStatus.IN_REVIEW)
            .updatedAt(now.minusMinutes(10))
            .build()
        val toDoTask = TaskBuilder().withStatus(TaskStatus.TO_DO)
            .updatedAt(
                now.minusMinutes(20)
            ).build()
        val rejectedTask = TaskBuilder().withStatus(TaskStatus.TO_DO).withDueDate(LocalDate.MIN)
            .updatedAt(now.minusMinutes(70))
            .withRejectionReason("rejection reason")
            .build()
        val rejectTaskNew = TaskBuilder().withStatus(TaskStatus.REJECTED)
            .updatedAt(now.minusMinutes(50))
            .withDueDate(LocalDate.MAX).build()

        val taskList = listOf(
            toDoTask,
            firstDoneTask,
            secondDoneTask,
            inReviewTask,
            rejectedTask,
            rejectTaskNew
        )

        val tasksStatusSummary = DealCategoryTaskStatusSummary.getSummaryFor(taskList, MemberObjectMother.buyer())

        assertEquals(tasksStatusSummary.nextDueDate, LocalDate.MIN)
        assertEquals(tasksStatusSummary.lastUpdate, inReviewTask.updatedAt)
    }

    @Test
    fun `It find next due date null, because its not set`() {
        val firstDoneTask = TaskBuilder().withStatus(TaskStatus.DONE).withDueDate(LocalDate.now()).build()
        val secondDoneTask = TaskBuilder().withStatus(TaskStatus.DONE).build()
        val inReviewTask = TaskBuilder().withStatus(TaskStatus.IN_REVIEW).build()
        val toDoTask = TaskBuilder().withStatus(TaskStatus.TO_DO).build()
        val rejectedTask = TaskBuilder().withStatus(TaskStatus.TO_DO)
            .withRejectionReason("rejection reason")
            .build()
        val rejectTaskNew = TaskBuilder().withStatus(TaskStatus.REJECTED).build()

        val taskList = listOf(
            toDoTask,
            firstDoneTask,
            secondDoneTask,
            inReviewTask,
            rejectedTask,
            rejectTaskNew
        )

        val tasksStatusSummary = DealCategoryTaskStatusSummary.getSummaryFor(taskList, MemberObjectMother.buyer())

        Assertions.assertNull(tasksStatusSummary.nextDueDate)
    }

    @Test
    fun `it works with empty list of tasks`() {
        val result = DealCategoryTaskStatusSummary.getSummaryFor(tasks = listOf(), member = MemberObjectMother.buyer())

        assertThat(result.toDo, equalTo(0))
        assertThat(result.done, equalTo(0))
        assertThat(result.rejected, equalTo(0))
        assertThat(result.inReview, equalTo(0))
        assertThat(result.total, equalTo(0))
        assertThat(result.nextDueDate, nullValue())
        assertThat(result.lastUpdate, nullValue())
        assertThat(result.disabled, equalTo(0))
    }
}
