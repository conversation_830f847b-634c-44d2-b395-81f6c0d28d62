package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.core.exception.InvalidTokenException
import realestate.unlock.dealroom.api.core.gateway.sign.RetrieveSignViewUrlOutput
import realestate.unlock.dealroom.api.core.gateway.sign.RetrieveViewUrlInput
import realestate.unlock.dealroom.api.core.gateway.sign.SignDocumentType
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentSignRepository
import realestate.unlock.dealroom.api.core.usecase.exception.deal.loi.LetterOfIntentNotFoundException
import realestate.unlock.dealroom.api.core.usecase.loi.SendLoiSigningEmail
import realestate.unlock.dealroom.api.core.usecase.loi.TransformTokenToSignViewUrl
import realestate.unlock.dealroom.api.infrastructure.configuration.model.JwtGatewayConfig
import realestate.unlock.dealroom.api.infrastructure.gateway.jwt.JWTAuthGateway
import realestate.unlock.dealroom.api.utils.entity.loi.LoiSignBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.MemberSignBuilder
import java.net.URI
import java.time.Clock
import java.time.OffsetDateTime

class TransformTokenToSignViewUrlTest {

    private val jwtGateway = JWTAuthGateway(
        clock = Clock.systemUTC(),
        jwtGatewayConfig = JwtGatewayConfig(
            issuer = "issuer",
            secret = "secret",
            tokenExpirationInSeconds = 1000
        )
    )

    private val letterOfIntentSignRepository: LetterOfIntentSignRepository = mockk(relaxed = true)
    private val signGateway: SignGateway = mockk(relaxed = true)
    private lateinit var target: TransformTokenToSignViewUrl

    @BeforeEach
    fun setUp() {
        target = TransformTokenToSignViewUrl(
            jwtGateway = jwtGateway,
            letterOfIntentSignRepository = letterOfIntentSignRepository,
            signGateway = signGateway
        )
    }

    @Test
    fun `can read token and retrieve url`() {
        val loiSigners = LoiSignBuilder().build()
        val token = buildToken(loiSigners.sellerSign.id, loiSigners.loiId)
        val input = slot<RetrieveViewUrlInput>()
        every { signGateway.retrieveSignerViewUrl(capture(input)) } returns RetrieveSignViewUrlOutput(
            viewUrl = URI("http://ole.com.ar")
        )
        every { letterOfIntentSignRepository.findByLoiId(loiSigners.loiId) } returns loiSigners

        target.invoke(token, "www.return.com")

        Assertions.assertEquals(input.captured.signingId, loiSigners.signingId)
        Assertions.assertEquals(input.captured.recipient.id, loiSigners.sellerSign.id)
        Assertions.assertEquals(input.captured.recipient.email, loiSigners.sellerSign.email)
        Assertions.assertEquals(input.captured.recipient.name, loiSigners.sellerSign.fullName)
    }

    @Test
    fun `token is invalid`() {
        val loiSigners = LoiSignBuilder().build()
        val token = buildToken(loiSigners.sellerSign.id, loiSigners.loiId).removeRange(0, 1)
        assertThrows<InvalidTokenException> { target.invoke(token, "www.return.com") }
    }

    @Test
    fun `signer already sign, do not retrieve url`() {
        val loiSigners = LoiSignBuilder().apply {
            this.sellerSign = MemberSignBuilder()
                .apply { this.completedAt = OffsetDateTime.now() }.build()
        }.build()
        val token = buildToken(loiSigners.sellerSign.id, loiSigners.loiId)
        every { letterOfIntentSignRepository.findByLoiId(loiSigners.loiId) } returns loiSigners
        assertThrows<LetterOfIntentNotFoundException> { target.invoke(token, "www.return.com") }
    }

    private fun buildToken(signerId: String, loiId: Long) =
        mapOf(
            SendLoiSigningEmail.MEMBER_ID to signerId,
            SendLoiSigningEmail.LOI_ID to loiId.toString(),
            SendLoiSigningEmail.DOC_TYPE to SignDocumentType.LOI.name,
            SendLoiSigningEmail.APP_LINK to "www.dealroom.com"
        ).let {
            jwtGateway.generateToken(it)
        }
}
