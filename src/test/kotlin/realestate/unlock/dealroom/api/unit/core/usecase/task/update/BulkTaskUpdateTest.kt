package realestate.unlock.dealroom.api.unit.core.usecase.task.update

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.entity.task.TaskWithFilesAndHistory
import realestate.unlock.dealroom.api.core.usecase.task.update.BulkTaskUpdate
import realestate.unlock.dealroom.api.core.usecase.task.update.UpdateTaskMediator
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class BulkTaskUpdateTest {

    @MockK
    private lateinit var updateTaskMediator: UpdateTaskMediator

    private lateinit var bulkTaskUpdate: BulkTaskUpdate

    @BeforeEach
    fun setUp() {
        bulkTaskUpdate = BulkTaskUpdate(
            updateTaskMediator = updateTaskMediator
        )
    }

    @Test
    fun `can update task in bulk`() {
        val user = CompleteUserObjectMother.admin()
        val (idSuccessful, idWithFailure) = Pair(anyId(), anyId())
        val input = BulkTaskUpdate.Input(
            ids = listOf(idSuccessful, idWithFailure),
            dueDate = LocalDate.now(),
            assignedBuyerId = anyId(),
            enabled = true,
            member = user.member,
            status = TaskStatus.DONE
        )
        givenUpdateTask(idSuccessful, input)
        givenUpdateTaskFailure(idWithFailure, input)

        val result = bulkTaskUpdate(input)

        assertEquals(2, result.size)
        assert(result.first { it.id == idSuccessful }.successful)
        val failure = result.first { it.id == idWithFailure }
        assertFalse(failure.successful)
        assertNotNull(failure.errorMessage)
    }

    private fun givenUpdateTask(taskId: Long, input: BulkTaskUpdate.Input) {
        every {
            updateTaskMediator.update(
                inputFrom(taskId, input)
            )
        } returns mockk<TaskWithFilesAndHistory>()
    }

    private fun givenUpdateTaskFailure(taskId: Long, input: BulkTaskUpdate.Input) {
        every {
            updateTaskMediator.update(
                inputFrom(taskId, input)
            )
        } throws RuntimeException("An error")
    }

    private fun inputFrom(
        taskId: Long,
        input: BulkTaskUpdate.Input
    ) = UpdateTaskMediator.Input(
        id = taskId,
        dueDate = input.dueDate,
        assignedBuyerId = input.assignedBuyerId,
        member = input.member,
        status = input.status,
        attachedForm = null,
        enabled = input.enabled
    )
}
