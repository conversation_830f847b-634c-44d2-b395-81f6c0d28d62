package realestate.unlock.dealroom.api.unit.core.usecase.task.update

import io.mockk.*
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus.IN_REVIEW
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus.TO_DO
import realestate.unlock.dealroom.api.core.entity.task.file.TaskFile
import realestate.unlock.dealroom.api.core.entity.task.history.CreateTaskHistoryInput
import realestate.unlock.dealroom.api.core.entity.task.history.TaskHistory
import realestate.unlock.dealroom.api.core.event.task.TaskChanged
import realestate.unlock.dealroom.api.core.event.task.TaskChangedPublisher
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.core.repository.deal.file.LinkedFileRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.repository.task.file.TaskFileRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarTaskEvent
import realestate.unlock.dealroom.api.core.usecase.deal.UpdateDealDataFromTask
import realestate.unlock.dealroom.api.core.usecase.exception.task.TaskNotFoundException
import realestate.unlock.dealroom.api.core.usecase.task.GetCompleteTask
import realestate.unlock.dealroom.api.core.usecase.task.file.SaveTaskFile
import realestate.unlock.dealroom.api.core.usecase.task.history.CreateTaskHistory
import realestate.unlock.dealroom.api.core.usecase.task.update.*
import realestate.unlock.dealroom.api.infrastructure.client.database.exception.NoRowFoundException
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entity.deal.DealCategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.task.file.TaskFileObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class UpdateTaskTest {

    private lateinit var taskChangedPublisher: TaskChangedPublisher

    @MockK
    private lateinit var taskRepository: TaskRepository

    @MockK
    private lateinit var saveTaskFile: SaveTaskFile

    @MockK
    private lateinit var createTaskHistory: CreateTaskHistory

    @MockK
    private lateinit var taskResponseImpactOnDocument: TaskResponseImpactOnDocument

    @MockK
    private lateinit var taskFileRepository: TaskFileRepository

    @MockK
    private lateinit var applyChangesToTask: ApplyChangesToTask

    @MockK
    private lateinit var linkedFileRepository: LinkedFileRepository

    @MockK
    private lateinit var dealCategoryRepository: DealCategoryRepository

    @MockK
    private lateinit var getCompleteTask: GetCompleteTask

    @MockK
    private lateinit var updateDealDataFromTask: UpdateDealDataFromTask

    @MockK
    private lateinit var externalCalendarTaskEvent: ExternalCalendarTaskEvent

    private lateinit var updateTask: UpdateTask
    private lateinit var updateTaskMediator: UpdateTaskMediator

    @BeforeEach
    fun setUp() {
        TransactionManager.initialize(mockk(relaxed = true))
        taskChangedPublisher = spyk()
        updateTask = UpdateTask(
            taskRepository = taskRepository,
            applyChangesToTask = applyChangesToTask,
            taskChangedPublisher = taskChangedPublisher,
            taskResponseImpactOnDocument = taskResponseImpactOnDocument,
            updateTaskFilesAndHistory = UpdateTaskFilesAndHistory(
                saveTaskFile = saveTaskFile,
                taskFileRepository = taskFileRepository,
                linkedFileRepository = linkedFileRepository,
                dealCategoryRepository = dealCategoryRepository,
                kFileParser = spyk(),
                createTaskHistory = createTaskHistory
            ),
            getCompleteTask = getCompleteTask,
            updateDealDataFromTask = updateDealDataFromTask,
            externalCalendarTaskEvent = externalCalendarTaskEvent
        )

        updateTaskMediator = UpdateTaskMediator(
            taskRepository = taskRepository,
            updateTask = updateTask,
            taskTransition = mockk()
        )
    }

    @Test
    fun `When task does not exit throws TaskNotFoundException`() {
        // Given

        val invalidTaskId = 1L
        givenNotTask(invalidTaskId)
        val member = MemberObjectMother.buyer()
        val updateInput = UpdateTaskMediator.Input(
            id = invalidTaskId,
            status = IN_REVIEW,
            dueDate = null,
            attachedForm = emptyMap(),
            member = member
        )

        // When
        val error = assertThrows<TaskNotFoundException> { updateTaskMediator.update(updateInput) }

        // Then
        assertThat(error.message, equalTo("Unable to find task - [TASK_ID:$invalidTaskId]"))
        assertEventNotPublished()
    }

    @Test
    fun `Can update a task without files`() {
        // Given
        val taskId = 65L
        val member = MemberObjectMother.buyer()
        val taskFiles = givenNoFiles()
        val (task, completeTask) = givenTask(taskId = taskId, files = taskFiles)

        val updateInput = givenUpdateTaskInput(taskId = task.id, member = member)
        val taskToUpdate = mockApplyChanges(task, updateInput)
        val taskUpdated = mockUpdateChanges(taskToUpdate, task)

        val completeTaskAfterUpdate = taskUpdated.toTaskWithFilesAndHistory()
        val taskHistory = mockCreateTaskHistory(completeTask, completeTaskAfterUpdate, updateInput, taskFiles, member)
        every { dealCategoryRepository.findById(task.dealCategoryId) } returns DealCategoryBuilder().build()

        // When
        val result = updateTask.update(updateInput, task)

        // Then
        val expectedResult = completeTaskAfterUpdate.copy(history = listOf(taskHistory))
        assertThat(result, equalTo(expectedResult))
        assertEventPublished(taskUpdated)
    }

    @Test
    fun `Can update a task with files`() {
        // Given
        val taskId = 26L
        val member = MemberObjectMother.buyer()
        val taskFile = givenTaskFile(taskId = taskId, memberId = member.id)
        val taskFiles = listOf(taskFile)
        val (task, completeTask) = givenTask(taskId = taskId, files = taskFiles)

        val updateInput = givenUpdateTaskInput(taskId = task.id, member = member)

        val taskToUpdate = mockApplyChanges(task, updateInput)
        val taskUpdated = mockUpdateChanges(taskToUpdate, task)
        val taskFilesCreated = givenFilesInput(task = taskUpdated, updateTaskInput = updateInput, files = listOf(KFile(anyString(), anyString())))

        val completeTaskAfterUpdate = taskUpdated.toTaskWithFilesAndHistory(files = taskFiles)
        val taskHistory = mockCreateTaskHistory(completeTask, completeTaskAfterUpdate, updateInput, taskFilesCreated, member)

        every { dealCategoryRepository.findById(task.dealCategoryId) } returns DealCategoryBuilder().build()

        // When
        val result = updateTask.update(updateInput, task)

        // Then
        val expectedResult = completeTaskAfterUpdate.copy(history = listOf(taskHistory))
        assertThat(result, equalTo(expectedResult))
        assertEventPublished(taskUpdated)
    }

    private fun givenUpdateTaskInput(taskId: Long, member: Member, statusKey: TaskStatus = IN_REVIEW, files: List<String>? = null): UpdateTaskInput {
        return UpdateTaskInput(
            id = taskId,
            status = statusKey,
            dueDate = null,
            attachedForm = mutableMapOf<String, Any>()
                .also {
                    if (files != null) {
                        it["file"] = JsonMapper.encode(files.map { mapOf("uid" to it) })
                    }
                },
            member = member,
            assignedTeam = null,
            assignedBuyerId = null,
            rejectionReason = null,
            enabled = null
        )
    }

    private fun givenFilesInput(task: Task, updateTaskInput: UpdateTaskInput, files: List<KFile>): List<TaskFile> {
        val filesCreated = files.map { givenTaskFile(taskId = task.id, memberId = updateTaskInput.member.id) }
        every { saveTaskFile.save(task, updateTaskInput) } returns filesCreated
        return filesCreated
    }

    private fun mockCreateTaskHistory(
        taskWithFilesAndHistory: TaskWithFilesAndHistory,
        taskWithFilesAndHistoryAfterUpdate: TaskWithFilesAndHistory,
        updateInput: UpdateTaskInput,
        taskFiles: List<TaskFile>,
        member: Member
    ): TaskHistory {
        val createTaskHistoryInput = CreateTaskHistoryInput(
            currentStatus = taskWithFilesAndHistory,
            updatedStatus = taskWithFilesAndHistoryAfterUpdate,
            updates = updateInput.toHistoryUpdates(taskFiles),
            member = member
        )

        return mockk<TaskHistory>().also {
            every { createTaskHistory.create(createTaskHistoryInput) } returns it
        }
    }

    private fun mockUpdateChanges(taskToUpdate: TaskToUpdate, task: Task): Task {
        return task.copy(updatedAt = OffsetDateTime.now()).also {
            every { taskRepository.update(taskToUpdate) } returns it
            every { externalCalendarTaskEvent.impactOnCalendar(it) } returns Unit
        }
    }

    private fun mockApplyChanges(taskToUpdate: Task, updateInput: UpdateTaskInput): TaskToUpdate {
        val slot = slot<Task>()
        return TaskToUpdate(taskToUpdate).also { task ->
            every { applyChangesToTask.apply(capture(slot), updateInput) } answers { task }
        }
    }

    private fun givenTask(taskId: Long, status: TaskStatus = TO_DO, files: List<TaskFile> = listOf()): Pair<Task, TaskWithFilesAndHistory> {
        val task = TaskBuilder()
            .withId(taskId)
            .withDealCategoryId(anyId())
            .withAssignedTeam(MemberDealTeam.SELLER)
            .withTemplateKey("form")
            .withStatus(status)
            .withTypeKey("physical_and_environmental")
            .withAttachedFormData(
                if (files.isEmpty()) {
                    null
                } else {
                    mapOf("file" to JsonMapper.encode(files.map { mapOf("uid" to it.kFileId, "name" to it.name) }))
                }
            )
            .build()

        val completeTask = task.toTaskWithFilesAndHistory(files).also { taskWithFilesAndHistory ->
            every { getCompleteTask.get(taskId) } returns taskWithFilesAndHistory
            every { getCompleteTask.get(task) } returns taskWithFilesAndHistory
            every { taskFileRepository.findByTaskId(taskId) } returns files
        }
        return Pair(task, completeTask)
    }

    private fun givenNotTask(invalidTaskId: Long) {
        every { taskRepository.findById(invalidTaskId) } throws NoRowFoundException("Task")
    }

    private fun givenTaskFile(taskId: Long, memberId: Long) = TaskFileObjectMother.taskFile(id = 55, taskId = taskId, memberId = memberId)

    private fun assertEventPublished(task: Task) {
        verify { taskChangedPublisher.publish(TaskChanged(task)) }
    }

    private fun givenNoFiles() = emptyList<TaskFile>().apply {
        every { saveTaskFile.save(any(), any()) } returns emptyList()
    }

    private fun assertEventNotPublished() {
        verify { taskChangedPublisher wasNot Called }
    }
}
