package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.kfile.KFileWithUrl
import realestate.unlock.dealroom.api.core.exception.DocumentFileNotFoundException
import realestate.unlock.dealroom.api.core.repository.document.DocumentInteractionRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentRoundRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentFile
import realestate.unlock.dealroom.api.core.usecase.deal.documents.GetDocument
import realestate.unlock.dealroom.api.core.usecase.file.GetFileUrl
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.document.InteractionBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.file.FileBuilder

@ExtendWith(MockKExtension::class)
class FindDocumentFileTest {

    @MockK
    private lateinit var documentInteractionRepository: DocumentInteractionRepository

    @MockK
    private lateinit var getDocument: GetDocument

    @MockK
    private lateinit var documentRoundRepository: DocumentRoundRepository

    @MockK
    private lateinit var getFileUrl: GetFileUrl

    private lateinit var findDocumentFile: FindDocumentFile

    @BeforeEach
    fun setUp() {
        findDocumentFile = FindDocumentFile(
            getDocument = getDocument,
            documentRoundRepository = documentRoundRepository,
            documentInteractionRepository = documentInteractionRepository,
            getFileUrl = getFileUrl
        )
    }

    @Test
    fun `can get a file from deal and a round file id`() {
        val input = givenInput()
        val psa = givenPSA(input.dealId)
        val file = givenAFile(input.fileId)
        giveRoundWithFile(psa.id, input.fileId)

        val result = findDocumentFile(input)
        assertEquals(file, result)
    }

    @Test
    fun `can get a file from deal and an interaction file id`() {
        val input = givenInput()
        val psa = givenPSA(input.dealId)
        val roundFile = givenAFile(fileId = anyId())
        val interactionFile = givenAFile(fileId = input.fileId)
        giveRoundWithFile(psa.id, input.fileId)
        givenInteractionWithFile(psa.id, input.fileId)

        val result = findDocumentFile(input)
        assertEquals(interactionFile, result)
    }

    @Test
    fun `if file does not belong to the psa it fails`() {
        val input = givenInput()
        val psa = givenPSA(input.dealId)
        every { documentRoundRepository.findByDocumentAndContainsFileId(psa.id, input.fileId) } returns emptyList()
        every { documentInteractionRepository.findByDocumentIdAndFileId(psa.id, input.fileId) } returns emptyList()

        assertThrows<DocumentFileNotFoundException> {
            findDocumentFile(input)
        }
    }

    private fun givenInput() = FindDocumentFile.Input(
        dealId = anyId(),
        fileId = anyId(),
        documentType = DocumentType.PSA
    )

    private fun givenPSA(dealId: Long) = DocumentBuilder().build().also {
        every { getDocument.byDealIdAndDocumentType(dealId, DocumentType.PSA) } returns it
    }

    private fun giveRoundWithFile(psaId: Long, fileId: Long) {
        every { documentRoundRepository.findByDocumentAndContainsFileId(psaId, fileId) } returns listOf(DocumentRoundObjectMother.draftDocuments())
    }

    private fun givenInteractionWithFile(psaId: Long, fileId: Long) {
        every { documentInteractionRepository.findByRoundIdAndDocumentId(psaId, fileId) } returns listOf(InteractionBuilder().build())
    }

    private fun givenAFile(fileId: Long) = FileBuilder().apply {
        id = fileId
    }.build().let {
        KFileWithUrl(it.kFileId, url = anyString(), name = anyString())
    }
        .also {
            every { getFileUrl.invoke(fileId) } returns it
        }
}
