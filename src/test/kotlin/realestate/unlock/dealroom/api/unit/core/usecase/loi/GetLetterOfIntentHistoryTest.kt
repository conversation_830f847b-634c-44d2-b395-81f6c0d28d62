package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntry
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntryType
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentHistoryEntryRepository
import realestate.unlock.dealroom.api.core.usecase.loi.GetLetterOfIntentHistory
import java.time.OffsetDateTime

class GetLetterOfIntentHistoryTest {

    private val dealId: Long = 1
    private val historyEntries = listOf(
        LetterOfIntentHistoryEntry(
            id = 1,
            dealId = dealId,
            loiId = 1,
            type = LetterOfIntentHistoryEntryType.OFFER,
            description = "some text",
            createdAt = OffsetDateTime.now(),
            kFile = null,
            member = null
        )
    )

    private val letterOfIntentHistoryEntryRepository: LetterOfIntentHistoryEntryRepository = mockk {
        every { findByDealId(dealId) } returns historyEntries
    }

    private val getLetterOfIntentHistory = GetLetterOfIntentHistory(letterOfIntentHistoryEntryRepository)

    @Test
    fun `can retrieve history`() {
        // when
        val result = getLetterOfIntentHistory.get(dealId = dealId)

        // then
        assertThat(result, IsEqual(historyEntries))
    }
}
