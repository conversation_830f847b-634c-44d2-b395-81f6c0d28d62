package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.ExecutedDocument
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.repository.document.DocumentRoundRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.ConfirmExecutedDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FileType
import realestate.unlock.dealroom.api.core.usecase.file.ConfirmStagedFile
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class ConfirmExecutedDocumentTest {

    @MockK
    private lateinit var confirmStagedFile: ConfirmStagedFile

    @MockK(relaxed = true)
    private lateinit var documentRoundRepository: DocumentRoundRepository

    private lateinit var confirmExecutedDocument: ConfirmExecutedDocument

    @BeforeEach
    fun setUp() {
        TransactionManager.initialize(mockk(relaxed = true))
        confirmExecutedDocument = ConfirmExecutedDocument(
            confirmStagedFile = confirmStagedFile,
            documentRoundRepository = documentRoundRepository
        )
    }

    @Test
    fun `can confirm final document`() {
        val nextId = nextRoundId()
        val psa = DocumentBuilder().apply { currentRoundId = anyId() }.build()
        val psaNextId = psa.copy(currentRoundId = nextId)

        val input = ConfirmExecutedDocument.Input(
            dealId = anyId(),
            executedVersionFile = givenFileInput()
        )

        val file = givenFilesConfirmed(psaNextId, input)

        val documentsConfirmed = confirmExecutedDocument(input, psa)

        assertEquals(psa.id, documentsConfirmed.documentId)
        assertEquals(file.first().id, documentsConfirmed.fileId)
        assertNewDocumentWasSaved(documentsConfirmed)
    }

    private fun givenFileInput() = FileInput(anyString(), anyString())

    private fun assertNewDocumentWasSaved(expected: ExecutedDocument) {
        verify {
            documentRoundRepository.save(expected)
        }
    }

    private fun nextRoundId(): Long =
        anyId().also {
            every { documentRoundRepository.nextId() } returns it
        }

    private fun givenFilesConfirmed(document: Document, input: ConfirmExecutedDocument.Input): List<File> {
        val filesToConfirm = listOfNotNull(fileToConfirm(input.executedVersionFile, document))
        return filesToConfirm.map {
            File(
                id = anyId(),
                name = it.name,
                kFileId = it.kFileId,
                memberId = null,
                createdAt = OffsetDateTime.now(Clock.systemUTC())
            )
        }.also {
            every { confirmStagedFile(filesToConfirm) } returns it
        }
    }

    private fun fileToConfirm(
        it: FileInput,
        document: Document
    ) = ConfirmStagedFile.FileToConfirm(
        name = it.name,
        kFileId = it.uid,
        path = "deal/${document.dealId}/psa/${document.id}/executed/${document.currentRoundId}_${FileType.EXECUTED_VERSION.name}",
        member = null

    )
}
