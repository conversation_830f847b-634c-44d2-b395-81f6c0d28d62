package realestate.unlock.dealroom.api.unit.core.usecase.deal.report

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFile
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportInput
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportStatus
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportType
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportVendor
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.repository.deal.report.DealReportRepository
import realestate.unlock.dealroom.api.core.repository.deal.report.ReportTypeRepository
import realestate.unlock.dealroom.api.core.repository.deal.report.ReportVendorRepository
import realestate.unlock.dealroom.api.core.repository.deal.report.UpdateReportInput
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalCalendarReportEvent
import realestate.unlock.dealroom.api.core.usecase.deal.files.linked.DeleteLinkedFile
import realestate.unlock.dealroom.api.core.usecase.deal.reports.ConfirmDealReportFiles
import realestate.unlock.dealroom.api.core.usecase.deal.reports.UpdateDealReport
import realestate.unlock.dealroom.api.core.usecase.deal.reports.ValidateReportNotDuplicated
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.LocalDate
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class UpdateDealReportTest {

    @MockK(relaxed = true)
    private lateinit var dealReportRepository: DealReportRepository

    @MockK
    private lateinit var confirmDealReportFiles: ConfirmDealReportFiles

    @MockK(relaxed = true)
    private lateinit var validateReportNotDuplicated: ValidateReportNotDuplicated

    @MockK
    private lateinit var reportTypeRepository: ReportTypeRepository

    @MockK
    private lateinit var reportVendorRepository: ReportVendorRepository

    @MockK
    private lateinit var deleteLinkedFile: DeleteLinkedFile

    private lateinit var updateDealReport: UpdateDealReport

    @MockK
    private lateinit var externalCalendarReportEvent: ExternalCalendarReportEvent

    @BeforeEach
    fun setUp() {
        updateDealReport = UpdateDealReport(
            dealReportRepository = dealReportRepository,
            confirmDealReportFiles = confirmDealReportFiles,
            validateReportNotDuplicated = validateReportNotDuplicated,
            reportTypeRepository = reportTypeRepository,
            reportVendorRepository = reportVendorRepository,
            deleteLinkedFile = deleteLinkedFile,
            externalCalendarReportEvent = externalCalendarReportEvent
        )
        every { externalCalendarReportEvent.impactOnCalendar(any()) } returns Unit
    }

    @Test
    fun `It removes a file`() {
        // given
        val firstReportFile = file()
        val secondReportFile = file()
        val givenReportId = 123L
        val givenInput = givenUpdateReportInput()
            .copy(documents = listOf(firstReportFile.toFileInput()))
        val givenMember = mockk<Member>()
        val authToken = anyString()

        every { confirmDealReportFiles.invoke(files = listOf(), dealId = givenInput.dealId, reportId = givenReportId, member = givenMember, authToken = authToken) } returns listOf()
        every { dealReportRepository.findFilesByReportId(givenReportId) } returns listOf(firstReportFile, secondReportFile)
        every { reportTypeRepository.findByKey(givenInput.type) } returns ReportType(givenInput.type, anyString())
        every { reportVendorRepository.findByReportType(givenInput.type) } returns listOf(ReportVendor(anyId(), givenInput.vendorKey, anyString()))
        every {
            deleteLinkedFile.removeReportLink(
                dealId = givenInput.dealId,
                entityId = givenReportId.toString(),
                fileId = secondReportFile.kFileId
            )
        } returns LinkedFile(
            id = anyId(),
            dealId = givenInput.dealId,
            fileId = secondReportFile.kFileId,
            entityType = LinkedFileEntityType.REPORT,
            entityId = givenReportId.toString(),
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now()
        )

        // when
        updateDealReport(input = givenInput, reportId = givenReportId, member = givenMember, authToken = authToken)

        // then
        verify {
            dealReportRepository.update(
                UpdateReportInput(
                    report = givenInput.toReport(id = givenReportId, files = listOf()),
                    newFiles = listOf(),
                    filesToBeDeleted = listOf(secondReportFile),
                    newTags = listOf(),
                    tagsToBeDeleted = listOf()
                )
            )

            deleteLinkedFile.removeReportLink(
                dealId = givenInput.dealId,
                entityId = givenReportId.toString(),
                fileId = secondReportFile.kFileId
            )
        }
    }

    @Test
    fun `It adds a file`() {
        // given
        val firstReportFile = file()
        val newReportFile = file()
        val givenReportId = 456L
        val newReportFileInput = newReportFile.toFileInput()
        val givenInput = givenUpdateReportInput().copy(
            documents = listOf(
                firstReportFile.toFileInput(),
                newReportFileInput
            )
        )
        val givenMember = mockk<Member>()
        val authToken = anyString()

        every { confirmDealReportFiles.invoke(files = listOf(newReportFileInput), dealId = givenInput.dealId, reportId = givenReportId, member = givenMember, authToken = authToken) } returns listOf(
            newReportFile
        )
        every { dealReportRepository.findFilesByReportId(givenReportId) } returns listOf(firstReportFile)
        every { reportTypeRepository.findByKey(givenInput.type) } returns ReportType(givenInput.type, anyString())
        every { reportVendorRepository.findByReportType(givenInput.type) } returns listOf(ReportVendor(anyId(), givenInput.vendorKey, anyString()))

        // when
        updateDealReport(input = givenInput, reportId = givenReportId, member = givenMember, authToken = authToken)

        // then
        verify {
            dealReportRepository.update(
                UpdateReportInput(
                    report = givenInput.toReport(id = givenReportId, files = listOf()),
                    newFiles = listOf(newReportFile),
                    filesToBeDeleted = listOf(),
                    newTags = listOf(),
                    tagsToBeDeleted = listOf()
                )
            )
        }

        verify(exactly = 0) {
            deleteLinkedFile.removeReportLink(
                dealId = givenInput.dealId,
                entityId = givenReportId.toString(),
                fileId = any()
            )
        }
    }

    private fun givenUpdateReportInput() = ReportInput(
        dealId = 13L,
        type = "SEISMIC_REPORT",
        reportName = null,
        vendorKey = "EBI",
        vendorName = null,
        status = ReportStatus.ORDERED,
        expectedDate = LocalDate.now(),
        costEstimate = null,
        cost = null,
        findings = "no findings yet",
        tags = listOf(),
        documents = listOf()
    )

    private fun file() = File(
        id = anyId(),
        kFileId = anyString(),
        name = anyString(),
        memberId = 12L,
        createdAt = OffsetDateTime.now()
    )

    private fun File.toFileInput() = FileInput(uid = this.kFileId, name = this.name)
}
