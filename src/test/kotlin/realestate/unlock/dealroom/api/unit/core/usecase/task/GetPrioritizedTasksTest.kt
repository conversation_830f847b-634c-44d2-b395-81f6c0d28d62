package realestate.unlock.dealroom.api.unit.core.usecase.task

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.deal.category.DealCategory
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.task.GetSellerPrioritizedTask
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DealTaskConfig
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.*
import java.time.temporal.ChronoUnit

@ExtendWith(MockKExtension::class)
class GetPrioritizedTasksTest {

    companion object {
        private const val mostPriorityTaskSize = 6
        val now: Instant = Instant.now()
        val zoneId: ZoneId = ZoneId.systemDefault()
    }

    @MockK
    private lateinit var dealRepository: DealRepository

    @MockK
    private lateinit var taskRepository: TaskRepository

    @MockK
    private lateinit var featureFlags: FeatureFlags

    @MockK
    private lateinit var dealCategoryRepository: DealCategoryRepository

    private var clock: Clock = Clock.fixed(now, zoneId)
    private lateinit var getSellerPrioritizedTask: GetSellerPrioritizedTask

    @BeforeEach
    fun setUp() {
        getSellerPrioritizedTask = GetSellerPrioritizedTask(
            dealRepository = dealRepository,
            taskRepository = taskRepository,
            clock = clock,
            dealTaskConfig = DealTaskConfig(topPriorityTasksSize = mostPriorityTaskSize),
            dealCategoryRepository = dealCategoryRepository,
            featureFlags = featureFlags
        )
    }

    @Test
    fun `if deal is not in CONTRACT_NEGOTIATION returns empty task`() {
        // given
        val deal = givenADeal(stage = Stage.EVALUATION)
        val completeUser = CompleteUserObjectMother.seller()
        // when
        val prioritizedTasks = getSellerPrioritizedTask.get(dealId = deal.id, member = completeUser.member)

        // then
        Assertions.assertTrue(prioritizedTasks.top.isEmpty())
        Assertions.assertTrue(prioritizedTasks.upcoming.isEmpty())
        Assertions.assertTrue(prioritizedTasks.additional.isEmpty())
    }

    @Test
    fun `tasks are sorted by priority`() {
        // given
        val deal = givenADeal()
        val seller = MemberObjectMother.seller(authId = anyString())

        val highestPriorityTasks = givenTasksWithPriority(3, PriorityValue.HIGHEST, true)
        val highPriorityTasks = givenTasksWithPriority(7, PriorityValue.HIGH, true)
        val lowPriorityTasks = givenTasksWithPriority(7, PriorityValue.LOW, false)
        val naPriorityTasks = givenTasksWithPriority(7, PriorityValue.NA, false)
        every { taskRepository.findByDeal(dealId = deal.id) } returns highPriorityTasks.plus(highestPriorityTasks)
            .plus(lowPriorityTasks).plus(naPriorityTasks)
        every { featureFlags.isOn(Feature.CLOSING, seller.authId!!) } returns true

        // when
        val prioritizedTasks = getSellerPrioritizedTask.get(dealId = deal.id, member = seller)

        // then
        Assertions.assertEquals(mostPriorityTaskSize, prioritizedTasks.top.size)
        Assertions.assertEquals(highestPriorityTasks.plus(highPriorityTasks.subList(0, 3)), prioritizedTasks.top)
        Assertions.assertEquals(highPriorityTasks.subList(3, 7), prioritizedTasks.upcoming)
        Assertions.assertEquals(lowPriorityTasks, prioritizedTasks.additional)
    }

    @Test
    fun `tasks are rejected should be top priority`() {
        // given
        val deal = givenADeal()
        val seller = MemberObjectMother.seller(authId = anyString())

        val highestPriorityTasks = givenTasksWithPriority(count = 3, priority = PriorityValue.HIGHEST, required = true)
        val highPriorityTasks = givenTasksWithPriority(count = 7, priority = PriorityValue.HIGH, required = true)
        val lowPriorityTasks = givenTasksWithPriority(count = 7, priority = PriorityValue.LOW, required = false)
        val rejectedTask = givenTasksWithPriority(count = 1, priority = PriorityValue.LOW, required = false, rejectedReason = anyString())
        val naPriorityTasks = givenTasksWithPriority(7, PriorityValue.NA, false)
        every { featureFlags.isOn(Feature.CLOSING, seller.authId!!) } returns true

        every { taskRepository.findByDeal(dealId = deal.id) } returns highPriorityTasks
            .plus(highestPriorityTasks).plus(lowPriorityTasks).plus(rejectedTask).plus(naPriorityTasks)

        // when
        val prioritizedTasks = getSellerPrioritizedTask.get(dealId = deal.id, member = seller)

        // then
        Assertions.assertEquals(mostPriorityTaskSize, prioritizedTasks.top.size)
        Assertions.assertEquals(rejectedTask.plus(highestPriorityTasks.plus(highPriorityTasks.subList(0, 2))), prioritizedTasks.top)
        Assertions.assertEquals(highPriorityTasks.subList(2, 7), prioritizedTasks.upcoming)
        Assertions.assertEquals(lowPriorityTasks, prioritizedTasks.additional)
    }

    @Test
    fun `tasks that apply after closing should be upcoming when deal is not in closing`() {
        // given
        val deal = givenADeal(initialClosingDate = LocalDate.now(clock).plusDays(10))
        val seller = MemberObjectMother.seller(authId = anyString())

        val highPriorityTasks = givenTasksWithPriority(count = 7, priority = PriorityValue.HIGH, required = true)
        val lowPriorityTasks = givenTasksWithPriority(count = 7, priority = PriorityValue.LOW, required = false)
        val taskAfterClosing = givenTasksWithPriority(
            count = 1,
            priority = PriorityValue.HIGHEST,
            required = true,
            afterClosing = true
        )
        every { taskRepository.findByDeal(dealId = deal.id) } returns highPriorityTasks.plus(lowPriorityTasks).plus(taskAfterClosing)
        every { featureFlags.isOn(Feature.CLOSING, seller.authId!!) } returns true

        // when
        val prioritizedTasks = getSellerPrioritizedTask.get(dealId = deal.id, member = seller)

        // then
        Assertions.assertEquals(mostPriorityTaskSize, prioritizedTasks.top.size)
        Assertions.assertEquals(highPriorityTasks.subList(0, 6), prioritizedTasks.top)
        Assertions.assertEquals(highPriorityTasks.subList(6, 7).plus(taskAfterClosing), prioritizedTasks.upcoming)
        Assertions.assertEquals(lowPriorityTasks, prioritizedTasks.additional)
    }

    @Test
    fun `tasks that apply after closing should be prioritized when deal is after closing`() {
        // given
        val deal = givenADeal(initialClosingDate = LocalDate.now(clock).minus(9, ChronoUnit.DAYS))
        val seller = MemberObjectMother.seller(authId = anyString())

        val highPriorityTasks = givenTasksWithPriority(count = 7, priority = PriorityValue.HIGH, required = true)
        val lowPriorityTasks = givenTasksWithPriority(count = 7, priority = PriorityValue.LOW, required = false)
        val taskAfterClosing = givenTasksWithPriority(
            count = 1,
            priority = PriorityValue.HIGHEST,
            required = true,
            afterClosing = true
        )
        every { taskRepository.findByDeal(dealId = deal.id) } returns highPriorityTasks.plus(lowPriorityTasks).plus(taskAfterClosing)
        every { featureFlags.isOn(Feature.CLOSING, seller.authId!!) } returns true

        // when
        val prioritizedTasks = getSellerPrioritizedTask.get(dealId = deal.id, member = seller)

        // then
        Assertions.assertEquals(mostPriorityTaskSize, prioritizedTasks.top.size)
        Assertions.assertEquals(taskAfterClosing.plus(highPriorityTasks.subList(0, 5)), prioritizedTasks.top)
        Assertions.assertEquals(highPriorityTasks.subList(5, 7), prioritizedTasks.upcoming)
        Assertions.assertEquals(lowPriorityTasks, prioritizedTasks.additional)
    }

    @Test
    fun `tasks in review and task in done are not included in prioritized tasks`() {
        // given
        val deal = givenADeal()
        val seller = MemberObjectMother.seller(authId = anyString())

        val highPriority = Priority(sorting = 7, value = PriorityValue.HIGH)
        val taskDone = taskWithPriority(
            priority = highPriority,
            required = true,
            status = TaskStatus.DONE,
            team = MemberDealTeam.BUYER
        )
        val taskInReview = taskWithPriority(
            priority = highPriority,
            required = true,
            status = TaskStatus.IN_REVIEW,
            team = MemberDealTeam.SELLER
        )
        every { taskRepository.findByDeal(dealId = deal.id) } returns listOf(taskDone, taskInReview)
        every { featureFlags.isOn(Feature.CLOSING, seller.authId!!) } returns true

        // when
        val prioritizedTasks = getSellerPrioritizedTask.get(dealId = deal.id, member = seller)

        // then
        Assertions.assertTrue(prioritizedTasks.top.isEmpty())
        Assertions.assertTrue(prioritizedTasks.upcoming.isEmpty())
        Assertions.assertTrue(prioritizedTasks.additional.isEmpty())
    }

    @Test
    fun `closing task should be not present if closing feature is off`() {
        // given
        val deal = givenADeal(initialClosingDate = LocalDate.now(clock).minus(9, ChronoUnit.DAYS))
        val seller = MemberObjectMother.seller(authId = anyString())
        val dealCategoryOfClosingStage = givenDealCategory(1, deal.id)
        val dealCategoryOfDueDiligence = givenDealCategory(2, deal.id)

        val highPriorityTasks = givenTasksWithPriority(
            count = 8,
            priority = PriorityValue.HIGH,
            required = true,
            dealCategoryId = dealCategoryOfDueDiligence.id
        )
        val highPriorityClosingTasks = givenTasksWithPriority(
            count = 1,
            priority = PriorityValue.HIGH,
            required = true,
            dealCategoryId = dealCategoryOfClosingStage.id
        )
        val lowPriorityTasks = givenTasksWithPriority(
            count = 7,
            priority = PriorityValue.LOW,
            required = false,
            dealCategoryId = dealCategoryOfDueDiligence.id
        )
        every { taskRepository.findByDeal(dealId = deal.id) } returns highPriorityTasks.plus(highPriorityClosingTasks).plus(lowPriorityTasks)
        every { dealCategoryRepository.findByDealIdAndStages(dealId = deal.id, stages = listOf(Stage.CLOSING)) } returns listOf(dealCategoryOfClosingStage)
        every { featureFlags.isOn(Feature.CLOSING, seller.authId!!) } returns false

        // when
        val prioritizedTasks = getSellerPrioritizedTask.get(dealId = deal.id, member = seller)

        // then
        Assertions.assertEquals(mostPriorityTaskSize, prioritizedTasks.top.size)
        Assertions.assertEquals(highPriorityTasks.subList(0, 6), prioritizedTasks.top)
        Assertions.assertEquals(highPriorityTasks.subList(6, 8), prioritizedTasks.upcoming)
        Assertions.assertEquals(lowPriorityTasks, prioritizedTasks.additional)
    }

    @Test
    fun `psa task should be present when it is in seller review`() {
        // given
        val deal = givenADeal()
        val seller = MemberObjectMother.seller(authId = anyString())
        val dealCategoryOfClosingStage = givenDealCategory(1, deal.id)
        val dealCategoryOfDueDiligence = givenDealCategory(2, deal.id)

        val highPriorityTasks = givenTasksWithPriority(
            count = 4,
            priority = PriorityValue.HIGH,
            required = true,
            dealCategoryId = dealCategoryOfDueDiligence.id
        )
        val highestPriority = Priority(sorting = 1, value = PriorityValue.HIGHEST)
        val psaTask = taskWithPriority(
            priority = highestPriority,
            required = true,
            status = TaskStatus.IN_REVIEW,
            team = seller.getDealTeam(),
            taskType = TaskType.PSA
        )
        val lowPriorityTasks = givenTasksWithPriority(
            count = 7,
            priority = PriorityValue.LOW,
            required = false,
            dealCategoryId = dealCategoryOfDueDiligence.id
        )
        every { taskRepository.findByDeal(dealId = deal.id) } returns highPriorityTasks.plus(psaTask).plus(lowPriorityTasks)
        every { dealCategoryRepository.findByDealIdAndStages(dealId = deal.id, stages = listOf(Stage.CLOSING)) } returns listOf(dealCategoryOfClosingStage)
        every { featureFlags.isOn(Feature.CLOSING, seller.authId!!) } returns true

        // when
        val prioritizedTasks = getSellerPrioritizedTask.get(dealId = deal.id, member = seller)

        // then
        Assertions.assertEquals(5, prioritizedTasks.top.size)
        Assertions.assertEquals(listOf(psaTask).plus(highPriorityTasks), prioritizedTasks.top)
        Assertions.assertEquals(lowPriorityTasks, prioritizedTasks.additional)
    }

    @Test
    fun `returns only task that seller side can show`() {
        // given
        val deal = givenADeal()
        val seller = MemberObjectMother.seller(authId = anyString())

        val highestPriority = Priority(sorting = 1, value = PriorityValue.HIGHEST)
        val taskForAll = taskWithPriority(
            visibility = Task.Visibility.ALL,
            priority = highestPriority,
            required = true,
            status = TaskStatus.TO_DO,
            team = seller.getDealTeam()
        )
        val taskForSeller = taskWithPriority(
            visibility = Task.Visibility.SELLER_TEAM,
            priority = highestPriority,
            required = true,
            status = TaskStatus.TO_DO,
            team = seller.getDealTeam()
        )
        val taskForBuyer = taskWithPriority(
            visibility = Task.Visibility.BUYER_TEAM,
            priority = highestPriority,
            required = true,
            status = TaskStatus.TO_DO,
            team = seller.getDealTeam()
        )

        every { taskRepository.findByDeal(dealId = deal.id) } returns listOf(taskForAll, taskForSeller, taskForBuyer)
        every { dealCategoryRepository.findByDealIdAndStages(dealId = deal.id, stages = listOf(Stage.CLOSING)) } returns listOf()
        every { featureFlags.isOn(Feature.CLOSING, seller.authId!!) } returns true

        // when
        val prioritizedTasks = getSellerPrioritizedTask.get(dealId = deal.id, member = seller)

        // then
        Assertions.assertEquals(2, prioritizedTasks.top.size)
        Assertions.assertEquals(listOf(taskForAll, taskForSeller), prioritizedTasks.top)
    }

    private fun givenDealCategory(id: Long, dealId: Long) = DealCategory(id, dealId, anyString(), OffsetDateTime.now())

    private fun givenADeal(stage: Stage = Stage.NEGOTIATION, initialClosingDate: LocalDate? = null) =
        DealBuilder().apply {
            this.initialClosingDate = initialClosingDate
            this.stage = stage
        }.build()
            .also { deal ->
                every { dealRepository.findById(deal.id) } returns deal
            }

    private fun givenTasksWithPriority(
        count: Int,
        priority: PriorityValue,
        required: Boolean,
        rejectedReason: String? = null,
        afterClosing: Boolean = false,
        dealCategoryId: Long = 1,
        taskType: TaskType = TaskType.FORM_AND_FILE
    ) =
        (1..count).map {
            taskWithPriority(
                priority = Priority(priority, it),
                required = required,
                rejectedReason = rejectedReason,
                afterClosing = afterClosing,
                dealCategoryId = dealCategoryId,
                taskType = taskType
            )
        }

    private fun taskWithPriority(
        priority: Priority,
        required: Boolean,
        rejectedReason: String? = null,
        afterClosing: Boolean = false,
        status: TaskStatus = TaskStatus.TO_DO,
        team: MemberDealTeam = MemberDealTeam.SELLER,
        dealCategoryId: Long = anyId(),
        taskType: TaskType = TaskType.FORM_AND_FILE,
        visibility: Task.Visibility = Task.Visibility.ALL
    ) =
        TaskBuilder()
            .withPriority(priority)
            .withRequired(required)
            .withRejectionReason(rejectedReason)
            .withPrioritizeAfterClosing(afterClosing)
            .withStatus(status)
            .withDealCategoryId(dealCategoryId)
            .withAssignedTeam(team)
            .withTypeKey(taskType.key)
            .withVisibility(visibility)
            .build()
}
