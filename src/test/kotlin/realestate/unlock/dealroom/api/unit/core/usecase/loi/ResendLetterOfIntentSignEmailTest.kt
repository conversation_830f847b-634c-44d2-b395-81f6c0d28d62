package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.loi.*
import realestate.unlock.dealroom.api.core.gateway.sign.ResendDocumentInput
import realestate.unlock.dealroom.api.core.repository.loi.MedicalLetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealById
import realestate.unlock.dealroom.api.core.usecase.exception.deal.loi.InvalidLetterOfIntentStatusException
import realestate.unlock.dealroom.api.core.usecase.loi.GetLetterOfIntentSign
import realestate.unlock.dealroom.api.core.usecase.loi.ResendLetterOfIntentSignEmail
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.DocuSignSignGateway
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString

@ExtendWith(MockKExtension::class)
class ResendLetterOfIntentSignEmailTest {

    @MockK
    private lateinit var getDealById: GetDealById

    @MockK
    private lateinit var letterOfIntentRoundRepository: MedicalLetterOfIntentRoundRepository

    @MockK
    private lateinit var getLetterOfIntentSign: GetLetterOfIntentSign

    @MockK(relaxed = true)
    private lateinit var docusignSignGateway: DocuSignSignGateway

    private lateinit var resendLetterOfIntentSignEmail: ResendLetterOfIntentSignEmail

    @BeforeEach
    fun setUp() {
        resendLetterOfIntentSignEmail = ResendLetterOfIntentSignEmail(
            getDealById = getDealById,
            letterOfIntentRoundRepository = letterOfIntentRoundRepository,
            getLetterOfIntentSign = getLetterOfIntentSign,
            docuSignSignGateway = docusignSignGateway
        )
    }

    @Test
    fun `Cannot resend the LOI signing email of a deal in contract negotiation`() {
        val givenDeal = givenDealInStage(Stage.NEGOTIATION)

        val input = ResendLetterOfIntentEmailInput(dealId = givenDeal.id, loiId = 12L)
        val exception = assertThrows<BadRequestException> { resendLetterOfIntentSignEmail.resend(input) }

        assertThat(exception.message, equalTo("Cannot resend the LOI email of a deal in stage [${givenDeal.stage}]"))
    }

    @Test
    fun `Cannot resend the LOI signing email of a new deal`() {
        val givenDeal = givenDealInStage(Stage.EVALUATION)

        val input = ResendLetterOfIntentEmailInput(dealId = givenDeal.id, loiId = 12L)
        val exception = assertThrows<BadRequestException> { resendLetterOfIntentSignEmail.resend(input) }

        assertThat(exception.message, equalTo("Cannot resend the LOI email of a deal in stage [${givenDeal.stage}]"))
    }

    @Test
    fun `Cannot resend the LOI signing email of a rejected loi`() {
        val givenDeal = givenDealInStage(Stage.OFFER)
        val givenLoi = givenLoiWithStatus(LetterOfIntentRoundStatus.REJECTED)

        val input = ResendLetterOfIntentEmailInput(dealId = givenDeal.id, loiId = givenLoi.id)
        val exception = assertThrows<InvalidLetterOfIntentStatusException> { resendLetterOfIntentSignEmail.resend(input) }

        assertThat(exception.message, equalTo("Only letter of intents with status ${LetterOfIntentRoundStatus.ACCEPTED} can perform this operation"))
    }

    @Test
    fun `Cannot resend the LOI signing email of an already completed LOI sign`() {
        val givenDeal = givenDealInStage(Stage.OFFER)
        val givenLoi = givenLoiWithStatus(LetterOfIntentRoundStatus.ACCEPTED)
        val givenLoiSignature = CompleteLoiSign(loiId = givenLoi.id, signingId = anyString(), status = LoiSignStatus.COMPLETED, buyerSign = mockk(), sellerSign = mockk())
        every { getLetterOfIntentSign.get(givenLoi.id) } returns givenLoiSignature

        val input = ResendLetterOfIntentEmailInput(dealId = givenDeal.id, loiId = givenLoi.id)
        val exception = assertThrows<BadRequestException> { resendLetterOfIntentSignEmail.resend(input) }
        assertThat(exception.message, equalTo("Cannot resend the LOI email of an already signed LOI"))
    }

    @Test
    fun `Given a deal with an accepted loi pending its signature, resending the docusign email should be possible`() {
        val givenDeal = givenDealInStage(Stage.OFFER)
        val givenLoi = givenLoiWithStatus(LetterOfIntentRoundStatus.ACCEPTED)
        val givenLoiSignature = CompleteLoiSign(loiId = givenLoi.id, signingId = anyString(), status = LoiSignStatus.PENDING, buyerSign = mockk(), sellerSign = mockk())
        every { getLetterOfIntentSign.get(givenLoi.id) } returns givenLoiSignature

        val input = ResendLetterOfIntentEmailInput(dealId = givenDeal.id, loiId = givenLoi.id)
        resendLetterOfIntentSignEmail.resend(input)

        verify {
            docusignSignGateway.resendDocument(
                ResendDocumentInput(
                    signingId = givenLoiSignature.signingId
                )
            )
        }
    }

    private fun givenDealInStage(stage: Stage): Deal {
        val givenDeal = DealBuilder().withStage(stage).build()
        every { getDealById.get(givenDeal.id) } returns givenDeal

        return givenDeal
    }

    private fun givenLoiWithStatus(status: LetterOfIntentRoundStatus): MedicalLoiRound {
        val givenLoi = LoiMedicalRoundBuilder().withStatus(status).build()
        every { letterOfIntentRoundRepository.findById(givenLoi.id) } returns givenLoi

        return givenLoi
    }
}
