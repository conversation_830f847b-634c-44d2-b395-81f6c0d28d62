package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntryType
import realestate.unlock.dealroom.api.core.entity.loi.SignLetterOfIntentInput
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentSignRepository
import realestate.unlock.dealroom.api.core.repository.loi.MedicalLetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.loi.CreateLetterOfIntentHistoryEntry
import realestate.unlock.dealroom.api.core.usecase.loi.CreateLetterOfIntentHistoryEntryInput
import realestate.unlock.dealroom.api.core.usecase.loi.SignLetterOfIntent
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiSignBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class SignMedicalLoiRoundTest {

    companion object {
        private val loiRound = LoiMedicalRoundBuilder().build()
        private val now = Instant.now()
        private val zone = ZoneId.systemDefault()
        private val closedAt = now.atZone(zone).toOffsetDateTime()
    }

    private lateinit var clock: Clock

    @MockK
    private lateinit var letterOfIntentRoundRepository: MedicalLetterOfIntentRoundRepository

    @MockK(relaxed = true)
    private lateinit var letterOfIntentSignRepository: LetterOfIntentSignRepository

    @MockK
    private lateinit var memberRepository: MemberRepository

    @MockK(relaxed = true)
    private lateinit var createLetterOfIntentHistoryEntry: CreateLetterOfIntentHistoryEntry

    private lateinit var signLetterOfIntent: SignLetterOfIntent

    @BeforeEach
    fun setUp() {
        every { letterOfIntentRoundRepository.findById(any()) } returns loiRound
        clock = Clock.fixed(now, zone)
        signLetterOfIntent = SignLetterOfIntent(
            letterOfIntentRoundRepository = letterOfIntentRoundRepository,
            letterOfIntentSignRepository = letterOfIntentSignRepository,
            memberRepository = memberRepository,
            clock = clock,
            createLetterOfIntentHistoryEntry = createLetterOfIntentHistoryEntry
        )
    }

    @Test
    fun `can register seller sign`() {
        val envelopeId = anyString()
        val loiSign = givenSignLoi(signingId = envelopeId)
        val signLetterOfIntentInput = givenSignLetterOfIntentInput(recipientId = loiSign.sellerSign.id, envelopeId = envelopeId)
        val loiSignExpected = loiSign.copy(sellerSign = loiSign.sellerSign.copy(completedAt = closedAt))

        signLetterOfIntent.sign(signLetterOfIntentInput)

        verify { letterOfIntentSignRepository.update(loiSignExpected) }
        verify {
            createLetterOfIntentHistoryEntry.execute(
                CreateLetterOfIntentHistoryEntryInput(
                    dealId = loiRound.dealId,
                    loiId = loiRound.id,
                    type = LetterOfIntentHistoryEntryType.SIGNATURE,
                    description = "Signed the document",
                    member = null
                )
            )
        }
    }

    @Test
    fun `can register buyer sign`() {
        val envelopeId = anyString()
        val loiSign = givenSignLoi(signingId = envelopeId)
        val signLetterOfIntentInput = givenSignLetterOfIntentInput(recipientId = loiSign.buyerSign.id, envelopeId = envelopeId)
        val loiSignExpected = loiSign.copy(buyerSign = loiSign.buyerSign.copy(completedAt = closedAt))
        val member = givenMember(MemberTypeEnum.BUYER.key, loiSign.buyerSign.id.toLong())
        signLetterOfIntent.sign(signLetterOfIntentInput)

        verify { letterOfIntentSignRepository.update(loiSignExpected) }
        verify {
            createLetterOfIntentHistoryEntry.execute(
                CreateLetterOfIntentHistoryEntryInput(
                    dealId = loiRound.dealId,
                    loiId = loiRound.id,
                    type = LetterOfIntentHistoryEntryType.SIGNATURE,
                    description = "Signed the document",
                    member = member
                )
            )
        }
    }

    @Test
    fun `fails if loiSign does not exist`() {
        val signLetterOfIntentInput = givenSignLetterOfIntentInput(envelopeId = anyString())
        givenNoSignLoi()

        assertThrows<RuntimeException> { signLetterOfIntent.sign(signLetterOfIntentInput) }
    }

    private fun givenSignLetterOfIntentInput(envelopeId: String, recipientId: String = "1") = SignLetterOfIntentInput(
        recipientId = recipientId,
        envelopeId = envelopeId
    )

    private fun givenSignLoi(signingId: String) = LoiSignBuilder()
        .apply {
            this.loiId = loiRound.id
            this.signingId = signingId
        }
        .build()
        .also { loiSing ->
            every { letterOfIntentSignRepository.findBySigningId(loiSing.signingId) } returns loiSing
        }

    private fun givenNoSignLoi() {
        every { letterOfIntentSignRepository.findBySigningId(any()) } returns null
    }

    private fun givenMember(type: String, id: Long): Member =
        MemberBuilder()
            .withId(id)
            .withTypeKey(type)
            .build()
            .also { member ->
                every { memberRepository.findById(id) } returns member
            }
}
