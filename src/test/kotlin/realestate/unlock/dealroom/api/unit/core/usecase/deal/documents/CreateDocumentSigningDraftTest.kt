package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.document.DocumentSigner
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.FinalDocument
import realestate.unlock.dealroom.api.core.entity.kfile.KFileWithUrl
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.gateway.sign.*
import realestate.unlock.dealroom.api.core.repository.document.DocumentSignRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetCompleteDeal
import realestate.unlock.dealroom.api.core.usecase.deal.documents.CreateDocumentSigningDraft
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentAndCurrentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.GetDocumentEmailParams
import realestate.unlock.dealroom.api.core.usecase.deal.documents.UpsertDocumentSigner
import realestate.unlock.dealroom.api.core.usecase.file.GetFileUrl
import realestate.unlock.dealroom.api.utils.entity.deal.CompleteDealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class CreateDocumentSigningDraftTest {
    @MockK(relaxed = true)
    private lateinit var findDocumentAndCurrentRound: FindDocumentAndCurrentRound

    @MockK(relaxed = true)
    private lateinit var getCompleteDeal: GetCompleteDeal

    @MockK(relaxed = true)
    private lateinit var getFileUrl: GetFileUrl

    @MockK(relaxed = true)
    private lateinit var signGateway: SignGateway

    @MockK(relaxed = true)
    private lateinit var documentSignRepository: DocumentSignRepository

    @MockK(relaxed = true)
    private lateinit var upsertDocumentSigner: UpsertDocumentSigner

    private lateinit var createDocumentSigningDraft: CreateDocumentSigningDraft

    private val getDocumentEmailParams = GetDocumentEmailParams()
    private val clock = Clock.systemDefaultZone()

    @BeforeEach
    fun setUp() {
        createDocumentSigningDraft = CreateDocumentSigningDraft(
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            getCompleteDeal = getCompleteDeal,
            getFileUrl = getFileUrl,
            signGateway = signGateway,
            getDocumentEmailParams = getDocumentEmailParams,
            documentSignRepository = documentSignRepository,
            clock = clock,
            upsertDocumentSigner = upsertDocumentSigner
        )
    }

    @Test
    fun `can create document draft to sign`() {
        val member = MemberObjectMother.buyer()
        val dealId = 123L
        val deal = getDealSummary(dealId = dealId)
        val document = DocumentBuilder().build()
        val finalDoc = DocumentRoundObjectMother.finalDocument(documentId = document.id)
        buildRecipients(document.id, deal, member)
        val slot = mutableListOf<DocumentSigner>()

        every { signGateway.beginFlow(any()) } returns BeginSignFlowOutput(signingId = anyString())
        every { findDocumentAndCurrentRound.byDealIdAndDocumentType<FinalDocument>(dealId, document.type) } returns Pair(document.copy(currentRoundId = finalDoc.id), finalDoc)
        every { upsertDocumentSigner(capture(slot)) } returns Unit
        createDocumentSigningDraft(dealId = dealId, member, document.type)

        verify(exactly = 1) { signGateway.beginFlow(any()) }
        verify(exactly = 3) { upsertDocumentSigner(any()) }

        Assertions.assertEquals(slot.count { it.docusignDocumentId == null }, 1)
        Assertions.assertEquals(slot.count { it.envelopeId.isNullOrEmpty() }, 1)
    }

    @Test
    fun `create valid input for document draft`() {
        val member = MemberObjectMother.buyer()
        val dealId = 123L
        val deal = getDealSummary(dealId = dealId)
        val psaId = anyId()
        val finalDoc = DocumentRoundObjectMother.finalDocument(documentId = psaId)
        val document = DocumentBuilder().apply {
            id = psaId
            currentRoundId = finalDoc.id
        }.build()
        val slot = slot<BeginSignFlowInput>()
        buildRecipients(document.id, deal, member)
        every { signGateway.beginFlow(capture(slot)) } returns BeginSignFlowOutput(signingId = anyString())
        every { findDocumentAndCurrentRound.byDealIdAndDocumentType<FinalDocument>(dealId, document.type) } returns Pair(document, finalDoc)
        every { getFileUrl(finalDoc.fileId) } returns KFileWithUrl(kFileId = anyString(), url = "http://google.com", name = "pepe.pdf")

        createDocumentSigningDraft(dealId = dealId, member, documentType = document.type)
        Assertions.assertTrue(slot.isCaptured)
        Assertions.assertTrue(!slot.isNull)
        val input = slot.captured

        Assertions.assertEquals(EnvelopeStatus.DRAFT, input.envelopeStatus)
        Assertions.assertEquals(2, input.recipients.signers.size)
        Assertions.assertEquals(1, input.recipients.signers.count { it.email == member.email })
        Assertions.assertEquals(document.currentRoundId?.toString(), input.docusignDocument.id)
        Assertions.assertEquals("pdf", input.docusignDocument.extension)
    }

    private fun getDealSummary(dealId: Long = 123): CompleteDeal {
        val deal = CompleteDealBuilder().apply { id = dealId }.build()
        every { getCompleteDeal.toCompleteDeal(dealId) } returns deal
        return deal
    }

    private fun buildRecipients(psaId: Long, deal: CompleteDeal, member: Member): Recipients {
        val signers = listOf(
            DocumentSigner(
                documentId = psaId,
                signerTeamType = MemberDealTeam.SELLER,
                signerEmail = "fake",
                signerFirstName = "name",
                signerLastName = "last",
                envelopeId = "",
                docusignDocumentId = "",
                createdAt = OffsetDateTime.now(),
                signCompletedAt = null,
                signerRecipientId = "OL2A"
            ),
            DocumentSigner(documentId = psaId, member, OffsetDateTime.now())
        )

        every { documentSignRepository.findByDocumentId(psaId) } returns signers

        return Recipients(
            signers = signers.map {
                Signer(
                    id = it.signerRecipientId,
                    name = it.fullName,
                    email = it.signerEmail!!,
                    emailParams = getDocumentEmailParams.getSignerEmailParams(it.fullName, deal, DocumentType.PSA.name)
                )
            },
            carbonCopies = emptyList()
        )
    }
}
