package realestate.unlock.dealroom.api.unit.core.usecase.member

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.collection.IsEmptyCollection
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.member.GetMembersByDealCategoryIdAndTypeKey
import realestate.unlock.dealroom.api.utils.entity.deal.DealCategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother

@ExtendWith(MockKExtension::class)
class GetMembersByDealCategoryIdAndTypeKeyTest {

    @MockK
    private lateinit var dealCategoryRepository: DealCategoryRepository

    @MockK
    private lateinit var memberRepository: MemberRepository

    private lateinit var getMembersByDealCategoryIdAndTypeKey: GetMembersByDealCategoryIdAndTypeKey

    @BeforeEach
    fun setUp() {
        getMembersByDealCategoryIdAndTypeKey = GetMembersByDealCategoryIdAndTypeKey(
            dealCategoryRepository = dealCategoryRepository,
            memberRepository = memberRepository
        )
    }

    @Test
    fun `Can find a member by type`() {
        // Given
        val dealId = 99L
        val dealCategoryId = 71L
        givenDealCategory(dealCategoryId, dealId)
        val (seller, _) = givenMembersForDeal(dealId)

        // When
        val members = getMembersByDealCategoryIdAndTypeKey.findByMemberType(dealCategoryId, MemberTypeEnum.SELLER)

        // then
        assertThat(members, equalTo(listOf(seller)))
    }

    @Test
    fun `Returns empty when there is not match by type`() {
        // Given
        val dealId = 92L
        val dealCategoryId = 76L
        givenDealCategory(dealCategoryId, dealId)
        givenMembersForDeal(dealId)

        // When
        val members = getMembersByDealCategoryIdAndTypeKey.findByMemberType(dealCategoryId, MemberTypeEnum.SELLER_SUPPORT)

        // then
        assertThat(members, IsEmptyCollection())
    }

    @Test
    fun `Can find a member by type  by type or team`() {
        // Given
        val dealId = 99L
        val dealCategoryId = 71L
        givenDealCategory(dealCategoryId, dealId)
        val (seller, _) = givenMembersForDeal(dealId)

        // When
        val members = getMembersByDealCategoryIdAndTypeKey.findByMemberTypeOrMemberTeam(dealCategoryId, MemberTypeEnum.SELLER)

        // then
        assertThat(members, equalTo(listOf(seller)))
    }

    @Test
    fun `Cant find a member by type and returns one team`() {
        // Given
        val dealId = 99L
        val dealCategoryId = 71L
        givenDealCategory(dealCategoryId, dealId)
        val (seller, _) = givenMembersForDeal(dealId)

        // When
        val members = getMembersByDealCategoryIdAndTypeKey.findByMemberTypeOrMemberTeam(dealCategoryId, MemberTypeEnum.SELLER_BROKER)

        // then
        assertThat(members, equalTo(listOf(seller)))
    }

    @Test
    fun `Returns empty when there is not match by type or team`() {
        // Given
        val dealId = 92L
        val dealCategoryId = 76L
        givenDealCategory(dealCategoryId, dealId)
        every { memberRepository.findByDealId(dealId) } returns setOf(MemberObjectMother.buyer())
        // When
        val members = getMembersByDealCategoryIdAndTypeKey.findByMemberTypeOrMemberTeam(dealCategoryId, MemberTypeEnum.SELLER_SUPPORT)

        // then
        assertThat(members, IsEmptyCollection())
    }

    private fun givenDealCategory(dealCategoryId: Long, dealId: Long) = DealCategoryBuilder().apply {
        this.id = dealCategoryId
        this.dealId = dealId
        this.categoryKey = "building_documents"
    }.build().also { dealCategory ->
        every { dealCategoryRepository.findById(dealCategory.id) } returns dealCategory
    }

    private fun givenMembersForDeal(dealId: Long) = Pair(MemberObjectMother.seller(), MemberObjectMother.buyer()).also {
        every { memberRepository.findByDealId(dealId) } returns setOf(it.first, it.second)
    }
}
