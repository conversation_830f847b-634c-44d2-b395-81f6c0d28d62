package realestate.unlock.dealroom.api.unit.core.usecase.dashboard.calendar

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEvent
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventType
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput
import realestate.unlock.dealroom.api.core.usecase.calendar.GetCalendarDealEvents
import realestate.unlock.dealroom.api.core.usecase.calendar.GetCalendarDocumentEvents
import realestate.unlock.dealroom.api.core.usecase.calendar.GetCalendarEvents
import realestate.unlock.dealroom.api.core.usecase.calendar.GetCalendarReportEvents
import realestate.unlock.dealroom.api.core.usecase.calendar.GetCalendarTaskEvents
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class GetCalendarEventsTest {

    @MockK
    private lateinit var getCalendarDealEvents: GetCalendarDealEvents

    @MockK
    private lateinit var getCalendarReportEvents: GetCalendarReportEvents

    @MockK
    private lateinit var getCalendarTaskEvents: GetCalendarTaskEvents

    @MockK
    private lateinit var getCalendarDocumentEvents: GetCalendarDocumentEvents

    private lateinit var getCalendarEvents: GetCalendarEvents

    @BeforeEach
    fun setUp() {
        getCalendarEvents = GetCalendarEvents(
            getCalendarDealEvents = getCalendarDealEvents,
            getCalendarReportEvents = getCalendarReportEvents,
            getCalendarTaskEvents = getCalendarTaskEvents,
            getCalendarDocumentEvents = getCalendarDocumentEvents
        )
    }

    @Test
    fun `it retrieves all events grouped by date and sorted`() {
        val untilDays = 7
        val today = LocalDate.now()
        val eventWithDateToday = getEvent(type = CalendarEventType.REPORT, date = today)
        val eventWithDateInFiveDays = getEvent(type = CalendarEventType.REPORT, date = today.plusDays(5))
        val eventWitDateInSixDays = getEvent(type = CalendarEventType.CLOSING_DATE, date = today.plusDays(6))
        val eventWitDateInTwoDays = getEvent(type = CalendarEventType.DUE_DILIGENCE, date = today.plusDays(2))
        val eventWitDateTomorrow = getEvent(type = CalendarEventType.DUE_DILIGENCE, date = today.plusDays(1))
        val eventWithDateYesterday = getEvent(type = CalendarEventType.TASK, date = today.minusDays(1))
        val eventWithDateTwoDaysAgo = getEvent(type = CalendarEventType.DOCUMENT_PSA, date = today.minusDays(2))

        every { getCalendarDealEvents.get(any()) } returns listOf(
            eventWitDateInSixDays,
            eventWitDateInTwoDays,
            eventWitDateTomorrow
        )

        every { getCalendarReportEvents.get(any()) } returns listOf(
            eventWithDateInFiveDays,
            eventWithDateToday
        )

        every { getCalendarTaskEvents.get(any()) } returns listOf(
            eventWithDateYesterday
        )

        every { getCalendarDocumentEvents.get(any()) } returns listOf(
            eventWithDateTwoDaysAgo
        )

        val result = getCalendarEvents(CalendarEventsInput(null, untilDays, null, null, organizationId = "ABC"))

        assertThat(result, hasSize(7))
        assertThat(result[0].dealId, equalTo(eventWithDateTwoDaysAgo.dealId))
        assertThat(result[1].dealId, equalTo(eventWithDateYesterday.dealId))
        assertThat(result[2].dealId, equalTo(eventWithDateToday.dealId))
        assertThat(result[3].dealId, equalTo(eventWitDateTomorrow.dealId))
        assertThat(result[4].dealId, equalTo(eventWitDateInTwoDays.dealId))
        assertThat(result[5].dealId, equalTo(eventWithDateInFiveDays.dealId))
        assertThat(result[6].dealId, equalTo(eventWitDateInSixDays.dealId))
    }

    private fun getEvent(type: CalendarEventType, date: LocalDate) =
        CalendarEvent(
            type = type,
            date = date,
            dealId = anyId(),
            eventId = null,
            propertyTitle = "name",
            isImportant = true
        )
}
