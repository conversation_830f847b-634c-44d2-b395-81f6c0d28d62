package realestate.unlock.dealroom.api.unit.core.usecase.deal.report

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.reports.*
import realestate.unlock.dealroom.api.core.usecase.deal.reports.GetDealReportsByDealId
import realestate.unlock.dealroom.api.core.usecase.deal.reports.ValidateReportNotDuplicated
import realestate.unlock.dealroom.api.core.usecase.exception.deal.report.DuplicatedReportException
import realestate.unlock.dealroom.api.utils.entity.report.ReportBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId

@ExtendWith(MockKExtension::class)
class ValidateReportNotDuplicatedTest {

    @MockK
    private lateinit var getDealReportsByDealId: GetDealReportsByDealId

    private lateinit var validateReportNotDuplicated: ValidateReportNotDuplicated

    @BeforeEach
    fun setUp() {
        validateReportNotDuplicated = ValidateReportNotDuplicated(getDealReportsByDealId)
    }

    @Test
    fun `if there is not any report with the same type and vendor, it can be created successfully`() {
        val givenDealId = anyId()
        val asbestosReport = ReportBuilder().apply {
            dealId = givenDealId
        }.build()
        every { getDealReportsByDealId(givenDealId) } returns listOf(asbestosReport)

        assertDoesNotThrow {
            validateReportNotDuplicated.validateCreation(
                dealId = givenDealId,
                reportType = "SEISMIC_REPORT",
                vendor = "EBI"
            )
        }
    }

    @Test
    fun `if there is any report with the same type and vendor, it should throw an exception`() {
        val givenDealId = anyId()
        val asbestosReport = ReportBuilder().apply {
            dealId = givenDealId
        }.build()
        every { getDealReportsByDealId(givenDealId) } returns listOf(asbestosReport)

        assertThrows<DuplicatedReportException> {
            validateReportNotDuplicated.validateCreation(
                dealId = givenDealId,
                reportType = asbestosReport.type,
                vendor = asbestosReport.vendorKey
            )
        }
    }

    @Test
    fun `if there is not any report with the same type and vendor, it can be updated successfully`() {
        val givenDealId = anyId()
        val asbestosReport = ReportBuilder().apply {
            dealId = givenDealId
        }.build()
        every { getDealReportsByDealId(givenDealId) } returns listOf(asbestosReport)

        assertDoesNotThrow {
            validateReportNotDuplicated.validateUpdate(
                dealId = givenDealId,
                reportType = asbestosReport.type,
                vendor = asbestosReport.vendorKey,
                reportId = asbestosReport.id
            )
        }
    }

    @Test
    fun `if there is any report with the same type and vendor with different id, it should throw an exception`() {
        val givenDealId = anyId()
        val asbestosReport = ReportBuilder().apply {
            dealId = givenDealId
        }.build()
        every { getDealReportsByDealId(givenDealId) } returns listOf(asbestosReport)

        assertThrows<DuplicatedReportException> {
            validateReportNotDuplicated.validateUpdate(
                dealId = givenDealId,
                reportType = asbestosReport.type,
                vendor = asbestosReport.vendorKey,
                reportId = 123L
            )
        }
    }

    @Test
    fun `creating an order with type OTHER should be possible no matter if there is another order with the same type`() {
        val givenDealId = anyId()
        val otherReport = ReportBuilder().apply {
            type = ReportType.OTHER
            dealId = givenDealId
        }.build()
        every { getDealReportsByDealId(givenDealId) } returns listOf(otherReport)

        assertDoesNotThrow {
            validateReportNotDuplicated.validateCreation(
                dealId = givenDealId,
                reportType = ReportType.OTHER,
                vendor = otherReport.vendorKey
            )
        }
    }

    @Test
    fun `creating an order with vendor OTHER should be possible no matter if there is another order with the same vendor`() {
        val givenDealId = anyId()
        val otherReport = ReportBuilder().apply {
            vendorKey = ReportVendor.OTHER
            dealId = givenDealId
        }.build()
        every { getDealReportsByDealId(givenDealId) } returns listOf(otherReport)

        assertDoesNotThrow {
            validateReportNotDuplicated.validateCreation(
                dealId = givenDealId,
                reportType = otherReport.type,
                vendor = ReportVendor.OTHER
            )
        }
    }

    @Test
    fun `updating an order with type OTHER should be possible no matter if there is another order with the same type`() {
        val givenDealId = anyId()
        val otherReport = ReportBuilder().apply {
            type = ReportType.OTHER
            dealId = givenDealId
        }.build()
        val anotherReport = ReportBuilder().apply {
            dealId = givenDealId
        }.build()
        every { getDealReportsByDealId(givenDealId) } returns listOf(otherReport)

        assertDoesNotThrow {
            validateReportNotDuplicated.validateUpdate(
                dealId = givenDealId,
                reportType = ReportType.OTHER,
                vendor = otherReport.vendorKey,
                reportId = anotherReport.id
            )
        }
    }

    @Test
    fun `updating an order with vendor OTHER should be possible no matter if there is another order with the same vendor`() {
        val givenDealId = anyId()
        val otherReport = ReportBuilder().apply {
            vendorKey = ReportVendor.OTHER
            dealId = givenDealId
        }.build()
        val anotherReport = ReportBuilder().apply {
            dealId = givenDealId
        }.build()

        every { getDealReportsByDealId(givenDealId) } returns listOf(otherReport)

        assertDoesNotThrow {
            validateReportNotDuplicated.validateUpdate(
                dealId = givenDealId,
                reportType = otherReport.type,
                vendor = ReportVendor.OTHER,
                reportId = anotherReport.id
            )
        }
    }
}
