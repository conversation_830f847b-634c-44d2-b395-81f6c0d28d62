package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntryType
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRound.Companion.ALL_STATES
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.loi.MedicalLoiRound
import realestate.unlock.dealroom.api.core.entity.loi.file.LetterOfIntentFile
import realestate.unlock.dealroom.api.core.entity.loi.input.MedicalRoundInput
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.loi.*
import realestate.unlock.dealroom.api.core.repository.loi.MedicalLetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.repository.loi.SaveMedicalLetterOfIntentRound
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.usecase.deal.update.DealUpdater
import realestate.unlock.dealroom.api.core.usecase.email.loi.SendLoiSentEmail
import realestate.unlock.dealroom.api.core.usecase.loi.*
import realestate.unlock.dealroom.api.core.usecase.property.PropertyUpdater
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.utils.entity.deal.CompleteDealBuilder
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.MedicalOfferBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.OfferInputBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.*

@ExtendWith(MockKExtension::class)
class CreateMedicalLoiRoundTest {

    private val givenDealId = 676L
    private val completeDeal = CompleteDealBuilder()
        .apply { id = givenDealId }
        .apply { contractExecutedDate = LocalDate.now().plusDays(7) }
        .build()
    private val givenOfferFileInput = FileInput(uid = anyString(), name = anyString())
    private val buyer = MemberObjectMother.buyer()

    private val letterOfIntentRoundRepository: MedicalLetterOfIntentRoundRepository = mockk(relaxed = true)
    private val confirmLetterOfIntentFiles: ConfirmLetterOfIntentFiles = mockk(relaxed = true)
    private val createLetterOfIntentHistoryEntry: CreateLetterOfIntentHistoryEntry = mockk(relaxed = true)
    private val memberRepository: MemberRepository = mockk {
        every { findById(buyer.id) } returns buyer
    }
    private val dealRepository: DealRepository = mockk {
        every { findById(completeDeal.id) } returns buildDeal(completeDeal)
    }
    private val dealUpdater: DealUpdater = mockk {
        every { update(any()) } returnsArgument(0)
    }
    private val propertyRepository: PropertyRepository = mockk {
        every { findById(completeDeal.propertyId) } returns completeDeal.property
    }
    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)
    private val sendLoiSentEmail: SendLoiSentEmail = mockk(relaxed = true)
    private val letterOfIntentDraftRepository: LetterOfIntentDraftRepository = mockk(relaxed = true)
    private val propertyUpdater: PropertyUpdater = mockk()

    private val createMedicalLoiRound = CreateMedicalLoiRound(
        letterOfIntentRoundRepository = letterOfIntentRoundRepository,
        dealRepository = dealRepository,
        propertyRepository = propertyRepository,
        clock = clock,
        createLoiRound = CreateLoiRound(
            sendLoiSentEmail = sendLoiSentEmail,
            letterOfIntentDraftRepository = letterOfIntentDraftRepository,
            confirmLetterOfIntentFiles = confirmLetterOfIntentFiles,
            createLetterOfIntentHistoryEntry = createLetterOfIntentHistoryEntry,
            memberRepository = memberRepository
        ),
        dealUpdater = dealUpdater,
        propertyUpdater = propertyUpdater
    )

    @BeforeEach
    fun beforeEachTest() {
        TransactionManager.initialize(mockk(relaxed = true))
        every { propertyUpdater.update(any(), shouldSendUpdateNotification = false) } returns mockk()
    }

    @Test
    fun `can create a loi`() {
        // given
        val offerInput = OfferInputBuilder().apply {
            this.dealId = givenDealId
            this.memberId = buyer.id
            this.files = listOf(givenOfferFileInput)
        }.build()
        val expectedLoi = givenLoi(
            dealId = givenDealId,
            loiStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION,
            files = listOf(
                givenLetterOfIntentFile(
                    kFileId = givenOfferFileInput.uid,
                    name = givenOfferFileInput.name
                )
            )
        )
        givenDealWithActiveLoi(dealId = givenDealId, loi = null)
        every { letterOfIntentRoundRepository.save(any()) } returns expectedLoi

        // when
        val result = createMedicalLoiRound.create(offerInput)

        // then
        assertThat(result, IsEqual(expectedLoi))
        verifyLoiRoundSaved(offerInput)
        verify { confirmLetterOfIntentFiles.confirm(offerInput.files, expectedLoi) }
        verify(exactly = 0) { letterOfIntentRoundRepository.update(any()) }
    }

    @Test
    fun `can create a loi if its already in negotiation for the deal and aborts the current`() {
        // given
        val loi = givenLoi(dealId = givenDealId, loiStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION)
        givenDealWithActiveLoi(givenDealId, loi)
        val offerInput = OfferInputBuilder().apply {
            this.dealId = loi.dealId
            this.memberId = buyer.id
            this.files = listOf(givenOfferFileInput)
        }.build()
        val expectedLoi = givenLoi(
            dealId = givenDealId,
            loiStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION,
            files = listOf(
                givenLetterOfIntentFile(
                    kFileId = givenOfferFileInput.uid,
                    name = givenOfferFileInput.name
                )
            )
        )
        every { letterOfIntentRoundRepository.save(any()) } returns expectedLoi

        // when
        val result = createMedicalLoiRound.create(offerInput)

        // then
        assertThat(result, IsEqual(expectedLoi))
        verifyLoiRoundSaved(offerInput)
        verify { confirmLetterOfIntentFiles.confirm(offerInput.files, expectedLoi) }
        verify { letterOfIntentRoundRepository.updateLoiStatus(loiId = expectedLoi.id, status = LetterOfIntentRoundStatus.ABORTED) }
    }

    @Test
    fun `can create a loi if its rejected`() {
        // given
        val loi = givenLoi(dealId = givenDealId, loiStatus = LetterOfIntentRoundStatus.REJECTED)
        givenDealWithActiveLoi(givenDealId, loi)
        val offerInput = OfferInputBuilder().apply {
            this.dealId = loi.dealId
            this.memberId = buyer.id
            this.files = listOf(givenOfferFileInput)
        }.build()
        val expectedLoi = givenLoi(
            dealId = givenDealId,
            loiStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION,
            files = listOf(
                givenLetterOfIntentFile(
                    kFileId = givenOfferFileInput.uid,
                    name = givenOfferFileInput.name
                )
            )
        )
        every { letterOfIntentRoundRepository.save(any()) } returns expectedLoi

        // when
        val result = createMedicalLoiRound.create(offerInput)

        // then
        assertThat(result, IsEqual(expectedLoi))
        verifyLoiRoundSaved(offerInput)
        verify { confirmLetterOfIntentFiles.confirm(offerInput.files, expectedLoi) }
        verify(exactly = 0) { letterOfIntentRoundRepository.update(any()) }
    }

    @Test
    fun `when created also creates a history entry`() {
        // given
        val offerInput = OfferInputBuilder().apply {
            this.dealId = givenDealId
            this.memberId = buyer.id
            this.files = listOf(givenOfferFileInput)
        }.build()
        val expectedLoi = givenLoi(
            dealId = givenDealId,
            loiStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION,
            files = listOf(givenLetterOfIntentFile(kFileId = givenOfferFileInput.uid, name = givenOfferFileInput.name)),
            comments = "some comments"
        )
        givenDealWithActiveLoi(dealId = givenDealId, loi = null)
        every { letterOfIntentRoundRepository.save(any()) } returns expectedLoi

        // when
        createMedicalLoiRound.create(offerInput)

        // then
        verify {
            createLetterOfIntentHistoryEntry.execute(
                CreateLetterOfIntentHistoryEntryInput(
                    dealId = expectedLoi.dealId,
                    loiId = expectedLoi.id,
                    type = LetterOfIntentHistoryEntryType.OFFER,
                    description = null,
                    kFile = expectedLoi.offer.files.map { file -> KFile(file.kFileId, file.name) }.first(),
                    member = buyer
                )
            )
        }
    }

    @Test
    fun `when created for a new deal updates it status to loi submitted and data`() {
        // given
        val offerInput = OfferInputBuilder().apply {
            this.dealId = givenDealId
            this.memberId = buyer.id
            this.files = listOf(givenOfferFileInput)
        }.build()
        val expectedLoi = givenLoi(
            dealId = givenDealId,
            loiStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION,
            files = listOf(
                givenLetterOfIntentFile(
                    kFileId = givenOfferFileInput.uid,
                    name = givenOfferFileInput.name
                )
            )
        )
        givenDealWithActiveLoi(dealId = givenDealId, loi = null)
        every { letterOfIntentRoundRepository.save(any()) } returns expectedLoi

        // when
        createMedicalLoiRound.create(offerInput)

        // then
        verify {
            dealUpdater.update(
                buildDeal(completeDeal)
                    .copy(
                        updatedAt = OffsetDateTime.now(clock),
                        offerPrice = expectedLoi.offer.salesPrice,
                        stage = Stage.OFFER,
                        // dealType = expectedLoi.offer.lease.condition,
                        vertical = expectedLoi.vertical,
                        lease = completeDeal.lease.copy(
                            rent = expectedLoi.offer.lease.rent,
                            type = expectedLoi.offer.lease.type,
                            rentIncrease = expectedLoi.offer.lease.rentIncrease,
                            increaseEveryYear = expectedLoi.offer.lease.increaseEveryYear,
                            length = expectedLoi.offer.lease.length,
                            expirationYear = expectedLoi.offer.lease.expirationYear,
                            numberOfOptions = expectedLoi.offer.lease.numberOfOptions,
                            optionLengths = expectedLoi.offer.lease.optionLengths,
                            rentCpi = expectedLoi.offer.lease.rentCpi,
                            rentStepType = expectedLoi.offer.lease.rentStepType,
                            dueDiligenceNumber = expectedLoi.offer.dueDiligence.number,
                            closingPeriod = expectedLoi.offer.closing.period,
                            closingPeriodExtension = expectedLoi.offer.closing.periodExtension
                        ),
                        guaranteeType = expectedLoi.guaranteeType,
                        earnestMoneyDeposit = expectedLoi.offer.earnestMoneyDeposit,
                        extensionDeposit = expectedLoi.offer.closing.extensionDeposit
                    )
            )
        }
        verify {
            propertyUpdater.update(
                property = completeDeal.property.copy(
                    updatedAt = OffsetDateTime.now(clock),
                    squareFootage = expectedLoi.propertySquareFootage
                ),
                shouldSendUpdateNotification = false
            )
        }
    }

    @Test
    fun `when created for a new deal sends loi sent email and deletes loi draft`() {
        // given
        val offerInput = OfferInputBuilder().apply {
            this.dealId = givenDealId
            this.memberId = buyer.id
            this.files = listOf(givenOfferFileInput)
        }.build()
        val expectedLoi = givenLoi(
            dealId = givenDealId,
            loiStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION,
            files = listOf(
                givenLetterOfIntentFile(
                    kFileId = givenOfferFileInput.uid,
                    name = givenOfferFileInput.name
                )
            )
        )
        givenDealWithActiveLoi(dealId = givenDealId, loi = null)
        every { letterOfIntentRoundRepository.save(any()) } returns expectedLoi

        // when
        createMedicalLoiRound.create(offerInput)

        // then
        verify { sendLoiSentEmail.send(deal = buildDeal(completeDeal), expectedLoi, offerInput.emailOptions) }
        verify { letterOfIntentDraftRepository.deleteByDealId(givenDealId) }
    }

    private fun givenDealWithActiveLoi(dealId: Long, loi: MedicalLoiRound?) {
        every {
            letterOfIntentRoundRepository.findByDealIdAndStatus(dealId, ALL_STATES)
        } returns loi
    }

    private fun givenLoi(
        dealId: Long,
        loiStatus: LetterOfIntentRoundStatus,
        files: List<LetterOfIntentFile> = listOf(),
        comments: String? = null
    ) = LoiMedicalRoundBuilder().apply {
        this.status = loiStatus
        this.dealId = dealId
        this.offer = MedicalOfferBuilder()
            .apply { this.files = files }
            .apply { this.comments = comments }
            .build()
    }.build()

    private fun givenLetterOfIntentFile(kFileId: String = anyString(), name: String = anyString()) =
        LetterOfIntentFile(kFileId = kFileId, name = name, id = 39L, memberId = buyer.id)

    private fun buildDeal(completeDeal: CompleteDeal) = DealBuilder()
        .apply { id = completeDeal.id }
        .apply { propertyId = completeDeal.propertyId }
        .apply { stage = completeDeal.stage }
        .apply { loiExecutedDate = completeDeal.loiExecutedDate }
        .apply { contractExecutedDate = completeDeal.contractExecutedDate }
        .apply { diligenceExpirationDate = completeDeal.diligenceExpirationDate }
        .apply { initialClosingDate = completeDeal.initialClosingDate }
        .apply { outsideClosingDate = completeDeal.outsideClosingDate }
        .apply { updatedAt = completeDeal.updatedAt }
        .apply { createdAt = completeDeal.createdAt }
        .apply { schemaId = completeDeal.schemaData.schemaId }
        .apply { leaderId = completeDeal.leaderId }
        .build()

    private fun verifyLoiRoundSaved(offerInput: MedicalRoundInput) {
        verify {
            letterOfIntentRoundRepository.save(
                SaveMedicalLetterOfIntentRound(
                    dealId = offerInput.dealId,
                    status = LetterOfIntentRoundStatus.IN_NEGOTIATION,
                    tenantName = offerInput.tenantName,
                    propertySquareFootage = offerInput.propertySquareFootage,
                    vertical = offerInput.vertical,
                    brokerName = offerInput.broker.name,
                    brokerCompanyName = offerInput.broker.company,
                    offerClosingCost = offerInput.contractExecution.closingCost,
                    offerFiles = offerInput.files.map {
                        SaveLetterOfIntentFile(
                            uid = it.uid,
                            name = it.name
                        )
                    },
                    offerPrice = offerInput.salesPrice,
                    offerLeaseRent = offerInput.lease.rent,
                    offerLeaseType = offerInput.lease.type,
                    offerLeaseCondition = offerInput.lease.condition,
                    offerLeaseRentIncrease = offerInput.lease.rentIncrease,
                    offerLeaseIncreaseEveryYear = offerInput.lease.increaseEveryYear,
                    offerLeaseLength = offerInput.lease.length,
                    offerLeaseExpirationYear = offerInput.lease.expirationYear,
                    offerLeaseNumberOfOptions = offerInput.lease.numberOfOptions,
                    offerLeaseOptionLengths = offerInput.lease.optionLengths,
                    offerClosingPeriod = offerInput.contractExecution.closingPeriod,
                    offerClosingPeriodExtension = offerInput.contractExecution.closingExtensionPeriod,
                    offerClosingExtensionDeposit = offerInput.contractExecution.closingExtensionDeposit,
                    offerContractTermination = offerInput.lease.contractTermination,
                    offerEarnestMoneyDeposit = offerInput.lease.earnestMoneyDeposit,
                    offerDueDiligenceNumber = offerInput.contractExecution.dueDiligenceNumber,
                    offerComments = null,
                    memberId = offerInput.memberId,
                    dealTeam = offerInput.dealTeam,
                    offerRentCpi = offerInput.lease.rentCpi,
                    offerRentStepType = offerInput.lease.rentStepType,
                    offerCustomSections = offerInput.offerCustomSections?.let {
                        SaveOfferCustomSections(
                            sections = it.sections.map { section ->
                                SaveOfferCustomSection(
                                    title = section.title,
                                    content = section.content
                                )
                            }
                        )
                    },
                    guaranteeType = offerInput.lease.guaranteeType
                )
            )
        }
    }
}
