package realestate.unlock.dealroom.api.unit.core.usecase.task

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.DealData
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedInput
import realestate.unlock.dealroom.api.core.entity.search.SortOrder
import realestate.unlock.dealroom.api.core.entity.task.Priority
import realestate.unlock.dealroom.api.core.entity.task.PriorityValue
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.task.TaskExternalDataDefinition
import realestate.unlock.dealroom.api.core.entity.task.template.TaskTemplate
import realestate.unlock.dealroom.api.core.repository.deal.schema.DealSchemaRepository
import realestate.unlock.dealroom.api.core.repository.task.template.TaskTemplateRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.json.IncludeNullJsonMapper
import realestate.unlock.dealroom.api.utils.json.IncludeNullJsonMapperCC

class TaskExternalFieldsValidator : BaseFunctionalWithoutRestTest() {

    private val sqlClient = Context.injector.getInstance(SqlClient::class.java)
    private val taskTemplateRepository = Context.injector.getInstance(TaskTemplateRepository::class.java)
    private val dealSchemaRepository = Context.injector.getInstance(DealSchemaRepository::class.java)

    // What happend if this test fail?
    // It means that you are changing a prop that is used by one task
    // Change task and task template whit sql update, also add deal data to attached form data
    @Test
    fun `validate fields for task template`() {
        val dealData = DealData(
            deal = DealBuilder().apply {
                schemaData = getDealSchemaData()
            }.build(),
            property = PropertyBuilder().build()
        ).let {
            IncludeNullJsonMapperCC.convertToMap<String, Any>(it)
        }

        getAllDealTemplateKeys().forEach { dealKey ->
            val tasksTemplates = taskTemplateRepository.findByDealTemplateKey(dealKey)
            tasksTemplates.forEach {
                it.verifyExternalDataFields(dealData)
            }
        }
    }

    @Test
    fun `validate fields for task template test works`() {
        val dealData = DealData(
            deal = DealBuilder().build(),
            property = PropertyBuilder().build()
        ).let {
            IncludeNullJsonMapper.convertToMap<String, Any>(it)
        }

        val template = TaskTemplate(
            id = 1,
            key = "",
            taskCategory = "",
            taskType = "",
            memberTypeAssignment = "",
            taskTitle = "",
            taskDescription = "",
            formSchema = emptyMap(),
            data = emptyMap(),
            priority = Priority(PriorityValue.HIGH, 1),
            required = true,
            prioritizeAfterClosing = true,
            visibility = Task.Visibility.ALL,
            taskExternalDataDefinition = TaskExternalDataDefinition("deal.ultra_mega_archie_fake", emptySet())
        )

        Assertions.assertThrows(
            ErrorPropNotFoundException::class.java,
            {
                template.verifyExternalDataFields(dealData)
            },
            "ERROR MUST FAIL"
        )
    }

    private fun TaskTemplate.verifyExternalDataFields(dealData: Map<String, Any>) {
        this.taskExternalDataDefinition.dueDate?.let {
            findField(it, dealData)
        }
        this.taskExternalDataDefinition.formData.forEach {
            findField(it, dealData)
        }
    }

    private fun getAllDealTemplateKeys() =
        sqlClient.getAll(
            query = "SELECT DISTINCT deal_template_key FROM deal_task_template",
            mapHandler = { it["deal_template_key"].toString() }
        )

    private fun findField(fieldId: String, data: Map<String, Any?>): Any? {
        val parts = fieldId.split(".")
        return recursiveFind(data, parts, fieldId)
    }

    private fun recursiveFind(map: Map<String, Any?>, parts: List<String>, fieldId: String): Any? {
        val part = parts[0]
        return when {
            parts.size == 1 && map.containsKey(part) -> map[part]
            parts.size == 1 && !map.containsKey(part) -> throw ErrorPropNotFoundException(fieldId)
            else -> recursiveFind(
                IncludeNullJsonMapper.convertToMap(map[part]!!),
                parts.subList(1, parts.size),
                fieldId
            )
        }
    }

    private class ErrorPropNotFoundException(fieldId: String) : AssertionError("PROPERTY NOT FOUND -> $fieldId PLEASE FIX IT")

    private fun getDealSchemaData(): Map<String, Any> {
        val schemaFields = mutableMapOf<String, Any>()
        dealSchemaRepository.findAll(PaginatedInput(100000, 100000, 0, "created_at", SortOrder.DESC, mapOf())).data.forEach {
                dealSchema ->
            dealSchema.jsonSchema.properties.forEach {
                schemaFields.putIfAbsent(it.key, 1)
            }
        }
        return schemaFields
    }
}
