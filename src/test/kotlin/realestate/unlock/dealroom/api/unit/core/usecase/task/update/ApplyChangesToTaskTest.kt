package realestate.unlock.dealroom.api.unit.core.usecase.task.update

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.nullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.ProcessedTaskUpdate
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus.IN_REVIEW
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus.TO_DO
import realestate.unlock.dealroom.api.core.entity.task.UpdateTaskInput
import realestate.unlock.dealroom.api.core.usecase.task.update.ApplyChangesToTask
import realestate.unlock.dealroom.api.core.usecase.task.update.process.ProcessDefaultTaskChanges
import realestate.unlock.dealroom.api.core.usecase.task.update.process.ProcessDocumentBackAndForthTaskChanges
import realestate.unlock.dealroom.api.core.usecase.task.update.process.ProcessPsaAndLeaseTaskChanges
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class ApplyChangesToTaskTest {

    @MockK
    private lateinit var processDefaultTaskChanges: ProcessDefaultTaskChanges

    @MockK
    private lateinit var processDocumentBackAndForthTaskChanges: ProcessDocumentBackAndForthTaskChanges

    @MockK
    private lateinit var processPsaAndLeaseTaskChanges: ProcessPsaAndLeaseTaskChanges

    private lateinit var applyChangesToTask: ApplyChangesToTask

    @BeforeEach
    fun setUp() {
        applyChangesToTask = ApplyChangesToTask(
            processDefaultTaskChanges = processDefaultTaskChanges,
            processDocumentBackAndForthTaskChanges = processDocumentBackAndForthTaskChanges,
            processPsaAndLeaseTaskChanges = processPsaAndLeaseTaskChanges
        )
    }

    @Test
    fun `applies changes to document_back_and_forth task`() {
        // Given
        val taskId = anyId()
        val member = MemberObjectMother.buyer()
        val completeTask = givenTask(taskId = taskId, typeKey = "document_back_and_forth")
        val updateTaskInput = givenUpdateTaskInput(taskId, member)
        every { processDocumentBackAndForthTaskChanges.process(completeTask, updateTaskInput) } returns ProcessedTaskUpdate(updateTaskInput, completeTask.data)

        // When
        val taskToUpdate = applyChangesToTask.apply(completeTask, updateTaskInput)

        // Then
        assertThat(taskToUpdate.id, equalTo(updateTaskInput.id))
        assertThat(taskToUpdate.statusKey, equalTo(updateTaskInput.status.key))
        assertThat(taskToUpdate.assignedTeam, equalTo(completeTask.assignedTeam))
        assertThat(taskToUpdate.assignedBuyerId, equalTo(completeTask.assignedBuyer.id))
        assertThat(taskToUpdate.dueDate, equalTo(updateTaskInput.dueDate))
        assertThat(taskToUpdate.attachedForm, nullValue())
    }

    @Test
    fun `applies changes to others tasks`() {
        // Given
        val taskId = anyId()
        val member = MemberObjectMother.buyer()
        val completeTask = givenTask(taskId = taskId, typeKey = "form")
        val form = mapOf("key" to "value")
        val updateTaskInput = givenUpdateTaskInput(taskId = taskId, member = member, attachedForm = form)
        every { processDefaultTaskChanges.process(completeTask, updateTaskInput) } returns ProcessedTaskUpdate(updateTaskInput, completeTask.data)

        // When
        val taskToUpdate = applyChangesToTask.apply(completeTask, updateTaskInput)

        // Then
        assertThat(taskToUpdate.id, equalTo(updateTaskInput.id))
        assertThat(taskToUpdate.statusKey, equalTo(updateTaskInput.status.key))
        assertThat(taskToUpdate.assignedTeam, equalTo(completeTask.assignedTeam))
        assertThat(taskToUpdate.assignedBuyerId, equalTo(completeTask.assignedBuyer.id))
        assertThat(taskToUpdate.dueDate, equalTo(updateTaskInput.dueDate))
        assertThat(taskToUpdate.attachedForm, equalTo(form))
    }

    private fun givenTask(taskId: Long, typeKey: String) = TaskBuilder()
        .withId(taskId)
        .withDealCategoryId(anyId())
        .withAssignedTeam(MemberDealTeam.SELLER)
        .withTemplateKey("form")
        .withStatus(TO_DO)
        .withTypeKey(typeKey)
        .build()
    private fun givenUpdateTaskInput(
        taskId: Long,
        member: Member,
        dueDate: LocalDate? = null,
        attachedForm: Map<String, Any>? = null
    ) = UpdateTaskInput(
        id = taskId,
        status = IN_REVIEW,
        dueDate = dueDate,
        attachedForm = attachedForm,
        member = member,
        assignedTeam = null,
        assignedBuyerId = null,
        rejectionReason = null,
        enabled = null
    )
}
