package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.loi.MedicalLoiRound
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.kfileservice.StagingFileToConfirm
import realestate.unlock.dealroom.api.core.usecase.loi.ConfirmLetterOfIntentFiles

@ExtendWith(MockKExtension::class)
class ConfirmMedicalLoiRoundFilesTest {

    @RelaxedMockK
    private lateinit var fileGateway: FileGateway

    private lateinit var confirmLetterOfIntentFiles: ConfirmLetterOfIntentFiles

    @BeforeEach
    fun setUp() {
        confirmLetterOfIntentFiles = ConfirmLetterOfIntentFiles(fileGateway)
    }

    @Test
    fun `can confirm letter of intent staging files`() {
        // given
        val files = listOf(
            FileInput(uid = "file-1-uid", name = "file-1-name"),
            FileInput(uid = "file-2-uid", name = "file-2-name")
        )

        val dealId = 1L
        val loiId = 2L
        val loi = mockk<MedicalLoiRound>()
        every { loi.dealId } returns dealId
        every { loi.id } returns loiId

        // when
        confirmLetterOfIntentFiles.confirm(files = files, loi)

        // then
        verify {
            fileGateway.confirmStagingFile(files.map { expectedStagingFileToConfirmFrom(it, dealId, loiId) })
        }
    }

    private fun expectedStagingFileToConfirmFrom(file: FileInput, dealId: Long, loiId: Long) = StagingFileToConfirm(kFileId = file.uid, path = "deal/$dealId/loi/$loiId")
}
