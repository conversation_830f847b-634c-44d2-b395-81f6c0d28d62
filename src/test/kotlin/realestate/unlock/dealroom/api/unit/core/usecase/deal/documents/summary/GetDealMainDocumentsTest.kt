package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.summary

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.summary.LeaseDocument
import realestate.unlock.dealroom.api.core.entity.document.summary.LoiDocument
import realestate.unlock.dealroom.api.core.entity.document.summary.PSADocument
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealById
import realestate.unlock.dealroom.api.core.usecase.deal.documents.summary.GetDealMainDocuments
import realestate.unlock.dealroom.api.core.usecase.deal.documents.summary.GetLeaseDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.summary.GetLoiDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.summary.GetPSADocument
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder

class GetDealMainDocumentsTest {

    private val getDealById: GetDealById = mockk()
    private val getLoiDocument: GetLoiDocument = mockk()
    private val getPSADocument: GetPSADocument = mockk()
    private val getLeaseDocument: GetLeaseDocument = mockk()

    private val getDealMainDocuments = GetDealMainDocuments(
        getDealById = getDealById,
        getLoiDocument = getLoiDocument,
        getPSADocument = getPSADocument,
        getLeaseDocument = getLeaseDocument
    )

    @Test
    fun `It retrieves the documents`() {
        // given
        val givenDealId = 12L
        val givenDeal = DealBuilder().withId(givenDealId).build()
        val givenLoiDocument = mockk<LoiDocument>()
        val givenPSADocument = mockk<PSADocument>()
        val givenLeaseDocument = mockk<LeaseDocument>()

        every { getDealById.get(givenDealId) } returns givenDeal
        every { getLoiDocument.get(givenDeal) } returns givenLoiDocument
        every { getPSADocument(givenDeal) } returns givenPSADocument
        every { getLeaseDocument(givenDeal) } returns givenLeaseDocument

        // when
        val mainDocuments = getDealMainDocuments.get(givenDealId)

        // then
        verify { getLoiDocument.get(givenDeal) }
        assertThat(mainDocuments, hasSize(3))
        assertThat(mainDocuments[0], equalTo(givenLoiDocument))
        assertThat(mainDocuments[1], equalTo(givenPSADocument))
        assertThat(mainDocuments[2], equalTo(givenLeaseDocument))
    }
}
