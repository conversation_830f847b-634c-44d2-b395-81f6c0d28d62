package realestate.unlock.dealroom.api.unit.core.usecase.lease

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.exception.DocumentNotFoundException
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.GetDocument
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId

@ExtendWith(MockKExtension::class)
class GetDocumentTest {

    @MockK(relaxed = true)
    private lateinit var documentRepository: DocumentRepository

    private lateinit var getDocument: GetDocument

    @BeforeEach
    fun setUp() {
        getDocument = GetDocument(
            documentRepository = documentRepository
        )
    }

    @Test
    fun `if there is not document it fails`() {
        val dealId = anyId()
        every { documentRepository.findByDealIdAndDocumentType(dealId, DocumentType.LEASE) } returns null
        assertThrows<DocumentNotFoundException> { getDocument.byDealIdAndDocumentType(dealId, DocumentType.LEASE) }
    }

    @Test
    fun `returns lease data`() {
        val dealId = anyId()
        val document = DocumentBuilder().apply { type = DocumentType.LEASE }.build()
        every { documentRepository.findByDealIdAndDocumentType(dealId, DocumentType.LEASE) } returns document
        val response = getDocument.byDealIdAndDocumentType(dealId, document.type)
        Assertions.assertEquals(document, response)
    }
}
