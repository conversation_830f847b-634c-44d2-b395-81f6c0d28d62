package realestate.unlock.dealroom.api.unit.core.usecase.deal.schema

import com.fasterxml.jackson.core.type.TypeReference
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.DealData
import realestate.unlock.dealroom.api.core.entity.deal.SchemaData
import realestate.unlock.dealroom.api.core.entity.deal.schema.DealSchema
import realestate.unlock.dealroom.api.core.entity.deal.schema.JsonSchema
import realestate.unlock.dealroom.api.core.entity.deal.schema.ViewSchema
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.exception.InvalidJsonSchemaException
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealData
import realestate.unlock.dealroom.api.core.usecase.deal.schema.FieldsCalculatorUtils.defaultScale
import realestate.unlock.dealroom.api.core.usecase.deal.schema.GetDealSchemaById
import realestate.unlock.dealroom.api.core.usecase.deal.schema.UpdateDealSchemaData
import realestate.unlock.dealroom.api.core.usecase.deal.schema.ValidateJsonSchema
import realestate.unlock.dealroom.api.core.usecase.deal.update.DealUpdater
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.deal.DealDataBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

@ExtendWith(MockKExtension::class)
class UpdateDealSchemaDataTest {

    @RelaxedMockK
    private lateinit var dealUpdater: DealUpdater

    @MockK
    private lateinit var getDealSchemaById: GetDealSchemaById

    @MockK
    private lateinit var getDealData: GetDealData

    private lateinit var updateDealSchemaData: UpdateDealSchemaData

    @BeforeEach
    fun setUp() {
        updateDealSchemaData = UpdateDealSchemaData(
            getDealData = getDealData,
            dealUpdater = dealUpdater,
            getDealSchemaById = getDealSchemaById,
            validateJsonSchema = ValidateJsonSchema()
        )
    }

    @Test
    fun `can update custom fields`() {
        val schemaId = givenSchema()
        val dealData = givenDeal(schemaId = schemaId, currentCustomData = mapOf("creditType" to anyId()))
        val data = mapOf(
            "updateNotes" to anyString(),
            "pipelineNotes" to anyString(),
            "offerNoi" to 50.6
        )

        updateDealSchemaData.update(dealData.deal.id, data)

        val expectedOfferNoiPSF = data["offerNoi"].toString().toBigDecimal().divide(dealData.property.squareFootage)

        assertDataUpdated(
            dealData.deal.copy(
                schemaData = SchemaData(
                    schemaId,
                    data.plus("offerNoiPsf" to expectedOfferNoiPSF.defaultScale())
                )
            )
        )
    }

    @Test
    fun `can update partially custom fields`() {
        val schemaId = givenSchema()
        val dataBeforeUpdate = mapOf(
            "updateNotes" to anyString(),
            "pipelineNotes" to anyString()
        )
        val dealData = givenDeal(schemaId = schemaId, currentCustomData = dataBeforeUpdate)
        val data = mapOf("offerNoi" to 50.6)

        updateDealSchemaData.partialUpdate(dealData.deal.id, data)

        val expectedOfferNoiPSF = data["offerNoi"].toString().toBigDecimal().divide(dealData.property.squareFootage)

        assertDataUpdated(
            dealData.deal.copy(
                schemaData = SchemaData(
                    schemaId,
                    dataBeforeUpdate.plus(data).plus("offerNoiPsf" to expectedOfferNoiPSF.defaultScale())
                )
            )
        )
    }

    @Test
    fun `fails if custom fails are not valid`() {
        val schemaId = givenSchema()
        val dealData = givenDeal(schemaId = schemaId, currentCustomData = mapOf())
        val data = mapOf("askingNoi" to anyString())

        assertThrows<InvalidJsonSchemaException> {
            updateDealSchemaData.update(dealData.deal.id, data)
        }
    }

    private fun givenSchema() = anyId().also { schemaId ->
        every {
            getDealSchemaById.get(schemaId)
        } returns DealSchema(
            id = schemaId,
            propertyType = PropertyType.MEDICAL,
            viewSchema = ViewSchema(listOf()),
            jsonSchema = JsonSchema("object", JsonMapper.decode(customFieldsSchema(), object : TypeReference<Map<String, Any>>() {})),
            fieldDefinitions = mapOf(),
            createdAt = OffsetDateTime.now()
        )
    }

    private fun givenDeal(schemaId: Long, currentCustomData: Map<String, Any>): DealData = anyId().let { id ->
        DealDataBuilder().apply {
            deal = DealBuilder().withId(id).apply {
                this.schemaData = currentCustomData
                this.schemaId = schemaId
            }.build()
        }.build().also {
            every {
                getDealData.get(id)
            } returns it
        }
    }

    private fun customFieldsSchema() =
        """{ "updateNotes": {"type": "string", "title": "Update Notes"}, 
            |"pipelineNotes": {"type": "string", "title": "Pipeline Notes"}, 
            |"propertyNotes": {"type": "string", "title": "Property Notes"}, 
            |"offerNoi": {"type": "number", "title": "Offer NOI"}, 
            |"askingCapRate": {"type": "number", "title": "Asking Cap Rate", "readOnly": true}, 
            |"askingNoi": {"type": "number", "title": "Asking NOI"}, 
            |"offerCapRate": {"type": "number", "title": "Offer Cap Rate"}, 
            |"creditType": {"type": "number", "title": "Credit Type"}}
        """.trimMargin()

    private fun assertDataUpdated(deal: Deal) {
        verify {
            dealUpdater.updateSchemaData(deal = deal, triggerDealEvent = true)
        }
    }
}
