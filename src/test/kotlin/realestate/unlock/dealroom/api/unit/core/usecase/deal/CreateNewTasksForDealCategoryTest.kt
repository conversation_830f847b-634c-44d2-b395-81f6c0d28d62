package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.core.entity.task.template.TaskTemplate
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.task.CreateNewTasksForDealCategory
import realestate.unlock.dealroom.api.utils.entity.deal.DealCategoryBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import java.time.OffsetDateTime

class CreateNewTasksForDealCategoryTest {
    private lateinit var createNewTasksForDealCategory: CreateNewTasksForDealCategory
    private lateinit var taskRepository: TaskRepository

    companion object {
        private const val PSA_CATEGORY_TYPE = "purchase_and_sale_contract"
    }

    @BeforeEach
    fun setUp() {
        taskRepository = mockk()
        createNewTasksForDealCategory = CreateNewTasksForDealCategory(taskRepository)
    }

    @Test
    fun `do create task from template with different memberType`() {
        val user = CompleteUserObjectMother.admin()
        val buyerSide = MemberObjectMother.buyer()
        val sellerSide = MemberObjectMother.sellerBroker()

        val taskCreated = createNewTasksForDealCategory.create(
            taskTemplates = createTaskTemplateData(buyerSide),
            dealCategory = DealCategoryBuilder().apply {
                dealId = 1
                id = 1
                categoryKey = "Nose"
            }.build(),
            leaderId = buyerSide.id
        )

        MemberTypeEnum.values().forEach { memberType ->
            assertEquals(memberType.team, taskCreated.first { it.title == memberType.name }.assignedTeam)
        }
    }

    private fun createTaskTemplateData(buyer: Member): List<TaskTemplate> {
        return MemberTypeEnum.values().map { memberType ->
            givenTaskTemplate(memberType).also { taskTemplate ->
                givenTaskAfterSave(memberType, buyer, taskTemplate)
            }
        }
    }

    private fun givenTaskAfterSave(
        memberType: MemberTypeEnum,
        buyer: Member,
        taskTemplate: TaskTemplate
    ): Task {
        val input = newTaskToSaveFromTemplate(memberType, buyer, taskTemplate)
        return taskSavedFrom(input, buyer).also {
            every { taskRepository.save(input) } returns it
        }
    }

    private fun newTaskToSaveFromTemplate(
        memberType: MemberTypeEnum,
        buyer: Member,
        taskTemplate: TaskTemplate
    ) = NewTaskToSave(
        assignedBuyerId = buyer.id,
        assignedTeam = memberType.team,
        dealCategoryId = 1,
        typeKey = taskTemplate.taskType,
        statusKey = TaskStatus.TO_DO.key,
        templateKey = taskTemplate.key,
        title = taskTemplate.taskTitle,
        description = taskTemplate.taskDescription,
        formSchema = taskTemplate.formSchema,
        dueDate = null,
        data = taskTemplate.data,
        priority = taskTemplate.priority,
        required = taskTemplate.required,
        afterClosing = taskTemplate.prioritizeAfterClosing,
        visibility = Task.Visibility.ALL,
        taskExternalDataDefinition = TaskExternalDataDefinition(null, emptySet())
    )

    private fun taskSavedFrom(
        input: NewTaskToSave,
        buyer: Member
    ): Task =
        TaskBuilder().apply {
            this.id = 1
            this.dealCategoryId = input.dealCategoryId
            this.assignedBuyer = buyer
            this.assignedTeam = input.assignedTeam
            this.typeKey = input.typeKey
            this.statusKey = TaskStatus.TO_DO
            this.templateKey = input.templateKey
            this.attachedFormData = emptyMap()
            this.title = input.title
            this.description = input.description
            this.dueDate = null
            this.updatedAt = OffsetDateTime.now()
            this.createdAt = OffsetDateTime.now()
            this.formSchema = input.formSchema
            this.rejectionReason = ""
            this.data = input.data
            this.priority = input.priority
            this.required = input.required
            this.prioritizeAfterClosing = input.afterClosing
            this.visibility = Task.Visibility.ALL
            this.enabled = true
        }.build()

    private fun givenTaskTemplate(
        memberType: MemberTypeEnum = MemberTypeEnum.BUYER,
        taskCategory: String = PSA_CATEGORY_TYPE,
        taskType: String = "document_back_and_forth"
    ) = TaskTemplate(
        id = 1,
        key = "",
        taskCategory = taskCategory,
        taskType = taskType,
        memberTypeAssignment = memberType.key,
        taskTitle = memberType.name,
        taskDescription = "",
        formSchema = emptyMap(),
        data = emptyMap(),
        priority = Priority(PriorityValue.HIGH, 1),
        required = true,
        prioritizeAfterClosing = true,
        visibility = Task.Visibility.ALL,
        taskExternalDataDefinition = TaskExternalDataDefinition(null, emptySet())
    )
}
