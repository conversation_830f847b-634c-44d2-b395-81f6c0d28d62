package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.hamcrest.core.IsNull
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRound
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.loi.MedicalLoiRound
import realestate.unlock.dealroom.api.core.entity.loi.MultifamilyLoiRound
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.repository.loi.MedicalLetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.repository.loi.MultifamilyLetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.usecase.loi.GetLetterOfIntentRound
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMultifamilyRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder

class GetLetterOfIntentRoundTest {
    private val dealId: Long = 1
    private val letterOfIntentRoundRepository: LetterOfIntentRoundRepository = mockk()
    private val medicalLetterOfIntentRoundRepository: MedicalLetterOfIntentRoundRepository = mockk()
    private val multifamilyLetterOfIntentRoundRepository: MultifamilyLetterOfIntentRoundRepository = mockk()
    private val propertyRepository: PropertyRepository = mockk()
    private val target =
        GetLetterOfIntentRound(
            medicalLetterOfIntentRoundRepository,
            multifamilyLetterOfIntentRoundRepository,
            letterOfIntentRoundRepository,
            propertyRepository
        )

    @Test
    fun `returns null when no letter of intent offer exists of medical`() {
        // given
        givenProperty(dealId, PropertyType.MEDICAL)
        every { medicalLetterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns null

        // when
        val result = target.getByDealId(dealId)

        // then
        assertThat(result, IsNull())
    }

    @Test
    fun `retrieves a letter of intent offer in negotiation of medical`() {
        // given
        givenProperty(dealId, PropertyType.MEDICAL)
        val loiOffer = givenMedicalLoiOffer(status = LetterOfIntentRoundStatus.IN_NEGOTIATION)
        every { medicalLetterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns loiOffer

        // when
        val result = target.getByDealId(dealId)

        // then
        assertThat(result, IsEqual(loiOffer))
    }

    @Test
    fun `retrieves a letter of intent offer rejected of medical`() {
        // given
        givenProperty(dealId, PropertyType.MEDICAL)
        val loiOffer = givenMedicalLoiOffer(status = LetterOfIntentRoundStatus.REJECTED)
        every { medicalLetterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns loiOffer

        // when
        val result = target.getByDealId(dealId)

        // then
        assertThat(result, IsEqual(loiOffer))
    }

    @Test
    fun `retrieves a letter of intent offer accepted of medical`() {
        // given
        givenProperty(dealId, PropertyType.MEDICAL)
        val loiOffer = givenMedicalLoiOffer(status = LetterOfIntentRoundStatus.ACCEPTED)
        every { medicalLetterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns loiOffer

        // when
        val result = target.getByDealId(dealId)

        // then
        assertThat(result, IsEqual(loiOffer))
    }

    @Test
    fun `returns null when no letter of intent offer exists of multifamily`() {
        // given
        givenProperty(dealId, PropertyType.MULTIFAMILY)
        every { multifamilyLetterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns null

        // when
        val result = target.getByDealId(dealId)

        // then
        assertThat(result, IsNull())
    }

    @Test
    fun `retrieves a letter of intent offer in negotiation of multifamily`() {
        // given
        givenProperty(dealId, PropertyType.MULTIFAMILY)
        val loiOffer = givenMultifamilyLoiOffer(status = LetterOfIntentRoundStatus.IN_NEGOTIATION)
        every { multifamilyLetterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns loiOffer

        // when
        val result = target.getByDealId(dealId)

        // then
        assertThat(result, IsEqual(loiOffer))
    }

    @Test
    fun `retrieves a letter of intent offer rejected of multifamily`() {
        // given
        givenProperty(dealId, PropertyType.MULTIFAMILY)
        val loiOffer = givenMultifamilyLoiOffer(status = LetterOfIntentRoundStatus.REJECTED)
        every { multifamilyLetterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns loiOffer

        // when
        val result = target.getByDealId(dealId)

        // then
        assertThat(result, IsEqual(loiOffer))
    }

    @Test
    fun `retrieves a letter of intent offer accepted of multifamily`() {
        // given
        givenProperty(dealId, PropertyType.MULTIFAMILY)
        val loiOffer = givenMultifamilyLoiOffer(status = LetterOfIntentRoundStatus.ACCEPTED)
        every { multifamilyLetterOfIntentRoundRepository.findByDealIdAndStatus(eq(dealId), eq(LetterOfIntentRound.ALL_STATES)) } returns loiOffer

        // when
        val result = target.getByDealId(dealId)

        // then
        assertThat(result, IsEqual(loiOffer))
    }

    private fun givenMedicalLoiOffer(status: LetterOfIntentRoundStatus): MedicalLoiRound {
        return LoiMedicalRoundBuilder().apply { this.status = status }.build()
    }

    private fun givenMultifamilyLoiOffer(status: LetterOfIntentRoundStatus): MultifamilyLoiRound {
        return LoiMultifamilyRoundBuilder().apply { this.status = status }.build()
    }

    private fun givenProperty(dealId: Long, type: PropertyType) {
        every { propertyRepository.findByDealId(dealId) } returns PropertyBuilder().apply { this.type = type }.build()
    }
}
