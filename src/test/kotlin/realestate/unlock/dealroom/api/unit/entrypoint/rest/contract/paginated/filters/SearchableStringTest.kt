package realestate.unlock.dealroom.api.unit.entrypoint.rest.contract.paginated.filters

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.search.StringOperator
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableString
import realestate.unlock.dealroom.api.utils.extensions.anyString

class SearchableStringTest {

    private fun createSearchableString(operator: StringOperator, values: List<String>, mustFail: Boolean = true) {
        try {
            SearchableString(operator, values)
            Assertions.assertFalse(mustFail)
        } catch (e: Exception) {
            print(e)
            Assertions.assertTrue(mustFail)
        }
    }

    @Test
    fun `can create datetime search with 1 value`() {
        StringOperator.values().forEach {
            createSearchableString(it, listOf(anyString()), mustFail = false)
        }
    }

    @Test
    fun `can create datetime search with 2 value`() {
        createSearchableString(StringOperator.IN, listOf(anyString(), anyString()), mustFail = false)
    }
}
