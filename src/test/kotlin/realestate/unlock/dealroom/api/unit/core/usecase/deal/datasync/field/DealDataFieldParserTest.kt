package realestate.unlock.dealroom.api.unit.core.usecase.deal.datasync.field

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.*
import realestate.unlock.dealroom.api.core.entity.kfile.KFileWithUrl
import realestate.unlock.dealroom.api.core.entity.property.Address
import realestate.unlock.dealroom.api.core.entity.property.Coordinates
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.field.DealDataFieldParser
import realestate.unlock.dealroom.api.utils.entity.deal.DealLeaseBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset

class DealDataFieldParserTest {

    @Test
    fun `getter of a field`() {
        // Given
        val fieldId = "property.address.street"
        val dealData = DealData(
            deal = givenDeal(),
            property = givenProperty()
        )

        // When
        val value = DealDataFieldParser.getValue(fieldId, dealData)

        // Then
        assertEquals("street", value)
    }

    @Test
    fun `setter of a field`() {
        // Given
        val fieldId = "property.address.street"
        val dealData = DealData(
            deal = givenDeal(),
            property = givenProperty()
        )

        // When
        DealDataFieldParser.setValue(fieldId, dealData, "new street")

        // Then
        assertEquals("new street", dealData.property.address.street)
    }

    @Test
    fun `setter of om`() {
        val fieldId = "deal.omFileId"
        val fileId = anyString()
        val dealData = DealData(
            deal = givenDeal(),
            property = givenProperty()
        )
        DealDataFieldParser.setValue(fieldId, dealData, fileId)

        assertEquals(fileId, dealData.deal.omFileId)
    }

    private fun givenDeal() = Deal(
        id = 1L,
        propertyId = 2L,
        stage = Stage.EVALUATION,
        loiExecutedDate = LocalDate.of(2023, 5, 10),
        diligenceExpirationDate = LocalDate.of(2023, 5, 11),
        initialClosingDate = LocalDate.of(2023, 5, 12),
        outsideClosingDate = LocalDate.of(2023, 5, 13),
        contractExecutedDate = LocalDate.of(2023, 5, 14),
        createdAt = OffsetDateTime.of(2023, 5, 5, 10, 10, 10, 10, ZoneOffset.UTC),
        updatedAt = OffsetDateTime.of(2023, 5, 5, 15, 10, 10, 10, ZoneOffset.UTC),
        lease = DealLeaseBuilder().build(),
        guaranteeType = GuaranteeType.NA,
        type = DealType.SALE_LEASEBACK,
        vertical = "Cardiology",
        status = DealStatus.ACTIVE,
        evaluationDueDate = LocalDate.of(2023, 5, 16),
        underwritingDueDate = LocalDate.of(2023, 5, 17),
        schemaData = SchemaData(
            schemaId = 1,
            data = mapOf(
                "offerNoi" to 307000,
                "offerCapRate" to 2.3,
                "askingNoi" to 23,
                "propertyNotes" to "lala",
                "updateNotes" to "lele"
            )
        ),
        omFileId = null,
        sourceType = SourceType.OFF_MARKET,
        tenantName = "tenant-name",
        tags = setOf("tag-1", "tag-2"),
        leaderId = 3L,
        offerPrice = BigDecimal.valueOf(23),
        earnestMoneyDeposit = BigDecimal.valueOf(99),
        extensionDeposit = BigDecimal.valueOf(43),
        calendarId = null,
        hasFindings = false,
        organizationId = "organizationId",
        buyerCompanyName = null,
        brokerCompanyName = null,
        sellerCompanyName = null,
        firstPassId = null
    )

    private fun givenProperty() = Property(
        id = 1L,
        name = "name",
        keywayId = "keyway-id",
        address = Address(
            street = "street",
            apartment = "1b",
            city = "city",
            state = "state",
            zip = "zip",
            coordinates = Coordinates(
                latitude = BigDecimal.valueOf(10),
                longitude = BigDecimal.valueOf(15)
            )
        ),
        yearBuilt = 1970,
        type = PropertyType.MEDICAL,
        squareFootage = BigDecimal.TEN,
        askingPrice = BigDecimal.valueOf(213),
        mainPhotoId = "9-12-18",
        mainPhoto = KFileWithUrl(kFileId = "9-12-18", url = "www.fakeurl.com", name = "file_name"),
        interiorPhotoId = "3-4-5",
        interiorPhoto = KFileWithUrl(kFileId = "3-4-5", url = "www.fakeurl.com", name = "photo_name"),
        createdAt = OffsetDateTime.of(2023, 5, 6, 10, 10, 10, 10, ZoneOffset.UTC),
        updatedAt = OffsetDateTime.of(2023, 5, 6, 11, 10, 10, 10, ZoneOffset.UTC),
        multifamilyData = null
    )
}
