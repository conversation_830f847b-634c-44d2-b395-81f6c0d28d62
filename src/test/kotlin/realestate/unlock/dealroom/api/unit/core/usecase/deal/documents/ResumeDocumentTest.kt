package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.*
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.repository.document.OnHoldPreviousStatusRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.InteractWithDocumentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.ResumeDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.ResumeDocumentImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class ResumeDocumentTest : InteractWithDocumentRoundTest() {

    companion object {
        private val now = Instant.now()
    }

    @MockK(relaxed = true)
    private lateinit var resumeDocumentImpactOnTask: ResumeDocumentImpactOnTask

    @MockK
    private lateinit var onHoldPreviousStatusRepository: OnHoldPreviousStatusRepository

    private lateinit var resumePSA: ResumeDocument

    @BeforeEach
    fun setUp() {
        clock = Clock.fixed(now, ZoneId.systemDefault())
        resumePSA = ResumeDocument(
            findDocumentAndCurrentRound = findDocumentAndCurrentRound,
            documentRepository = documentRepository,
            createDocumentInteraction = createDocumentInteraction,
            resumeDocumentImpactOnTask = resumeDocumentImpactOnTask,
            onHoldPreviousStatusRepository = onHoldPreviousStatusRepository,
            clock = clock,
            externalCalendarDocumentEvent = externalCalendarDocumentEvent
        )
    }

    @Test
    fun `can resume a psa on hold`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val (psaId, draftDocuments) = pasInDraft()
        val psa = givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.ON_HOLD, documentType = DocumentType.PSA)
        val onHoldPreviousStatus = givenOnHoldPreviousStatus(psaId)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "resume",
            member = user.member,
            documentType = DocumentType.PSA
        )

        val psaUpdated = resumePSA.update(input)

        assertDocumentWasUpdated(psaUpdated, onHoldPreviousStatus.documentStatus)
        assertInteractionWasCreated(draftDocuments, input, Interaction.Type.RESUME)
        verify { resumeDocumentImpactOnTask(psa, onHoldPreviousStatus, user.member) }
    }

    @Test
    fun `cannot resume a psa on hold when status is invalid`() {
        val deal = DealBuilder().build()
        val user = CompleteUserObjectMother.buyer()
        val (psaId, draftDocuments) = pasInDraft()
        givenDocumentWithCurrentRound(id = psaId, deal = deal, currentRound = draftDocuments, status = DocumentStatus.BUYER_REVIEW, documentType = DocumentType.PSA)

        val input = InteractWithDocumentRound.Input(
            dealId = deal.id,
            comment = "resume",
            member = user.member,
            documentType = DocumentType.PSA
        )

        assertThrows<InvalidDocumentStatusException> { resumePSA.update(input) }
    }

    private fun givenOnHoldPreviousStatus(psaId: Long) =
        OnHoldPreviousStatus(psaId, DocumentStatus.BUYER_REVIEW, TaskStatus.TO_DO).also {
            every { onHoldPreviousStatusRepository.findByDocumentId(psaId) } returns it
        }

    private fun pasInDraft(): Pair<Long, DraftDocuments> {
        val psaId = anyId()
        val draftDocuments = DocumentRoundObjectMother.draftDocuments(documentId = psaId)
        return Pair(psaId, draftDocuments)
    }
}
