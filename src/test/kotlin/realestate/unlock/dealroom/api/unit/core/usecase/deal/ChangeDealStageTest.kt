package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.DealHistory
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.repository.deal.DealHistoryRepository
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealStage
import realestate.unlock.dealroom.api.core.usecase.deal.update.DealUpdater
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@ExtendWith(MockKExtension::class)
class ChangeDealStageTest {

    @MockK
    private lateinit var dealRepository: DealRepository

    @MockK
    private lateinit var dealUpdater: DealUpdater

    @MockK
    private lateinit var dealHistoryRepository: DealHistoryRepository

    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)

    private lateinit var changeDealStage: ChangeDealStage

    @BeforeEach
    fun init() {
        TransactionManager.initialize(mockk(relaxed = true))
        changeDealStage = ChangeDealStage(
            dealRepository = dealRepository,
            dealHistoryRepository = dealHistoryRepository,
            clock = clock,
            dealUpdater = dealUpdater
        )
    }

    @Test
    fun `it updates the deal stage as expected`() {
        val currentStage = Stage.EVALUATION
        val givenDeal = DealBuilder().withStage(currentStage).build()
        val givenNewStage = Stage.DILIGENCE
        val givenUpdatedDeal = givenDeal.copy(stage = givenNewStage)
        val givenHistoryId = anyId()

        every { dealRepository.findById(givenDeal.id) } returns givenDeal
        every { dealUpdater.update(givenUpdatedDeal) } returns givenUpdatedDeal
        every { dealHistoryRepository.nextId() } returns givenHistoryId
        every { dealHistoryRepository.save(any()) } returns Unit
//        every { dealEventPublisher.publishDealEvent(any()) } returns Unit

        changeDealStage.invoke(dealId = givenDeal.id, newStage = givenNewStage)

        verify {
            dealUpdater.update(givenUpdatedDeal)
        }

        verify {
            dealHistoryRepository.save(
                DealHistory(
                    id = givenHistoryId,
                    dealId = givenDeal.id,
                    oldStage = currentStage,
                    newStage = givenNewStage,
                    oldStatus = givenDeal.status,
                    newStatus = givenDeal.status,
                    comment = null,
                    memberId = null,
                    createdAt = OffsetDateTime.now(clock)
                )
            )
        }
    }
}
