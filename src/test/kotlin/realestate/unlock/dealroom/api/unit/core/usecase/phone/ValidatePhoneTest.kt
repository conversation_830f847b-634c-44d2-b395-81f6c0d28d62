package realestate.unlock.dealroom.api.unit.core.usecase.phone

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.core.exception.phone.PhoneValidationException
import realestate.unlock.dealroom.api.core.usecase.phone.ValidatePhone

class ValidatePhoneTest {

    private val target = ValidatePhone()

    @Test
    fun `When phone input is valid should return Phone`() {
        // Given
        val phoneString = "+1 ************"

        // When
        val result = target.validate(phoneString)

        // Then
        assertThat(result, equalTo("+18775974032"))
    }

    @Test
    fun `When phone input is invalid should fail`() {
        // Given
        val phoneString = "************"

        // When
        val result = { target.validate(phoneString) }

        // Then
        assertThrows<PhoneValidationException> {
            result()
        }
    }
}
