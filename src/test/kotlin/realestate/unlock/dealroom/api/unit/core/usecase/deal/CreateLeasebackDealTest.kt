package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.*
import realestate.unlock.dealroom.api.core.entity.deal.inputs.ContractExecutionInput
import realestate.unlock.dealroom.api.core.entity.deal.inputs.DealDataInput
import realestate.unlock.dealroom.api.core.entity.deal.inputs.LeaseDataInput
import realestate.unlock.dealroom.api.core.entity.deal.inputs.MembersInput
import realestate.unlock.dealroom.api.core.entity.deal.schema.DealSchema
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.entity.property.PropertyCreationByAddressInput
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.event.deal.DealEventPublisher
import realestate.unlock.dealroom.api.core.event.deal.EventType
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.repository.deal.DealMemberRelationRepository
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.deal.SaveDealData
import realestate.unlock.dealroom.api.core.repository.deal.schema.DealSchemaRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.ExternalDealCalendarActions
import realestate.unlock.dealroom.api.core.usecase.deal.category.CreateCategoriesAndTasksForDeal
import realestate.unlock.dealroom.api.core.usecase.deal.create.CreateDeal
import realestate.unlock.dealroom.api.core.usecase.deal.create.CreateLeasebackDeal
import realestate.unlock.dealroom.api.core.usecase.deal.create.FindOrCreatePropertyAssetsProperty
import realestate.unlock.dealroom.api.core.usecase.deal.team.ValidateMembersOnDeal
import realestate.unlock.dealroom.api.core.usecase.property.CreateProperty
import realestate.unlock.dealroom.api.core.usecase.property.UploadPropertyPhotoToKFile
import realestate.unlock.dealroom.api.core.usecase.tag.ValidateAndCreateTags
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.images.PropertyImagesResponse
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyAssetsPropertyBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.stub.FeatureFlagsStub
import java.math.BigDecimal
import java.time.Clock
import java.time.OffsetDateTime

class CreateLeasebackDealTest {

    private lateinit var createLeasebackDeal: CreateLeasebackDeal
    private val dealRepository: DealRepository = mockk(relaxed = true)
    private val dealSchemaRepository: DealSchemaRepository = mockk(relaxed = true)
    private val clock: Clock = mockk(relaxed = true)
    private val createProperty: CreateProperty = mockk(relaxed = true)
    private val memberRepository: MemberRepository = mockk(relaxed = true)
    private val uploadPropertyPhotoToKFile: UploadPropertyPhotoToKFile = mockk(relaxed = true)
    private val saveDealMemberRelationRepository: DealMemberRelationRepository = mockk(relaxed = true)
    private val createCategoriesAndTasksForDeal: CreateCategoriesAndTasksForDeal = mockk(relaxed = true)
    private val validateAndCreateTags: ValidateAndCreateTags = mockk(relaxed = true)
    private val validateMembersOnDeal: ValidateMembersOnDeal = mockk(relaxed = true)
    private val dealEventPublisher: DealEventPublisher = mockk()
    private val externalDealCalendarActions: ExternalDealCalendarActions = mockk()
    private val featureFlags = FeatureFlagsStub(mutableMapOf(Feature.ORG_CAN_CREATE_NNN to true))
    private val findOrCreatePropertyAssetsProperty: FindOrCreatePropertyAssetsProperty = mockk(relaxed = true)
    private val givenPropertyAssetsResponse = PropertyAssetsPropertyBuilder().build()

    @BeforeEach
    fun setUp() {
        TransactionManager.initialize(mockk(relaxed = true))
        createLeasebackDeal = CreateLeasebackDeal(
            CreateDeal(
                dealRepository,
                dealSchemaRepository,
                clock,
                createProperty,
                memberRepository,
                uploadPropertyPhotoToKFile,
                saveDealMemberRelationRepository,
                createCategoriesAndTasksForDeal,
                validateAndCreateTags,
                validateMembersOnDeal,
                dealEventPublisher,
                externalDealCalendarActions,
                featureFlags,
                findOrCreatePropertyAssetsProperty
            )
        )
        every { findOrCreatePropertyAssetsProperty.invoke(any<PropertyCreationByAddressInput>()) } returns givenPropertyAssetsResponse
    }

    @Test
    fun `creates property`() {
        // given
        val input = givenInput()
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, any(), property = any()) } returns Unit
        // when
        createLeasebackDeal.create(input)

        // then
        verify {
            createProperty.create(input.property, givenPropertyAssetsResponse)
        }
    }

    @Test
    fun `creates deal`() {
        // given
        val givenProperty = PropertyBuilder().build()
        val input = givenInput()
        val givenDeal = DealBuilder().apply { propertyId = givenProperty.id }.build()
        every { createProperty.create(input.property, any()) } returns givenProperty
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit

        // when
        val deal = createLeasebackDeal.create(input)

        // then
        Assertions.assertNotNull(deal)
        Assertions.assertEquals(Stage.EVALUATION, deal.stage)
        Assertions.assertEquals(deal.propertyId, givenProperty.id)
    }

    @Test
    fun `do not creates executed loi when no loi executed date`() {
        // given
        val givenProperty = PropertyBuilder().build()
        val givenDeal = DealBuilder().build()
        val input = givenInput()
        every { createProperty.create(input.property, any()) } returns givenProperty
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit

        val completeDeal = givenDeal.toCompleteDeal(givenProperty)
        // when
        val deal = createLeasebackDeal.create(input)

        // then
        Assertions.assertEquals(Stage.EVALUATION, deal.stage)
    }

    @Test
    fun `should create a property and download photo`() {
        val input = givenInput()
        val givenProperty = PropertyBuilder().build()
        val givenDeal = DealBuilder().withPropertyId(givenProperty.id).build()
        val givenPropertyPhotoUrl = anyString()
        val givenPropertyAssetsImages = PropertyImagesResponse(urls = listOf(givenPropertyPhotoUrl))
        val givenPropertyAssetsProperty = givenPropertyAssetsResponse.copy(images = givenPropertyAssetsImages)

        every { findOrCreatePropertyAssetsProperty(input.property) } returns givenPropertyAssetsProperty
        every { createProperty.create(input.property, givenPropertyAssetsProperty) } returns givenProperty
        every { uploadPropertyPhotoToKFile.invoke(givenProperty.id, givenDeal.id, givenPropertyPhotoUrl) } returns KFile(anyString(), anyString())
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit

        val deal = createLeasebackDeal.create(input)

        Assertions.assertEquals(deal.propertyId, givenProperty.id)
        verify {
            uploadPropertyPhotoToKFile.invoke(givenProperty.id, givenDeal.id, givenPropertyPhotoUrl)
        }
    }

    @Test
    fun `download photo fails but dont affect flow`() {
        val input = givenInput()
        val givenProperty = PropertyBuilder().build()
        val givenDeal = DealBuilder()
            .apply { propertyId = givenProperty.id }
            .build()
        val givenPropertyPhotoUrl = anyString()
        val givenPropertyAssetsImages = PropertyImagesResponse(urls = listOf(givenPropertyPhotoUrl))
        val givenPropertyAssetsProperty = givenPropertyAssetsResponse.copy(images = givenPropertyAssetsImages)

        every { findOrCreatePropertyAssetsProperty(input.property) } returns givenPropertyAssetsProperty
        every { createProperty.create(input.property, givenPropertyAssetsProperty) } returns givenProperty
        every { dealRepository.save(any()) } returns givenDeal
        every { uploadPropertyPhotoToKFile.invoke(givenProperty.id, givenDeal.id, givenPropertyPhotoUrl) }.throws(Exception("NO"))
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty) } returns Unit

        val deal = createLeasebackDeal.create(input)

        Assertions.assertEquals(deal.propertyId, givenProperty.id)
        verify {
            uploadPropertyPhotoToKFile.invoke(givenProperty.id, givenDeal.id, givenPropertyPhotoUrl)
        }
    }

    @Test
    fun `saves deal data in repositories`() {
        // given
        val givenInput = givenInput()
        val givenProperty = PropertyBuilder().build()
        val givenDeal = DealBuilder().withPropertyId(givenProperty.id).build()
        every { dealRepository.save(any()) } returns givenDeal

        val slot = slot<Iterable<DealMemberRelation>>()
        every { saveDealMemberRelationRepository.save(capture(slot)) } returns Unit
        val dealSchema: DealSchema = mockk(relaxed = true)
        every { dealSchema.id } returns 123
        every { dealSchemaRepository.findLastByPropertyType(any()) } returns dealSchema
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = any()) } returns Unit

        // when
        createLeasebackDeal.create(givenInput)

        val expectedSaveDealData = expectedSaveDealData(givenInput, givenProperty, 123)
        // then
        verify { dealRepository.save(expectedSaveDealData) }
        givenInput.members.dealMembers.forEach {
            Assertions.assertTrue(slot.captured.any { captured -> captured.memberId == it })
        }
    }

    @Test
    fun `creates categories and tasks for deal`() {
        // given
        val givenDeal = DealBuilder().build()
        val members = setOf(seller(), buyer())
        val input = givenInput(membersInput = givenMemberInput(members))
        val givenProperty = mockProperty()
        every { dealRepository.save(any()) } returns givenDeal
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = any()) } returns Unit
        // when
        createLeasebackDeal.create(input)

        // then
        assertCategoriesWereCreated(givenDeal, input, members, givenProperty)
    }

    @Test
    fun `creates categories and tasks for deal with seller broker only`() {
        // given )
        val members = setOf(sellerBroker(), buyer())
        val property = PropertyBuilder().build()
        val input = givenInput(
            membersInput = givenMemberInput(dealMembers = members),
            propertyInput = givenPropertyInput(property)
        )
        val givenDeal = DealBuilder().build()

        every { dealRepository.save(any()) } returns givenDeal
        val slot = slot<Iterable<DealMemberRelation>>()
        every { saveDealMemberRelationRepository.save(capture(slot)) } returns Unit
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = property) } returns Unit
        // when
        createLeasebackDeal.create(input)

        // then
        assertCategoriesWereCreated(givenDeal, input, members, property)
        input.members.dealMembers.forEach {
            Assertions.assertTrue(slot.captured.any { captured -> captured.memberId == it })
        }
    }

    @Test
    fun `creates categories and tasks for deal with 2 sellers`() {
        // given
        val members = setOf(buyer(), seller(), sellerBroker())
        val property = PropertyBuilder().build()
        val input = givenInput(
            membersInput = givenMemberInput(dealMembers = members),
            propertyInput = givenPropertyInput(property)
        )
        val givenDeal = DealBuilder().build()

        every { dealRepository.save(any()) } returns givenDeal
        val slot = slot<Iterable<DealMemberRelation>>()
        every { saveDealMemberRelationRepository.save(capture(slot)) } returns Unit
        every { dealEventPublisher.publish(eventType = EventType.NEW_DEAL, deal = givenDeal, property = property) } returns Unit

        // when
        createLeasebackDeal.create(input)

        // then
        assertCategoriesWereCreated(givenDeal, input, members, property)
        input.members.dealMembers.forEach {
            Assertions.assertTrue(slot.captured.any { captured -> captured.memberId == it })
        }
    }

    private fun assertCategoriesWereCreated(
        givenDeal: Deal,
        input: CreateLeasebackDeal.Input,
        members: Set<Member>,
        property: Property
    ) {
        verify {
            createCategoriesAndTasksForDeal.create(
                deal = givenDeal.toCompleteDeal(property)
                    .copy(members = members),
                dealTemplateKey = if (property.type == PropertyType.MEDICAL) "default" else "multifamily"
            )
        }
    }

    private fun seller(id: Long = 1): Member = MemberBuilder()
        .withId(id)
        .withSellerType()
        .build()

    private fun buyer(id: Long = 2): Member = MemberBuilder()
        .withId(id)
        .withBuyerType()
        .build()

    private fun sellerBroker(id: Long = 3): Member = MemberBuilder()
        .withId(id)
        .withSellerBrokerType()
        .build()

    private fun mockProperty(property: Property = PropertyBuilder().build()) =
        property.also { every { createProperty.create(any<PropertyCreationByAddressInput>(), any()) } returns it }

    private fun givenInput(
        dealDataInput: DealDataInput = givenDealDataInput(),
        propertyInput: PropertyCreationByAddressInput = givenPropertyInput(),
        leaseDataInput: LeaseDataInput = givenLeaseDataInput(),
        contractExecutionInput: ContractExecutionInput = givenContractExecutionInput(),
        membersInput: MembersInput = givenMemberInput()
    ): CreateLeasebackDeal.Input =
        CreateLeasebackDeal.Input(
            property = propertyInput,
            dealData = dealDataInput,
            leaseData = leaseDataInput,
            members = membersInput,
            contractExecution = contractExecutionInput,
            organizationId = "org-id"
        )

    private fun givenMemberInput(
        dealMembers: Set<Member> = setOf(seller(), sellerBroker(), buyer()),
        leaderId: Long = dealMembers.first { it.type == MemberTypeEnum.BUYER }.id
    ): MembersInput =
        MembersInput(
            dealMembers = dealMembers.map { it.id }.toSet(),
            tenantName = "tenantName",
            leaderId = leaderId
        ).also {
            every { memberRepository.findByMemberIds(it.dealMembers) } returns dealMembers.toList()
        }

    private fun givenDealDataInput() = DealDataInput(
        vertical = null,
        guaranteeType = null,
        type = DealType.SALE_LEASEBACK,
        estimateId = null,
        offerPrice = BigDecimal.TEN,
        earnestMoneyDeposit = BigDecimal.valueOf(1),
        extensionDeposit = BigDecimal.valueOf(2)
    )

    private fun givenPropertyInput(property: Property = PropertyBuilder().build()) =
        PropertyCreationByAddressInput(
            keywayId = property.keywayId,
            street = property.address.street,
            apartment = property.address.apartment,
            city = property.address.city,
            state = property.address.state,
            zip = property.address.zip,
            yearBuilt = property.yearBuilt,
            squareFootage = property.squareFootage,
            askingPrice = property.askingPrice,
            latitude = property.address.coordinates?.latitude,
            longitude = property.address.coordinates?.longitude,
            name = property.name,
            type = PropertyType.MEDICAL,
            multifamilyData = null
        ).also {
            every { createProperty.create(it, any()) } returns property
        }

    private fun givenLeaseDataInput() = LeaseDataInput()

    private fun givenContractExecutionInput() = ContractExecutionInput()

    private fun expectedSaveDealData(input: CreateLeasebackDeal.Input, property: Property, dealSchemaId: Long): SaveDealData {
        return SaveDealData(
            propertyId = property.id,
            status = DealStatus.ACTIVE,
            stage = Stage.EVALUATION,
            loiExecutedDate = null,
            contractExecutedDate = null,
            diligenceExpirationDate = null,
            initialClosingDate = null,
            outsideClosingDate = null,
            evaluationDueDate = null,
            underwritingDueDate = null,
            updatedAt = OffsetDateTime.now(clock),
            createdAt = OffsetDateTime.now(clock),
            dealType = input.dealData.type,
            tenantName = input.members.tenantName,
            vertical = input.dealData.vertical,
            leaseRent = input.leaseData.rent,
            leaseType = input.leaseData.type,
            leaseRentIncrease = input.leaseData.rentIncrease,
            leaseIncreaseEveryYear = input.leaseData.increaseEveryYear,
            leaseLength = input.leaseData.length,
            leaseExpirationYear = input.leaseData.expirationYear,
            leaseNumberOfOptions = input.leaseData.numberOfOptions,
            leaseOptionLengths = input.leaseData.optionLengths,
            leaseRentCpi = input.leaseData.rentCpi,
            leaseRentStepType = input.leaseData.rentStepType,
            guaranteeType = input.dealData.guaranteeType,
            dueDiligenceNumber = input.contractExecution.dueDiligenceNumber,
            closingPeriod = input.contractExecution.closingPeriod,
            closingPeriodExtension = input.contractExecution.closingPeriodExtension,
            dealSchemaId = dealSchemaId,
            tags = input.dealData.tags,
            leaderId = input.members.leaderId,
            offerPrice = input.dealData.offerPrice,
            earnestMoneyDeposit = input.dealData.earnestMoneyDeposit,
            extensionDeposit = input.dealData.extensionDeposit,
            organizationId = input.organizationId,
            buyerCompanyName = input.dealData.buyerCompanyName,
            brokerCompanyName = input.dealData.brokerCompanyName,
            sellerCompanyName = input.dealData.sellerCompanyName,
            leaseDate = input.leaseData.date,
            firstPassId = input.dealData.firstPassId
        )
    }
}
