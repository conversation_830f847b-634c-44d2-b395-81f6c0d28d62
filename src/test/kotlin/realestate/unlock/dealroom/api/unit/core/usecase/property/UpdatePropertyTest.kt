package realestate.unlock.dealroom.api.unit.core.usecase.property

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.property.*
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.kfileservice.StagingFileToConfirm
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.usecase.property.PropertyUpdater
import realestate.unlock.dealroom.api.core.usecase.property.UpdateProperty
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.Clock
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class UpdatePropertyTest {

    @MockK(relaxed = true)
    private lateinit var fileGateway: FileGateway

    @MockK
    private lateinit var propertyRepository: PropertyRepository

    @MockK
    private lateinit var propertyUpdater: PropertyUpdater

    private lateinit var updateProperty: UpdateProperty

    private val clock = Clock.fixed(Instant.now(), ZoneId.systemDefault())

    @BeforeEach
    fun setUp() {
        updateProperty = UpdateProperty(
            propertyRepository = propertyRepository,
            fileGateway = fileGateway,
            propertyUpdater = propertyUpdater,
            clock = clock
        )
    }

    @Test
    fun `can update a property`() {
        val property = givenProperty()
        val updateInput = givenPropertyUpdateInput(property = property, mainPhotoId = null, interiorPhotoId = null)
        every { propertyUpdater.update(match { it.id == property.id }) } returns property.copy(
            mainPhotoId = updateInput.mainPhotoId,
            interiorPhotoId = updateInput.interiorPhotoId
        )

        updateProperty.update(
            updateInput = updateInput
        )

        assertPropertyUpdated(property, updateInput)
    }

    @Test
    fun `can update a property with photos`() {
        val property = givenProperty()

        val updateInput = givenPropertyUpdateInput(property)
        every { propertyUpdater.update(match { it.id == property.id }) } returns property.copy(
            mainPhotoId = updateInput.mainPhotoId,
            interiorPhotoId = updateInput.interiorPhotoId
        )

        updateProperty.update(
            updateInput = updateInput
        )

        assertPropertyUpdated(property, updateInput)
    }

    private fun givenProperty() = PropertyBuilder().apply { this.type = PropertyType.MULTIFAMILY }.build().also { property ->
        every { propertyRepository.findById(property.id) } returns property
        every { propertyUpdater.update(any()) } returns property
    }

    private fun assertPropertyUpdated(
        property: Property,
        updateInput: PropertyUpdateInput
    ) {
        verify {
            propertyUpdater.update(
                property.copy(
                    yearBuilt = updateInput.yearBuilt ?: property.yearBuilt,
                    squareFootage = updateInput.squareFootage ?: property.squareFootage,
                    askingPrice = updateInput.askingPrice ?: property.askingPrice,
                    updatedAt = OffsetDateTime.now(clock),
                    mainPhotoId = updateInput.mainPhotoId ?: property.mainPhotoId,
                    interiorPhotoId = updateInput.interiorPhotoId ?: property.interiorPhotoId,
                    keywayId = updateInput.keywayId ?: property.keywayId,
                    multifamilyData = updateInput.multifamilyData?.let {
                        MultifamilyData(
                            brokerFirm = it.brokerFirm ?: property.multifamilyData?.brokerFirm,
                            units = it.units ?: property.multifamilyData?.units,
                            averageSquareFootage = it.averageSquareFootage ?: property.multifamilyData?.averageSquareFootage,
                            occupancy = it.occupancy ?: property.multifamilyData?.occupancy,
                            parkingSpots = it.parkingSpots ?: property.multifamilyData?.parkingSpots,
                            parkingRatio = it.parkingRatio ?: property.multifamilyData?.parkingRatio,
                            owner = it.owner ?: property.multifamilyData?.owner,
                            propertyManager = it.propertyManager ?: property.multifamilyData?.propertyManager,
                            unitsMix = it.unitsMix ?: property.multifamilyData?.unitsMix ?: listOf()
                        )
                    },
                    name = updateInput.name ?: property.name
                )
            )
        }

        val filesToConfirm = listOfNotNull(updateInput.mainPhotoId, updateInput.interiorPhotoId).map {
            StagingFileToConfirm(
                kFileId = it,
                path = "property/${property.id}"
            )
        }
        if (filesToConfirm.isEmpty()) {
            verify(exactly = 0) { fileGateway.confirmStagingFile(any()) }
        } else {
            verify {
                fileGateway.confirmStagingFile(filesToConfirm)
            }
        }
    }

    private fun givenPropertyUpdateInput(property: Property, mainPhotoId: String? = anyString(), interiorPhotoId: String? = null) = PropertyUpdateInput(
        id = property.id,
        yearBuilt = 2020,
        squareFootage = BigDecimal(3000),
        mainPhotoId = mainPhotoId,
        interiorPhotoId = interiorPhotoId,
        keywayId = anyString(),
        multifamilyData = MultifamilyDataInput(
            brokerFirm = anyString(),
            units = 10,
            averageSquareFootage = BigDecimal(4500),
            occupancy = BigDecimal(85),
            parkingSpots = 12,
            parkingRatio = BigDecimal(80),
            owner = anyString(),
            propertyManager = anyString(),
            unitsMix = listOf(
                UnitsMix(
                    bedrooms = 2,
                    quantity = 4,
                    squareFootage = BigDecimal(800),
                    studio = false,
                    rent = BigDecimal.valueOf(412),
                    renoRent = BigDecimal.valueOf(612)
                )
            )
        ),
        askingPrice = BigDecimal(8000),
        name = anyString()
    )
}
