package realestate.unlock.dealroom.api.unit.core.usecase.lease

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.gateway.sign.RetrieveSignViewUrlOutput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.repository.document.DocumentSignRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.GetDocument
import realestate.unlock.dealroom.api.core.usecase.deal.documents.GetDocumentSignViewUrl
import realestate.unlock.dealroom.api.core.usecase.email.GetApplicationLinkByMember
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentSignerBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.net.URI

@ExtendWith(MockKExtension::class)
class GetLeaseSignViewUrlTest {

    @MockK
    private lateinit var getDocument: GetDocument

    @MockK
    private lateinit var documentSignRepository: DocumentSignRepository

    @MockK
    private lateinit var signGateway: SignGateway

    @MockK
    private lateinit var getApplicationLinkByMember: GetApplicationLinkByMember

    private lateinit var getDocumentSignViewUrl: GetDocumentSignViewUrl

    @BeforeEach
    fun setUp() {
        getDocumentSignViewUrl = GetDocumentSignViewUrl(
            getDocument = getDocument,
            documentSignRepository = documentSignRepository,
            signGateway = signGateway,
            getApplicationLinkByMember = getApplicationLinkByMember
        )
    }

    @Test
    fun `if there is not pending sign, it fails`() {
        val dealId = anyId()
        givenLease(dealId, DocumentStatus.BUYER_REVIEW)
        val member = givenBuyer()

        assertThrows<InvalidDocumentStatusException> {
            getDocumentSignViewUrl(
                GetDocumentSignViewUrl.Input(
                    dealId = dealId,
                    member = member,
                    returnPath = anyString(),
                    documentType = DocumentType.LEASE
                )
            )
        }
    }

    @Test
    fun `if user has to sign, it returns sign view url`() {
        val dealId = anyId()
        val lease = givenLease(dealId, DocumentStatus.PENDING_SIGN)
        val member = givenBuyer()
        givenSigner(documentId = lease.id, email = member.email)
        val signViewUrl = URI("http://return-path")
        every { signGateway.retrieveSignerViewUrl(any()) } returns RetrieveSignViewUrlOutput(signViewUrl)

        val result = getDocumentSignViewUrl(
            GetDocumentSignViewUrl.Input(
                dealId = dealId,
                member = member,
                returnPath = anyString(),
                documentType = DocumentType.LEASE
            )
        )

        assertEquals(signViewUrl, result)
    }

    @Test
    fun `if user has not to sign, it returns carbon copy  url`() {
        val dealId = anyId()
        val lease = givenLease(dealId, DocumentStatus.PENDING_SELLER_SIGN)
        val member = givenBuyer()
        givenSigner(documentId = lease.id, email = anyString())
        val carbonCopyUrl = URI("http://return-path")
        every { signGateway.retrieveCarbonCopyViewUrl(any()) } returns RetrieveSignViewUrlOutput(carbonCopyUrl)

        val result = getDocumentSignViewUrl(
            GetDocumentSignViewUrl.Input(
                dealId = dealId,
                member = member,
                returnPath = anyString(),
                documentType = DocumentType.LEASE
            )
        )

        assertEquals(carbonCopyUrl, result)
    }

    private fun givenBuyer(): Member {
        val member = MemberObjectMother.buyer()
        givenApplicationLinkFor(member)
        return member
    }

    private fun givenLease(dealId: Long, status: DocumentStatus) = DocumentBuilder().apply {
        this.dealId = dealId
        this.status = status
        this.type = DocumentType.LEASE
    }.build().also {
        every { getDocument.byDealIdAndDocumentType(dealId, DocumentType.LEASE) } returns it
    }

    private fun givenSigner(documentId: Long, email: String) = DocumentSignerBuilder().apply {
        this.signerEmail = email
    }.build().also {
        every { documentSignRepository.findByDocumentId(documentId) } returns listOf(it)
    }

    private fun givenApplicationLinkFor(member: Member) = every { getApplicationLinkByMember.get(member) } returns "http://localhost"
}
