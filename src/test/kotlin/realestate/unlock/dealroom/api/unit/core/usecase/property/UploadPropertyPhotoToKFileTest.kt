package realestate.unlock.dealroom.api.unit.core.usecase.property

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import kong.unirest.HttpResponse
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.usecase.property.PropertyUpdater
import realestate.unlock.dealroom.api.core.usecase.property.UploadPropertyPhotoToKFile
import realestate.unlock.dealroom.api.infrastructure.client.rest.PropertyAssetsRestClient
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.file.FileCreator
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub
import java.io.File

@ExtendWith(MockKExtension::class)
class UploadPropertyPhotoToKFileTest {

    private lateinit var fileGateway: FileGatewayStub

    @MockK
    private lateinit var propertyRepository: PropertyRepository

    @MockK
    private lateinit var propertyUpdater: PropertyUpdater

    @MockK(relaxed = true)
    private lateinit var propertyAssetsRestClient: PropertyAssetsRestClient

    private lateinit var uploadPropertyPhotoToKFile: UploadPropertyPhotoToKFile

    @BeforeEach
    fun setUp() {
        fileGateway = FileGatewayStub()
        uploadPropertyPhotoToKFile = UploadPropertyPhotoToKFile(
            propertyAssetsRestClient = propertyAssetsRestClient,
            propertyUpdater = propertyUpdater,
            fileGateway = fileGateway,
            propertyRepository = propertyRepository
        )
    }

    @Test
    fun `should download and update property photo`() {
        val photoUrl = anyString()
        val propertyID = anyId()
        val dealID = anyId()
        val fileUid = anyString()
        val file = FileCreator.file(
            "pepe.jpg",
            fileContent = "pepe"
        )
        mockPropertyRepo(propertyID, fileUid)
        mockGetPhoto(photoUrl, file, propertyID)
        fileGateway.nextUploadFileUid(fileUid)

        every { propertyUpdater.update(match { it.id == propertyID }) } returns mockk()

        var response = uploadPropertyPhotoToKFile.invoke(propertyID, dealID, photoUrl)

        val expectedName = "$propertyID.${file.extension}"
        Assertions.assertEquals(
            KFile(
                uid = fileUid,
                name = expectedName
            ),
            response
        )
    }

    private fun mockGetPhoto(fileUrl: String, photo: File, propertyID: Long) {
        val httpResponse = mockk<HttpResponse<File>>().also {
            every { it.body } returns photo
        }
        every {
            propertyAssetsRestClient.getClientForUrl(fileUrl)
                .get(path = "", responseType = File::class.java, fileName = "property_${propertyID}_photo")
        } returns httpResponse
    }

    private fun mockPropertyRepo(propertyID: Long, uid: String) {
        val prop = PropertyBuilder().apply {
            id = propertyID
        }.build()
        every { propertyRepository.findById(propertyID) } returns prop
        every { propertyUpdater.update(prop.copy(mainPhotoId = uid)) } returns prop.copy(mainPhotoId = uid)
    }
}
