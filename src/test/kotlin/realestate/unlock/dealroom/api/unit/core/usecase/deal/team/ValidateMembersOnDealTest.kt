package realestate.unlock.dealroom.api.unit.core.usecase.deal.team

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.usecase.deal.team.ValidateMembersOnDeal
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother.buyer
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother.seller
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother.sellerBroker
import realestate.unlock.dealroom.api.utils.extensions.anyId

class ValidateMembersOnDealTest {

    private val validateMembersOnDeal = ValidateMembersOnDeal()

    @Test
    fun `should fail because no buyer`() {
        // given
        val members = setOf(seller(), sellerBroker())

        // then
        val exception = org.junit.jupiter.api.assertThrows<Exception> {
            validateMembersOnDeal.validate(members, anyId())
        }
        assertEquals("Buyer member is required", exception.message)
    }

    @Test
    fun `should fail because leader is seller`() {
        // given
        val members = setOf(seller(1), buyer())

        // then
        val exception = org.junit.jupiter.api.assertThrows<Exception> {
            validateMembersOnDeal.validate(members, 1)
        }
        assertEquals("Leader must be a buyer member", exception.message)
    }

    @Test
    fun `should fail because leader is not a member`() {
        // given
        val members = setOf(seller(), buyer())

        // then
        val exception = org.junit.jupiter.api.assertThrows<Exception> {
            validateMembersOnDeal.validate(members, anyId())
        }
        assertEquals("Leader must be a buyer member", exception.message)
    }
}
