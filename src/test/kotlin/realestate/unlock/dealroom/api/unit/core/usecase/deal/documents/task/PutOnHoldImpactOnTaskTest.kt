package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents.task

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.task.TaskStatusTransitions
import realestate.unlock.dealroom.api.core.entity.task.TaskTransitionInput
import realestate.unlock.dealroom.api.core.usecase.deal.documents.task.PutOnHoldImpactOnTask
import realestate.unlock.dealroom.api.core.usecase.task.GetDocumentTask
import realestate.unlock.dealroom.api.core.usecase.task.TaskTransition
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.entity.user.CompleteUserObjectMother

@ExtendWith(MockKExtension::class)
class PutOnHoldImpactOnTaskTest {

    @MockK
    private lateinit var getDocumentTask: GetDocumentTask

    @MockK(relaxed = true)
    private lateinit var taskTransition: TaskTransition

    private lateinit var putOnHoldImpactOnTask: PutOnHoldImpactOnTask

    @BeforeEach
    fun setUp() {
        putOnHoldImpactOnTask = PutOnHoldImpactOnTask(
            getDocumentTask = getDocumentTask,
            taskTransition = taskTransition
        )
    }

    @Test
    fun `put task on hold`() {
        val psa = DocumentBuilder().build()
        val task = givenTask(psa)
        val user = CompleteUserObjectMother.buyer()

        val oldStatus = putOnHoldImpactOnTask(psa, user.member)

        assertEquals(task.statusKey, oldStatus)
        verify {
            taskTransition(
                TaskTransitionInput(
                    taskId = task.id,
                    member = user.member,
                    transition = TaskStatusTransitions.PUT_ON_HOLD
                )
            )
        }
    }

    private fun givenTask(document: Document) = TaskBuilder().build().also {
        every { getDocumentTask(document) } returns it
    }
}
