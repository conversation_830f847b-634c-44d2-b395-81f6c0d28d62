package realestate.unlock.dealroom.api.unit.core.usecase.deal

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetCompleteDeal
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealsByMemberId

class GetDealsByMemberIdTest {

    private val dealRepository: DealRepository = mockk(relaxed = true)
    private val getCompleteDeal: GetCompleteDeal = mockk(relaxed = true)
    private val getDealsByMemberId = GetDealsByMemberId(dealRepository = dealRepository, getCompleteDeal = getCompleteDeal)

    @Test
    fun `It retrieves non archived deals`() {
        // given
        val memberId = 1L
        every { dealRepository.findAllNotArchivedByMemberId(any()) } returns emptyList()

        // when
        getDealsByMemberId.get(memberId)

        // then
        verify { dealRepository.findAllNotArchivedByMemberId(memberId) }
    }

    @Test
    fun `It gets complete deal info from not archived deals`() {
        // given
        val memberId = 1L
        val firstDealPropertyId = 2L
        val secondDealPropertyId = 3L
        val firstDeal = mockk<Deal>(relaxed = true) // relaxed to avoid mocking the #toCompleteDeal
        val secondDeal = mockk<Deal>(relaxed = true)
        every { firstDeal.propertyId } returns firstDealPropertyId
        every { secondDeal.propertyId } returns secondDealPropertyId
        every { dealRepository.findAllNotArchivedByMemberId(memberId) } returns listOf(firstDeal, secondDeal)

        // when
        getDealsByMemberId.get(memberId)

        // then
        verify {
            getCompleteDeal.toCompleteDeal(firstDeal)
            getCompleteDeal.toCompleteDeal(secondDeal)
        }

        verify { dealRepository.findAllNotArchivedByMemberId(memberId) }
    }
}
