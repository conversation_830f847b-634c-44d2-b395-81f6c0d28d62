package realestate.unlock.dealroom.api.unit.core.usecase.dashboard.calendar.deal

import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventType
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput
import realestate.unlock.dealroom.api.core.entity.calendar.model.DealCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.usecase.calendar.eventfinder.DiligenceExpirationCalendarEventFinder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

class DiligenceExpirationEventValidatorTest {

    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)
    private val diligenceExpirationEventValidator = DiligenceExpirationCalendarEventFinder(clock)

    @Test
    fun `it should create an event report if diligence expiration date is within the given days`() {
        val givenDays = 7
        val givenDealEvent = DealCalendarEventModel(
            dealId = anyId(), stage = Stage.EVALUATION, diligenceExpirationDate = LocalDate.now(clock).plusDays(4), initialClosingDate = null,
            propertyName = "any", addressStreet = "any", addressApartment = "any", addressCity = "any", addressState = "any", addressZip = "any"
        )
        val result = diligenceExpirationEventValidator.find(givenDealEvent, CalendarEventsInput(untilDays = givenDays, organizationId = "org-id"))

        MatcherAssert.assertThat(result, Matchers.notNullValue())
        MatcherAssert.assertThat(result!!.dealId, Matchers.equalTo(givenDealEvent.dealId))
        MatcherAssert.assertThat(result.type, Matchers.equalTo(CalendarEventType.DUE_DILIGENCE))
    }

    @Test
    fun `it shouldn't create an event report if diligence expiration date is more than the given days`() {
        val givenDays = 7
        val givenDealEvent = DealCalendarEventModel(
            dealId = anyId(), stage = Stage.EVALUATION, diligenceExpirationDate = LocalDate.now(clock).plusDays(8), initialClosingDate = null,
            propertyName = "any", addressStreet = "any", addressApartment = "any", addressCity = "any", addressState = "any", addressZip = "any"
        )
        val result = diligenceExpirationEventValidator.find(givenDealEvent, CalendarEventsInput(untilDays = givenDays, organizationId = "org-id"))

        MatcherAssert.assertThat(result, Matchers.nullValue())
    }

    @Test
    fun `it should create an event report if diligence expiration date was yesterday`() {
        val givenDealEvent = DealCalendarEventModel(
            dealId = anyId(), stage = Stage.EVALUATION, diligenceExpirationDate = LocalDate.now(clock).minusDays(1), initialClosingDate = null,
            propertyName = "any", addressStreet = "any", addressApartment = "any", addressCity = "any", addressState = "any", addressZip = "any"
        )
        val result = diligenceExpirationEventValidator.find(givenDealEvent, CalendarEventsInput(untilDays = 7, organizationId = "org-id"))

        MatcherAssert.assertThat(result, Matchers.notNullValue())
    }
}
