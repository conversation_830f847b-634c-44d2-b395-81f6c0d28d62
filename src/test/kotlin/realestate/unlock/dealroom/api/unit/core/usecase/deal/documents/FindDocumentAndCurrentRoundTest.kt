package realestate.unlock.dealroom.api.unit.core.usecase.deal.documents

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentRound
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.repository.document.DocumentRoundRepository
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentAndCurrentRound
import realestate.unlock.dealroom.api.core.usecase.deal.documents.GetDocument
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId

@ExtendWith(MockKExtension::class)
class FindDocumentAndCurrentRoundTest {

    @MockK
    private lateinit var getDocument: GetDocument

    @MockK
    private lateinit var documentRoundRepository: DocumentRoundRepository

    private lateinit var findDocumentAndCurrentRound: FindDocumentAndCurrentRound

    @BeforeEach
    fun setUp() {
        findDocumentAndCurrentRound = FindDocumentAndCurrentRound(
            getDocument = getDocument,
            documentRoundRepository = documentRoundRepository
        )
    }

    @Test
    fun `fails if there is not psa round`() {
        val dealId = anyId()
        givenPSA(dealId = dealId, currentRoundId = null)

        assertThrows<InvalidDocumentStatusException> { findDocumentAndCurrentRound.byDealIdAndDocumentType<DocumentRound>(dealId, DocumentType.PSA) }
    }

    @Test
    fun `can find psa and current round`() {
        val dealId = anyId()
        val document = givenPSA(dealId = dealId, currentRoundId = anyId())
        val documentRound = givenPsaRound(document.currentRoundId!!)

        val (psaFound, psaRoundFound) = findDocumentAndCurrentRound.byDealIdAndDocumentType<DocumentRound>(dealId, document.type)

        assertEquals(document, psaFound)
        assertEquals(documentRound, psaRoundFound)
    }

    private fun givenPSA(dealId: Long, currentRoundId: Long?) = DocumentBuilder().apply {
        this.dealId = dealId
        this.currentRoundId = currentRoundId
    }.build().also {
        every { getDocument.byDealIdAndDocumentType(dealId, DocumentType.PSA) } returns it
    }

    private fun givenPsaRound(id: Long) = DocumentRoundObjectMother.draftDocuments(id = id)
        .also {
            every { documentRoundRepository.findById(id) } returns it
        }
}
