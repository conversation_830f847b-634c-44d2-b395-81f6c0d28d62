package realestate.unlock.dealroom.api.unit.core.usecase.deal.team

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.containsInAnyOrder
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import realestate.unlock.dealroom.api.core.entity.member.team
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.member.type.MemberType
import realestate.unlock.dealroom.api.core.usecase.deal.team.GetDealMembers
import realestate.unlock.dealroom.api.core.usecase.member.GetMembersByDealId
import realestate.unlock.dealroom.api.core.usecase.member.type.FindMemberTypes
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetDealTeamUseCaseTest {

    private val getMembersByDealId = mockk<GetMembersByDealId>()
    private val findMemberTypes = mockk<FindMemberTypes>()
    private val getDealTeam = GetDealMembers(getMembersByDealId)
    private val dealId = 1L
    private val memberTypes = listOf(
        MemberType(1, "seller", "Seller"),
        MemberType(2, "buyer", "Buyer"),
        MemberType(3, "seller_counsel", "Seller Counsel"),
        MemberType(4, "buyer_counsel", "Buyer Counsel")
    )

    @BeforeEach
    fun setup() {
        clearAllMocks()
        every { findMemberTypes.get() } returns memberTypes
    }

    @Test
    fun `given a seller and a buyer should get deal team succesfully`() {
        // Given
        val seller = MemberObjectMother.seller()
        val buyer = MemberObjectMother.buyer()

        val dealMembers = setOf(seller, buyer)
        every { getMembersByDealId.get(dealId) } returns dealMembers

        // When
        val dealTeam = getDealTeam.get(dealId)

        // Then
        assertThat(dealTeam.team(MemberDealTeam.SELLER).size, equalTo(1))
        assertThat(dealTeam.team(MemberDealTeam.SELLER).first(), equalTo(seller))
        assertThat(dealTeam.team(MemberDealTeam.BUYER).size, equalTo(1))
        assertThat(dealTeam.team(MemberDealTeam.BUYER).first(), equalTo(buyer))
    }

    @Test
    fun `given a seller buyer and counsels should get deal team succesfully`() {
        // Given
        val seller = MemberObjectMother.seller()
        val buyer = MemberObjectMother.buyer()
        val sellerCounsel = MemberObjectMother.sellerCounsel()
        val buyerCounsel = MemberObjectMother.buyerCounsel()

        val dealMembers = setOf(seller, buyer, sellerCounsel, buyerCounsel)
        every { getMembersByDealId.get(dealId) } returns dealMembers

        // When
        val dealTeam = getDealTeam.get(dealId)

        // Then
        assertThat(dealTeam.team(MemberDealTeam.SELLER).size, equalTo(2))
        assertThat(dealTeam.team(MemberDealTeam.SELLER), containsInAnyOrder(seller, sellerCounsel))
        assertThat(dealTeam.team(MemberDealTeam.BUYER).size, equalTo(2))
        assertThat(dealTeam.team(MemberDealTeam.BUYER), containsInAnyOrder(buyer, buyerCounsel))
    }
}
