package realestate.unlock.dealroom.api.unit.core.usecase.deal.file

import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFile
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.deal.reports.Report
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportType
import realestate.unlock.dealroom.api.core.entity.kfile.KPathFile
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.repository.deal.file.LinkedFileRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.deal.files.GetLinkedFileData
import realestate.unlock.dealroom.api.core.usecase.deal.reports.GetDealReportsByDealId
import realestate.unlock.dealroom.api.core.usecase.deal.reports.GetReportTypes
import realestate.unlock.dealroom.api.utils.entity.report.ReportBuilder
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Instant
import java.time.OffsetDateTime

class GetLinkedFileDataTest {

    private val linkedFileRepository: LinkedFileRepository = mockk(relaxed = true)
    private val getDealReportsByDealId: GetDealReportsByDealId = mockk(relaxed = true)
    private val taskRepository: TaskRepository = mockk(relaxed = true)
    private val fileGateway: FileGateway = mockk()
    private val getReportTypes: GetReportTypes = mockk()

    private lateinit var target: GetLinkedFileData

    @BeforeEach
    fun setUp() {
        target = GetLinkedFileData(
            linkedFileRepository = linkedFileRepository,
            getDealReportsByDealId = getDealReportsByDealId,
            taskRepository = taskRepository,
            fileGateway = fileGateway,
            getReportTypes = getReportTypes
        )
    }

    @Test
    fun `should not fail without linked files`() {
        val dealId = 123L
        mockLinkedData()
        mockReports()
        mockTask()
        mockFiles()

        val result = target.byDealId(dealId)

        Assertions.assertEquals(0, result.size)
    }

    @Test
    fun `should get task linked data`() {
        val dealId = 123L

        val task = TaskBuilder().build()
        val linkedFile = LinkedFile(
            id = anyId(),
            dealId = dealId,
            fileId = anyString(),
            entityType = LinkedFileEntityType.TASK,
            entityId = task.id.toString(),
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now()
        )
        mockLinkedData(list = listOf(linkedFile))
        mockFiles(list = listOf(linkedFile))
        mockReports()
        mockTask(tasks = listOf(task))

        val result = target.byDealId(dealId)

        Assertions.assertEquals(1, result.size)
        Assertions.assertEquals(task.id.toString(), result.first().entityId)
    }

    @Test
    fun `should get report linked data`() {
        val dealId = 123L

        val report = ReportBuilder().build()
        val linkedFile = LinkedFile(
            id = anyId(),
            dealId = dealId,
            fileId = anyString(),
            entityType = LinkedFileEntityType.REPORT,
            entityId = report.id.toString(),
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now()
        )
        mockLinkedData(list = listOf(linkedFile))
        mockFiles(list = listOf(linkedFile))
        mockReports(reports = listOf(report))
        mockTask()

        val result = target.byDealId(dealId)

        Assertions.assertEquals(1, result.size)
        Assertions.assertEquals(report.id.toString(), result.first().entityId)
        Assertions.assertEquals("ASBESTOS SURVEY", result.first().entityName)
    }

    private fun mockFiles(
        dealId: Long = 123L,
        list: List<LinkedFile> = emptyList()
    ) = every { fileGateway.getFiles(any()) } returns list.map {
        KPathFile(
            uid = it.fileId,
            name = "name-${it.fileId}",
            path = anyString(),
            sizeInBytes = 0,
            lastModified = Instant.now()
        )
    }

    private fun mockLinkedData(
        dealId: Long = 123L,
        list: List<LinkedFile> = emptyList()
    ) =
        every { linkedFileRepository.findByDealId(dealId) } returns list

    private fun mockTask(
        dealId: Long = 123L,
        tasks: List<Task> = emptyList()
    ) =
        every { taskRepository.findByDeal(dealId) } returns tasks

    private fun mockReports(
        dealId: Long = 123L,
        reports: List<Report> = emptyList()
    ) {
        every { getDealReportsByDealId.invoke(dealId) } returns reports
        every { getReportTypes() } returns listOf(ReportType(key = "ASBESTOS_SURVEY", description = "ASBESTOS SURVEY"))
    }
}
