package realestate.unlock.dealroom.api.unit.core.usecase.dashboard.calendar

import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventType
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput
import realestate.unlock.dealroom.api.core.entity.calendar.model.TaskCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.task.PriorityValue
import realestate.unlock.dealroom.api.core.repository.calendar.CalendarEventRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.GetCalendarTaskEvents
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.LocalDate

class GetCalendarTaskEventsTest {

    private val calendarEventRepository: CalendarEventRepository = mockk()

    private lateinit var getCalendarTaskEvents: GetCalendarTaskEvents

    @BeforeEach
    fun setUp() {
        getCalendarTaskEvents = GetCalendarTaskEvents(calendarEventRepository)
    }

    @Test
    fun `should return task events`() {
        val task = givenEventModel()

        every { calendarEventRepository.findTasksForEvents(any()) } returns listOf(task)

        val events = getCalendarTaskEvents.get(CalendarEventsInput(untilDays = 7, dealId = 123, organizationId = anyString()))

        Assertions.assertEquals(1, events.size)
        Assertions.assertEquals(CalendarEventType.TASK, events[0].type)
        Assertions.assertEquals("this is a task title", events[0].title)
        Assertions.assertEquals(false, events[0].isImportant)
    }

    @Test
    fun `should return task events that is important`() {
        val task = givenEventModel(priorityValue = PriorityValue.HIGHEST)

        every { calendarEventRepository.findTasksForEvents(any()) } returns listOf(task)

        val events = getCalendarTaskEvents.get(CalendarEventsInput(untilDays = 7, dealId = 123, organizationId = anyString()))

        Assertions.assertEquals(1, events.size)
        Assertions.assertEquals(CalendarEventType.TASK, events[0].type)
        Assertions.assertEquals("this is a task title", events[0].title)
        Assertions.assertEquals(true, events[0].isImportant)
    }

    fun givenEventModel(priorityValue: PriorityValue = PriorityValue.NA) =
        TaskCalendarEventModel(
            id = 1,
            dealId = 123,
            title = "this is a task title",
            dueDate = LocalDate.now(),
            priority = priorityValue.name,
            propertyName = "property name",
            addressApartment = "123",
            addressCity = "NY",
            addressState = "NY",
            addressStreet = "123",
            addressZip = "123"
        )
}
