package realestate.unlock.dealroom.api.unit.infrastructure.client.aws.sqs

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.usecase.deal.documents.DocumentReadyToSign
import realestate.unlock.dealroom.api.core.usecase.deal.documents.ExecutePsa
import realestate.unlock.dealroom.api.core.usecase.deal.documents.SignDocument
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.PsaDocuSignListener
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString

@ExtendWith(MockKExtension::class)
class PsaDocuSignListenerTest {

    @MockK
    private lateinit var signDocument: SignDocument

    @MockK
    private lateinit var executePsa: ExecutePsa

    @MockK
    private lateinit var documentReadyToSign: DocumentReadyToSign

    private lateinit var psaDocuSignListener: PsaDocuSignListener

    @BeforeEach
    fun setUp() {
        psaDocuSignListener = PsaDocuSignListener(
            signDocument = signDocument,
            documentReadyToSign = documentReadyToSign,
            executePsa = executePsa
        )
    }

    @Test
    fun `mark as sent fails throw the error`() {
        val envelopeId = anyString()
        val error = RuntimeException("test")
        every { documentReadyToSign.markAsSent(envelopeId) } throws error

        val failure = assertThrows<RuntimeException> {
            psaDocuSignListener.sent(envelopeId)
        }

        assertEquals(error, failure)
    }

    @Test
    fun `mark as sent fails with invalid psa status and should avoid retry does not fail`() {
        val envelopeId = anyString()
        val error = InvalidDocumentStatusException(DocumentBuilder().apply { status = DocumentStatus.PENDING_SELLER_SIGN }.build())
        every { documentReadyToSign.markAsSent(envelopeId) } throws error

        psaDocuSignListener.sent(envelopeId)
    }

    @Test
    fun `mark as sent fails with invalid psa status and shouldn't avoid retry it fails`() {
        val envelopeId = anyString()
        val error = InvalidDocumentStatusException(DocumentBuilder().apply { status = DocumentStatus.SELLER_REVIEW }.build())
        every { documentReadyToSign.markAsSent(envelopeId) } throws error

        val failure = assertThrows<InvalidDocumentStatusException> {
            psaDocuSignListener.sent(envelopeId)
        }

        assertEquals(error, failure)
    }
}
