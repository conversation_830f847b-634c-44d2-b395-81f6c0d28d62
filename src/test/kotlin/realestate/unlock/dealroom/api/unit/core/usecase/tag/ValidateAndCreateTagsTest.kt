package realestate.unlock.dealroom.api.unit.core.usecase.tag

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.tag.Tag
import realestate.unlock.dealroom.api.core.entity.tag.TagType
import realestate.unlock.dealroom.api.core.repository.tag.TagRepository
import realestate.unlock.dealroom.api.core.usecase.tag.ValidateAndCreateTags
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import java.time.OffsetDateTime

class ValidateAndCreateTagsTest {
    private lateinit var validateAndCreateTags: ValidateAndCreateTags

    private val tagRepository: TagRepository = mockk(relaxed = true)

    @BeforeEach
    fun setup() {
        validateAndCreateTags = ValidateAndCreateTags(tagRepository)
    }

    @Test
    fun `should create tags if new`() {
        // Given
        every { tagRepository.findByTypeAndNameLike(TagType.DEAL, any()) } returns null

        // When
        validateAndCreateTags.execute(setOf("tag1", "tag2"), TagType.DEAL)

        // Then
        verify { tagRepository.create(TagType.DEAL, "tag1") }
        verify { tagRepository.create(TagType.DEAL, "tag2") }
    }

    @Test
    fun `should not create tag that already exist`() {
        // Given
        every { tagRepository.findByTypeAndNameLike(TagType.DEAL, "tag1") } returns null
        every { tagRepository.findByTypeAndNameLike(TagType.DEAL, "tag2") } returns Tag(TagType.DEAL, "tag2", OffsetDateTime.now())
        every { tagRepository.findByTypeAndNameLike(TagType.DEAL, "tag3") } returns null

        // When
        validateAndCreateTags.execute(setOf("tag1", "tag2", "tag3"), TagType.DEAL)

        // Then
        verify { tagRepository.create(TagType.DEAL, "tag1") }
        verify(exactly = 0) { tagRepository.create(TagType.DEAL, "tag2") }
        verify { tagRepository.create(TagType.DEAL, "tag3") }
    }

    @Test
    fun `adding a tag that already exist with different caps`() {
        // Given
        every { tagRepository.findByTypeAndNameLike(TagType.DEAL, "tag1") } returns Tag(TagType.DEAL, "Tag1", OffsetDateTime.now())

        // When
        val exception = assertThrows(BadRequestException::class.java) {
            validateAndCreateTags.execute(setOf("tag1"), TagType.DEAL)
        }

        // Then
        assertEquals("Can not add tag 'tag1' because already exists as 'Tag1'", exception.message)
        verify(exactly = 0) { tagRepository.create(TagType.DEAL, any()) }
    }
}
