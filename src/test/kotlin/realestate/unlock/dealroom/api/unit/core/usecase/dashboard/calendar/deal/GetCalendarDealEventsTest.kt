package realestate.unlock.dealroom.api.unit.core.usecase.dashboard.calendar.deal

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventType
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput
import realestate.unlock.dealroom.api.core.entity.calendar.model.DealCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.repository.calendar.CalendarEventRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.GetCalendarDealEvents
import realestate.unlock.dealroom.api.core.usecase.calendar.eventfinder.ClosingDateExpirationCalendarEventFinder
import realestate.unlock.dealroom.api.core.usecase.calendar.eventfinder.DiligenceExpirationCalendarEventFinder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

@ExtendWith(MockKExtension::class)
class GetCalendarDealEventsTest {

    @MockK
    private lateinit var calendarEventRepository: CalendarEventRepository

    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)

    private var closingDateExpirationEventValidator = ClosingDateExpirationCalendarEventFinder(clock)

    private var diligenceExpirationEventValidator = DiligenceExpirationCalendarEventFinder(clock)

    private lateinit var getCalendarDealEvents: GetCalendarDealEvents

    @BeforeEach
    fun setUp() {
        getCalendarDealEvents = GetCalendarDealEvents(
            calendarEventRepository = calendarEventRepository,
            dateExpirationValidators = setOf(closingDateExpirationEventValidator, diligenceExpirationEventValidator)
        )
    }

    @Test
    fun `It should retrieve the events of all deals that have expiration dates within one week`() {
        // given
        val untilDays = 7
        val dealWithClosingDateInLessThanOneWeek = givenDealWithDates(
            initialClosingDate = LocalDate.now().plusDays(3),
            diligenceExpirationDate = LocalDate.now().plusDays(10)
        )
        val dealWithDiligenceExpirationInLessThanOneWeek = givenDealWithDates(
            initialClosingDate = LocalDate.now().plusDays(10),
            diligenceExpirationDate = LocalDate.now().plusDays(3)
        )

        every { calendarEventRepository.findDealsEvents(any()) } returns listOf(
            dealWithClosingDateInLessThanOneWeek,
            dealWithDiligenceExpirationInLessThanOneWeek
        )

        // when
        val result = getCalendarDealEvents.get(CalendarEventsInput(null, untilDays, null, null, organizationId = "org-id"))

        // then
        assertThat(result, hasSize(2))
        assertTrue(result.any { it.dealId == dealWithClosingDateInLessThanOneWeek.dealId && it.type == CalendarEventType.CLOSING_DATE })
        assertTrue(result.any { it.dealId == dealWithDiligenceExpirationInLessThanOneWeek.dealId && it.type == CalendarEventType.DUE_DILIGENCE })
    }

    @Test
    fun `if a deal has two expiration dates within one week it should generate both events`() {
        val untilDates = 7
        val givenDeal = givenDealWithDates(
            initialClosingDate = LocalDate.now().plusDays(1),
            diligenceExpirationDate = LocalDate.now().plusDays(2)
        )

        every { calendarEventRepository.findDealsEvents(any()) } returns listOf(givenDeal)

        val result = getCalendarDealEvents.get(CalendarEventsInput(null, untilDates, null, null, organizationId = "org-id"))

        // then
        assertThat(result, hasSize(2))
        assertTrue(result.any { it.dealId == givenDeal.dealId && it.type == CalendarEventType.CLOSING_DATE })
        assertTrue(result.any { it.dealId == givenDeal.dealId && it.type == CalendarEventType.DUE_DILIGENCE })
    }

    private fun givenDealWithDates(initialClosingDate: LocalDate, diligenceExpirationDate: LocalDate) =
        DealCalendarEventModel(
            dealId = anyId(),
            stage = Stage.EVALUATION,
            initialClosingDate = initialClosingDate,
            diligenceExpirationDate = diligenceExpirationDate,
            propertyName = "any", addressStreet = "any", addressApartment = "any", addressCity = "any", addressState = "any", addressZip = "any"
        )
}
