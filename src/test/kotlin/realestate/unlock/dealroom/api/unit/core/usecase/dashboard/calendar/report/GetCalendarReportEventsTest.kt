package realestate.unlock.dealroom.api.unit.core.usecase.dashboard.calendar.report

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventType
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput
import realestate.unlock.dealroom.api.core.entity.calendar.model.ReportCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportType
import realestate.unlock.dealroom.api.core.repository.calendar.CalendarEventRepository
import realestate.unlock.dealroom.api.core.usecase.calendar.GetCalendarReportEvents
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class GetCalendarReportEventsTest {

    @MockK
    private lateinit var calendarEventRepository: CalendarEventRepository

    private lateinit var getCalendarReportEvents: GetCalendarReportEvents

    @BeforeEach
    fun setUp() {
        getCalendarReportEvents = GetCalendarReportEvents(calendarEventRepository)
    }

    @Test
    fun `for each report with date within one week it should generate a calendar event`() {
        val untilDays = 7
        val today = LocalDate.now()
        val reportWithDateToday = givenReport(reportType = "SEISMIC_REPORT", expectedDate = today)
        val reportWithDateTomorrow = givenReport(reportType = "SOIL_AND_GEOLOGIC_REPORT", expectedDate = today.plusDays(1))
        val reportWithDateInThreeDays = givenReport(reportType = ReportType.OTHER, expectedDate = today.plusDays(3))
        every { calendarEventRepository.findReportsEvents(any()) } returns listOf(
            reportWithDateToday,
            reportWithDateTomorrow,
            reportWithDateInThreeDays
        )

        val result = getCalendarReportEvents.get(CalendarEventsInput(untilDays = untilDays, organizationId = "org-id"))

        assertThat(result, hasSize(3))
        assertTrue(result.any { it.dealId == reportWithDateToday.dealId && it.type == CalendarEventType.REPORT && it.title == "SEISMIC REPORT" })
        assertTrue(result.any { it.dealId == reportWithDateTomorrow.dealId && it.type == CalendarEventType.REPORT && it.title == "SOIL AND GEOLOGIC REPORT" })
        assertTrue(result.any { it.dealId == reportWithDateInThreeDays.dealId && it.type == CalendarEventType.REPORT && it.title == reportWithDateInThreeDays.reportName })
    }

    @Test
    fun `for each report with date within one week it should generate a calendar event by deal`() {
        val untilDays = 7
        val today = LocalDate.now()
        val reportWithDateToday = givenReport(reportType = "SEISMIC_REPORT", expectedDate = today)
        val reportWithDateTomorrow = givenReport(reportType = "SOIL_AND_GEOLOGIC_REPORT", expectedDate = today.plusDays(1))
        val reportWithDateInThreeDays = givenReport(reportType = ReportType.OTHER, expectedDate = today.plusDays(3))
        every { calendarEventRepository.findReportsEvents(any()) } returns listOf(
            reportWithDateToday,
            reportWithDateTomorrow,
            reportWithDateInThreeDays
        )

        val result = getCalendarReportEvents.get(CalendarEventsInput(untilDays = untilDays, dealId = 2, organizationId = "org-id"))

        assertThat(result, hasSize(3))
        assertTrue(result.any { it.eventId == reportWithDateToday.id && it.type == CalendarEventType.REPORT && it.title == "SEISMIC REPORT" })
        assertTrue(result.any { it.eventId == reportWithDateTomorrow.id && it.type == CalendarEventType.REPORT && it.title == "SOIL AND GEOLOGIC REPORT" })
        assertTrue(result.any { it.eventId == reportWithDateInThreeDays.id && it.type == CalendarEventType.REPORT && it.title == reportWithDateInThreeDays.reportName })
    }

    private fun givenReport(reportType: String, expectedDate: LocalDate) = ReportCalendarEventModel(
        id = anyId(),
        dealId = anyId(),
        type = reportType,
        reportName = null,
        expectedDate = expectedDate,
        propertyName = "any", addressStreet = "any", addressApartment = "any", addressCity = "any", addressState = "any", addressZip = "any"
    )
}
