package realestate.unlock.dealroom.api.unit.core.usecase.loi

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.AllOf
import org.hamcrest.core.IsEqual
import org.hamcrest.core.IsIterableContaining
import org.hamcrest.core.IsNot
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.kfile.KFileWithUrl
import realestate.unlock.dealroom.api.core.entity.loi.*
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.sign.*
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentSignRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetCompleteDeal
import realestate.unlock.dealroom.api.core.usecase.loi.BeginLetterOfIntentSigning
import realestate.unlock.dealroom.api.core.usecase.loi.GetLoiEmailParams
import realestate.unlock.dealroom.api.core.usecase.loi.SendLoiSigningEmail
import realestate.unlock.dealroom.api.infrastructure.utils.file.SanitizedFilename
import realestate.unlock.dealroom.api.utils.entity.deal.CompleteDealBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.LoiMedicalRoundBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.MemberSignBuilder
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.stub.FeatureFlagsStub
import java.net.URI
import java.time.*

class BeginMedicalLoiRoundSigningTest {

    private val sellerEmailParams = EmailParams(
        subject = "seller subject",
        body = "seller body"
    )
    private val carbonCopyEmailParams = EmailParams(
        subject = "cc subject",
        body = "cc body"
    )
    private val letterOfIntentSignRepository: LetterOfIntentSignRepository = mockk(relaxed = true) {
        every { findByLoiId(any()) } returns null
    }
    private val fileGateway: FileGateway = mockk()
    private val signGateway: SignGateway = mockk(relaxed = true)
    private val getCompleteDeal: GetCompleteDeal = mockk()
    private val sendLoiSigningEmail: SendLoiSigningEmail = mockk(relaxed = true)
    private val getLoiEmailParams: GetLoiEmailParams = mockk(relaxed = true)
    private val clock: Clock = Clock.fixed(Instant.now(), ZoneOffset.UTC)
    private val featureFlags = FeatureFlagsStub(mutableMapOf(Feature.SIGN_EMAIL to true))

    private val target = BeginLetterOfIntentSigning(
        letterOfIntentSignRepository,
        fileGateway,
        signGateway,
        getCompleteDeal,
        getLoiEmailParams,
        clock,
        sendLoiSigningEmail,
        featureFlags = featureFlags
    )

    @Test
    fun `begins signing`() {
        // given
        val givenCompleteDeal = givenCompleteDeal()
        val givenInput = givenBeginLetterOfIntentSigningInput()
        val givenLoi = LoiMedicalRoundBuilder()
            .apply { id = givenInput.loiId }
            .apply { dealId = givenCompleteDeal.id }
            .apply { status = LetterOfIntentRoundStatus.ACCEPTED }
            .build()

        val givenKFile = givenKFile(givenLoi)
        every { fileGateway.getFileWithUrl(givenLoi.offer.files.first().kFileId) } returns givenKFile
        every { getLoiEmailParams.getSellerTemplate(any(), givenLoi, any(), any()) } returns sellerEmailParams
        every { getLoiEmailParams.getCarbonCopyTemplate(any(), givenLoi, any(), any()) } returns carbonCopyEmailParams

        val givenSeller = MemberSignBuilder().build()

        // when
        target.execute(givenLoi, givenSeller)

        // then
        verify(exactly = 1) {
            signGateway.beginFlow(
                BeginSignFlowInput(
                    docusignDocument = DocusignDocument(
                        id = givenLoi.id.toString(),
                        name = SanitizedFilename("LOI_${givenCompleteDeal.property.address.fullAddress()}"),
                        extension = "pdf",
                        sourceUrl = URI.create(givenKFile.url)
                    ),
                    recipients = Recipients(
                        signers = listOf(
                            Signer(
                                givenSeller.id,
                                givenSeller.fullName,
                                givenSeller.email,
                                sellerEmailParams
                            )
                        ),
                        carbonCopies = listOf(
                            Contact(
                                givenCompleteDeal.buyer().id,
                                givenCompleteDeal.buyer().fullName,
                                givenCompleteDeal.buyer().email,
                                carbonCopyEmailParams
                            ),
                            Contact(
                                givenCompleteDeal.seller().id,
                                givenCompleteDeal.seller().fullName,
                                givenCompleteDeal.seller().email,
                                carbonCopyEmailParams
                            )
                        )
                    ),
                    tabs = Tabs(
                        signHere = listOf(
                            FormTab(
                                "seller_signature",
                                "/seller_signature/",
                                givenSeller.id,
                                0,
                                0
                            )
                        ),
                        dateSigned = listOf(
                            FormTab(
                                "seller_date_signed",
                                "/seller_date_signed/",
                                givenSeller.id,
                                0,
                                0
                            )
                        ),
                        company = listOf(
                            FormTab(
                                "seller_company",
                                "/seller_company/",
                                givenSeller.id,
                                0,
                                0
                            )
                        ),
                        fullName = listOf(
                            FormTab(
                                "seller_fullname",
                                "/seller_fullname/",
                                givenSeller.id,
                                0,
                                0
                            )
                        ),
                        title = listOf(
                            FormTab(
                                "seller_title",
                                "/seller_title/",
                                givenSeller.id,
                                0,
                                0
                            )
                        )
                    ),
                    reminder = null,
                    envelopeStatus = EnvelopeStatus.SENT,
                    signDocumentType = SignDocumentType.LOI
                )
            )
        }
    }

    @Test
    fun `begins signing with ff off`() {
        // given
        val givenCompleteDeal = givenCompleteDeal()
        val givenInput = givenBeginLetterOfIntentSigningInput()
        val givenLoi = LoiMedicalRoundBuilder()
            .apply { id = givenInput.loiId }
            .apply { dealId = givenCompleteDeal.id }
            .apply { status = LetterOfIntentRoundStatus.ACCEPTED }
            .build()
        featureFlags.configureFeature(Feature.SIGN_EMAIL, false)
        val givenKFile = givenKFile(givenLoi)
        every { fileGateway.getFileWithUrl(givenLoi.offer.files.first().kFileId) } returns givenKFile

        val givenSeller = MemberSignBuilder().build()
        every { getLoiEmailParams.getSellerTemplate(any(), givenLoi, any(), any()) } returns sellerEmailParams
        every { getLoiEmailParams.getCarbonCopyTemplate(any(), givenLoi, any(), any()) } returns carbonCopyEmailParams

        // when
        target.execute(givenLoi, givenSeller)

        // then
        verify(exactly = 0) { sendLoiSigningEmail.invoke(any(), givenLoi, any()) }
        verify(exactly = 1) {
            signGateway.beginFlow(
                BeginSignFlowInput(
                    docusignDocument = DocusignDocument(
                        id = givenLoi.id.toString(),
                        name = SanitizedFilename("LOI_${givenCompleteDeal.property.address.fullAddress()}"),
                        extension = "pdf",
                        sourceUrl = URI.create(givenKFile.url)
                    ),
                    recipients = Recipients(
                        signers = listOf(
                            Signer(
                                givenSeller.id,
                                givenSeller.fullName,
                                givenSeller.email,
                                sellerEmailParams
                            )
                        ),
                        carbonCopies = listOf(
                            Contact(
                                givenCompleteDeal.buyer().id,
                                givenCompleteDeal.buyer().fullName,
                                givenCompleteDeal.buyer().email,
                                carbonCopyEmailParams
                            ),
                            Contact(
                                givenCompleteDeal.seller().id,
                                givenCompleteDeal.seller().fullName,
                                givenCompleteDeal.seller().email,
                                carbonCopyEmailParams
                            )
                        )
                    ),
                    tabs = Tabs(
                        signHere = listOf(
                            FormTab(
                                "seller_signature",
                                "/seller_signature/",
                                givenSeller.id,
                                0,
                                0
                            )
                        ),
                        dateSigned = listOf(
                            FormTab(
                                "seller_date_signed",
                                "/seller_date_signed/",
                                givenSeller.id,
                                0,
                                0
                            )
                        ),
                        company = listOf(
                            FormTab(
                                "seller_company",
                                "/seller_company/",
                                givenSeller.id,
                                0,
                                0
                            )
                        ),
                        fullName = listOf(
                            FormTab(
                                "seller_fullname",
                                "/seller_fullname/",
                                givenSeller.id,
                                0,
                                0
                            )
                        ),
                        title = listOf(
                            FormTab(
                                "seller_title",
                                "/seller_title/",
                                givenSeller.id,
                                0,
                                0
                            )
                        )
                    ),
                    reminder = Reminder(Duration.ofDays(2)),
                    envelopeStatus = EnvelopeStatus.SENT,
                    signDocumentType = SignDocumentType.LOI
                )
            )
        }
    }

    @Test
    fun `saves signing result into db`() {
        // given
        val givenCompleteDeal = givenCompleteDeal()

        val givenInput = givenBeginLetterOfIntentSigningInput()
        val givenLoi = LoiMedicalRoundBuilder()
            .apply { id = givenInput.loiId }
            .apply { dealId = givenCompleteDeal.id }
            .apply { status = LetterOfIntentRoundStatus.ACCEPTED }
            .build()

        val givenKFile = givenKFile(givenLoi)
        every { fileGateway.getFileWithUrl(givenLoi.offer.files.first().kFileId) } returns givenKFile

        val givenSignOutput =
            BeginSignFlowOutput(anyString())
        every { signGateway.beginFlow(any()) } returns givenSignOutput
        every { getLoiEmailParams.getSellerTemplate(any(), givenLoi, any(), any()) } returns sellerEmailParams
        every { getLoiEmailParams.getCarbonCopyTemplate(any(), givenLoi, any(), any()) } returns carbonCopyEmailParams
        // when
        target.execute(givenLoi, MemberSign(givenCompleteDeal.seller(), completedAt = null))

        // then
        verify(exactly = 1) {
            letterOfIntentSignRepository.save(
                LetterOfIntentSign(
                    loiId = givenLoi.id,
                    signingId = givenSignOutput.signingId,
                    documentId = givenLoi.id,
                    buyerSign = MemberSign(givenCompleteDeal.buyer(), completedAt = OffsetDateTime.now(clock)),
                    sellerSign = MemberSign(givenCompleteDeal.seller(), completedAt = null)
                )
            )
        }
    }

    @Test
    fun `team members should be set as carbon copies`() {
        // given
        val givenBuyer = MemberObjectMother.buyer()
        val givenSeller = MemberObjectMother.seller()
        val givenBuyerCounsel = MemberObjectMother.buyerCounsel()
        val givenSellerCounsel = MemberObjectMother.sellerCounsel()
        val givenSellerBroker = MemberObjectMother.sellerBroker()
        val givenCompleteDeal = CompleteDealBuilder().apply {
            members = setOf(
                givenBuyer,
                givenSeller,
                givenBuyerCounsel,
                givenSellerCounsel,
                givenSellerBroker
            )
        }.build()
        every { getCompleteDeal.toCompleteDeal(givenCompleteDeal.id) } returns givenCompleteDeal

        val givenInput = givenBeginLetterOfIntentSigningInput()
        val givenLoi = LoiMedicalRoundBuilder()
            .apply { id = givenInput.loiId }
            .apply { dealId = givenCompleteDeal.id }
            .apply { status = LetterOfIntentRoundStatus.ACCEPTED }
            .build()

        val givenKFile = givenKFile(givenLoi)
        every { fileGateway.getFileWithUrl(givenLoi.offer.files.first().kFileId) } returns givenKFile
        every { getLoiEmailParams.getSellerTemplate(any(), givenLoi, any(), any()) } returns sellerEmailParams
        every { getLoiEmailParams.getCarbonCopyTemplate(any(), givenLoi, any(), any()) } returns carbonCopyEmailParams
        // when
        target.execute(givenLoi, MemberSign(givenCompleteDeal.seller(), completedAt = null))

        // then
        verify(exactly = 1) {
            signGateway.beginFlow(
                withArg {
                    assertThat(
                        it.recipients.carbonCopies,
                        AllOf(
                            IsIterableContaining(
                                IsEqual(
                                    Contact(
                                        givenBuyerCounsel.id,
                                        givenBuyerCounsel.fullName,
                                        givenBuyerCounsel.email,
                                        carbonCopyEmailParams
                                    )
                                )
                            ),
                            IsIterableContaining(
                                IsEqual(
                                    Contact(
                                        givenSellerCounsel.id,
                                        givenSellerCounsel.fullName,
                                        givenSellerCounsel.email,
                                        carbonCopyEmailParams
                                    )
                                )
                            ),
                            IsIterableContaining(
                                IsEqual(
                                    Contact(
                                        givenSellerBroker.id,
                                        givenSellerBroker.fullName,
                                        givenSellerBroker.email,
                                        carbonCopyEmailParams
                                    )
                                )
                            )
                        )
                    )
                    assertThat(
                        it.recipients.carbonCopies,
                        AllOf(
                            IsIterableContaining(
                                IsNot(
                                    IsEqual(
                                        Contact(
                                            givenSeller.id,
                                            givenSeller.fullName,
                                            givenSeller.email,
                                            sellerEmailParams
                                        )
                                    )
                                )
                            )
                        )
                    )
                }
            )
        }
    }

    private fun givenBeginLetterOfIntentSigningInput() =
        BeginLetterOfIntentSigningInput(loiId = 1)

    private fun givenKFile(loi: MedicalLoiRound) =
        KFileWithUrl(loi.offer.files.first().kFileId, "http://localhost:8081/${anyString()}", anyString())

    private fun CompleteDeal.seller() = this.members.first { it.typeKey == "seller" }
    private fun CompleteDeal.buyer() = this.members.first { it.typeKey == "buyer" }

    private fun givenCompleteDeal() = CompleteDealBuilder().build().also { completeDeal ->
        every { getCompleteDeal.toCompleteDeal(completeDeal.id) } returns completeDeal
    }
}
