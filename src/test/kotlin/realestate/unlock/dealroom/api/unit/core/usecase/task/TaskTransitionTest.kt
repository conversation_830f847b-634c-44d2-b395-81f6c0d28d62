package realestate.unlock.dealroom.api.unit.core.usecase.task

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.task.TaskTransition
import realestate.unlock.dealroom.api.core.usecase.task.update.UpdateTask
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.task.TaskBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId

@ExtendWith(MockKExtension::class)
class TaskTransitionTest {

    @MockK
    private lateinit var taskRepository: TaskRepository

    @MockK
    private lateinit var updateTask: UpdateTask
    private lateinit var taskTransition: TaskTransition

    @BeforeEach
    fun setUp() {
        taskTransition = TaskTransition(
            taskRepository = taskRepository,
            updateTask = updateTask
        )
    }

    @Test
    fun `Cannot approve a task that is not in review`() {
        // Given
        val buyer = MemberObjectMother.buyer()
        val task = givenTaskInStatus(status = TaskStatus.DONE)

        // When
        val error = assertThrows<RuntimeException> {
            taskTransition(TaskTransitionInput(task.id, buyer, transition = TaskStatusTransitions.APPROVE))
        }

        // Then
        assertThat("Invalid task status - [TASK_ID:${task.id}]", equalTo(error.message))
    }

    @Test
    fun `Can approve a task in review state`() {
        // Given
        val buyer = MemberObjectMother.buyer()
        val task = givenTaskInStatus(status = TaskStatus.IN_REVIEW)
        val input = TaskTransitionInput(task.id, buyer, transition = TaskStatusTransitions.APPROVE)
        val completedTaskAfterUpdate = updateResultFor(task = task, member = buyer, taskInput = input)

        // When
        val result = taskTransition(input)

        // Then
        assertThat(completedTaskAfterUpdate, equalTo(result))
    }

    @Test
    fun `Can approve a task in to_do state`() {
        // Given
        val buyer = MemberObjectMother.buyer()
        val task = givenTaskInStatus(status = TaskStatus.TO_DO)
        val attachedForm = mapOf(pair = Pair(first = "uno", second = 0))
        val input = TaskTransitionInput(task.id, buyer, transition = TaskStatusTransitions.APPROVE, attachedForm = attachedForm)
        val completedTaskAfterUpdate = updateResultFor(task = task, member = buyer, taskInput = input)

        // When
        val result = taskTransition(input)

        // Then
        assertThat(completedTaskAfterUpdate, equalTo(result))
    }

    @Test
    fun `Cannot reject a task that is not in review`() {
        // Given
        val buyer = MemberObjectMother.buyer()
        val task = givenTaskInStatus(dealCategoryId = anyId(), status = TaskStatus.DONE, assignedTeam = MemberDealTeam.BUYER, assignedBuyer = buyer)

        // When
        val error = assertThrows<RuntimeException> {
            taskTransition(
                TaskTransitionInput(
                    taskId = task.id,
                    member = buyer,
                    rejectReason = "reason",
                    transition = TaskStatusTransitions.REJECT
                )
            )
        }

        // Then
        assertThat("Invalid task status - [TASK_ID:${task.id}]", equalTo(error.message))
    }

    @Test
    fun `Can reject a task in review state`() {
        // Given
        val dealCategoryId = anyId()
        val buyer = MemberObjectMother.buyer()
        val task = givenTaskInStatus(dealCategoryId = dealCategoryId, status = TaskStatus.IN_REVIEW, assignedBuyer = buyer, assignedTeam = buyer.getDealTeam())
        val rejectionReason = "this is the rejected reason"
        val input = TaskTransitionInput(
            taskId = task.id,
            member = buyer,
            rejectReason = rejectionReason,
            transition = TaskStatusTransitions.REJECT
        )

        val completedTaskAfterUpdate = updateResultFor(
            task = task,
            status = TaskStatus.REJECTED,
            newAssignedTeam = MemberDealTeam.SELLER,
            member = buyer,
            rejectionReason = rejectionReason,
            taskInput = input
        )

        // When
        val result = taskTransition(input)

        // Then
        assertThat(completedTaskAfterUpdate, equalTo(result))
    }

    private fun givenTaskInStatus(
        dealCategoryId: Long = anyId(),
        status: TaskStatus,
        assignedTeam: MemberDealTeam = MemberDealTeam.SELLER,
        assignedBuyer: Member = MemberObjectMother.buyer()
    ): Task {
        return TaskBuilder()
            .withId(anyId())
            .withDealCategoryId(dealCategoryId)
            .withAssignedBuyer(assignedBuyer)
            .withAssignedTeam(assignedTeam)
            .withTemplateKey("upload_file")
            .withStatus(status)
            .withTypeKey("building_documents")
            .build()
            .also { task ->
                every { taskRepository.findById(task.id) } returns task
            }
    }

    private fun updateResultFor(
        task: Task,
        status: TaskStatus = TaskStatus.DONE,
        member: Member,
        taskInput: TaskTransitionInput,
        newAssignedTeam: MemberDealTeam? = null,
        rejectionReason: String? = null
    ) =
        task.toTaskWithFilesAndHistory().also {
            every {
                updateTask.update(
                    getUpdateTaskInput(
                        task = task,
                        taskInput = taskInput,
                        assignedTeam = newAssignedTeam ?: task.assignedTeam,
                        member = member,
                        rejectionReason = rejectionReason,
                        status = status
                    ),
                    task
                )
            } returns it
        }

    private fun getUpdateTaskInput(
        task: Task,
        taskInput: TaskTransitionInput,
        assignedBuyer: Member? = null,
        assignedTeam: MemberDealTeam?,
        member: Member,
        rejectionReason: String?,
        status: TaskStatus
    ): UpdateTaskInput =
        UpdateTaskInput(
            id = task.id,
            status = status,
            dueDate = taskInput.dueDate,
            attachedForm = taskInput.attachedForm,
            assignedTeam = assignedTeam ?: task.assignedTeam,
            assignedBuyerId = assignedBuyer?.id ?: taskInput.assignedBuyerId,
            member = member,
            rejectionReason = rejectionReason,
            enabled = taskInput.enabled
        )
}
