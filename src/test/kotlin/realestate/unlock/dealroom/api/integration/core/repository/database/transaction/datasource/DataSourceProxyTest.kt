package realestate.unlock.dealroom.api.integration.core.repository.database.transaction.datasource

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.DataSourceProxy
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.TransactionalDataSource
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest

class DataSourceProxyTest : BaseFunctionalWithoutRestTest() {

    lateinit var dataSourceProxy: DataSourceProxy

    @BeforeEach
    fun setUp() {
        dataSourceProxy = Context.injector.getInstance(TransactionalDataSource::class.java) as DataSourceProxy
    }

    @Test
    fun `when finalize a transaction, connection is closed`() {
        dataSourceProxy.beginTransaction()
        val currentConnection = dataSourceProxy.connection

        dataSourceProxy.finalizeTransaction()

        assertThat(currentConnection.isClosed, IsEqual(true))
    }

    @Test
    fun `when rollback a transaction, connection is closed`() {
        dataSourceProxy.beginTransaction()
        val currentConnection = dataSourceProxy.connection

        dataSourceProxy.rollback()

        assertThat(currentConnection.isClosed, IsEqual(true))
    }
}
