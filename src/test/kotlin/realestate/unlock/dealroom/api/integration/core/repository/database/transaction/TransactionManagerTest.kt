package realestate.unlock.dealroom.api.integration.core.repository.database.transaction

import com.google.i18n.phonenumbers.PhoneNumberUtil
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.MemberToSave
import realestate.unlock.dealroom.api.core.entity.member.type.MemberType
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager.useTransaction
import realestate.unlock.dealroom.api.repository.database.member.MemberDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.stub.CreateMemberTransactionManagementStub

class TransactionManagerTest : BaseFunctionalWithoutRestTest() {

    @Test
    fun `Should rollback transaction`() {
        // Given
        val memberToSave = givenMemberToSave("Should rollback transaction")
        val givenException = RuntimeException("Testing transactions")

        val target = Context.injector.getInstance(CreateMemberTransactionManagementStub::class.java)

        // When
        val result = target.runCatching {
            useTransaction {
                this.execute(memberToSave, givenException)
            }
        }

        // Then
        assertThat(result.isFailure, equalTo(true))
        assertThat(result.exceptionOrNull(), instanceOf(RuntimeException::class.java))
        assertThat(result.exceptionOrNull()?.localizedMessage, equalTo("Testing transactions"))

        result.getOrNull()

        Context.injector.getInstance(MemberDatabaseRepository::class.java)
            .findFiltered(mapOf(), memberToSave.organizationId)
            .let { members ->
                assertThat(members, emptyIterable())
            }
    }

    @Test
    fun `Should not rollback transaction if does not use transaction manager`() {
        // Given
        val givenException = RuntimeException("Testing transactions")
        val memberToSave = givenMemberToSave("Should not rollback transaction if does not use transaction manager")

        val target = Context.injector.getInstance(CreateMemberTransactionManagementStub::class.java)

        // When
        val result = target.runCatching {
            this.execute(memberToSave, givenException)
        }

        // Then
        assertThat(result.isFailure, equalTo(true))
        assertThat(result.exceptionOrNull(), instanceOf(RuntimeException::class.java))
        assertThat(result.exceptionOrNull()?.localizedMessage, equalTo("Testing transactions"))

        result.getOrNull()

        Context.injector.getInstance(MemberDatabaseRepository::class.java)
            .findFiltered(mapOf(), memberToSave.organizationId)
            .let { members ->
                assertThat(members.size, equalTo(1))
            }
    }

    @Test
    fun `Should save both tables correctly`() {
        // Given
        val memberToSave = givenMemberToSave("Should save both tables correctly")

        val target = Context.injector.getInstance(CreateMemberTransactionManagementStub::class.java)

        // When
        val result = target.runCatching {
            useTransaction {
                this.execute(memberToSave)
            }
        }

        // Then
        assertThat(result.isFailure, equalTo(false))
        assertThat(result.exceptionOrNull(), nullValue())

        result.getOrNull()

        Context.injector.getInstance(MemberDatabaseRepository::class.java)
            .findFiltered(mapOf(), memberToSave.organizationId)
            .let { members ->
                assertThat(members.size, equalTo(1))
            }
    }

    @Test
    fun `should propagate transaction, save all`() {
        val memberToSave1 = givenMemberToSave("memberToSave1")
        val memberToSave2 = givenMemberToSave("memberToSave2")

        val target = Context.injector.getInstance(CreateMemberTransactionManagementStub::class.java)

        useTransaction(propagateTransaction = true) {
            target.execute(memberToSave1)
            useTransaction {
                target.execute(memberToSave2)
            }
        }

        Context.injector.getInstance(MemberDatabaseRepository::class.java)
            .findFiltered(mapOf(), memberToSave1.organizationId)
            .let { members ->
                assertThat(members.size, equalTo(2))
                assertTrue(members.any { it.lastName == memberToSave1.lastName })
                assertTrue(members.any { it.lastName == memberToSave2.lastName })
            }
    }

    @Test
    fun `when propagates transaction, if there is an error in the first block, rollback all`() {
        val memberToSave1 = givenMemberToSave("memberToSave1")
        val memberToSave2 = givenMemberToSave("memberToSave2")

        val error = RuntimeException("test")
        val target = Context.injector.getInstance(CreateMemberTransactionManagementStub::class.java)

        val result = target.runCatching {
            useTransaction(propagateTransaction = true) {
                useTransaction {
                    target.execute(memberToSave2)
                }
                target.execute(memberToSave1, error)
            }
        }

        assertThat(result.isFailure, equalTo(true))
        assertThat(result.exceptionOrNull(), instanceOf(RuntimeException::class.java))
        assertThat(result.exceptionOrNull()?.localizedMessage, equalTo(error.localizedMessage))

        Context.injector.getInstance(MemberDatabaseRepository::class.java)
            .findFiltered(mapOf(), memberToSave1.organizationId)
            .let { members ->
                assertThat(members.size, equalTo(0))
            }
    }

    @Test
    fun `when propagates transaction, if there is an error in the second block, rollback all`() {
        val memberToSave1 = givenMemberToSave("memberToSave1")
        val memberToSave2 = givenMemberToSave("memberToSave2")

        val error = RuntimeException("test")
        val target = Context.injector.getInstance(CreateMemberTransactionManagementStub::class.java)

        val result = target.runCatching {
            useTransaction(propagateTransaction = true) {
                useTransaction {
                    target.execute(memberToSave2, error)
                }
                target.execute(memberToSave1)
            }
        }

        assertThat(result.isFailure, equalTo(true))
        assertThat(result.exceptionOrNull(), instanceOf(RuntimeException::class.java))
        assertThat(result.exceptionOrNull()?.localizedMessage, equalTo(error.localizedMessage))

        Context.injector.getInstance(MemberDatabaseRepository::class.java)
            .findFiltered(mapOf(), memberToSave1.organizationId)
            .let { members ->
                assertThat(members.size, equalTo(0))
            }
    }

    @Test
    fun `when does not propagate transaction, if there is an error, transaction without error is committed `() {
        val memberToSave1 = givenMemberToSave("memberToSave1")
        val memberToSave2 = givenMemberToSave("memberToSave2")

        val error = RuntimeException("test")
        val target = Context.injector.getInstance(CreateMemberTransactionManagementStub::class.java)

        val result = target.runCatching {
            useTransaction {
                useTransaction(propagateTransaction = false) {
                    target.execute(memberToSave2)
                }
                target.execute(memberToSave1, error)
            }
        }

        assertThat(result.isFailure, equalTo(true))
        assertThat(result.exceptionOrNull(), instanceOf(RuntimeException::class.java))
        assertThat(result.exceptionOrNull()?.localizedMessage, equalTo(error.localizedMessage))

        Context.injector.getInstance(MemberDatabaseRepository::class.java)
            .findFiltered(mapOf(), memberToSave1.organizationId)
            .let { members ->
                assertThat(members.size, equalTo(1))
                assertTrue(members.any { it.lastName == memberToSave2.lastName })
            }
    }

    private fun givenMemberToSave(lastName: String): MemberToSave {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        return MemberToSave(
            type = MemberType(
                id = anyId(),
                key = "seller",
                name = "Seller"
            ),
            firstName = "Test",
            lastName = lastName,
            companyName = "Test Company Name",
            address = "Test Address",
            phoneNumber = givenPhone,
            email = "${anyString()}@test.com",
            authId = null,
            walkThroughDone = false,
            organizationId = "org-id"
        )
    }
}
