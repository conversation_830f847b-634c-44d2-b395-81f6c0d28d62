package realestate.unlock.dealroom.api.repository.database.file

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest

class FileTypePromptDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var fileTypePromptDatabaseRepository: DocumentTypePromptDatabaseRepository

    @BeforeEach
    fun setUp() {
        fileTypePromptDatabaseRepository = Context.injector.getInstance(DocumentTypePromptDatabaseRepository::class.java)
    }

    @Test
    fun `it retrieves the correct prompt`() {
        val givenReportType = "PHASE_I"
        val givenPrompt = "this is the prompt for the report type PHASE_I"
        saveReportPrompt(givenReportType, givenPrompt)

        val got = fileTypePromptDatabaseRepository.getPrompt(document = TokenFileType.REPORT.name, documentType = givenReportType)

        assertThat(got.prompt, equalTo(givenPrompt))
    }

    @Test
    fun `retrieving the prompt of a report that does not contains a specified prompt should return the default one`() {
        val prompt = fileTypePromptDatabaseRepository.getPrompt(document = TokenFileType.REPORT.name, documentType = "report-with-no-prompt")
        assertThat(prompt, equalTo(fileTypePromptDatabaseRepository.getPrompt(document = "DEFAULT", documentType = null)))
    }

    private fun saveReportPrompt(reportType: String, prompt: String) = fileTypePromptDatabaseRepository.save(
        document = TokenFileType.REPORT.name,
        documentType = reportType,
        prompt = prompt
    )
}
