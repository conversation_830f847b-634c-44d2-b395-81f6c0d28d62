package realestate.unlock.dealroom.api.repository.database.loi

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.loi.ExecutedLetterOfIntent
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentSigned
import realestate.unlock.dealroom.api.core.entity.loi.MedicalLoiRound
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.SaveMedicalLetterOfIntentRoundBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class ExecutedLetterOfIntentDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var letterOfIntentRoundDatabaseRepository: MedicalLetterOfIntentRoundDatabaseRepository

    private lateinit var saveExecutedLetterOfIntentDatabaseRepository: ExecutedLetterOfIntentDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        letterOfIntentRoundDatabaseRepository = Context.injector.getInstance(MedicalLetterOfIntentRoundDatabaseRepository::class.java)
        saveExecutedLetterOfIntentDatabaseRepository = Context.injector.getInstance(ExecutedLetterOfIntentDatabaseRepository::class.java)
    }

    @Test
    fun `can save an executed loi`() {
        // given
        val deal = DealCreator.createDealByRest()
        val loi = givenLoi(deal)
        val executedLetterOfIntent = ExecutedLetterOfIntent(
            dealId = deal.id,
            loiId = loi.id,
            letterOfIntentSigned = LetterOfIntentSigned(
                name = anyString(),
                kFileId = anyString()
            ),
            executedAt = LocalDate.now()
        )

        // when
        val result = saveExecutedLetterOfIntentDatabaseRepository.save(executedLetterOfIntent)

        // then
        assertThat(result, IsEqual(executedLetterOfIntent))
    }

    private fun givenLoi(deal: CompleteDeal): MedicalLoiRound {
        val loiOffer = SaveMedicalLetterOfIntentRoundBuilder().apply {
            memberId = deal.members.first().id
            dealId = deal.id
        }.build()
        return letterOfIntentRoundDatabaseRepository.save(loiOffer)
    }
}
