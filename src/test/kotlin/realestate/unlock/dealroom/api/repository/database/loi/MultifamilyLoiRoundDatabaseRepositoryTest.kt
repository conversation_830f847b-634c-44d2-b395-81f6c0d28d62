package realestate.unlock.dealroom.api.repository.database.loi

import org.hamcrest.MatcherAssert
import org.hamcrest.core.IsEqual
import org.hamcrest.core.IsNull
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.loi.MultifamilyLoiRound
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.SaveMultiFamilyLetterOfIntentRoundBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class MultifamilyLoiRoundDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var target: MultifamilyLetterOfIntentRoundDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        target = Context.injector.getInstance(MultifamilyLetterOfIntentRoundDatabaseRepository::class.java)
    }

    @Test
    fun `can find by deal and status`() {
        // given
        val loiRound = givenLoi()

        // when
        val loiRoundFound = target.findByDealIdAndStatus(dealId = loiRound.dealId, statusIn = listOf(loiRound.status))

        // then
        assertEquals(loiRound.id, loiRoundFound?.id)
        assertEquals(loiRound.dealId, loiRoundFound?.dealId)
        assertEquals(loiRound.status, loiRoundFound?.status)
    }

    @Test
    fun `returns null if there is not match`() {
        // given
        val loiRound = givenLoi()

        // when
        val loiRoundFound = target.findByDealIdAndStatus(dealId = loiRound.dealId, statusIn = listOf(LetterOfIntentRoundStatus.REJECTED))

        // then
        assertNull(loiRoundFound)
    }

    @Test
    fun `can find a loi by dealId and status in`() {
        // given
        val loi = givenLoi()

        // when
        val result = target.findByDealIdAndStatus(dealId = loi.dealId, statusIn = listOf(loi.status))

        // then
        assertEquals(loi.id, result?.id)
        assertEquals(loi.dealId, result?.dealId)
        assertEquals(loi.status, result?.status)
    }

    @Test
    fun `can handle empty result`() {
        // given
        val loi = givenLoi()

        // when
        val result = target.findByDealIdAndStatus(dealId = loi.dealId, statusIn = listOf(LetterOfIntentRoundStatus.REJECTED))

        // then
        MatcherAssert.assertThat(result, IsNull())
    }

    @Test
    fun `can save a loi`() {
        // given
        val deal = DealCreator.createDealByRest()
        val offerInput = SaveMultiFamilyLetterOfIntentRoundBuilder().apply {
            memberId = deal.members.first().id
            dealId = deal.id
        }.build()

        // when
        val result = target.save(offerInput)

        // then
        MatcherAssert.assertThat(result.dealId, IsEqual(deal.id))
        MatcherAssert.assertThat(result.status, IsEqual(LetterOfIntentRoundStatus.IN_NEGOTIATION))
        MatcherAssert.assertThat(result.offer.customSections!!.sections.first().title, IsEqual(offerInput.offerCustomSections!!.sections.first().title))
        MatcherAssert.assertThat(result.offer.customSections!!.sections.first().content, IsEqual(offerInput.offerCustomSections!!.sections.first().content))
    }

    private fun givenLoi(): MultifamilyLoiRound {
        val deal = DealCreator.createDealByRest()
        return target.save(
            SaveMultiFamilyLetterOfIntentRoundBuilder().apply {
                dealId = deal.id
                memberId = deal.members.first().id
            }.build()
        )
    }
}
