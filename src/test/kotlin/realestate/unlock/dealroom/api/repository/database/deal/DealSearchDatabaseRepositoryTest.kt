package realestate.unlock.dealroom.api.repository.database.deal

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.deal.DealSearch
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedInput
import realestate.unlock.dealroom.api.core.entity.search.SortOrder
import realestate.unlock.dealroom.api.core.repository.deal.DealSearchRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class DealSearchDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var propertyRepository: PropertyRepository

    private lateinit var dealSearchRepository: DealSearchRepository

    private lateinit var member: Member

    @BeforeEach
    fun setUp() {
        member = AuthMock.createMemberWithUser()
        dealSearchRepository = Context.injector.getInstance(DealSearchRepository::class.java)
        propertyRepository = Context.injector.getInstance(PropertyRepository::class.java)
    }

    @Test
    fun `can get recent deals`() {
        val firstDeal = givenDeal()
        val secondDeal = givenDeal()
        val thirdDeal = givenDeal()

        val deals = dealSearchRepository.findDeals(
            input = PaginatedInput(size = 10, offset = 0, order = SortOrder.DESC, orderBy = "updated_at", filters = emptyMap(), total = 0),
            memberId = null,
            organizationId = member.organizationId
        )

        assertEquals(3, deals.size)

        assertRecentDeal(thirdDeal, deals[0])
        assertRecentDeal(secondDeal, deals[1])
        assertRecentDeal(firstDeal, deals[2])
    }

    @Test
    fun `can not get deal of other organizations`() {
        val firstDeal = givenDeal()
        val secondDeal = givenDeal()

        val deals = dealSearchRepository.findDeals(
            input = PaginatedInput(size = 10, offset = 0, order = SortOrder.DESC, orderBy = "updated_at", filters = emptyMap(), total = 0),
            memberId = null,
            organizationId = anyString()
        )

        assertThat(deals, hasSize(0))
    }

    private fun assertRecentDeal(expectedDeal: CompleteDeal, currentDeal: DealSearch) {
        assertEquals(expectedDeal.id, currentDeal.id)
        assertEquals(expectedDeal.status, currentDeal.status)
        assertEquals(expectedDeal.stage, currentDeal.stage)
        assertEquals(expectedDeal.tenantName, currentDeal.tenantName)
        val property = propertyRepository.findById(expectedDeal.propertyId)
        assertEquals(property.address, currentDeal.address)
        assertEquals(property.mainPhoto, currentDeal.photo)
        assertEquals(property.name, currentDeal.name)
        assertEquals(property.type, currentDeal.type)
    }

    private fun givenDeal() =
        DealCreator.createDealByRest(
            seller = givenMember("seller"),
            buyer = givenMember("buyer")
        )

    private fun givenMember(memberType: String) =
        AuthMock.createMemberWithUser(memberType = memberType, token = anyString(), email = "${anyString()}@test.com", uid = anyString())
}
