package realestate.unlock.dealroom.api.repository.database.member.type

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest

class MemberTypeDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var memberTypeDatabaseRepository: MemberTypeDatabaseRepository

    @BeforeEach
    fun setUp() {
        memberTypeDatabaseRepository = Context.injector.getInstance(MemberTypeDatabaseRepository::class.java)
    }

    @Test
    fun `can find by key`() {
        val buyer = memberTypeDatabaseRepository.findByKey(MemberTypeEnum.BUYER.key)
        assertEquals(MemberTypeEnum.BUYER, buyer.getEnumType())

        val sellerUpperCase = memberTypeDatabaseRepository.findByKey("SELLER")
        assertEquals(MemberTypeEnum.SELLER, sellerUpperCase.getEnumType())
    }

    @Test
    fun `can find all`() {
        val memberTypes = memberTypeDatabaseRepository.findAll()

        MemberTypeEnum.values().forEach { type ->
            assertNotNull(memberTypes.firstOrNull { it.key == type.key })
        }
    }
}
