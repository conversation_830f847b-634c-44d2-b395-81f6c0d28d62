package realestate.unlock.dealroom.api.repository.database

import com.fasterxml.jackson.core.type.TypeReference
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.SchemaData
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.repository.database.deal.DealDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class JsonBQueryExtensionTest : BaseFunctionalWithoutRestTest() {

    private enum class TestFields {
        ID,
        DATA
    }

    @Test
    fun `can build one level condition`() {
        Assertions.assertEquals(
            "(DATA->>'one') = ?",
            TestFields.DATA.jsonbField(
                listOf("one"),
                "="
            )
        )
    }

    @Test
    fun `can build more than two level condition`() {
        Assertions.assertEquals(
            "(DATA->'un'->'dos'->'tres'->>'catorce') = ?",
            TestFields.DATA.jsonbField(
                listOf("un", "dos", "tres", "catorce"),
                "="
            )
        )
    }

    @Test
    fun `can query with extension`() {
        AuthMock.createMemberWithUser()
        val deal = DealCreator.createDealByRest()
        Context.injector.getInstance(DealRepository::class.java).updateDealSchemaData(
            deal.id,
            SchemaData(
                schemaId = 123L,
                data = mapOf<String, Any>("uno" to 1, "dos" to "dos", "tres" to LocalDate.now().toString())
            )
        )

        val sqlClient = Context.injector.getInstance(SqlClient::class.java)

        val dealDb = sqlClient.getOneOrFail(
            """SELECT * FROM deal 
                WHERE ${DealDatabaseRepository.Fields.DEAL_SCHEMA_DATA.jsonbField(
                listOf("uno"),
                "=",
                cast = "::integer"
            )}
            """.trimIndent(),
            clazz = Map::class.java,
            params = listOf(1),
            messageKey = "TESTING"
        )

        Assertions.assertEquals(deal.id.toInt(), dealDb["id"])
        Assertions.assertEquals(decode(dealDb["deal_schema_data"])["uno"], 1)

        val dealDb2 = sqlClient.getOneOrFail(
            """SELECT * FROM deal 
                WHERE ${DealDatabaseRepository.Fields.DEAL_SCHEMA_DATA.jsonbField(
                listOf("dos"),
                "="
            )}
            """.trimIndent(),
            clazz = Map::class.java,
            params = listOf("dos"),
            messageKey = "TESTING"
        )

        Assertions.assertEquals(deal.id.toInt(), dealDb2["id"])
        Assertions.assertEquals(decode(dealDb["deal_schema_data"])["dos"], "dos")
    }

    fun decode(value: Any?): Map<String, Any> {
        return JsonMapper.decode((value as Map<*, *>)["value"] as String, object : TypeReference<Map<String, Any>>() {})
    }
}
