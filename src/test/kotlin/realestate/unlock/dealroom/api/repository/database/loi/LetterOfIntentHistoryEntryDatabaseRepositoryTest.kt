package realestate.unlock.dealroom.api.repository.database.loi

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntry
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntryMember
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentHistoryEntryType
import realestate.unlock.dealroom.api.core.entity.loi.MedicalLoiRound
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.loi.models.LetterOfIntentHistoryEntryDbModel
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.SaveMedicalLetterOfIntentRoundBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit

class LetterOfIntentHistoryEntryDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private val buyerToken = "buyer-token"
    private val sellerToken = "seller-token"

    private lateinit var sqlClient: SqlClient
    private lateinit var seller: Member
    private lateinit var buyer: Member
    private lateinit var letterOfIntentRoundDatabaseRepository: MedicalLetterOfIntentRoundDatabaseRepository
    private lateinit var letterOfIntentHistoryEntryDatabaseRepository: LetterOfIntentHistoryEntryDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = buyerToken, email = "<EMAIL>", uid = anyString())
        seller = AuthMock.createMemberWithUser(memberType = "seller", token = sellerToken, email = "<EMAIL>", uid = anyString())
        sqlClient = Context.injector.getInstance(SqlClient::class.java)
        letterOfIntentRoundDatabaseRepository = Context.injector.getInstance(MedicalLetterOfIntentRoundDatabaseRepository::class.java)
        letterOfIntentHistoryEntryDatabaseRepository = Context.injector.getInstance(LetterOfIntentHistoryEntryDatabaseRepository::class.java)
    }

    @Test
    fun `can save a loi history`() {
        // given
        val deal = DealCreator.createDealByRest()
        val loi = givenLoi(deal)
        val historyRecord = givenHistoryEntry(deal, loi)

        // when
        letterOfIntentHistoryEntryDatabaseRepository.save(historyRecord)

        // then
        val entries = getSavedHistoryEntries()

        assertThat(entries.size, IsEqual(1))
        val entry = entries.first()
        assertThat(entry.dealId, IsEqual(historyRecord.dealId))
        assertThat(entry.loiId, IsEqual(historyRecord.loiId))
        assertThat(entry.description, IsEqual(historyRecord.description))
        assertThat(entry.createdAt, IsEqual(historyRecord.createdAt))
        assertThat(entry.kFile!!.uid, IsEqual(historyRecord.kFile!!.uid))
        assertThat(entry.kFile!!.name, IsEqual(historyRecord.kFile!!.name))
        assertThat(entry.member!!.name, IsEqual(historyRecord.member!!.name))
        assertThat(entry.member!!.team, IsEqual(historyRecord.member!!.team))
    }

    @Test
    fun `can retrieve history sorted by creation desc`() {
        // given
        val deal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val loi = givenLoi(deal)

        val deal2 = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val loi2 = givenLoi(deal2)

        val historyRecord = givenHistoryEntry(deal, loi)
        letterOfIntentHistoryEntryDatabaseRepository.save(historyRecord)
        letterOfIntentHistoryEntryDatabaseRepository.save(historyRecord.copy(createdAt = historyRecord.createdAt.plusDays(10)))
        letterOfIntentHistoryEntryDatabaseRepository.save(historyRecord.copy(createdAt = historyRecord.createdAt.plusDays(1)))
        letterOfIntentHistoryEntryDatabaseRepository.save(givenHistoryEntry(deal2, loi2))

        val entries = getSavedHistoryEntries()
        val expectedHistory = entries
            .filter { it.dealId == deal.id }
            .sortedByDescending { it.createdAt }

        // when
        val result = letterOfIntentHistoryEntryDatabaseRepository.findByDealId(deal.id)

        // then
        assertThat(result, IsEqual(expectedHistory))
    }

    private fun givenLoi(deal: CompleteDeal): MedicalLoiRound {
        val loiOffer = SaveMedicalLetterOfIntentRoundBuilder().apply {
            memberId = deal.members.first().id
            dealId = deal.id
        }.build()
        return letterOfIntentRoundDatabaseRepository.save(loiOffer)
    }

    private fun givenHistoryEntry(
        deal: CompleteDeal,
        loi: MedicalLoiRound
    ) = LetterOfIntentHistoryEntry(
        id = 0,
        dealId = deal.id,
        loiId = loi.id,
        type = LetterOfIntentHistoryEntryType.OFFER,
        description = "description",
        createdAt = OffsetDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.SECONDS),
        kFile = KFile(anyString(), anyString()),
        member = LetterOfIntentHistoryEntryMember("Someone", "buyer")
    )

    private fun getSavedHistoryEntries(): List<LetterOfIntentHistoryEntry> {
        return sqlClient
            .getAll("SELECT id, deal_id, loi_id, type, description, created_at, file_id, file_name, member_name, member_team FROM loi_history", clazz = LetterOfIntentHistoryEntryDbModel::class.java)
            .map { dbModel ->
                LetterOfIntentHistoryEntry(
                    id = dbModel.id,
                    type = dbModel.type,
                    dealId = dbModel.dealId,
                    loiId = dbModel.loiId,
                    description = dbModel.description,
                    createdAt = dbModel.createdAt,
                    kFile = dbModel.takeIf { it.fileId != null && it.fileName != null }
                        ?.let {
                            KFile(
                                uid = it.fileId!!,
                                name = it.fileName!!
                            )
                        },
                    member = dbModel.takeIf { it.memberName != null && it.memberTeam != null }
                        ?.let {
                            LetterOfIntentHistoryEntryMember(
                                name = it.memberName!!,
                                team = it.memberTeam!!
                            )
                        }
                )
            }
    }
}
