package realestate.unlock.dealroom.api.repository.database.deal.category

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class DealCategoryDealDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var dealCategoryDatabaseRepository: DealCategoryDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        dealCategoryDatabaseRepository = Context.injector.getInstance(DealCategoryDatabaseRepository::class.java)
    }

    @Test
    fun `It can find the categories by deal and stage`() {
        // given
        val deal = DealCreator.createDealByRest()

        // when
        val expectedStage = Stage.DILIGENCE
        val categories = dealCategoryDatabaseRepository.findByDealIdAndStages(dealId = deal.id, stages = listOf(expectedStage))

        // then
        assertThat(categories, hasSize(12))
        assertTrue(categories.any { it.categoryKey == "purchase_and_sale_contract" })
        assertTrue(categories.any { it.categoryKey == "lease" })
        assertTrue(categories.any { it.categoryKey == "financial" })
        assertTrue(categories.any { it.categoryKey == "physical_and_environmental" })
        assertTrue(categories.any { it.categoryKey == "building_documents" })
        assertTrue(categories.any { it.categoryKey == "insurance" })
        assertTrue(categories.any { it.categoryKey == "property_taxes" })
        assertTrue(categories.any { it.categoryKey == "permits_contracts_and_warranties" })
        assertTrue(categories.any { it.categoryKey == "title" })
        assertTrue(categories.any { it.categoryKey == "survey" })
        assertTrue(categories.any { it.categoryKey == "lease_documents" })
        assertTrue(categories.any { it.categoryKey == "nnn_icm2" })
    }

    @Test
    fun `It retrieves the categories sorted by id`() {
        // given
        val deal = DealCreator.createDealByRest()

        // when
        val dueDiligence = Stage.DILIGENCE
        val closing = Stage.CLOSING
        val categories = dealCategoryDatabaseRepository.findByDealIdAndStages(dealId = deal.id, stages = listOf(dueDiligence, closing))

        // then
        val categoriesSortedById = categories.sortedBy { it.id }
        assertThat(categories, equalTo(categoriesSortedById))
    }
}
