package realestate.unlock.dealroom.api.repository.database.deal.report

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.core.entity.deal.reports.Report
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportStatus
import realestate.unlock.dealroom.api.core.exception.ResourceNotFoundException
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.repository.database.deal.reports.DealReportDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal
import java.time.LocalDate

class DealReportDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var dealReportDatabaseRepository: DealReportDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        dealReportDatabaseRepository = Context.injector.getInstance(DealReportDatabaseRepository::class.java)
    }

    @Test
    fun `can find by id`() {
        val deal = DealCreator.createDealByRest()
        val asbestosReport = givenReport(deal.id, "ASBESTOS_SURVEY")

        val reportFound = dealReportDatabaseRepository.findById(asbestosReport.id)

        assertThat(reportFound, equalTo(asbestosReport))
    }

    @Test
    fun `it should throw an exception if report is not found`() {
        assertThrows<ResourceNotFoundException> {
            dealReportDatabaseRepository.findById(123L)
        }
    }

    private fun givenReport(
        dealId: Long,
        type: String,
        expectedDate: LocalDate? = LocalDate.now(),
        status: ReportStatus = ReportStatus.ORDERED
    ) = Report(
        id = anyId(),
        dealId = dealId,
        type = type,
        reportName = null,
        vendorKey = "EBI",
        vendorName = null,
        status = status,
        expectedDate = expectedDate,
        costEstimate = BigDecimal.TEN,
        cost = BigDecimal.valueOf(15),
        findings = null,
        tags = listOf(),
        documents = listOf()
    ).also {
        dealReportDatabaseRepository.save(it)
    }
}
