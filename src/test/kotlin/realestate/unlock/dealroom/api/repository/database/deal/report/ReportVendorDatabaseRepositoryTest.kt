package realestate.unlock.dealroom.api.repository.database.deal.report

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasSize
import org.hamcrest.Matchers.notNullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.repository.database.deal.reports.ReportVendorDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest

class ReportVendorDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var reportVendorDatabaseRepository: ReportVendorDatabaseRepository

    @BeforeEach
    fun setUp() {
        reportVendorDatabaseRepository = Context.injector.getInstance(ReportVendorDatabaseRepository::class.java)
    }

    @Test
    fun `it retrieves the expected vendors for SURVEY`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("SURVEY")
        assertThat(vendors, hasSize(3))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for ZONING`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("ZONING")
        assertThat(vendors, hasSize(4))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "PARTNERS" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for TITLE`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("TITLE")
        assertThat(vendors, hasSize(4))
        assertThat(vendors.find { it.key == "FIRST_AMERICAN" }, notNullValue())
        assertThat(vendors.find { it.key == "CHICAGO_TITLE" }, notNullValue())
        assertThat(vendors.find { it.key == "PROVIDENCE_TITLE" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for PHASE_I`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("PHASE_I")
        assertThat(vendors, hasSize(4))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "PARTNERS" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for PHASE_II`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("PHASE_II")
        assertThat(vendors, hasSize(4))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "PARTNERS" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for APPRAISAL`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("APPRAISAL")
        assertThat(vendors, hasSize(3))
        assertThat(vendors.find { it.key == "BOWERY_VALUATION" }, notNullValue())
        assertThat(vendors.find { it.key == "INTEGRA_REALTY_RESOURCES" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for PCR`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("PCR")
        assertThat(vendors, hasSize(4))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "PARTNERS" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for SEISMIC REPORT`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("SEISMIC_REPORT")
        assertThat(vendors, hasSize(3))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for SOIL AND GEOLOGICAL REPORT`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("SOIL_AND_GEOLOGIC_REPORT")
        assertThat(vendors, hasSize(4))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "PARTNERS" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for MOLD SURVEY`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("MOLD_SURVEY")
        assertThat(vendors, hasSize(3))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for HAZARDOUS MATERIALS`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("HAZARDOUS_MATERIALS_RECORDS")
        assertThat(vendors, hasSize(3))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }

    @Test
    fun `it retrieves the expected vendors for ASBESTOS SURVEY`() {
        val vendors = reportVendorDatabaseRepository.findByReportType("ASBESTOS_SURVEY")
        assertThat(vendors, hasSize(3))
        assertThat(vendors.find { it.key == "CRETELLIGENT" }, notNullValue())
        assertThat(vendors.find { it.key == "EBI" }, notNullValue())
        assertThat(vendors.find { it.key == "OTHER" }, notNullValue())
    }
}
