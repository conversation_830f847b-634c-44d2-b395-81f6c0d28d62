package realestate.unlock.dealroom.api.repository.database.deal

import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.dashboard.inputs.DealSummaryInput
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.deal.DealsSummaryRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.UpdateDates
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.LetterOfIntentFileRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.creator.FileCreator
import realestate.unlock.dealroom.api.utils.creator.LoiCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class ActiveDealsSummaryRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var dealsSummaryRepository: DealsSummaryRepository

    private var dealRepository: DealRepository = Context.injector.getInstance(DealRepository::class.java)
    private lateinit var member: Member

    @BeforeEach
    fun setUp() {
        member = AuthMock.createMemberWithUser()
        dealsSummaryRepository = Context.injector.getInstance(DealsSummaryRepository::class.java)
    }

    @Test
    fun `can get the deals summary`() {
        givenNewDeal()
        givenDealWithLoiSummited()
        givenDealInDiligence()
        givenDealInClosing()
        givenDealInNegotiation()
        val otherBuyer = givenMember("buyer")
        givenDealInNegotiation(buyer = otherBuyer)

        val summary = dealsSummaryRepository.countDealsByStatus(DealSummaryInput(null, null, member.organizationId))

        assertEquals(2, summary.activeDealsFor(Stage.NEGOTIATION).activeDeals)
        assertEquals(1, summary.activeDealsFor(Stage.OFFER).activeDeals)
        assertEquals(1, summary.activeDealsFor(Stage.CLOSING).activeDeals)
        assertEquals(1, summary.activeDealsFor(Stage.DILIGENCE).activeDeals)
    }

    @Test
    fun `can get the deals summary by member id`() {
        givenNewDeal()
        givenDealWithLoiSummited()
        givenDealInDiligence()
        givenDealInClosing()
        givenDealInNegotiation()
        val otherBuyer = givenMember("buyer")
        givenDealInNegotiation(buyer = otherBuyer)

        val summary = dealsSummaryRepository.countDealsByStatus(DealSummaryInput(otherBuyer.id, null, member.organizationId))

        assertEquals(1, summary.activeDealsFor(Stage.NEGOTIATION).activeDeals)
        assertEquals(0, summary.activeDealsFor(Stage.OFFER).activeDeals)
        assertEquals(0, summary.activeDealsFor(Stage.CLOSING).activeDeals)
        assertEquals(0, summary.activeDealsFor(Stage.DILIGENCE).activeDeals)
    }

    @Test
    fun `can get the deals summary by property type`() {
        givenNewDeal()
        givenDealWithLoiSummited()
        givenDealInDiligence()
        givenDealInClosing()
        givenDealInNegotiation(propertyType = PropertyType.MEDICAL)
        givenDealInNegotiation(propertyType = PropertyType.MULTIFAMILY)

        val summary = dealsSummaryRepository.countDealsByStatus(DealSummaryInput(null, PropertyType.MULTIFAMILY, member.organizationId))

        assertEquals(1, summary.activeDealsFor(Stage.NEGOTIATION).activeDeals)
        assertEquals(0, summary.activeDealsFor(Stage.OFFER).activeDeals)
        assertEquals(0, summary.activeDealsFor(Stage.CLOSING).activeDeals)
        assertEquals(0, summary.activeDealsFor(Stage.DILIGENCE).activeDeals)
    }

    private fun givenDealWithLoiSummited() {
        val dealWithLoiSummited = givenNewDeal()
        val givenFile = FileCreator.create()
        val uploadFileResponse = Unirest.post("$localUrl/deal/${dealWithLoiSummited.id}/loi/file")
            .headers(AuthMock.getAuthHeader())
            .field("file", givenFile)
            .asString()
            .let { JsonMapper.decode(it.body, KFile::class.java) }
        LoiCreator.createMedicalLoiByRest(
            dealWithLoiSummited.id,
            offerFiles = listOf(LetterOfIntentFileRequest(uploadFileResponse.uid, uploadFileResponse.name))
        )
    }

    private fun givenDealInClosing() {
        val deal = givenNewDeal(loiExecutedDate = LocalDate.now().minusDays(10), initialClosingDate = LocalDate.now().minusDays(1))
        dealRepository.findById(dealId = deal.id).let {
            dealRepository.update(it.copy(stage = Stage.CLOSING))
        }
    }

    private fun givenDealInDiligence() {
        val deal = givenNewDeal(loiExecutedDate = LocalDate.now().minusDays(10))
        dealRepository.findById(dealId = deal.id).let {
            dealRepository.update(it.copy(stage = Stage.DILIGENCE))
        }
    }

    private fun givenDealInNegotiation(buyer: Member = givenMember("buyer"), propertyType: PropertyType = PropertyType.MEDICAL) {
        val deal = givenNewDeal(loiExecutedDate = LocalDate.now().minusDays(10), buyer = buyer, propertyType = propertyType)
        dealRepository.findById(dealId = deal.id).let {
            dealRepository.update(it.copy(stage = Stage.NEGOTIATION))
        }
    }

    private fun givenNewDeal(
        loiExecutedDate: LocalDate? = null,
        initialClosingDate: LocalDate? = null,
        buyer: Member = givenMember("buyer"),
        propertyType: PropertyType = PropertyType.MEDICAL
    ) =
        DealCreator.createDealByRest(
            seller = givenMember("seller"),
            buyer = buyer,
            updateDates = UpdateDates(
                loiExecutedDate = loiExecutedDate,
                initialClosingDate = initialClosingDate
            ),
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { this.propertyType = propertyType }
        )

    private fun givenMember(memberType: String) =
        AuthMock.createMemberWithUser(memberType = memberType, token = anyString(), email = "${anyString()}@test.com", uid = anyString())
}
