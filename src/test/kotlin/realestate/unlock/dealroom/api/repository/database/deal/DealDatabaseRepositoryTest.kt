package realestate.unlock.dealroom.api.repository.database.deal

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.*
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.deal.DealType
import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.RentStepType
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedInputByOrganization
import realestate.unlock.dealroom.api.core.entity.search.SortOrder
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal
import java.time.LocalDate

class DealDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var propertyRepository: PropertyRepository
    private lateinit var dealRepository: DealRepository

    private lateinit var buyer: Member
    private lateinit var seller: Member
    private lateinit var member: Member

    @BeforeEach
    fun setUp() {
        member = AuthMock.createMemberWithUser(email = "<EMAIL>")
        propertyRepository = Context.injector.getInstance(PropertyRepository::class.java)
        dealRepository = Context.injector.getInstance(DealRepository::class.java)
        buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
    }

    @Test
    fun `can update a deal`() {
        // given
        val deal = DealCreator.createDealByRest()
        val dealModified = dealRepository.findById(deal.id).let {
            it.copy(
                stage = Stage.NEGOTIATION,
                type = DealType.ACQUISITION,
                vertical = "Other",
                guaranteeType = GuaranteeType.NA,
                lease = it.lease.copy(
                    rent = BigDecimal.TEN,
                    type = "TYPE",
                    rentIncrease = Float.MIN_VALUE,
                    increaseEveryYear = Float.MIN_VALUE,
                    length = 1,
                    expirationYear = 1,
                    numberOfOptions = 1,
                    optionLengths = 1,
                    rentCpi = Float.MIN_VALUE,
                    rentStepType = RentStepType.CPI
                ),
                evaluationDueDate = LocalDate.now().minusDays(10),
                underwritingDueDate = LocalDate.now().minusDays(5),
                omFileId = anyString(),
                sourceType = SourceType.OFF_MARKET,
                buyerCompanyName = "new buyer company name",
                brokerCompanyName = "new broker company name",
                sellerCompanyName = "new seller company name"
            )
        }

        // when
        val result = dealRepository.update(dealModified)

        // Then
        assertThat(result.id, IsEqual(deal.id))
        assertThat(result.stage, IsEqual(dealModified.stage))
        assertThat(result.loiExecutedDate, IsEqual(dealModified.loiExecutedDate))
        assertThat(result.contractExecutedDate, IsEqual(dealModified.contractExecutedDate))
        assertThat(result.diligenceExpirationDate, IsEqual(dealModified.diligenceExpirationDate))
        assertThat(result.initialClosingDate, IsEqual(dealModified.initialClosingDate))
        assertThat(result.outsideClosingDate, IsEqual(dealModified.outsideClosingDate))

        assertEquals(result.type, dealModified.type)
        assertEquals(result.vertical, dealModified.vertical)
        assertEquals(result.lease.rent, dealModified.lease.rent)
        assertEquals(result.lease.type, dealModified.lease.type)
        assertEquals(result.lease.rentIncrease, dealModified.lease.rentIncrease)
        assertEquals(result.lease.increaseEveryYear, dealModified.lease.increaseEveryYear)
        assertEquals(result.lease.length, dealModified.lease.length)
        assertEquals(result.lease.expirationYear, dealModified.lease.expirationYear)
        assertEquals(result.lease.numberOfOptions, dealModified.lease.numberOfOptions)
        assertEquals(result.lease.optionLengths, dealModified.lease.optionLengths)
        assertEquals(result.lease.rentCpi, dealModified.lease.rentCpi)
        assertEquals(result.lease.rentStepType, dealModified.lease.rentStepType)
        assertEquals(result.guaranteeType, dealModified.guaranteeType)
        assertEquals(result.evaluationDueDate, dealModified.evaluationDueDate)
        assertEquals(result.underwritingDueDate, dealModified.underwritingDueDate)
        assertEquals(result.omFileId, dealModified.omFileId)
        assertEquals(result.sourceType, dealModified.sourceType)
        assertEquals(result.buyerCompanyName, dealModified.buyerCompanyName)
        assertEquals(result.brokerCompanyName, dealModified.brokerCompanyName)
        assertEquals(result.sellerCompanyName, dealModified.sellerCompanyName)
    }

    @Test
    fun `can find deal by propertyId`() {
        val deal = DealCreator.createDealByRest()

        val dealFound = dealRepository.findByPropertyId(deal.propertyId)

        assertThat(deal.id, equalTo(dealFound!!.id))
    }

    @Test
    fun `can update a deal schema and custom fields data`() {
        // given
        val deal = DealCreator.createDealByRest()

        val schemaId = 123L
        val data = mapOf<String, Any>("uno" to 1, "dos" to "dos", "tres" to LocalDate.now().toString())

        // when
        val result = dealRepository.updateDealSchemaData(
            deal.id,
            SchemaData(
                schemaId = schemaId,
                data = data
            )
        )

        assertEquals(result.id, result.id)
        assertEquals(result.schemaData.schemaId, schemaId)
        data.forEach {
            assertEquals(it.value, result.schemaData.data?.get(it.key))
        }
    }

    @Test
    fun `Can return only non archived deals ordered by created date descending`() {
        // given
        val firstDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val secondDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val thirdDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)

        val archivedDeal = dealRepository.findById(secondDeal.id).copy(status = DealStatus.DELETED)
        dealRepository.update(archivedDeal)

        // when
        val result = dealRepository.findAll(
            input = PaginatedInputByOrganization(10, 10, 0, "created_at", SortOrder.DESC, mapOf(), member.organizationId)
        )

        // then
        assertThat(result.data, hasSize(2))
        assertThat(result.data[0].id, equalTo(thirdDeal.id))
        assertThat(result.data[1].id, equalTo(firstDeal.id))
        assertTrue(result.data.none { it.id == secondDeal.id })
    }

    @Test
    fun `does not retrieve deals of other organizations`() {
        val firstDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)

        val memberOfAnotherOrganization = AuthMock.createMemberWithUser(email = "<EMAIL>", uid = anyString(), organizationId = "another-org-id")
        val secondDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)

        val result = dealRepository.findAll(
            input = PaginatedInputByOrganization(10, 10, 0, "created_at", SortOrder.DESC, mapOf(), memberOfAnotherOrganization.organizationId)
        )

        assertThat(result.data, hasSize(1))
        assertThat(result.data[0].id, equalTo(secondDeal.id))
        assertTrue(result.data.none { it.id == firstDeal.id })
    }

    @Test
    fun `Can return non archived deals by user id ordered by created date descending`() {
        // given
        val anotherSeller = AuthMock.createMemberWithUser(memberType = "seller", token = "another-seller-token", email = "<EMAIL>", uid = anyString())

        val firstDeal = DealCreator.createDealByRest(seller = anotherSeller, buyer = buyer)
        val secondDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val thirdDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val fourthDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)

        val archivedDeal = dealRepository.findById(secondDeal.id).copy(status = DealStatus.DELETED)
        dealRepository.update(archivedDeal)

        // when
        val result = dealRepository.findAllNotArchivedByMemberId(seller.id)

        // then
        assertThat(result, hasSize(2))
        assertThat(result[0].id, equalTo(fourthDeal.id))
        assertThat(result[1].id, equalTo(thirdDeal.id))
        assertTrue(result.none { it.id == firstDeal.id }) // the first deal is from another user
        assertTrue(result.none { it.id == secondDeal.id }) // the second deal is from the same user but is archived
    }

    @Test
    fun `can find a deal by keyway id`() {
        // given
        val dealId = DealCreator.createDealByRest().id
        val deal = dealRepository.findById(dealId)
        val keywayId = propertyRepository.findById(deal.propertyId).keywayId!!

        // when
        val dealFound = dealRepository.findByKeywayId(keywayId, member.organizationId)

        // then
        assertThat(dealFound, IsEqual(deal))
    }

    @Test
    fun `can find all deals ordered by created date descending`() {
        // given
        val deal1 = DealCreator.createDealByRest(buyer = buyer, seller = seller)
        val deal2 = DealCreator.createDealByRest(buyer = buyer, seller = seller)

        // when
        val deals = dealRepository.findAll(
            input = PaginatedInputByOrganization(10, 10, 0, "created_at", SortOrder.DESC, mapOf(), member.organizationId)
        )

        // then
        assertThat(deals.data, hasSize(2))
        assertThat(deals.data[0].id, equalTo(deal2.id))
        assertThat(deals.data[1].id, equalTo(deal1.id))
    }

    @Test
    fun `Can find not archived deals by member id ordered by created date descending`() {
        // given
        val firstDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val secondDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val thirdDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)

        dealRepository.findById(secondDeal.id).also {
            dealRepository.update(it.copy(status = DealStatus.DELETED))
        }

        // when
        val deals = dealRepository.findAllNotArchivedByMemberId(seller.id)

        assertThat(deals, hasSize(2))
        assertThat(deals[0].id, equalTo(thirdDeal.id))
        assertThat(deals[1].id, equalTo(firstDeal.id))
    }

    @Test
    fun `can find closing expired deals`() {
        val deals = mutableMapOf<Int, CompleteDeal>()
        for (i in 1..4) {
            deals[i] = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        }

        for (i in 1..2) {
            deals[i]?.let { deal ->
                dealRepository.findById(deal.id).let {
                    dealRepository.update(
                        it.copy(
                            stage = Stage.DILIGENCE,
                            initialClosingDate = LocalDate.now().plusDays(-1)
                        )
                    )
                }
            }
        }

        dealRepository.findById(deals[3]!!.id).let {
            dealRepository.update(
                it.copy(
                    stage = Stage.DILIGENCE,
                    initialClosingDate = LocalDate.now().plusDays(1)
                )
            )
        }

        dealRepository.findById(deals[4]!!.id).let {
            dealRepository.update(
                it.copy(
                    stage = Stage.CLOSING,
                    initialClosingDate = LocalDate.now().plusDays(-100)
                )
            )
        }

        // when
        val searchResponse = dealRepository.findByExpiredClosingDate(LocalDate.now())

        assertThat(searchResponse, hasSize(2))
        for (i in 1..2) {
            assertTrue(searchResponse.any { deal -> deal.id == deals[i]?.id })
        }
        for (i in 3..4) {
            assertTrue(searchResponse.none { deal -> deal.id == deals[i]?.id })
        }
    }
}
