package realestate.unlock.dealroom.api.repository.database.document

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.DocumentRound
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentRoundRepository
import realestate.unlock.dealroom.api.core.repository.file.FileRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.OffsetDateTime

class DocumentInteractionDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var psaInteractionDatabaseRepository: DocumentInteractionDatabaseRepository

    private lateinit var documentRepository: DocumentRepository

    private lateinit var documentRoundRepository: DocumentRoundRepository

    private lateinit var fileRepository: FileRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        documentRoundRepository = Context.injector.getInstance(DocumentRoundRepository::class.java)
        documentRepository = Context.injector.getInstance(DocumentRepository::class.java)
        psaInteractionDatabaseRepository = Context.injector.getInstance(DocumentInteractionDatabaseRepository::class.java)
        fileRepository = Context.injector.getInstance(FileRepository::class.java)
    }

    @Test
    fun `can save and retrieve a document interaction`() {
        val document = givenPSARound()

        val interaction = Interaction(
            id = psaInteractionDatabaseRepository.nextId(),
            roundId = document.id,
            type = Interaction.Type.NEW_DRAFT,
            comment = anyString(),
            files = listOf(givenFile()),
            date = OffsetDateTime.now(),
            memberId = anyId()
        )

        psaInteractionDatabaseRepository.save(interaction)

        val savedInteraction = psaInteractionDatabaseRepository.findById(interaction.id)

        assertInteraction(interaction, savedInteraction)
    }

    @Test
    fun `can find interactions by psa`() {
        val psaRound = givenPSARound()
        val interactionDraft = givenInteraction(documentId = psaRound.id, type = Interaction.Type.NEW_DRAFT)
        val interactionComment = givenInteraction(documentId = psaRound.id, type = Interaction.Type.COMMENT)

        val interactions = psaInteractionDatabaseRepository.findByRoundIdAndDocumentId(roundId = psaRound.id, documentId = psaRound.documentId)

        assertEquals(2, interactions.size)
        assertInteraction(interactionDraft, interactions[0])
        assertInteraction(interactionComment, interactions[1])
    }

    @Test
    fun `can find interactions by psa and file`() {
        val psaRound = givenPSARound()
        val file = givenFile()
        givenInteraction(documentId = psaRound.id, type = Interaction.Type.COMMENT)
        val interactionRequestChange = givenInteraction(documentId = psaRound.id, type = Interaction.Type.REQUEST_CHANGES, files = listOf(file))

        val interactions = psaInteractionDatabaseRepository.findByDocumentIdAndFileId(documentId = psaRound.documentId, fileId = file.id)

        assertEquals(1, interactions.size)
        assertInteraction(interactionRequestChange, interactions[0])
    }

    private fun assertInteraction(
        interaction: Interaction,
        savedInteraction: Interaction
    ) {
        assertEquals(interaction.id, savedInteraction.id)
        assertEquals(interaction.roundId, savedInteraction.roundId)
        assertEquals(interaction.type, savedInteraction.type)
        assertEquals(interaction.comment, savedInteraction.comment)
        assertEquals(interaction.date.toEpochSecond(), savedInteraction.date.toEpochSecond())
        assertEquals(interaction.memberId, savedInteraction.memberId)
    }

    private fun givenInteraction(documentId: Long, type: Interaction.Type, files: List<File> = listOf()) = Interaction(
        id = psaInteractionDatabaseRepository.nextId(),
        roundId = documentId,
        type = type,
        comment = anyString(),
        files = files,
        date = OffsetDateTime.now(),
        memberId = anyId()
    ).apply { psaInteractionDatabaseRepository.save(this) }

    private fun givenPSARound(): DocumentRound {
        val deal = DealCreator.createDealByRest()
        val psa = DocumentBuilder().apply {
            id = documentRepository.nextId()
            dealId = deal.id
        }.build().apply {
            documentRepository.save(this)
        }
        return DocumentRoundObjectMother.draftDocuments(
            id = documentRoundRepository.nextId(),
            documentId = psa.id
        ).apply {
            documentRoundRepository.save(this)
        }
    }

    private fun givenFile(): File {
        return File(
            id = fileRepository.nextId(),
            name = anyString(),
            kFileId = anyString(),
            memberId = anyId(),
            createdAt = OffsetDateTime.now()
        ).apply { fileRepository.save(this) }
    }
}
