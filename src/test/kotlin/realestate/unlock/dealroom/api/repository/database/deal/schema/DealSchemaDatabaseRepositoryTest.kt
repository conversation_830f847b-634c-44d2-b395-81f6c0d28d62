package realestate.unlock.dealroom.api.repository.database.deal.schema

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.*
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.repository.deal.schema.DealSchemaRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest

class DealSchemaDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var dealSchemaRepository: DealSchemaRepository

    @BeforeEach
    fun setUp() {
        dealSchemaRepository = Context.injector.getInstance(DealSchemaDatabaseRepository::class.java)
    }

    @Test
    fun `should find last by property type MEDICAL`() {
        // When
        val dealSchema = dealSchemaRepository.findLastByPropertyType(PropertyType.MEDICAL)

        // Then
        assertThat(dealSchema.propertyType, equalTo(PropertyType.MEDICAL))
    }

    @Test
    fun `should find last by property type MULTIFAMILY`() {
        // When
        val dealSchema = dealSchemaRepository.findLastByPropertyType(PropertyType.MULTIFAMILY)

        // Then
        assertThat(dealSchema.propertyType, equalTo(PropertyType.MULTIFAMILY))
    }
}
