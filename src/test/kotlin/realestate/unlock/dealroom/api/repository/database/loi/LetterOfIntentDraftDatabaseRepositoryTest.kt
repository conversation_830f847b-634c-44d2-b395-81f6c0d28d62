package realestate.unlock.dealroom.api.repository.database.loi

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.RentStepType
import realestate.unlock.dealroom.api.core.entity.loi.ClosingCost
import realestate.unlock.dealroom.api.core.entity.loi.draft.LetterOfIntentDraft
import realestate.unlock.dealroom.api.core.entity.loi.draft.LetterOfIntentDraftInput
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentDraftRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal
import java.time.LocalDate

class LetterOfIntentDraftDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var letterOfIntentDrafRepository: LetterOfIntentDraftRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        letterOfIntentDrafRepository = Context.injector.getInstance(LetterOfIntentDraftRepository::class.java)
    }

    @Test
    fun `Can save a draft loi`() {
        val deal = DealCreator.createDealByRest()
        val givenInput = givenInput(deal.id)

        val result = letterOfIntentDrafRepository.save(givenInput)

        compareDraft(draft = result, input = givenInput)
    }

    @Test
    fun `Saving an already saved loi updates its values`() {
        val deal = DealCreator.createDealByRest()
        val givenInput = givenInput(deal.id)
        letterOfIntentDrafRepository.save(givenInput)
        val newTenantName = "new tenant name"
        val newLeaseType = "super lease type"
        val newClosingPeriod = 1123L
        val updateInput = givenInput.copy(tenantName = newTenantName, offerLeaseType = newLeaseType, offerClosingPeriod = newClosingPeriod)

        val result = letterOfIntentDrafRepository.save(updateInput)

        assertThat(result.tenantName, equalTo(newTenantName))
        assertThat(result.offer.lease.type, equalTo(newLeaseType))
        assertThat(result.offer.closing.period, equalTo(newClosingPeriod))
    }

    @Test
    fun `Can get an loi draft by deal id`() {
        val deal = DealCreator.createDealByRest()
        val givenInput = givenInput(deal.id)

        letterOfIntentDrafRepository.save(givenInput)
        val result = letterOfIntentDrafRepository.getByDealId(deal.id)

        assertThat(result, notNullValue())
        compareDraft(draft = result!!, input = givenInput)
    }

    @Test
    fun `Can delete a loi draft`() {
        val deal = DealCreator.createDealByRest()
        val givenInput = givenInput(deal.id)
        letterOfIntentDrafRepository.save(givenInput)

        val draftBeforeDelete = letterOfIntentDrafRepository.getByDealId(deal.id)
        letterOfIntentDrafRepository.deleteByDealId(deal.id)
        val draftAfterDelete = letterOfIntentDrafRepository.getByDealId(deal.id)

        assertThat(draftBeforeDelete, notNullValue())
        assertThat(draftAfterDelete, nullValue())
    }

    private fun givenInput(dealId: Long): LetterOfIntentDraftInput = LetterOfIntentDraftInput(
        dealId = dealId,
        tenantName = "tenant name",
        propertySquareFootage = BigDecimal(12),
        vertical = "Medical",
        brokerName = "broker name",
        brokerCompanyName = "broker company name",
        offerPrice = BigDecimal(1235),
        offerLeaseRent = BigDecimal(444),
        offerLeaseType = "Lease type",
        offerLeaseCondition = "lease condition",
        offerLeaseRentIncrease = 12.3f,
        offerLeaseIncreaseEveryYear = 2.5f,
        offerLeaseLength = 5L,
        offerLeaseExpirationYear = 200L,
        offerLeaseNumberOfOptions = 12L,
        offerLeaseOptionLengths = 5,
        offerClosingPeriod = 10L,
        offerClosingPeriodExtension = 5L,
        offerClosingExtensionDeposit = BigDecimal(12),
        offerContractTermination = LocalDate.now(),
        offerEarnestMoneyDeposit = BigDecimal(23),
        offerDueDiligenceNumber = 15L,
        offerComments = "Amazing coments",
        offerClosingCost = ClosingCost.ORIGINAL,
        offerRentCpi = 8.5f,
        offerRentStepType = RentStepType.CPI,
        guaranteeType = GuaranteeType.CORPORATE
    )

    private fun compareDraft(draft: LetterOfIntentDraft, input: LetterOfIntentDraftInput) {
        assertThat(draft.dealId, equalTo(input.dealId))
        assertThat(draft.tenantName, equalTo(input.tenantName))
        assertThat(draft.propertySquareFootage, equalTo(input.propertySquareFootage))
        assertThat(draft.vertical, equalTo(input.vertical))
        assertThat(draft.broker.name, equalTo(input.brokerName))
        assertThat(draft.broker.companyName, equalTo(input.brokerCompanyName))
        assertThat(draft.guaranteeType, equalTo(input.guaranteeType))
        assertThat(draft.offer.salesPrice, equalTo(input.offerPrice))
        assertThat(draft.offer.lease.rent, equalTo(input.offerLeaseRent))
        assertThat(draft.offer.lease.type, equalTo(input.offerLeaseType))
        assertThat(draft.offer.lease.rentIncrease, equalTo(input.offerLeaseRentIncrease))
        assertThat(draft.offer.lease.increaseEveryYear, equalTo(input.offerLeaseIncreaseEveryYear))
        assertThat(draft.offer.lease.length, equalTo(input.offerLeaseLength))
        assertThat(draft.offer.lease.expirationYear, equalTo(input.offerLeaseExpirationYear))
        assertThat(draft.offer.lease.numberOfOptions, equalTo(input.offerLeaseNumberOfOptions))
        assertThat(draft.offer.lease.optionLengths, equalTo(input.offerLeaseOptionLengths))
        assertThat(draft.offer.lease.condition, equalTo(input.offerLeaseCondition))
        assertThat(draft.offer.lease.rentCpi, equalTo(input.offerRentCpi))
        assertThat(draft.offer.lease.rentStepType, equalTo(input.offerRentStepType))
        assertThat(draft.offer.closing.period, equalTo(input.offerClosingPeriod))
        assertThat(draft.offer.closing.extensionDeposit, equalTo(input.offerClosingExtensionDeposit))
        assertThat(draft.offer.closing.periodExtension, equalTo(input.offerClosingPeriodExtension))
        assertThat(draft.offer.contractTermination, equalTo(input.offerContractTermination))
        assertThat(draft.offer.earnestMoneyDeposit, equalTo(input.offerEarnestMoneyDeposit))
        assertThat(draft.offer.comments, equalTo(input.offerComments))
        assertThat(draft.offer.dueDiligence.number, equalTo(input.offerDueDiligenceNumber))
        assertThat(draft.offer.closingCost, equalTo(input.offerClosingCost))
    }
}
