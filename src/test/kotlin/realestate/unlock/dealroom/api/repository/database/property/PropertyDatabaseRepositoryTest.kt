package realestate.unlock.dealroom.api.repository.database.property

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.property.*
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyToSave
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal

class PropertyDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var propertyRepository: PropertyRepository
    private lateinit var dealRepository: DealRepository

    private lateinit var buyer: Member
    private lateinit var seller: Member

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        propertyRepository = Context.injector.getInstance(PropertyRepository::class.java)
        dealRepository = Context.injector.getInstance(DealRepository::class.java)
        buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
    }

    @Test
    fun `can save a medical property`() {
        val input = givenPropertyCreationInput(PropertyType.MEDICAL)

        val result = propertyRepository.save(input)

        assertThat(result.name, IsEqual(input.name))
        assertThat(result.address.street, IsEqual(input.street))
        assertThat(result.address.apartment, IsEqual(input.apartment))
        assertThat(result.address.city, IsEqual(input.city))
        assertThat(result.address.state, IsEqual(input.state))
        assertThat(result.address.zip, IsEqual(input.zip))
        assertThat(result.keywayId, IsEqual(input.keywayId))
        assertThat(result.yearBuilt, IsEqual(input.yearBuilt))
        assertThat(result.squareFootage, IsEqual(input.squareFootage))
        assertThat(result.askingPrice, IsEqual(input.askingPrice))
        assertThat(result.address.coordinates?.latitude, IsEqual(input.latitude))
        assertThat(result.address.coordinates?.longitude, IsEqual(input.longitude))
        assertNull(result.multifamilyData)
    }

    @Test
    fun `can save a multifamily property`() {
        val input = givenPropertyCreationInput(PropertyType.MULTIFAMILY)

        val result = propertyRepository.save(input)

        assertThat(result.name, IsEqual(input.name))
        assertThat(result.address.street, IsEqual(input.street))
        assertThat(result.address.apartment, IsEqual(input.apartment))
        assertThat(result.address.city, IsEqual(input.city))
        assertThat(result.address.state, IsEqual(input.state))
        assertThat(result.address.zip, IsEqual(input.zip))
        assertThat(result.keywayId, IsEqual(input.keywayId))
        assertThat(result.yearBuilt, IsEqual(input.yearBuilt))
        assertThat(result.squareFootage, IsEqual(input.squareFootage))
        assertThat(result.askingPrice, IsEqual(input.askingPrice))
        assertThat(result.address.coordinates?.latitude, IsEqual(input.latitude))
        assertThat(result.address.coordinates?.longitude, IsEqual(input.longitude))
        assertNotNull(result.multifamilyData)
        assertThat(result.multifamilyData?.propertyManager, IsEqual(input.multifamilyData?.propertyManager))
        assertThat(result.multifamilyData?.owner, IsEqual(input.multifamilyData?.owner))
        assertThat(result.multifamilyData?.occupancy, IsEqual(input.multifamilyData?.occupancy))
        assertThat(result.multifamilyData?.brokerFirm, IsEqual(input.multifamilyData?.brokerFirm))
        assertThat(result.multifamilyData?.units, IsEqual(input.multifamilyData?.units))
        assertThat(result.multifamilyData?.averageSquareFootage, IsEqual(input.multifamilyData?.averageSquareFootage))
        assertThat(result.multifamilyData?.parkingSpots, IsEqual(input.multifamilyData?.parkingSpots))
        assertThat(result.multifamilyData?.parkingRatio, IsEqual(input.multifamilyData?.parkingRatio))
        assertThat(result.multifamilyData?.unitsMix, IsEqual(input.multifamilyData?.unitsMix))
    }

    @Test
    fun `can update a property`() {
        // given
        val deal = DealCreator.createDealByRest()
        val property = propertyRepository.findById(deal.propertyId)
            .copy(
                keywayId = anyString(),
                address = Address(anyString(), anyString(), anyString(), anyString(), anyString(), null),
                yearBuilt = null,
                squareFootage = null,
                askingPrice = null
            )

        // when
        val result = propertyRepository.update(property)

        // Then
        assertThat(result, IsEqual(property))
    }

    @Test
    fun `can find a property by id`() {
        // given
        val input = givenPropertyCreationInput(PropertyType.MEDICAL)
        val property = propertyRepository.save(input)

        // when
        val result = propertyRepository.findById(property.id)

        // then
        assertThat(property, IsEqual(result))
    }

    @Test
    fun `can find a property by deal id`() {
        // given
        val deal = DealCreator.createDealByRest()
        val property = propertyRepository.findById(deal.propertyId)

        // when
        val result = propertyRepository.findByDealId(deal.id)

        // then
        assertThat(property, IsEqual(result))
    }

    @Test
    fun `can find all properties`() {
        // given
        propertyRepository.save(givenPropertyCreationInput(PropertyType.MEDICAL))
        propertyRepository.save(givenPropertyCreationInput(PropertyType.MULTIFAMILY))
        propertyRepository.save(givenPropertyCreationInput(PropertyType.MEDICAL))

        // when
        val result = propertyRepository.get()

        // then
        assertThat(result.size, IsEqual(3))
    }

    private fun givenPropertyCreationInput(propertyType: PropertyType) = PropertyToSave(
        name = anyString(),
        street = "street",
        apartment = "apartment",
        city = "city",
        state = "state",
        zip = "zip",
        keywayId = anyString(),
        yearBuilt = 1987,
        squareFootage = BigDecimal.TEN,
        askingPrice = BigDecimal.TEN,
        latitude = BigDecimal.ONE,
        longitude = BigDecimal.ZERO,
        type = propertyType,
        multifamilyData = if (propertyType == PropertyType.MEDICAL) {
            null
        } else {
            MultifamilyDataInput(
                brokerFirm = anyString(),
                units = 10,
                averageSquareFootage = BigDecimal.ONE,
                occupancy = BigDecimal.ONE,
                parkingSpots = 10,
                parkingRatio = BigDecimal.ONE,
                owner = anyString(),
                propertyManager = anyString(),
                unitsMix = listOf(
                    UnitsMix(2, 10, BigDecimal.TEN, false, BigDecimal.valueOf(321), BigDecimal.valueOf(512))
                )
            )
        }
    )
}
