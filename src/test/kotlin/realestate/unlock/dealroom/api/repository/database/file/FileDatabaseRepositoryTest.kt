package realestate.unlock.dealroom.api.repository.database.file

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.OffsetDateTime

class FileDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var fileDatabaseRepository: FileDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        fileDatabaseRepository = Context.injector.getInstance(FileDatabaseRepository::class.java)
    }

    @Test
    fun `can save an retrieve a file`() {
        val file = File(
            id = fileDatabaseRepository.nextId(),
            name = anyString(),
            kFileId = anyString(),
            memberId = anyId(),
            createdAt = OffsetDateTime.now()
        )

        fileDatabaseRepository.save(file)

        val fileSaved = fileDatabaseRepository.findById(file.id)

        assertEquals(file.id, fileSaved.id)
        assertEquals(file.name, fileSaved.name)
        assertEquals(file.kFileId, fileSaved.kFileId)
        assertEquals(file.memberId, fileSaved.memberId)
        assertEquals(file.createdAt.toEpochSecond(), fileSaved.createdAt.toEpochSecond())
    }
}
