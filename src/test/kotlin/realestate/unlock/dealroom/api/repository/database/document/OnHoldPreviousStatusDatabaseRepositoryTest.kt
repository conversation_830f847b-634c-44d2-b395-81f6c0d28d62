package realestate.unlock.dealroom.api.repository.database.document

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.OnHoldPreviousStatus
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.repository.document.OnHoldPreviousStatusRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.extensions.anyId

class OnHoldPreviousStatusDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var onHoldPreviousStatusRepository: OnHoldPreviousStatusRepository

    @BeforeEach
    fun setUp() {
        onHoldPreviousStatusRepository = Context.injector.getInstance(OnHoldPreviousStatusRepository::class.java)
    }

    @Test
    fun `can store and get an onHoldPreviousStatus`() {
        val onHoldPreviousStatus = givenOnHoldPreviousStatus()

        onHoldPreviousStatusRepository.save(onHoldPreviousStatus)

        assertEquals(
            onHoldPreviousStatus,
            onHoldPreviousStatusRepository.findByDocumentId(onHoldPreviousStatus.documentId)
        )
    }

    @Test
    fun `can store two times an onHoldPreviousStatus`() {
        val onHoldPreviousStatus = givenOnHoldPreviousStatus()

        onHoldPreviousStatusRepository.save(onHoldPreviousStatus)

        val onHoldPreviousStatusUpdated = onHoldPreviousStatus.copy(taskStatus = TaskStatus.REJECTED, documentStatus = DocumentStatus.PENDING_SIGN)
        onHoldPreviousStatusRepository.save(onHoldPreviousStatusUpdated)

        assertEquals(
            onHoldPreviousStatusUpdated,
            onHoldPreviousStatusRepository.findByDocumentId(onHoldPreviousStatusUpdated.documentId)
        )
    }

    private fun givenOnHoldPreviousStatus() = OnHoldPreviousStatus(
        documentId = anyId(),
        documentStatus = DocumentStatus.BUYER_REVIEW,
        taskStatus = TaskStatus.TO_DO
    )
}
