package realestate.unlock.dealroom.api.repository.database.document

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.OffsetDateTime

class DocumentDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var psaDatabaseRepository: DocumentDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        psaDatabaseRepository = Context.injector.getInstance(DocumentDatabaseRepository::class.java)
    }

    @Test
    fun `can find PSA by deal id`() {
        val psa = givenPSASaved()

        val psaFound = psaDatabaseRepository.findByDealIdAndDocumentType(psa.dealId, DocumentType.PSA)

        assertPSA(psa, psaFound)
    }

    @Test
    fun `can save a PSA`() {
        val psa = givenPSASaved()

        assertPSA(psa, psaDatabaseRepository.findByDealIdAndDocumentType(psa.dealId, DocumentType.PSA))
    }

    @Test
    fun `can update a PSA`() {
        val psa = givenPSASaved()
        val updatedPSA = psa.copy(
            status = DocumentStatus.SELLER_REVIEW,
            expectedBy = OffsetDateTime.now(),
            executedAt = OffsetDateTime.now(),
            draftSubmittedAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now()
        )

        psaDatabaseRepository.update(updatedPSA)

        assertPSA(updatedPSA, psaDatabaseRepository.findByDealIdAndDocumentType(psa.dealId, DocumentType.PSA))
    }

    private fun assertPSA(
        document: Document,
        documentFound: Document?
    ) {
        checkNotNull(documentFound)
        assertEquals(document.id, documentFound.id)
        assertEquals(document.dealId, documentFound.dealId)
        assertEquals(document.status, documentFound.status)
        assertEquals(document.currentRoundId, documentFound.currentRoundId)
        assertEquals(document.draftSubmittedAt?.toEpochSecond(), documentFound.draftSubmittedAt?.toEpochSecond())
        assertEquals(document.expectedBy?.toEpochSecond(), documentFound.expectedBy?.toEpochSecond())
        assertEquals(document.executedAt?.toEpochSecond(), documentFound.executedAt?.toEpochSecond())
        assertEquals(document.createdAt.toEpochSecond(), documentFound.createdAt.toEpochSecond())
        assertEquals(document.updatedAt.toEpochSecond(), documentFound.updatedAt.toEpochSecond())
    }

    private fun givenPSASaved(documentStatus: DocumentStatus = DocumentStatus.NOT_STARTED, expectedBy: OffsetDateTime? = null) =
        givenPSA(documentStatus, expectedBy).also {
            psaDatabaseRepository.save(it)
        }

    private fun givenPSA(documentStatus: DocumentStatus, expectedBy: OffsetDateTime?): Document {
        val nextId = psaDatabaseRepository.nextId()
        val deal = DealCreator.createDealByRest(buyerEmail = "test-buyer-$<EMAIL>", sellerEmail = "test-seller-$<EMAIL>")
        return DocumentBuilder().apply {
            id = nextId
            dealId = deal.id
            status = documentStatus
            this.expectedBy = expectedBy
        }.build()
    }
}
