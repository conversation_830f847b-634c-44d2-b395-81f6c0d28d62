package realestate.unlock.dealroom.api.repository.database.deal.report

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.repository.database.deal.reports.ReportTagsDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest

class ReportTagsDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var reportTagsDatabaseRepository: ReportTagsDatabaseRepository

    @BeforeEach
    fun setUp() {
        reportTagsDatabaseRepository = Context.injector.getInstance(ReportTagsDatabaseRepository::class.java)
    }

    @Test
    fun `it retrieves the expected tags for ZONING`() {
        val tags = reportTagsDatabaseRepository.findByReportType("ZONING")
        assertThat(tags, hasSize(11))
        assertTrue(tags.any { it.name == "Legal Conforming" })
        assertTrue(tags.any { it.name == "No outstanding zoning code violations" })
        assertTrue(tags.any { it.name == "No outstanding building code violations" })
        assertTrue(tags.any { it.name == "No outstanding fire code violations" })
        assertTrue(tags.any { it.name == "Certificate of Occupancy available" })
        assertTrue(tags.any { it.name == "Legal Non-Conforming" })
        assertTrue(tags.any { it.name == "Non-Conforming" })
        assertTrue(tags.any { it.name == "Outstanding zoning code violations" })
        assertTrue(tags.any { it.name == "Outstanding building code violations" })
        assertTrue(tags.any { it.name == "Outstanding fire code violations" })
        assertTrue(tags.any { it.name == "Certificate of Occupancy unavailable" })
    }

    @Test
    fun `it retrieves the expected tags for PHASE I`() {
        val tags = reportTagsDatabaseRepository.findByReportType("PHASE_I")
        assertThat(tags, hasSize(8))
        assertTrue(tags.any { it.name == "No Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "No Controlled Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "No Historical Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "No Business Environmental Risk" })
        assertTrue(tags.any { it.name == "Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "Controlled Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "Historical Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "Business Environmental Risk" })
    }

    @Test
    fun `it retrieves the expected tags for PCR`() {
        val tags = reportTagsDatabaseRepository.findByReportType("PCR")
        assertThat(tags, hasSize(8))
        assertTrue(tags.any { it.name == "No Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "No Controlled Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "No Historical Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "No Business Environmental Risk" })
        assertTrue(tags.any { it.name == "Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "Controlled Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "Historical Recognized Environmental Conditions" })
        assertTrue(tags.any { it.name == "Business Environmental Risk" })
    }
}
