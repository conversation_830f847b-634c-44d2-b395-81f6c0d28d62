package realestate.unlock.dealroom.api.repository.database.calendar

import org.hamcrest.MatcherAssert
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportStatus
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.repository.calendar.CalendarEventRepository
import realestate.unlock.dealroom.api.core.repository.deal.report.DealReportRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.UpdateDates
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.report.ReportBuilder
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate
import java.time.OffsetDateTime

class CalendarEventDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var calendarEventRepository: CalendarEventRepository

    private lateinit var buyer: Member
    private lateinit var seller: Member
    private lateinit var dealReportRepository: DealReportRepository
    private lateinit var documentRepository: DocumentRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        calendarEventRepository = Context.injector.getInstance(CalendarEventRepository::class.java)
        dealReportRepository = Context.injector.getInstance(DealReportRepository::class.java)
        documentRepository = Context.injector.getInstance(DocumentRepository::class.java)
        buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
    }

    @Test
    fun `can find deal with diligence expiration date in the given expiration date by member`() {
        val deal1 = DealCreator.createDealByRest(updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2)), buyer = buyer, seller = seller)
        val deal2 = DealCreator.createDealByRest(updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2)))

        val dealFound = calendarEventRepository.findDealsEvents(CalendarEventsInput(untilDays = 5, memberId = buyer.id, organizationId = buyer.organizationId))

        MatcherAssert.assertThat(dealFound, Matchers.hasSize(1))
        MatcherAssert.assertThat(dealFound[0].dealId, Matchers.equalTo(deal1.id))
    }

    @Test
    fun `can find deal with diligence expiration date in the given expiration date by property type`() {
        val deal1 = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY },
            updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2)),
            buyer = buyer,
            seller = seller
        )
        val deal2 = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MEDICAL },
            updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2))
        )

        val dealFound = calendarEventRepository.findDealsEvents(CalendarEventsInput(untilDays = 5, propertyType = PropertyType.MULTIFAMILY, organizationId = buyer.organizationId))

        MatcherAssert.assertThat(dealFound, Matchers.hasSize(1))
        MatcherAssert.assertThat(dealFound[0].dealId, Matchers.equalTo(deal1.id))
    }

    @Test
    fun `can find deal with diligence expiration date in the given expiration date`() {
        val deal = DealCreator.createDealByRest(updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2)))

        val dealFound = calendarEventRepository.findDealsEvents(CalendarEventsInput(untilDays = 5, organizationId = buyer.organizationId))

        MatcherAssert.assertThat(dealFound, Matchers.hasSize(1))
        MatcherAssert.assertThat(dealFound[0].dealId, Matchers.equalTo(deal.id))
    }

    @Test
    fun `can find deal with closing date in the given expiration date`() {
        val deal = DealCreator.createDealByRest(updateDates = UpdateDates(initialClosingDate = LocalDate.now().plusDays(3)))

        val dealFound = calendarEventRepository.findDealsEvents(CalendarEventsInput(untilDays = 5, organizationId = buyer.organizationId))

        MatcherAssert.assertThat(dealFound, Matchers.hasSize(1))
        MatcherAssert.assertThat(dealFound[0].dealId, Matchers.equalTo(deal.id))
    }

    @Test
    fun `cannot find deal if expiration date is not in given expiration date`() {
        DealCreator.createDealByRest(updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusWeeks(2)))
        MatcherAssert.assertThat(calendarEventRepository.findDealsEvents(CalendarEventsInput(untilDays = 5, organizationId = buyer.organizationId)), Matchers.hasSize(0))
    }

    @Test
    fun `it can find reports with expected date within next week`() {
        val deal = DealCreator.createDealByRest(buyer = buyer, seller = seller)
        val report = givenReport(dealId = deal.id, type = "PHASE_I", expectedDate = LocalDate.now().plusDays(4))

        val deal2 = DealCreator.createDealByRest()
        val report2 = givenReport(dealId = deal2.id, type = "PHASE_I", expectedDate = LocalDate.now().plusDays(4))

        val reportFounds = calendarEventRepository.findReportsEvents(CalendarEventsInput(untilDays = 5, organizationId = buyer.organizationId))

        MatcherAssert.assertThat(reportFounds, Matchers.hasSize(2))
        MatcherAssert.assertThat(reportFounds[0].id, Matchers.equalTo(report.id))
        MatcherAssert.assertThat(reportFounds[1].id, Matchers.equalTo(report2.id))
    }

    @Test
    fun `it can find reports with expected date within next week by member`() {
        val deal = DealCreator.createDealByRest(buyer = buyer, seller = seller)
        val report = givenReport(dealId = deal.id, type = "PHASE_I", expectedDate = LocalDate.now().plusDays(4))

        val deal2 = DealCreator.createDealByRest()
        val report2 = givenReport(dealId = deal2.id, type = "PHASE_I", expectedDate = LocalDate.now().plusDays(4))

        val reportFounds = calendarEventRepository.findReportsEvents(CalendarEventsInput(untilDays = 5, memberId = buyer.id, organizationId = buyer.organizationId))

        MatcherAssert.assertThat(reportFounds, Matchers.hasSize(1))
        MatcherAssert.assertThat(reportFounds[0].id, Matchers.equalTo(report.id))
    }

    @Test
    fun `it can find reports for calendar`() {
        val deal = DealCreator.createDealByRest()
        val report = givenReport(dealId = deal.id, type = "PHASE_I", expectedDate = LocalDate.now().plusDays(4), status = ReportStatus.ORDERED)
        givenReport(dealId = deal.id, type = "PHASE_I", expectedDate = LocalDate.now().plusDays(8), status = ReportStatus.ORDERED)
        givenReport(dealId = deal.id, type = "PHASE_I", expectedDate = LocalDate.now().plusDays(4), status = ReportStatus.REVIEWED)

        val reportFounds = calendarEventRepository.findReportsEvents(CalendarEventsInput(untilDays = 7, dealId = deal.id, organizationId = buyer.organizationId))

        MatcherAssert.assertThat(reportFounds, Matchers.hasSize(1))
        MatcherAssert.assertThat(reportFounds[0].id, Matchers.equalTo(report.id))
    }

    @Test
    fun `it  retrieve reports with past expected date`() {
        val deal = DealCreator.createDealByRest()
        val report = givenReport(dealId = deal.id, type = "PHASE_I", expectedDate = LocalDate.now().minusDays(1))

        MatcherAssert.assertThat(calendarEventRepository.findReportsEvents(CalendarEventsInput(untilDays = 5, organizationId = buyer.organizationId)), Matchers.hasSize(1))
    }

    private fun givenReport(
        dealId: Long,
        type: String,
        expectedDate: LocalDate? = LocalDate.now(),
        status: ReportStatus = ReportStatus.ORDERED
    ) = ReportBuilder().apply {
        this.dealId = dealId
        this.type = type
        this.expectedDate = expectedDate
        this.status = status
    }.build()
        .also {
            dealReportRepository.save(it)
        }

    @Test
    fun `can find documents for calendar`() {
        val shouldBeFound = givenPSASaved(DocumentStatus.SELLER_REVIEW, OffsetDateTime.now().plusDays(3))
        givenPSASaved(DocumentStatus.SELLER_REVIEW, OffsetDateTime.now().minusDays(1))
        givenPSASaved(DocumentStatus.EXECUTED, OffsetDateTime.now().plusDays(3))
        givenPSASaved(DocumentStatus.SELLER_REVIEW, OffsetDateTime.now().plusDays(8))

        val results = calendarEventRepository.findDocumentsEvents(CalendarEventsInput(untilDays = 5, dealId = shouldBeFound.dealId, organizationId = buyer.organizationId))

        assertEquals(1, results.size)
        val documentFound = results[0]
        assertEquals(shouldBeFound.id, documentFound.id)
        assertEquals(shouldBeFound.dealId, documentFound.dealId)
        assertEquals(shouldBeFound.type, documentFound.type)
        assertEquals(shouldBeFound.expectedBy?.toEpochSecond(), documentFound.expectedBy.toEpochSecond())
    }

    @Test
    fun `should not retrieve information if the deal is from another organization`() {
        val deal1 = DealCreator.createDealByRest(updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2)), buyer = buyer, seller = seller)
        val deal2 = DealCreator.createDealByRest(updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2)))

        val dealFound = calendarEventRepository.findDealsEvents(CalendarEventsInput(untilDays = 5, memberId = buyer.id, organizationId = "fake-org"))

        assertThat(dealFound, hasSize(0))
    }

    private fun givenPSASaved(documentStatus: DocumentStatus = DocumentStatus.NOT_STARTED, expectedBy: OffsetDateTime? = null) =
        givenPSA(documentStatus, expectedBy).also {
            documentRepository.save(it)
        }

    private fun givenPSA(documentStatus: DocumentStatus, expectedBy: OffsetDateTime?): Document {
        val nextId = documentRepository.nextId()
        val deal = DealCreator.createDealByRest(buyerEmail = "test-buyer-$<EMAIL>", sellerEmail = "test-seller-$<EMAIL>")
        return DocumentBuilder().apply {
            id = nextId
            dealId = deal.id
            status = documentStatus
            this.expectedBy = expectedBy
        }.build()
    }
}
