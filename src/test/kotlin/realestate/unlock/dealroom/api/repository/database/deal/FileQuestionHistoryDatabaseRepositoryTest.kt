package realestate.unlock.dealroom.api.repository.database.deal

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileQuestionHistory
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileQuestionType
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.repository.database.file.FileQuestionHistoryDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.Instant

class FileQuestionHistoryDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var fileQuestionHistoryDatabaseRepository: FileQuestionHistoryDatabaseRepository

    @BeforeEach
    fun setUp() {
        fileQuestionHistoryDatabaseRepository = Context.injector.getInstance(FileQuestionHistoryDatabaseRepository::class.java)
    }

    @Test
    fun `it saves the history`() {
        val givenSeller = AuthMock.createMemberWithUser(memberType = "seller", email = "<EMAIL>", uid = anyString())
        val history = FileQuestionHistory(
            kFileId = anyString(),
            questionType = FileQuestionType.PERSONAL,
            question = anyString(),
            answer = anyString(),
            memberId = givenSeller.id,
            createdAt = Instant.now()
        )
        fileQuestionHistoryDatabaseRepository.save(history)

        val got = fileQuestionHistoryDatabaseRepository.get(fileId = history.kFileId, givenSeller.id)

        assertThat(got, hasSize(1))
        assertThat(got[0].kFileId, equalTo(history.kFileId))
        assertThat(got[0].questionType, equalTo(history.questionType))
        assertThat(got[0].question, equalTo(history.question))
        assertThat(got[0].answer, equalTo(history.answer))
        assertThat(got[0].memberId, equalTo(history.memberId))
        assertThat(got[0].createdAt.toEpochMilli(), equalTo(history.createdAt.toEpochMilli()))
    }
}
