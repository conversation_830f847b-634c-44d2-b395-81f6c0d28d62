package realestate.unlock.dealroom.api.repository.database.deal.category

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest

class FindDealStageByKeysDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var findDealStageByKeysDatabaseRepository: FindDealStageByKeysDatabaseRepository

    @BeforeEach
    fun setUp() {
        findDealStageByKeysDatabaseRepository = Context.injector.getInstance(FindDealStageByKeysDatabaseRepository::class.java)
    }

    @Test
    fun `It can find a single valid stage`() {
        val categories = findDealStageByKeysDatabaseRepository.find(listOf(Stage.DILIGENCE))
        assertThat(categories, hasSize(1))
        assertThat(categories[0].key, equalTo(Stage.DILIGENCE.key))
    }

    @Test
    fun `It can find a valid list of stages`() {
        val categories = findDealStageByKeysDatabaseRepository.find(listOf(Stage.DILIGENCE, Stage.CLOSING))
        assertThat(categories, hasSize(2))
        assertThat(categories.first { it.key == Stage.CLOSING.key }, notNullValue())
        assertThat(categories.first { it.key == Stage.DILIGENCE.key }, notNullValue())
    }
}
