package realestate.unlock.dealroom.api.repository.database.loi

import org.hamcrest.MatcherAssert
import org.hamcrest.core.IsEqual
import org.hamcrest.core.IsNull
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.loi.MedicalLoiRound
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.SaveMedicalLetterOfIntentRoundBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class MedicalLoiRoundDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var letterOfIntentRoundDatabaseRepository: MedicalLetterOfIntentRoundDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        letterOfIntentRoundDatabaseRepository = Context.injector.getInstance(MedicalLetterOfIntentRoundDatabaseRepository::class.java)
    }

    @Test
    fun `can find by deal and status`() {
        // given
        val loiRound = givenLoi()

        // when
        val loiRoundFound = letterOfIntentRoundDatabaseRepository.findByDealIdAndStatus(dealId = loiRound.dealId, statusIn = listOf(loiRound.status))

        // then
        assertEquals(loiRound, loiRoundFound)
    }

    @Test
    fun `returns null if there is not match`() {
        // given
        val loiRound = givenLoi()

        // when
        val loiRoundFound = letterOfIntentRoundDatabaseRepository.findByDealIdAndStatus(dealId = loiRound.dealId, statusIn = listOf(LetterOfIntentRoundStatus.REJECTED))

        // then
        assertNull(loiRoundFound)
    }

    @Test
    fun `can find a loi by dealId and status in`() {
        // given
        val loi = givenLoi()

        // when
        val result = letterOfIntentRoundDatabaseRepository.findByDealIdAndStatus(dealId = loi.dealId, statusIn = listOf(loi.status))

        // then
        MatcherAssert.assertThat(result, IsEqual(loi))
    }

    @Test
    fun `can handle empty result`() {
        // given
        val loi = givenLoi()

        // when
        val result = letterOfIntentRoundDatabaseRepository.findByDealIdAndStatus(dealId = loi.dealId, statusIn = listOf(LetterOfIntentRoundStatus.REJECTED))

        // then
        MatcherAssert.assertThat(result, IsNull())
    }

    @Test
    fun `can save a loi`() {
        // given
        val deal = DealCreator.createDealByRest()
        val offerInput = SaveMedicalLetterOfIntentRoundBuilder().apply {
            memberId = deal.members.first().id
            dealId = deal.id
        }.build()

        // when
        val result = letterOfIntentRoundDatabaseRepository.save(offerInput)

        // then
        MatcherAssert.assertThat(result.dealId, IsEqual(deal.id))
        MatcherAssert.assertThat(result.tenantName, IsEqual(offerInput.tenantName))
        MatcherAssert.assertThat(result.status, IsEqual(LetterOfIntentRoundStatus.IN_NEGOTIATION))
        MatcherAssert.assertThat(result.offer.comments, IsEqual(offerInput.offerComments))
        MatcherAssert.assertThat(result.offer.salesPrice, IsEqual(offerInput.offerPrice))
        MatcherAssert.assertThat(result.offer.contractTermination, IsEqual(offerInput.offerContractTermination))
        MatcherAssert.assertThat(result.offer.earnestMoneyDeposit, IsEqual(offerInput.offerEarnestMoneyDeposit))
        MatcherAssert.assertThat(result.offer.lease.rent, IsEqual(offerInput.offerLeaseRent))
        MatcherAssert.assertThat(result.offer.lease.length, IsEqual(offerInput.offerLeaseLength))
        MatcherAssert.assertThat(result.offer.lease.expirationYear, IsEqual(offerInput.offerLeaseExpirationYear))
        MatcherAssert.assertThat(result.offer.lease.type, IsEqual(offerInput.offerLeaseType))
        MatcherAssert.assertThat(result.offer.lease.numberOfOptions, IsEqual(offerInput.offerLeaseNumberOfOptions))
        MatcherAssert.assertThat(result.offer.lease.increaseEveryYear, IsEqual(offerInput.offerLeaseIncreaseEveryYear))
        MatcherAssert.assertThat(result.offer.lease.optionLengths, IsEqual(offerInput.offerLeaseOptionLengths))
        MatcherAssert.assertThat(result.offer.lease.rentIncrease, IsEqual(offerInput.offerLeaseRentIncrease))
        MatcherAssert.assertThat(result.offer.closing.period, IsEqual(offerInput.offerClosingPeriod))
        MatcherAssert.assertThat(result.offer.closing.extensionDeposit, IsEqual(offerInput.offerClosingExtensionDeposit))
        MatcherAssert.assertThat(result.offer.closing.periodExtension, IsEqual(offerInput.offerClosingPeriodExtension))
        MatcherAssert.assertThat(result.offer.customSections!!.sections.first().title, IsEqual(offerInput.offerCustomSections!!.sections.first().title))
        MatcherAssert.assertThat(result.offer.customSections!!.sections.first().content, IsEqual(offerInput.offerCustomSections!!.sections.first().content))
    }

    private fun givenLoi(): MedicalLoiRound {
        val deal = DealCreator.createDealByRest()
        return letterOfIntentRoundDatabaseRepository.save(
            SaveMedicalLetterOfIntentRoundBuilder().apply {
                dealId = deal.id
                memberId = deal.members.first().id
            }.build()
        )
    }
}
