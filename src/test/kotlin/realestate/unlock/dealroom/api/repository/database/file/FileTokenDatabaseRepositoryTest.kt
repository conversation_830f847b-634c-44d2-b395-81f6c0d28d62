package realestate.unlock.dealroom.api.repository.database.file

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileToken
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileTokenStatus
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class FileTokenDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var fileTokenDatabaseRepository: FileTokenDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        fileTokenDatabaseRepository = Context.injector.getInstance(FileTokenDatabaseRepository::class.java)
    }

    @Test
    fun `it can save a file token`() {
        val givenFileId = anyString()
        val givenToken = anyString()
        val givenStatus = FileTokenStatus.READY
        val givenDealId = DealCreator.createDealByRest().id

        val saved = fileTokenDatabaseRepository.save(FileToken(kFileId = givenFileId, token = givenToken, status = givenStatus, fileType = TokenFileType.REPORT, dealId = givenDealId))

        assertThat(saved.kFileId, equalTo(givenFileId))
        assertThat(saved.token, equalTo(givenToken))
        assertThat(saved.status, equalTo(givenStatus))
        assertThat(saved.dealId, equalTo(givenDealId))
    }

    @Test
    fun `saving a token for an already saved file should update it`() {
        val givenFileId = anyString()
        val givenToken = anyString()
        val givenStatus = FileTokenStatus.READY
        val newToken = "this-is-a-new-token"
        val newStatus = FileTokenStatus.PROCESSING
        val givenDealId = DealCreator.createDealByRest().id

        fileTokenDatabaseRepository.save(FileToken(kFileId = givenFileId, token = givenToken, status = givenStatus, fileType = TokenFileType.REPORT, dealId = givenDealId))
        fileTokenDatabaseRepository.save(FileToken(kFileId = givenFileId, token = newToken, status = newStatus, fileType = TokenFileType.REPORT, dealId = givenDealId))

        val got = fileTokenDatabaseRepository.findByFileId(givenFileId)

        assertThat(got!!.kFileId, equalTo(givenFileId))
        assertThat(got.token, equalTo(newToken))
        assertThat(got.status, equalTo(newStatus))
    }
}
