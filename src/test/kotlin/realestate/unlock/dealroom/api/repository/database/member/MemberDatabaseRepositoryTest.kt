package realestate.unlock.dealroom.api.repository.database.member

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.MemberToSave
import realestate.unlock.dealroom.api.core.entity.member.type.MemberType
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime
import java.time.temporal.ChronoUnit
import java.util.*

class MemberDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var memberDatabaseRepository: MemberRepository

    @BeforeEach
    fun setUp() {
        memberDatabaseRepository = Context.injector.getInstance(MemberDatabaseRepository::class.java)
    }

    @Test
    fun `it retrieves the member by its email`() {
        // given
        val givenMember = givenMember()

        // when
        val foundMember = memberDatabaseRepository.findByEmail(givenMember.email.uppercase(Locale.getDefault()))

        // then
        assertMember(givenMember, foundMember)
    }

    @Test
    fun `can find a user by auth id`() {
        // given
        val member = givenMember(authId = anyString())

        // when
        val foundMember = memberDatabaseRepository.findByAuthId(member.authId!!)

        // then
        assertMember(member, foundMember)
    }

    @Test
    fun `It retrieves null if no member has the given email`() {
        // given
        givenMember()

        // then
        Assertions.assertNull(memberDatabaseRepository.findByEmail("<EMAIL>".uppercase(Locale.getDefault())))
    }

    @Test
    fun `it does not retrieve members of other organizations`() {
        val firstMember = givenMember()
        val secondMember = givenMember(email = "<EMAIL>", organizationId = "<EMAIL>")

        val got = memberDatabaseRepository.findFiltered(mapOf(), secondMember.organizationId)

        assertThat(got, hasSize(1))
        assertMember(secondMember, got.first())
    }

    @Test
    fun `can update member`() {
        val orginalMember = givenMember()
        val changeMember = orginalMember.copy(
            authId = anyString(),
            email = "<EMAIL>",
            walkThroughDone = true,
            enabled = false,
            firstName = "el pibe",
            lastName = "cantina",
            updatedAt = OffsetDateTime.now()
        )

        val updatedMember = memberDatabaseRepository.update(changeMember)

        assertMember(changeMember, updatedMember)
    }

    @Test
    fun `should filter hide email`() {
        val hidden = listOf(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        )
        val visible = listOf(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        )
        val organizationId = "ONE-ORG-TO-RULE-THEM-ALL"
        hidden.forEach { givenMember(email = it, organizationId = organizationId) }
        visible.forEach { givenMember(email = it, organizationId = organizationId) }

        val response = memberDatabaseRepository.findFiltered(filters = mapOf(), organizationId = organizationId)

        hidden.forEach { hiddenEmail ->
            Assertions.assertFalse(response.any { m -> m.email == hiddenEmail })
        }

        visible.forEach { visibleEmail ->
            Assertions.assertTrue(response.any { m -> m.email == visibleEmail })
        }
    }

    private fun assertMember(given: Member, found: Member?) {
        Assertions.assertEquals(given.id, found?.id)
        Assertions.assertEquals(given.authId, found?.authId)
        Assertions.assertEquals(given.email, found?.email)
        Assertions.assertEquals(given.enabled, found?.enabled)
        Assertions.assertEquals(given.walkThroughDone, found?.walkThroughDone)
        Assertions.assertEquals(given.updatedAt.toInstant().truncatedTo(ChronoUnit.SECONDS), found?.updatedAt?.toInstant()?.truncatedTo(ChronoUnit.SECONDS))
        Assertions.assertEquals(given.createdAt.toInstant().truncatedTo(ChronoUnit.SECONDS), found?.createdAt?.toInstant()?.truncatedTo(ChronoUnit.SECONDS))
    }

    private fun givenMember(
        email: String = "<EMAIL>",
        authId: String? = null,
        organizationId: String = "org-id"
    ): Member =
        memberDatabaseRepository.save(
            MemberToSave(
                type = MemberType(id = 1L, key = "seller", name = "Seller"),
                firstName = "Elon",
                lastName = "Musk",
                companyName = "Tesla",
                email = email,
                address = null,
                phoneNumber = null,
                authId = authId,
                walkThroughDone = false,
                organizationId = organizationId
            )
        )
}
