package realestate.unlock.dealroom.api.repository.database.document

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentRoundRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.document.DocumentBuilder
import realestate.unlock.dealroom.api.utils.entity.document.DocumentRoundObjectMother
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class DocumentRoundDatabaseRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var documentRepository: DocumentRepository
    private lateinit var psaRoundDatabaseRepository: DocumentRoundRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        documentRepository = Context.injector.getInstance(DocumentRepository::class.java)
        psaRoundDatabaseRepository = Context.injector.getInstance(DocumentRoundRepository::class.java)
    }

    @Test
    fun `can save and retrieve a new draft document`() {
        val psa = givenPSA()
        val document = DocumentRoundObjectMother.draftDocuments(
            id = psaRoundDatabaseRepository.nextId(),
            documentId = psa.id
        )

        psaRoundDatabaseRepository.save(document)

        assertEquals(document, psaRoundDatabaseRepository.findById(document.id))
    }

    @Test
    fun `can save and retrieve a final version document`() {
        val psa = givenPSA()
        val document = DocumentRoundObjectMother.finalDocument(
            id = psaRoundDatabaseRepository.nextId(),
            documentId = psa.id
        )

        psaRoundDatabaseRepository.save(document)

        assertEquals(document, psaRoundDatabaseRepository.findById(document.id))
    }

    @Test
    fun `can save and retrieve an executed document`() {
        val psa = givenPSA()
        val document = DocumentRoundObjectMother.executedDocument(
            id = psaRoundDatabaseRepository.nextId(),
            documentId = psa.id
        )

        psaRoundDatabaseRepository.save(document)

        assertEquals(document, psaRoundDatabaseRepository.findById(document.id))
    }

    @Test
    fun `can find documents by psaId and file id`() {
        val psa = givenPSA()
        val document1 = givenAPSARound(psa)

        val documentsFound = psaRoundDatabaseRepository.findByDocumentAndContainsFileId(psa.id, document1.fileId)

        assertEquals(listOf(document1), documentsFound)
    }

    @Test
    fun `can find documents by psaId`() {
        val psa = givenPSA()
        val document1 = givenAPSARound(psa)
        val document2 = givenAPSARound(psa)

        val documentsFound = psaRoundDatabaseRepository.findByDocumentId(psa.id)

        assertEquals(listOf(document1, document2), documentsFound)
    }

    private fun givenAPSARound(document: Document) =
        DocumentRoundObjectMother.executedDocument(
            id = psaRoundDatabaseRepository.nextId(),
            documentId = document.id
        ).apply { psaRoundDatabaseRepository.save(this) }

    private fun givenPSA(): Document {
        val deal = DealCreator.createDealByRest()
        return DocumentBuilder().apply {
            id = documentRepository.nextId()
            dealId = deal.id
        }.build().apply {
            documentRepository.save(this)
        }
    }
}
