package realestate.unlock.dealroom.api.repository.database.document

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.DocumentSigner
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.entity.document.DocumentSignerBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.OffsetDateTime

class DocumentDatabaseSignRepositoryTest : BaseFunctionalWithoutRestTest() {

    private lateinit var psaDatabaseSignRepository: DocumentSignDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        psaDatabaseSignRepository = Context.injector.getInstance(DocumentSignDatabaseRepository::class.java)
    }

    @Test
    fun `can update a PSA`() {
        val psa = givenPsaSignSaved()
        val psaUpdated = psa.copy(signCompletedAt = OffsetDateTime.now(), signerLastName = "nose")
        psaDatabaseSignRepository.update(psaUpdated)
        comparePsaSigner(psaUpdated, psaDatabaseSignRepository.findByDocumentId(psaUpdated.documentId).first())
    }

    @Test
    fun `can save a PSA`() {
        val psa = givenPsaSign()
        psaDatabaseSignRepository.save(psa)
        comparePsaSigner(psa, psaDatabaseSignRepository.findByDocumentId(psa.documentId).first())
    }

    @Test
    fun `can find by PSAId`() {
        val signers = createTwoSigners()
        val signersFound = psaDatabaseSignRepository.findByDocumentId(signers.first().documentId)
        signers.forEach { saved ->
            comparePsaSigner(saved, signersFound.first { it.signerTeamType == saved.signerTeamType })
        }
    }

    @Test
    fun `can find by envelopeId`() {
        val signers = createTwoSigners()
        val signersFound = psaDatabaseSignRepository.findByEnvelopeId(signers.first().envelopeId!!)
        signers.forEach { saved ->
            comparePsaSigner(saved, signersFound.first { it.signerTeamType == saved.signerTeamType })
        }
    }

    private fun createTwoSigners(): List<DocumentSigner> {
        val signer = DocumentSignerBuilder().build()
        return listOf(signer, signer.copy(signerTeamType = MemberDealTeam.BUYER)).onEach(psaDatabaseSignRepository::save)
    }

    private fun comparePsaSigner(
        one: DocumentSigner,
        two: DocumentSigner
    ) {
        Assertions.assertEquals(one.documentId, two.documentId)
        Assertions.assertEquals(one.envelopeId, two.envelopeId)
        Assertions.assertEquals(one.docusignDocumentId, two.docusignDocumentId)
        Assertions.assertEquals(one.signerRecipientId, two.signerRecipientId)
        Assertions.assertEquals(one.signerEmail, two.signerEmail)
        Assertions.assertEquals(one.signerFirstName, two.signerFirstName)
        Assertions.assertEquals(one.signerLastName, two.signerLastName)
        Assertions.assertEquals(one.signerTeamType, two.signerTeamType)
    }

    private fun givenPsaSign(): DocumentSigner = DocumentSignerBuilder().build()

    private fun givenPsaSignSaved(): DocumentSigner = givenPsaSign().also(psaDatabaseSignRepository::save)
}
