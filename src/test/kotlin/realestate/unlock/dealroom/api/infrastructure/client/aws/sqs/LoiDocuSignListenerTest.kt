package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs

import com.docusign.esign.model.CustomFields
import com.docusign.esign.model.Envelope
import com.docusign.esign.model.EnvelopeDocument
import com.docusign.esign.model.TextCustomField
import com.keyway.security.domain.authentication.AuthenticationSdk
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.loi.ExecuteLetterOfIntentInput
import realestate.unlock.dealroom.api.core.entity.loi.SignLetterOfIntentInput
import realestate.unlock.dealroom.api.core.gateway.sign.SignDocumentType
import realestate.unlock.dealroom.api.core.usecase.loi.ExecuteLetterOfIntentFromSign
import realestate.unlock.dealroom.api.core.usecase.loi.SignLetterOfIntent
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.DocuSignListener
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.LeaseDocuSignListener
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.LoiDocuSignListener
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.PsaDocuSignListener
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.DocuSignSignGateway
import realestate.unlock.dealroom.api.infrastructure.utils.extension.trimIndentWithoutLineBreaks
import realestate.unlock.dealroom.api.utils.extensions.anyString
import software.amazon.awssdk.services.sqs.model.Message

@ExtendWith(MockKExtension::class)
class LoiDocuSignListenerTest {

    @MockK
    private lateinit var signLetterOfIntent: SignLetterOfIntent

    @MockK
    private lateinit var executeLetterOfIntentFromSign: ExecuteLetterOfIntentFromSign

    private lateinit var docuSignListener: DocuSignListener

    @MockK
    private lateinit var loiDocuSignListener: LoiDocuSignListener

    @MockK
    private lateinit var psaDocuSignListener: PsaDocuSignListener

    @MockK
    private lateinit var leaseDocuSignListener: LeaseDocuSignListener

    @MockK
    private lateinit var authenticationSdk: AuthenticationSdk

    private val authToken = "token"

    @BeforeEach
    fun setUp() {
        loiDocuSignListener = LoiDocuSignListener(signLetterOfIntent = signLetterOfIntent, executeLetterOfIntentFromSign = executeLetterOfIntentFromSign)
        every { authenticationSdk.accessTokenFor(any()) } returns authToken
        docuSignListener = DocuSignListener(
            docuSignSignGateway = mockk(relaxed = true) {
                every { retrieveEnvelopeData(SignDocumentType.PSA.name) } returns Envelope().envelopeDocuments(listOf(EnvelopeDocument().documentId("NOOOOHAY")))
                    .customFields(CustomFields().textCustomFields(listOf(TextCustomField().name(DocuSignSignGateway.CUSTOM_DOCUMENT_TYPE_METADATA).value(SignDocumentType.PSA.name))))
                every { retrieveEnvelopeData(SignDocumentType.LOI.name) } returns Envelope().envelopeDocuments(listOf(EnvelopeDocument().documentId("NOOOOHAY")))
                    .customFields(CustomFields().textCustomFields(listOf(TextCustomField().name(DocuSignSignGateway.CUSTOM_DOCUMENT_TYPE_METADATA).value(SignDocumentType.LOI.name))))
            },
            loiDocuSignListener = loiDocuSignListener,
            psaDocuSignListener = psaDocuSignListener,
            leaseDocuSignListener = leaseDocuSignListener,
            authenticationSdk = authenticationSdk,
            auth0TokenVerificationConfig = mockk(relaxed = true)
        )
    }

    @Test
    fun `can listen a recipient-completed event`() {
        // given
        val signLetterOfIntentInput = givenSignLetterOfIntentInput()
        val body = givenRecipientCompletedMessageBody(envelopeId = signLetterOfIntentInput.envelopeId, recipientId = signLetterOfIntentInput.recipientId)
        val sqsMessage = givenSqsMessage(body)
        every { signLetterOfIntent.sign(signLetterOfIntentInput) } returns Unit

        // when
        val result = docuSignListener.processMessage(sqsMessage)

        // then
        assertThat(result, equalTo(true))
        verify {
            signLetterOfIntent.sign(signLetterOfIntentInput)
        }
    }

    @Test
    fun `can handle an error on recipient-completed event`() {
        // given
        val signLetterOfIntentInput = givenSignLetterOfIntentInput()
        val body = givenRecipientCompletedMessageBody(envelopeId = signLetterOfIntentInput.envelopeId, recipientId = signLetterOfIntentInput.recipientId)
        val sqsMessage = givenSqsMessage(body)
        every { signLetterOfIntent.sign(signLetterOfIntentInput) } throws RuntimeException("test")

        // when
        val result = docuSignListener.processMessage(sqsMessage)

        // then
        assertThat(result, equalTo(false))
    }

    @Test
    fun `can listen a envelope-completed event`() {
        // given
        val signLetterOfIntentInput = givenExecuteLetterOfIntentInput()
        val body = givenEnvelopeCompletedMessageBody(envelopeId = signLetterOfIntentInput.envelopeId)
        val sqsMessage = givenSqsMessage(body)
        every { executeLetterOfIntentFromSign.execute(signLetterOfIntentInput, authToken) } returns Unit

        // when
        val result = docuSignListener.processMessage(sqsMessage)

        // then
        assertThat(result, equalTo(true))
        verify {
            executeLetterOfIntentFromSign.execute(signLetterOfIntentInput, authToken)
        }
    }

    @Test
    fun `can listen a envelope-sent event`() {
        // given
        val envelopeId = SignDocumentType.PSA.name
        val body = givenEnvelopeSentMessageBody(envelopeId = envelopeId)
        val sqsMessage = givenSqsMessage(body)
        every { psaDocuSignListener.sent(envelopeId) } returns Unit
        // when
        val result = docuSignListener.processMessage(sqsMessage)

        // then
        assertThat(result, equalTo(true))
        verify(exactly = 1) {
            psaDocuSignListener.sent(envelopeId)
        }
    }

    @Test
    fun `can handle an error on envelope-completed event`() {
        // given
        val signLetterOfIntentInput = givenExecuteLetterOfIntentInput()
        val body = givenEnvelopeCompletedMessageBody(envelopeId = signLetterOfIntentInput.envelopeId)
        val sqsMessage = givenSqsMessage(body)
        every { executeLetterOfIntentFromSign.execute(signLetterOfIntentInput, authToken) } throws RuntimeException("test")

        // when
        val result = docuSignListener.processMessage(sqsMessage)

        // then
        assertThat(result, equalTo(false))
    }

    private fun givenSqsMessage(body: String) =
        Message.builder().messageId(anyString()).body(body).build()

    private fun givenRecipientCompletedMessageBody(envelopeId: String, recipientId: String) = """
        "{\"event\":\"recipient-completed\",
        \"apiVersion\":\"v2.1\",
        \"uri\":\"/restapi/v2.1/accounts/18b1590a-1f5c-451a-b867-0678149a0919/envelopes/94ef8bf0-4219-44df-8153-129b7dbe9734\",
        \"retryCount\":0,
        \"configurationId\":********,
        \"generatedDateTime\":\"2022-03-25T12:33:04.4897437Z\",
        \"data\":{
                    \"accountId\":\"18b1590a-1f5c-451a-b867-0678149a0919\",
                    \"userId\":\"77596c4c-0d78-464c-999b-98d16f6ecbe2\",
                    \"envelopeId\":\"$envelopeId\",
                    \"recipientId\":\"$recipientId\"
                    }
        }"
    """.trimIndentWithoutLineBreaks()

    private fun givenEnvelopeCompletedMessageBody(envelopeId: String) = """
        "{\"event\":\"envelope-completed\",
        \"apiVersion\":\"v2.1\",
        \"uri\":\"/restapi/v2.1/accounts/18b1590a-1f5c-451a-b867-0678149a0919/envelopes/94ef8bf0-4219-44df-8153-129b7dbe9734\",
        \"retryCount\":0,
        \"configurationId\":********,
        \"generatedDateTime\":\"2022-03-25T12:33:04.4897437Z\",
        \"data\":{
                    \"accountId\":\"18b1590a-1f5c-451a-b867-0678149a0919\",
                    \"userId\":\"77596c4c-0d78-464c-999b-98d16f6ecbe2\",
                    \"envelopeId\":\"$envelopeId\"
                    }
        }"
    """.trimIndentWithoutLineBreaks()

    private fun givenEnvelopeSentMessageBody(envelopeId: String) = """
        "{\"event\":\"envelope-sent\",
        \"apiVersion\":\"v2.1\",
        \"uri\":\"/restapi/v2.1/accounts/18b1590a-1f5c-451a-b867-0678149a0919/envelopes/94ef8bf0-4219-44df-8153-129b7dbe9734\",
        \"retryCount\":0,
        \"configurationId\":********,
        \"generatedDateTime\":\"2022-03-25T12:33:04.4897437Z\",
        \"data\":{
                    \"accountId\":\"18b1590a-1f5c-451a-b867-0678149a0919\",
                    \"userId\":\"77596c4c-0d78-464c-999b-98d16f6ecbe2\",
                    \"envelopeId\":\"$envelopeId\"
                    }
        }"
    """.trimIndentWithoutLineBreaks()

    private fun givenSignLetterOfIntentInput() = SignLetterOfIntentInput(
        recipientId = anyString(),
        envelopeId = SignDocumentType.LOI.name
    )

    private fun givenExecuteLetterOfIntentInput() = ExecuteLetterOfIntentInput(
        envelopeId = SignDocumentType.LOI.name
    )
}
