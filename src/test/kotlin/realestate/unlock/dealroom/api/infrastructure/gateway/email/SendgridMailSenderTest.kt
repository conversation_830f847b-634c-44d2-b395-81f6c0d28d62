package realestate.unlock.dealroom.api.infrastructure.gateway.email

import com.sendgrid.Response
import com.sendgrid.SendGrid
import com.sendgrid.helpers.mail.Mail
import com.sendgrid.helpers.mail.objects.Content
import com.sendgrid.helpers.mail.objects.Email
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.infrastructure.client.sendgrid.exception.SendGridException

@ExtendWith(MockKExtension::class)
class SendgridMailSenderTest {

    companion object {
        private const val SUCCESSFUL = 202
        private const val FAILURE = 400
    }

    @MockK
    private lateinit var sendGrid: SendGrid

    private lateinit var sendgridMailSender: SendgridMailSender

    @BeforeEach
    fun setUp() {
        sendgridMailSender = SendgridMailSender(
            sendGrid = sendGrid
        )
    }

    @Test
    fun `if sendgrid returns an error, throws an exception`() {
        // Given
        val mail = givenMail()
        givenSendMailResponse(statusCodeResponse = FAILURE)

        // When
        val error = assertThrows<SendGridException> {
            sendgridMailSender(mail)
        }

        // Then
        assertThat(error.message, equalTo("Send email fails with code $FAILURE"))
    }

    @Test
    fun `can send an email by sendgrid`() {
        // Given
        val mail = givenMail()
        givenSendMailResponse(statusCodeResponse = SUCCESSFUL)

        // When
        sendgridMailSender(mail)

        // Then
        verify {
            sendGrid.api(any())
        }
    }

    private fun givenSendMailResponse(statusCodeResponse: Int) {
        every { sendGrid.api(any()) } returns Response().apply { statusCode = statusCodeResponse }
    }

    private fun givenMail(): Mail {
        return Mail(Email("<EMAIL>"), "subject", Email("<EMAIL>"), Content("text", "Hello world"))
    }
}
