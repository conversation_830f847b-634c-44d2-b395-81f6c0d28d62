package realestate.unlock.dealroom.api.infrastructure.client.aws.sns

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.event.deal.DealEvent
import realestate.unlock.dealroom.api.core.event.deal.EventType
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sns.AwsSNSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapperCC
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.entity.deal.DealBuilder
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder

@ExtendWith(MockKExtension::class)
class SnsDealEventPublisherTest : BaseFunctionalWithoutRestTest() {

    @MockK
    private lateinit var awsSNSClient: AwsSNSClient

    @MockK
    private lateinit var dealRepository: DealRepository

    @MockK
    private lateinit var propertyRepository: PropertyRepository

    private val snsConfig = AwsSNSConfig(taskChangedTopicArn = "task-changed", dealEventsTopicArn = "deal-events")

    private lateinit var snsDealEventPublisher: SnsDealEventPublisher

    @BeforeEach
    fun setUp() {
        every { awsSNSClient.publish(any(), snsConfig.dealEventsTopicArn) } returns Unit
        snsDealEventPublisher = SnsDealEventPublisher(
            snsClient = awsSNSClient,
            snsConfig = snsConfig,
            dealRepository = dealRepository,
            propertyRepository = propertyRepository
        )
    }

    @Test
    fun `it publishes correctly`() {
        val givenDeal = DealBuilder().build()
        val givenProperty = PropertyBuilder().build()

        snsDealEventPublisher.publish(EventType.NEW_DEAL, givenDeal, givenProperty)

        verifyMessageWasSent(eventType = EventType.NEW_DEAL, deal = givenDeal, property = givenProperty)
    }

    @Test
    fun `it takes the deal info from the repository`() {
        val givenDeal = DealBuilder().build()
        val givenProperty = PropertyBuilder().build()
        every { propertyRepository.findById(givenDeal.propertyId) } returns givenProperty

        snsDealEventPublisher.publish(givenDeal)
        verifyMessageWasSent(eventType = EventType.DEAL_UPDATE, deal = givenDeal, property = givenProperty)
    }

    @Test
    fun `it takes the property info from the repository`() {
        val givenDeal = DealBuilder().build()
        val givenProperty = PropertyBuilder().build()
        every { dealRepository.findByPropertyId(givenProperty.id) } returns givenDeal

        snsDealEventPublisher.publish(givenProperty)
        verifyMessageWasSent(eventType = EventType.DEAL_UPDATE, deal = givenDeal, property = givenProperty)
    }

    private fun verifyMessageWasSent(eventType: EventType, deal: Deal, property: Property) =
        verify {
            awsSNSClient.publish(
                message = JsonMapperCC.encode(
                    DealEvent(
                        type = eventType,
                        deal = deal,
                        property = property
                    )
                ),
                topicArn = snsConfig.dealEventsTopicArn
            )
        }
}
