package realestate.unlock.dealroom.api.infrastructure.gateway.property

import com.keyway.adapters.dtos.response.property.additional.multifamily.mortgages.MultifamilyMortgagesResponse
import com.keyway.adapters.dtos.response.property.additional.multifamily.sales.MultifamilySalesResponse
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.hamcrest.core.IsNull
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.SourceType
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.GeoPointResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyAssetsResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyType
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.MultifamilyAdditionalDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.MultifamilyRealEstateDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.units.MultifamilyUnitsResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.images.PropertyImagesResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapperCC
import java.math.BigDecimal

class GetPropertyDetailsResponseTest {

    @Test
    fun `can be deserialized from json`() {
        // given
        val expectedObject = PropertyAssetsResponse(
            id = "USWV-000045",
            address = "930 Benge Dr",
            fullAddress = "930 Benge Dr, Arlington, TX 76013, USA",
            city = "Arlington",
            county = "Tarrant County",
            zipCode = 76013,
            state = "Texas",
            tractCode = 48439122402,
            location = GeoPointResponse(
                latitude = BigDecimal.valueOf(32.7235632),
                longitude = BigDecimal.valueOf(-97.1201021)
            ),
            propertyType = PropertyType.MULTIFAMILY,
            squareFootage = BigDecimal.valueOf(130288),
            sourceType = SourceType.OFF_MARKET,
            additionalData = MultifamilyAdditionalDataResponse(
                realEstateData = MultifamilyRealEstateDataResponse(
                    source = "yardi",
                    name = "Maverick Place",
                    occupancyPercentage = BigDecimal.valueOf(0.99),
                    constructionYear = 2009,
                    productType = "Garden",
                    propertyClass = "B",
                    units = MultifamilyUnitsResponse(
                        records = listOf()
                    ),
                    communityAmenities = listOf(),
                    apartmentsAmenities = listOf(),
                    amenities = listOf(),
                    images = listOf()
                ),
                sales = MultifamilySalesResponse(records = listOf()),
                mortgages = MultifamilyMortgagesResponse(records = listOf())
            ),
            images = PropertyImagesResponse(
                urls = listOf("url1", "url2")
            ),
            constructionYear = 1980
        )

        // then
        assertThat(propertyAssetsResponse(), IsEqual(expectedObject))
    }

    @Test
    fun `can deserialize response without additionalData`() {
        val response = propertyAssetsResponse(json = givenJsonWithoutAdditionalData())
        assertThat(response.additionalData, IsNull())
    }
}

fun propertyAssetsResponse(propertyId: String = "USWV-000045", json: String = givenJson()): PropertyAssetsResponse {
    val detailResponse = JsonMapperCC.decode(json, PropertyAssetsResponse::class.java)
    return detailResponse.copy(id = propertyId)
}

fun givenJson() = """
        {
            "id": "USTX-014330",
            "address": "930 Benge Dr",
            "fullAddress": "930 Benge Dr, Arlington, TX 76013, USA",
            "city": "Arlington",
            "county": "Tarrant County",
            "zipCode": 76013,
            "state": "Texas",
            "tractCode": 48439122402,
            "constructionYear": 1980,
            "location": {
                "latitude": 32.7235632,
                "longitude": -97.1201021
            },
            "propertyType": "MULTIFAMILY",
            "squareFootage": 130288,
            "sourceType": "OFF_MARKET",
            "images": {
                "urls": ["url1", "url2"]
            },
            "additionalData": {
                "realEstateData": {
                    "source": "yardi",
                    "name": "Maverick Place",
                    "occupancyPercentage": 0.99,
                    "constructionYear": 2009,
                    "productType": "Garden",
                    "propertyClass": "B",
                    "units": {
                        "records": []
                    },
                    "communityAmenities": [],
                    "apartmentsAmenities": [],
                    "amenities": [],
                    "images": []
                },
                "sales": {
                    "records": []
                },
                "mortgages": {
                    "records": []
                }
            }
        }
""".trimIndent()

fun givenJsonWithoutAdditionalData() = """
    {
            "id": "USTX-014330",
            "address": "930 Benge Dr",
            "fullAddress": "930 Benge Dr, Arlington, TX 76013, USA",
            "city": "Arlington",
            "county": "Tarrant County",
            "zipCode": 76013,
            "state": "Texas",
            "tractCode": 48439122402,
            "location": {
                "latitude": 32.7235632,
                "longitude": -97.1201021
            },
            "propertyType": "MULTIFAMILY",
            "squareFootage": 130288,
            "sourceType": "OFF_MARKET"
    }
""".trimIndent()
