package realestate.unlock.dealroom.api.infrastructure.gateway.files

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.verify
import kong.unirest.HttpResponse
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.directory.UrlType
import realestate.unlock.dealroom.api.core.entity.kfile.FileToStaging
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.kfile.KFile
import realestate.unlock.dealroom.api.core.gateway.kfileservice.StagingFileToConfirm
import realestate.unlock.dealroom.api.infrastructure.client.rest.KFileRestClient
import realestate.unlock.dealroom.api.infrastructure.utils.extension.mapTo
import realestate.unlock.dealroom.api.infrastructure.utils.file.SanitizedFilename
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.creator.FileCreator
import realestate.unlock.dealroom.api.utils.entity.task.file.TaskFileObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.io.ByteArrayInputStream
import java.io.File
import java.time.Instant
import kotlin.io.path.ExperimentalPathApi

@ExtendWith(MockKExtension::class)
class KFileServiceFileGatewayTest {

    private val filename = SanitizedFilename("file.name")

    companion object {
        private const val APP_ID = "deal-room"
    }

    @MockK(relaxed = true)
    private lateinit var kFileRestClient: KFileRestClient

    private lateinit var kFileServiceTaskFileGateway: KFileServiceFileGateway

    @BeforeEach
    fun setUp() {
        mockkStatic("realestate.unlock.dealroom.api.infrastructure.utils.extension.HttpResponseExtensionKt")
        kFileServiceTaskFileGateway = KFileServiceFileGateway(
            kFileRestClient = kFileRestClient
        )
    }

    @Test
    fun `can confirm a file`() {
        // given
        val filesToConfirm = listOf(giveStagingFileToConfirm(), giveStagingFileToConfirm())

        // when
        kFileServiceTaskFileGateway.confirmStagingFile(filesToConfirm)

        // then
        val expectedBody = KFileServiceFileGateway.ConfirmStagingFiles(
            applicationId = APP_ID,
            stagedFiles = filesToConfirm.map { KFileServiceFileGateway.StagedFiles(id = it.kFileId, path = it.path) }
        )
        verify {
            kFileRestClient.restClient.put(
                path = "files/$APP_ID/staged",
                body = JsonMapper.encode(expectedBody)
            )
        }
    }

    @Test
    fun `can get file presigned url`() {
        // given
        val taskFile = TaskFileObjectMother.taskFile()
        val presignedUrl = anyString()
        val fileName = anyString()
        mockGetPresignedUrlHttpResponse(taskFile.kFileId, presignedUrl, fileName)

        // when
        val result = kFileServiceTaskFileGateway.getFileWithUrl(taskFile.kFileId, downloadName = null)

        // then
        assertThat(result.kFileId, IsEqual(taskFile.kFileId))
        assertThat(result.url, IsEqual(presignedUrl))
        Assertions.assertEquals(result.name, fileName)
        verify {
            kFileRestClient.restClient.get(
                path = "files/$APP_ID/${taskFile.kFileId}",
                responseType = String::class.java
            )
        }
    }

    @Test
    fun `can get file presigned url with download as param`() {
        // given
        val taskFile = TaskFileObjectMother.taskFile()
        val presignedUrl = anyString()
        mockGetPresignedUrlHttpResponse(taskFile.kFileId, presignedUrl, anyString())

        // when
        val result = kFileServiceTaskFileGateway.getFileWithUrl(taskFile.kFileId, downloadName = filename)

        // then
        assertThat(result.kFileId, IsEqual(taskFile.kFileId))
        assertThat(result.url, IsEqual(presignedUrl))
        verify {
            kFileRestClient.restClient.get(
                path = "files/$APP_ID/${taskFile.kFileId}",
                responseType = String::class.java,
                queryParams = mapOf(
                    "download-name" to filename.value
                )
            )
        }
    }

    @Test
    fun `can upload a file`() {
        // given
        val kFileId = anyString()
        val fileName = anyString()
        val stagingFile = FileToStaging(
            content = ByteArrayInputStream(anyString().toByteArray()),
            contentType = "text/plain",
            filename = fileName
        )
        mockPostFileHttpResponse(kFileId, stagingFile)

        // when
        val result = kFileServiceTaskFileGateway.uploadStagedFile(stagingFile)

        // then
        assertThat(result, IsEqual(KFile(kFileId, fileName)))
        verify {
            kFileRestClient.restClient.post(
                path = "files/$APP_ID/staged",
                fileName = stagingFile.filename,
                contentType = stagingFile.contentType,
                body = stagingFile.content
            )
        }
    }

    @Test
    fun `can upload a file confirmed`() {
        // given
        val kFileId = anyString()
        val fileName = anyString()
        val confirmFile = FileToUpload(
            content = ByteArrayInputStream(anyString().toByteArray()),
            contentType = "text/plain",
            filename = fileName,
            path = "/pepe"
        )
        mockPostFileHttpResponse(
            kFileId,
            confirmFile
        )

        // when
        val result = kFileServiceTaskFileGateway.uploadFile(confirmFile)

        // then
        assertThat(result, IsEqual(KFile(kFileId, fileName)))
        verify {
            kFileRestClient.restClient.post(
                path = "files/$APP_ID",
                fileName = confirmFile.filename,
                contentType = confirmFile.contentType,
                body = confirmFile.content,
                queryParams = mapOf("path" to confirmFile.path),
                headers = any()
            )
        }
    }

    @OptIn(ExperimentalPathApi::class)
    @Test
    fun `can get file`() {
        // given
        val taskFile = TaskFileObjectMother.taskFile()
        val fileName = "ESTO_ES-UN_NOMBRE.PDF"
        val givenFile = FileCreator.create(fileName = fileName)
        val presignedUrl = "http://localhost:8081"

        mockGetPresignedUrlHttpResponse(taskFile.kFileId, presignedUrl, fileName)
        mockGetFileFromPresignedUrlHttpResponse(presignedUrl, givenFile)

        // when
        val result = kFileServiceTaskFileGateway.getFile(taskFile.kFileId, fileName)

        // then
        assertThat(result.kFileId, IsEqual(taskFile.kFileId))
        assertThat(result.file.inputStream().readAllBytes(), IsEqual(givenFile.inputStream().readAllBytes()))
    }

    @Test
    fun `can upload file url`() {
        val path = "pepe/pepe.pdf"
        val url = mockGetFileUrlHttpResponse(path, urlType = UrlType.UPLOAD)
        var result = kFileServiceTaskFileGateway.getFileUploadUrl(path)
        Assertions.assertEquals(url.url, result)
    }

    @Test
    fun `can download file url`() {
        val path = "pepe/pepe.pdf"
        val url = mockGetFileUrlHttpResponse(path, urlType = UrlType.DOWNLOAD)
        var result = kFileServiceTaskFileGateway.getFileDownloadUrl(path)
        Assertions.assertEquals(url.url, result)
    }

    private fun mockGetFileUrlHttpResponse(path: String, urlType: UrlType): KFileServiceFileGateway.FileUrl {
        val httpResponse = mockk<HttpResponse<String>>()
        every {
            kFileRestClient.restClient.get(
                path = "files-url/$APP_ID",
                responseType = String::class.java,
                queryParams = mapOf(
                    "path" to path,
                    "url-type" to urlType.name
                )
            )
        } returns httpResponse
        return KFileServiceFileGateway.FileUrl(
            url = anyString(),
            urlType = urlType
        ).also {
            every { httpResponse.body } returns JsonMapper.encode(
                it
            )
        }
    }

    private fun mockGetPresignedUrlHttpResponse(kFileId: String, presignedUrl: String, fileName: String) {
        val httpResponse = mockk<HttpResponse<String>>()
        every {
            kFileRestClient.restClient.get(
                path = "files/$APP_ID/$kFileId",
                queryParams = any(),
                headers = any(),
                requestHandler = any(),
                responseType = String::class.java
            )
        } returns httpResponse
        every { httpResponse.body } returns JsonMapper.encode(
            KFileServiceFileGateway.KFileResponse(
                url = presignedUrl,
                name = fileName,
                sizeInBytes = 0,
                lastModified = Instant.now(),
                id = anyString(),
                path = anyString()
            )
        )
    }

    private fun mockGetFileFromPresignedUrlHttpResponse(presignedUrl: String, file: File) {
        val httpResponse = mockk<HttpResponse<File>>()
        every {
            kFileRestClient.getClientForUrl(presignedUrl)
                .get(
                    path = "",
                    responseType = File::class.java,
                    fileName = file.name
                )
        } returns httpResponse
        every { httpResponse.body } returns file
    }

    private fun mockPostFileHttpResponse(kFileId: String, stagingFile: FileToStaging) {
        val httpResponse = mockk<HttpResponse<String>>()
        every {
            kFileRestClient.restClient.post(
                path = "files/$APP_ID/staged",
                fileName = stagingFile.filename,
                contentType = stagingFile.contentType,
                body = stagingFile.content,
                headers = any()
            )
        } returns httpResponse
        every { httpResponse.mapTo(KFileServiceFileGateway.StageFileUpload::class.java) } returns KFileServiceFileGateway.StageFileUpload(id = kFileId, uploadUrl = null)
    }

    private fun mockPostFileHttpResponse(kFileId: String, fileToUpload: FileToUpload) {
        val httpResponse = mockk<HttpResponse<String>>()
        every {
            kFileRestClient.restClient.post(
                path = "files/$APP_ID",
                fileName = fileToUpload.filename,
                contentType = fileToUpload.contentType,
                body = fileToUpload.content,
                queryParams = mapOf("path" to fileToUpload.path),
                headers = any()
            )
        } returns httpResponse
        every { httpResponse.mapTo(KFileServiceFileGateway.StageFileUpload::class.java) } returns KFileServiceFileGateway.StageFileUpload(id = kFileId, uploadUrl = null)
    }

    private fun giveStagingFileToConfirm() = StagingFileToConfirm(path = anyString(), kFileId = anyString())
}
