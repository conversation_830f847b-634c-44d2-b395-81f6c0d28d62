package realestate.unlock.dealroom.api.infrastructure.gateway.splitio

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.split.client.SplitClient
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.utils.extensions.anyString

@ExtendWith(MockKExtension::class)
class FeatureFlagsSplitIoTest {
    @MockK(relaxed = true)
    private lateinit var client: SplitClient
    private lateinit var featureFlags: FeatureFlagsSplitIo

    @BeforeEach
    fun setUp() {
        featureFlags = FeatureFlagsSplitIo(client)
    }

    @Test
    fun `return true when feature is on`() {
        val user = anyString()
        val feature = givenFeature(user = user, on = true)

        val result = featureFlags.isOn(feature, user)

        assertTrue(result)
    }

    @Test
    fun `return false when feature is on`() {
        val user = anyString()
        val feature = givenFeature(user = user, on = false)

        val result = featureFlags.isOn(feature, user)

        assertFalse(result)
    }

    private fun givenFeature(user: String, on: Boolean, context: Map<String, Any> = emptyMap()) =
        Feature.CLOSING.also { feature ->
            every {
                client.getTreatment(user, feature.value, context)
            } returns if (on) "on" else "control"
        }
}
