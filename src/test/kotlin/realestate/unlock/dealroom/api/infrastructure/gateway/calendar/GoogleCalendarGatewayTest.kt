package realestate.unlock.dealroom.api.infrastructure.gateway.calendar

import com.google.api.services.calendar.Calendar
import com.google.api.services.calendar.model.Acl
import com.google.api.services.calendar.model.AclRule
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.gateway.calendar.CreateDealCalendarInput
import realestate.unlock.dealroom.api.core.gateway.calendar.DealCalendar
import realestate.unlock.dealroom.api.utils.extensions.anyString

class GoogleCalendarGatewayTest {

    private lateinit var gateway: GoogleCalendarGateway
    private val calendar: Calendar = mockk(relaxed = true)

    @BeforeEach
    fun setUp() {
        gateway = GoogleCalendarGateway(
            googleCalendarService = calendar
        )
    }

    @Test
    fun `can create calendar and send acl's`() {
        val input = CreateDealCalendarInput(
            description = "A calendar",
            summary = "A mega calendar",
            members = setOf("<EMAIL>")
        )
        val calendarId = anyString()
        every {
            calendar.calendars().insert(
                com.google.api.services.calendar.model.Calendar().apply {
                    description = input.description
                    summary = input.summary
                    timeZone = "America/New_York"
                }
            ).execute()
        } returns com.google.api.services.calendar.model.Calendar().apply {
            description = input.description
            summary = input.summary
            id = calendarId
        }

        every {
            calendar.acl().insert(
                calendarId,
                AclRule().apply {
                    scope = AclRule.Scope().apply {
                        type = "user"
                        value = "<EMAIL>"
                    }
                    role = "reader"
                }
            ).execute()
        } returns AclRule()

        val newCalendarID = gateway.createCalendar(input)

        Assertions.assertEquals(calendarId, newCalendarID)
    }

    @Test
    fun `can update calendar and acl's`() {
        val dealCalendar = DealCalendar(
            id = anyString(),
            description = "ONE CALENDAR",
            summary = "TWO CALENDARS",
            members = setOf("<EMAIL>", "<EMAIL>")
        )

        val gCalendar = com.google.api.services.calendar.model.Calendar().apply {
            description = dealCalendar.description
            summary = dealCalendar.summary
            id = dealCalendar.id
        }

        every { calendar.calendars().get(dealCalendar.id).execute() } returns gCalendar
        every { calendar.calendars().update(dealCalendar.id, gCalendar).execute() }
        val acelIdToDelete = anyString()
        val acls = Acl().apply {
            items = listOf(
                AclRule().apply {
                    scope =
                        AclRule.Scope().apply {
                            id = anyString()
                            type = "user"
                            value = "<EMAIL>"
                        }
                    role = "reader"
                },
                AclRule().apply {
                    scope =
                        AclRule.Scope().apply {
                            id = acelIdToDelete
                            type = "user"
                            value = "<EMAIL>"
                        }
                    role = "reader"
                }
            )
        }
        every { calendar.acl().list(dealCalendar.id).execute() } returns acls
        every {
            calendar.acl().insert(
                dealCalendar.id,
                AclRule().apply {
                    scope = AclRule.Scope().apply {
                        type = "user"
                        value = "<EMAIL>"
                    }
                    role = "reader"
                }
            ).execute()
        } returns AclRule()
        every {
            calendar.acl().delete(dealCalendar.id, acelIdToDelete)
                .execute()
        } returns null
        gateway.updateCalendar(dealCalendar)
    }
}
