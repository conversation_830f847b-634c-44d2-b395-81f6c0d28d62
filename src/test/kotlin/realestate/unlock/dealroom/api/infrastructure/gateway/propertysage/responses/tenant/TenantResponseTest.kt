package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.tenant

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.time.OffsetDateTime

class TenantResponseTest {

    @Test
    fun `it serializes the response properly`() {
        val response = JsonMapper.decode(givenJson(), TenantResponse::class.java)

        assertThat(response.id, equalTo(34037))
        assertThat(response.propertyId, equalTo("USCA-001078"))
        assertThat(response.name, equalTo("Inland Eye Institute Medical Group, Inc."))
        assertThat(response.industryNaicsCode, equalTo(621111L))
        assertThat(response.industrySicsCode, equalTo(80110513L))
        assertThat(response.employeeQuantity, equalTo(130))
        assertThat(response.selected, equalTo(true))
        assertThat(response.industryClusterName, equalTo("Eye doctors"))
        assertThat(response.createdAt, equalTo(OffsetDateTime.parse("2022-08-03T01:20:36.661Z")))
        assertThat(response.updatedAt, equalTo(OffsetDateTime.parse("2022-08-03T01:20:36.661Z")))
    }

    fun givenJson() = """
        {
            "id": 34037,
            "propertyId": "USCA-001078",
            "name": "Inland Eye Institute Medical Group, Inc.",
            "industryNaicsCode": 621111,
            "industrySicsCode": 80110513,
            "employeeQuantity": 130,
            "selected": true,
            "industryClusterName": "Eye doctors",
            "createdAt": "2022-08-03T01:20:36.661Z",
            "updatedAt": "2022-08-03T01:20:36.661Z"
        }
    """.trimIndent()
}
