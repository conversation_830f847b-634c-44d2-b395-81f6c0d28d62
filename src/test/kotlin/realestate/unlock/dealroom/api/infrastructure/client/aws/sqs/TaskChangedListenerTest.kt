package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.usecase.task.update.NotifyMembersAboutTaskChanged
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.TaskChangedListener
import realestate.unlock.dealroom.api.utils.extensions.anyString
import software.amazon.awssdk.services.sqs.model.Message

class TaskChangedListenerTest {

    private lateinit var notifyMembersAboutTaskChanged: NotifyMembersAboutTaskChanged

    private lateinit var taskChangedListener: TaskChangedListener

    @BeforeEach
    fun setUp() {
        notifyMembersAboutTaskChanged = mockk()
        taskChangedListener = TaskChangedListener(
            notifyMembersAboutTaskChanged = notifyMembersAboutTaskChanged
        )
    }

    @Test
    fun `can notify about task changed`() {
        // given
        val taskId = 989L
        val sqsMessage = givenSqsMessage(taskId)
        every { notifyMembersAboutTaskChanged(taskId) } returns Unit

        // when
        val result = taskChangedListener.processMessage(sqsMessage)

        // then
        assertThat(result, equalTo(true))
        verify {
            notifyMembersAboutTaskChanged.invoke(taskId)
        }
    }

    @Test
    fun `can handle an error on notify about task changed`() {
        // given
        val taskId = 9675L
        val sqsMessage = givenSqsMessage(taskId)
        every { notifyMembersAboutTaskChanged(taskId) } throws RuntimeException("test")

        // when
        val result = taskChangedListener.processMessage(sqsMessage)

        // then
        assertThat(result, equalTo(false))
    }

    private fun givenSqsMessage(taskId: Long) =
        Message.builder().messageId(anyString()).body(givenMessageBody(taskId)).build()

    private fun givenMessageBody(taskId: Long) = """
        {
          "Type" : "Notification",
          "MessageId" : "bf769080-cdf5-5a1b-bd3c-cae03b57e4ec",
          "TopicArn" : "arn:aws:sns:us-east-1:681574592108:dev-deal_room-task_updated",
          "Message" : "{\"id\":$taskId,\"deal_category_id\":798,\"deal_id\":81,\"title\":\"Zoning\",\"status\":\"done\",\"updated_at\":\"2022-01-18T17:09:54.481Z\"}",
          "Timestamp" : "2022-01-18T17:09:54.545Z",
          "SignatureVersion" : "1",
          "Signature" : "WF662zusmbnS4m0K7azGZPUo7jLHwHpMrvlgfnAyK+LxJcA3KyscYdBZFeem3Rq8vt+ZWGFfL8pK/YHYPoZF6VCzh3TRwE3cFCiNZhZHgNBC3I1BIKU6vUZb5oLdqKm8MG4s+fHUMME7sgyZSR2rDo5vzRGg5uwUCYbk9VsJ8fJ3dX6lNd+SDPJfzxrIk07Pss+WUiknj69If+RpfrQXsDtlRidNv3W7whRxP+UcrhKZv5/Rx4WDa35/Dm7YvcrVZG8NbEJQ6GhA5HqETbzYBEZuIHw1oGxanDMBIcTPJe4UWpg8UGNFGcGSo+o6X8R0QOdC5MOqx50BavCYLJGZxw==",
          "SigningCertURL" : "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-7ff5318490ec183fbaddaa2a969abfda.pem",
          "UnsubscribeURL" : "https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:681574592108:dev-deal_room-task_updated:3774db86-a5ff-47d6-8b3c-a8e986e850a8"
        }
    """.trimIndent()
}
