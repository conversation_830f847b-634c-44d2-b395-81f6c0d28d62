package realestate.unlock.dealroom.api.infrastructure.gateway.email

import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.gateway.email.*
import realestate.unlock.dealroom.api.infrastructure.configuration.model.SendgridConfig
import realestate.unlock.dealroom.api.infrastructure.utils.file.SanitizedFilename
import java.util.*

class SendgridEmailGatewayTest {

    private val sendgridMailSender: SendgridMailSender = mockk(relaxed = true)
    private val sendgridConfig = SendgridConfig(
        apiKey = "apiKey",
        emailFrom = "<EMAIL>",
        templates = mapOf(
            "some_template" to "some_template_id"
        )
    )

    private val target = SendgridEmailGateway(
        sendgridMailSender = sendgridMailSender,
        sendgridConfig = sendgridConfig
    )

    private val givenInputStream = "This is some text".byteInputStream()

    @Test
    fun `maps to sendgrid templated email`() {
        // given
        val givenEmailData = givenTemplatedEmailData()

        // when
        target.sendTemplated(givenEmailData)

        // then
        verify {
            sendgridMailSender.invoke(
                match {
                    it.from.email == sendgridConfig.emailFrom &&
                        it.templateId == sendgridConfig.templates.values.first() &&
                        it.personalization.first().tos.first().email == givenEmailData.toEmailAddress.first() &&
                        it.personalization.first().dynamicTemplateData.keys.first() == givenEmailData.templateParams.keys.first() &&
                        it.personalization.first().dynamicTemplateData.values.first() == givenEmailData.templateParams.values.first()
                }
            )
        }
    }

    @Test
    fun `maps to sendgrid email with default from`() {
        // given
        val givenEmailData = givenEmailData()
        val expectedContent = Base64.getEncoder().encodeToString(givenInputStream.readBytes())
        givenInputStream.reset()

        // when
        target.send(givenEmailData)

        // then
        verify {
            sendgridMailSender.invoke(
                match {
                    it.from.email == sendgridConfig.emailFrom &&
                        it.personalization.first().tos.first().email == givenEmailData.toEmailAddress &&
                        it.personalization.first().ccs.first().email == givenEmailData.carbonCopies.first() &&
                        it.subject == "some subject replaced" &&
                        it.content.first().type == givenEmailData.body.contentType.value &&
                        it.content.first().value == "sample body replaced" &&
                        it.attachments.first().filename == "test.txt" &&
                        it.attachments.first().content == expectedContent
                }
            )
        }
    }

    @Test
    fun `maps to sendgrid email with custom from`() {
        // given
        val givenEmailData = givenEmailData().copy(fromEmailAddress = "<EMAIL>")
        val expectedContent = Base64.getEncoder().encodeToString(givenInputStream.readBytes())
        givenInputStream.reset()

        // when
        target.send(givenEmailData)

        // then
        verify {
            sendgridMailSender.invoke(
                match {
                    it.from.email == givenEmailData.fromEmailAddress!! &&
                        it.personalization.first().tos.first().email == givenEmailData.toEmailAddress &&
                        it.personalization.first().ccs.first().email == givenEmailData.carbonCopies.first() &&
                        it.subject == "some subject replaced" &&
                        it.content.first().type == givenEmailData.body.contentType.value &&
                        it.content.first().value == "sample body replaced" &&
                        it.attachments.first().filename == "test.txt" &&
                        it.attachments.first().content == expectedContent
                }
            )
        }
    }

    private fun givenTemplatedEmailData(): TemplatedEmailData {
        return TemplatedEmailData(
            toEmailAddress = setOf("<EMAIL>"),
            templateKey = "some_template",
            templateParams = mapOf(
                "param1" to "param1_value"
            )
        )
    }

    private fun givenEmailData(): EmailData {
        return EmailData(
            toEmailAddress = "<EMAIL>",
            carbonCopies = setOf("<EMAIL>"),
            subject = "some subject {replace_this}",
            body = Body(
                content = "sample body {replace_this}",
                contentType = BodyContentType.TEXT_PLAIN
            ),
            replacementTokens = mapOf(
                "{replace_this}" to "replaced"
            ),
            attachments = listOf(
                EmailAttachment(
                    filename = SanitizedFilename("test.txt"),
                    content = givenInputStream
                )
            )
        )
    }
}
