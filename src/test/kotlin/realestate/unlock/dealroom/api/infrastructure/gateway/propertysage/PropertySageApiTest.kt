package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.mockkStatic
import kong.unirest.HttpResponse
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.infrastructure.client.rest.PropertySageRestClient
import realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.property.GetPropertyDetailsResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.property.MultifamilyProperty
import realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.property.PropertyResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class PropertySageApiTest {

    @MockK(relaxed = true)
    private lateinit var propertySageRestClient: PropertySageRestClient

    private lateinit var propertySageApi: PropertySageApi

    @BeforeEach
    fun setUp() {
        mockkStatic("realestate.unlock.dealroom.api.infrastructure.utils.extension.HttpResponseExtensionKt")
        propertySageApi = PropertySageApi(propertySageRestClient = propertySageRestClient)
    }

    private fun givenMultifamilyProperty(propertyId: String): GetPropertyDetailsResponse {
        val httpResponse = mockk<HttpResponse<String>>()
        every { httpResponse.body } returns multifamilyBody
        every {
            propertySageRestClient.restClient.get(
                path = "/properties/$propertyId/multifamily",
                responseType = String::class.java
            )
        } returns httpResponse
        return GetPropertyDetailsResponse(PropertyResponse(JsonMapper.decode(multifamilyBody, MultifamilyProperty::class.java)))
    }

    private fun givenPropertyDetails(propertyId: String): GetPropertyDetailsResponse {
        val httpResponse = mockk<HttpResponse<String>>()
        val getPropertyDetailsResponse = givenGetPropertyDetailsResponse(propertyId)
        every {
            propertySageRestClient.restClient.get(

                path = "/properties/$propertyId",
                responseType = String::class.java
            )
        } returns httpResponse
        every { httpResponse.body } returns JsonMapper.encode(getPropertyDetailsResponse)
        return getPropertyDetailsResponse
    }

    private fun givenGetPropertyDetailsResponse(propertyId: String) = GetPropertyDetailsResponse(
        property = PropertyResponse(
            id = propertyId,
            address = anyString(),
            fullAddress = anyString(),
            keywayScore = 10,
            city = anyString(),
            state = anyString(),
            price = BigDecimal.TEN,
            squareFootage = BigDecimal.TEN,
            leaseType = "NNN",
            rentBumpsDescription = null,
            constructionYear = 1998,
            propertyType = anyString(),
            sourceType = anyString(),
            leaseLength = null,
            latitude = BigDecimal.valueOf(37.8004511),
            longitude = BigDecimal.valueOf(-81.2289062),
            rentBumpsFrequencyInYears = BigDecimal.TEN,
            optionLength = BigDecimal.ONE,
            numberOfOptions = BigDecimal.ONE,
            diligencePeriod = BigDecimal.TEN,
            closingPeriod = BigDecimal.TEN,
            closingPeriodExtension = BigDecimal.valueOf(5),
            earnestMoneyDeposit = BigDecimal.valueOf(4),
            closingExtensionDeposit = BigDecimal.valueOf(3),
            contractExecutionDate = LocalDate.parse("2022-02-02"),
            guaranteeDetails = "Corporate",
            leaseCondition = "Lease condition",
            creditType = "Credit type",
            rentStepType = "Rent step type",
            cpiMultiplier = BigDecimal.valueOf(7),
            rentBumpsAmount = BigDecimal.valueOf(11)
        )
    )

    private val multifamilyBody = """
    {
"id": "USTX-015937",
"name": "Duplex in Central Austin",
"address": "6100 Calmar Cove",
"fullAddress": "6100 Calmar Cove, Austin, TX 78721, USA",
"latitude": 30.2566395,
"longitude": -97.6836178,
"squareFootage": 2071,
"score": 60,
"zipCode": 78721,
"city": "Austin",
"county": "Travis County",
"state": "TX",
"askingPrice": 799995,
"askingPricePerUnit": 399997.5,
"constructionYear": 1972,
"totalUnitsQuantity": 2,
"occupancyPercentage": 100,
"tractCode": 48453002111,
"sourceType": "on_market",
"propertyType": "multifamily",
"propertyUnits": [],
"offerCapRate": 5.52,
"squareFootagePerUnit": 1035.5,
"productType": "Garden"
}
"""
}
