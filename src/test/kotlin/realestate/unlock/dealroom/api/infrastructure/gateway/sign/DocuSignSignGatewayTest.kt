package realestate.unlock.dealroom.api.infrastructure.gateway.sign

import com.docusign.esign.api.EnvelopesApi
import com.docusign.esign.model.*
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.apache.http.client.utils.URIBuilder
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.core.gateway.sign.*
import realestate.unlock.dealroom.api.core.gateway.sign.Contact
import realestate.unlock.dealroom.api.core.gateway.sign.Recipients
import realestate.unlock.dealroom.api.core.gateway.sign.Signer
import realestate.unlock.dealroom.api.core.gateway.sign.Tabs
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DocuSignConfig
import realestate.unlock.dealroom.api.infrastructure.utils.file.SanitizedFilename
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.net.URI
import java.time.Duration

class DocuSignSignGatewayTest {

    private val documentId: Long = 1
    private val buyerId: String = "1"
    private val sellerId: String = "2"
    private val emailParams = EmailParams(subject = "subject", body = "body")

    private val docuSignConfig = DocuSignConfig(
        keyFileName = "keyFileName",
        appIntegrationKey = "appIntegrationKey",
        userId = "userId",
        tokenExpirationSeconds = 1,
        accountId = "accountId",
        keyEnvVar = null,
        baseUrl = "https://demo.docusign.net/restapi"
    )
    private val expectedSignViewUrl = "http://some-view-url.com"
    private val expectedCarbonCopyViewUrl = "http://some-carbon-copy-view-url.com"
    private val expectedEnvelopeId = "envelopeId"
    private val expectedEnvelopeId2 = "envelopeId2"
    private val editEnvelopeViewUrl = ViewUrl().apply { url = "http://altaurl.com/nose" }
    private val returnURL = "http://estodevuelta.com/"
    private val editEnvelopeViewRequest = CorrectViewRequest().returnUrl(URIBuilder(returnURL).build().normalize().toString())
    private val envelopesApi: EnvelopesApi = mockk {

        every { createEnvelope(eq(docuSignConfig.accountId), any()) } returns EnvelopeSummary().apply {
            envelopeId = expectedEnvelopeId
        }
        every { createRecipientView(docuSignConfig.accountId, expectedEnvelopeId, any()) } returns ViewUrl().apply {
            url = expectedSignViewUrl
        }
        every { createRecipientView(docuSignConfig.accountId, expectedEnvelopeId2, any()) } returns ViewUrl().apply {
            url = expectedCarbonCopyViewUrl
        }
        every { createCorrectView(docuSignConfig.accountId, expectedEnvelopeId, editEnvelopeViewRequest) } returns editEnvelopeViewUrl
    }
    private val docuSignApiProvider: DocuSignApiProvider = mockk {
        every { createEnvelopesApi() } returns envelopesApi
    }
    private val target = DocuSignSignGateway(docuSignConfig, docuSignApiProvider)

    @Test
    fun `creates envelope from input`() {
        // given
        val givenInput = givenBeginSignFlowInput()

        // when
        val result = target.beginFlow(givenInput)

        // then
        verify(exactly = 1) {
            envelopesApi.createEnvelope(
                docuSignConfig.accountId,
                expectedRequestFromInput(givenInput)
            )
        }
        assertThat(result.signingId, IsEqual(expectedEnvelopeId))
    }

    @Test
    fun `creates envelope from input with reminder`() {
        // given
        val givenInput = givenBeginSignFlowInput()
            .copy(reminder = Reminder(delay = Duration.ofDays(7)))
        val expectedRequestFromInput = expectedRequestFromInput(givenInput)
            .apply {
                notification(
                    Notification()
                        .reminders(
                            Reminders()
                                .reminderEnabled("true")
                                .reminderDelay("7")
                        )
                )
            }

        // when
        val result = target.beginFlow(givenInput)

        // then
        verify(exactly = 1) {
            envelopesApi.createEnvelope(
                docuSignConfig.accountId,
                expectedRequestFromInput
            )
        }
        assertThat(result.signingId, IsEqual(expectedEnvelopeId))
    }

    @Test
    fun `retrieves signer view url`() {
        // given
        val givenInput = givenRetrieveViewUrlInput()

        // when
        val result = target.retrieveSignerViewUrl(givenInput)

        // then
        verify(exactly = 1) {
            envelopesApi.createRecipientView(
                docuSignConfig.accountId,
                expectedEnvelopeId,
                expectedRequestFromInput(givenInput)
            )
        }
        assertThat(result.viewUrl.toString(), IsEqual(expectedSignViewUrl))
    }

    @Test
    fun `retrieves carbon copy view url`() {
        // given
        val givenInput = givenRetrieveViewUrlInput(envelopeId = expectedEnvelopeId2)

        // when
        val result = target.retrieveSignerViewUrl(givenInput)

        // then
        verify(exactly = 1) {
            envelopesApi.createRecipientView(
                docuSignConfig.accountId,
                expectedEnvelopeId2,
                expectedRequestFromInput(givenInput)
            )
        }
        assertThat(result.viewUrl.toString(), IsEqual(expectedCarbonCopyViewUrl))
    }

    @Test
    fun `can retrieve a document`() {
        // given
        val input = RetrieveDocumentInput(signingId = anyString(), documentId = "1")
        val documentContent = givenDocumentFor(input)

        // when
        val result = target.retrieveDocument(input)

        // then
        verify(exactly = 1) {
            envelopesApi.getDocument(docuSignConfig.accountId, input.signingId, input.documentId)
        }
        assertThat(result.documentId, IsEqual(input.documentId))
        assertThat(result.content.readBytes(), IsEqual(documentContent.inputStream().readBytes()))
    }

    @Test
    fun `if it fails to retrieve a document, the error is handled`() {
        val input = RetrieveDocumentInput(signingId = anyString(), documentId = "1")
        every { envelopesApi.getDocument(any(), any(), any()) } throws RuntimeException()

        assertThrows<DocusignException> { target.retrieveDocument(input) }
    }

    @Test
    fun `can retrieve correct view url`() {
        val input = RetrieveEditEnvelopeViewUrlInput(
            envelopId = expectedEnvelopeId,
            integrationSettings = IntegrationSettings(URIBuilder(returnURL).build().normalize(), 1, URI.create("http://estodevuelta.com/"))
        )
        val response = target.retrieveEditEnvelopeViewUrl(input)

        Assertions.assertEquals(
            editEnvelopeViewUrl.url.plus("&sendButtonAction=send")
                .plus("&backButtonAction=redirect")
                .plus("&showBackButton=false")
                .plus("&showEditRecipients=false")
                .plus("&showEditDocuments=true")
                .plus("&showEditDocumentVisibility=false")
                .plus("&showMatchingTemplatesPrompt=false")
                .plus("&showHeaderActions=false")
                .plus("&showDiscardAction=true")
                .plus("&send=1")
                .plus("&tabPaletteType=standard"),
            response.toString()
        )
    }

    private fun givenDocumentFor(input: RetrieveDocumentInput): ByteArray {
        return anyString().toByteArray().also {
            every { envelopesApi.getDocument(docuSignConfig.accountId, input.signingId, input.documentId.toString()) } returns it
        }
    }

    @Test
    fun `can resend a document`() {
        val input =
            ResendDocumentInput(signingId = anyString())
        val recipients = mockk<com.docusign.esign.model.Recipients>()
        every { envelopesApi.listRecipients(docuSignConfig.accountId, input.signingId) } returns recipients
        every { envelopesApi.updateRecipients(any(), any(), any(), any()) } returns mockk()

        target.resendDocument(input)

        verify { envelopesApi.listRecipients(docuSignConfig.accountId, input.signingId) }
        verify { envelopesApi.updateRecipients(docuSignConfig.accountId, input.signingId, recipients, match { it.resendEnvelope == "true" }) }
    }

    private fun givenBeginSignFlowInput(): BeginSignFlowInput =
        BeginSignFlowInput(
            docusignDocument = DocusignDocument(
                id = documentId.toString(),
                name = SanitizedFilename("loi.pdf"),
                extension = "pdf",
                sourceUrl = URI.create("http://www.some-url.com")
            ),
            recipients = Recipients(
                signers = listOf(
                    Signer(buyerId, "name", "email", emailParams),
                    Signer(sellerId, "name", "email", emailParams)
                ),
                carbonCopies = listOf(
                    Contact(99, "contact_name", "contact_email", emailParams)
                )
            ),
            tabs = Tabs(
                signHere = listOf(
                    FormTab(
                        name = "buyer_signature",
                        anchorString = "/buyer_signature/",
                        signerId = buyerId,
                        offsetX = 0,
                        offsetY = 0
                    ),
                    FormTab(
                        name = "seller_signature",
                        anchorString = "/seller_signature/",
                        signerId = sellerId,
                        offsetX = 0,
                        offsetY = 0
                    )
                ),
                dateSigned = listOf(
                    FormTab(
                        name = "buyer_date_signed",
                        anchorString = "/buyer_date_signed/",
                        signerId = buyerId,
                        offsetX = 0,
                        offsetY = 0
                    ),
                    FormTab(
                        name = "seller_date_signed",
                        anchorString = "/seller_date_signed/",
                        signerId = sellerId,
                        offsetX = 0,
                        offsetY = 0
                    )
                ),
                company = listOf(
                    FormTab(
                        name = "seller_company",
                        anchorString = "/seller_company/",
                        signerId = sellerId,
                        offsetX = 0,
                        offsetY = 0
                    )
                ),
                fullName = listOf(
                    FormTab(
                        name = "seller_fullname",
                        anchorString = "/seller_fullname/",
                        signerId = sellerId,
                        offsetX = 0,
                        offsetY = 0
                    )
                ),
                title = listOf(
                    FormTab(
                        name = "seller_title",
                        anchorString = "/seller_title/",
                        signerId = sellerId,
                        offsetX = 0,
                        offsetY = 0
                    )
                )
            ),
            envelopeStatus = EnvelopeStatus.SENT,
            signDocumentType = SignDocumentType.LOI
        )

    private fun expectedRequestFromInput(givenInput: BeginSignFlowInput): EnvelopeDefinition =
        EnvelopeDefinition().apply {
            status = EnvelopeStatus.SENT.toApiParameter()
            customFields = CustomFields()
                .addTextCustomFieldsItem(
                    TextCustomField()
                        .name(DocuSignSignGateway.CUSTOM_DOCUMENT_TYPE_METADATA)
                        .value(SignDocumentType.LOI.name)
                )
            documents = mutableListOf(
                Document().apply {
                    documentId = givenInput.docusignDocument.id.toString()
                    name = givenInput.docusignDocument.name.value
                    fileExtension = givenInput.docusignDocument.extension
                    remoteUrl = givenInput.docusignDocument.sourceUrl.toString()
                }
            )
            recipients = com.docusign.esign.model.Recipients().apply {
                carbonCopies = givenInput.recipients.carbonCopies.map { carbonCopy ->
                    CarbonCopy().apply {
                        email = carbonCopy.email
                        name = carbonCopy.name
                        recipientId = carbonCopy.id.toString()
                        routingOrder = DocuSignSignGateway.ROUTING_ORDER_FIRST
                        emailNotification = RecipientEmailNotification()
                            .emailSubject(emailParams.subject)
                            .emailBody(emailParams.body)
                    }
                }
                signers = givenInput.recipients.signers.map { signer ->
                    com.docusign.esign.model.Signer().apply {
                        clientUserId = signer.id
                        embeddedRecipientStartURL = "SIGN_AT_DOCUSIGN"
                        name = signer.name
                        email = signer.email
                        recipientId = signer.id
                        routingOrder = "1"
                        emailNotification = RecipientEmailNotification()
                            .emailSubject(emailParams.subject)
                            .emailBody(emailParams.body)
                        tabs = com.docusign.esign.model.Tabs().apply {
                            signHereTabs = givenInput.tabs.signHere.filter { it.signerId == signer.id }.map { tab ->
                                SignHere().apply {
                                    documentId = givenInput.docusignDocument.id.toString()
                                    name = tab.name
                                    anchorString = tab.anchorString
                                    anchorUnits = DocuSignSignGateway.TAB_ANCHOR_UNITS
                                    anchorYOffset = tab.offsetY.toString()
                                    anchorXOffset = tab.offsetX.toString()
                                    recipientId = tab.signerId.toString()
                                    tab.width?.also { width = it.toString() }
                                    tab.height?.also { height = it.toString() }
                                }
                            }
                            dateSignedTabs = givenInput.tabs.dateSigned.filter { it.signerId == signer.id }.map { tab ->
                                DateSigned().apply {
                                    documentId = givenInput.docusignDocument.id.toString()
                                    name = tab.name
                                    anchorString = tab.anchorString
                                    anchorUnits = DocuSignSignGateway.TAB_ANCHOR_UNITS
                                    anchorYOffset = tab.offsetY.toString()
                                    anchorXOffset = tab.offsetX.toString()
                                    recipientId = tab.signerId.toString()
                                    tab.width?.also { width = it.toString() }
                                    tab.height?.also { height = it.toString() }
                                }
                            }
                            companyTabs = givenInput.tabs.company.filter { it.signerId == signer.id }.map { tab ->
                                Company().apply {
                                    documentId = givenInput.docusignDocument.id.toString()
                                    name = tab.name
                                    anchorString = tab.anchorString
                                    anchorUnits = DocuSignSignGateway.TAB_ANCHOR_UNITS
                                    anchorYOffset = tab.offsetY.toString()
                                    anchorXOffset = tab.offsetX.toString()
                                    recipientId = tab.signerId.toString()
                                    tab.width?.also { width = it.toString() }
                                    tab.height?.also { height = it.toString() }
                                }
                            }
                            fullNameTabs = givenInput.tabs.fullName.filter { it.signerId == signer.id }.map { tab ->
                                FullName().apply {
                                    documentId = givenInput.docusignDocument.id.toString()
                                    name = tab.name
                                    anchorString = tab.anchorString
                                    anchorUnits = DocuSignSignGateway.TAB_ANCHOR_UNITS
                                    anchorYOffset = tab.offsetY.toString()
                                    anchorXOffset = tab.offsetX.toString()
                                    recipientId = tab.signerId.toString()
                                    tab.width?.also { width = it.toString() }
                                    tab.height?.also { height = it.toString() }
                                }
                            }
                            titleTabs = givenInput.tabs.title.filter { it.signerId == signer.id }.map { tab ->
                                Title().apply {
                                    documentId = givenInput.docusignDocument.id.toString()
                                    name = tab.name
                                    anchorString = tab.anchorString
                                    anchorUnits = DocuSignSignGateway.TAB_ANCHOR_UNITS
                                    anchorYOffset = tab.offsetY.toString()
                                    anchorXOffset = tab.offsetX.toString()
                                    recipientId = tab.signerId.toString()
                                    tab.width?.also { width = it.toString() }
                                    tab.height?.also { height = it.toString() }
                                }
                            }
                        }
                    }
                }
            }
        }

    private fun expectedRequestFromInput(givenInput: RetrieveViewUrlInput): RecipientViewRequest =
        RecipientViewRequest().apply {
            clientUserId = givenInput.recipient.id.toString()
            email = givenInput.recipient.email
            userName = givenInput.recipient.name
            returnUrl = givenInput.integrationSettings.returnUrl.toString()
            authenticationMethod = "none"
            pingFrequency = givenInput.integrationSettings.pingFrequency.toString()
            pingUrl = givenInput.integrationSettings.pingUrl.toString()
        }

    private fun givenRetrieveViewUrlInput(envelopeId: String = expectedEnvelopeId) =
        RetrieveViewUrlInput(
            signingId = envelopeId,
            recipient = Recipient(
                "1",
                "signer",
                "<EMAIL>"
            ),
            integrationSettings = IntegrationSettings(
                returnUrl = URI.create("http://www.some-return-url.com"),
                pingFrequency = 600,
                pingUrl = URI.create("http://www.some-ping-url.com")
            )
        )
}
