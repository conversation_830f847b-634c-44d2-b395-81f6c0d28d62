package realestate.unlock.dealroom.api.infrastructure.gateway.property

import io.mockk.every
import io.mockk.mockk
import kong.unirest.UnirestException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.NotFoundException
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyAssetsResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.PropertySageGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.keyscore.GetPropertyKeyscoreResponse
import java.math.BigDecimal

class PropertyGatewayTest {
    private val propertyId: String = "USTX-007240"
    private val propertyAssetsResponse: PropertyAssetsResponse = propertyAssetsResponse(propertyId)
    private val propertyKeyscoreResponse: GetPropertyKeyscoreResponse = getPropertyKeyscoreResponse()

    private lateinit var propertyGateway: PropertyGateway
    private lateinit var propertyAssetsApi: PropertyAssetsApi
    private lateinit var propertySageGateway: PropertySageGateway

    @BeforeEach
    fun setUp() {
        propertyAssetsApi = mockk()
        propertySageGateway = mockk()

        propertyGateway = PropertyGateway(
            propertyAssetsApi,
            propertySageGateway
        )

        every { propertyAssetsApi.findById(propertyId) } returns propertyAssetsResponse
        every { propertySageGateway.getPropertyKeyscoreByKeywayId(propertyId) } returns propertyKeyscoreResponse
    }

    @Test
    fun `can get a property`() {
        val response = propertyGateway.findByPropertyKeywayId(propertyId)
        assertEquals(response.keywayId, propertyId)

        assertEquals(response.address, propertyAssetsResponse.address)
        assertEquals(response.zipCode, propertyAssetsResponse.zipCode.toString())
        assertEquals(response.coordinates, propertyAssetsResponse.location.toCoordinates())

//        assertEquals(response.constructionYear, propertyAssetsResponse.constructionYear) uncomment when
    }

    @Test
    fun `if the request to property assets fails, it throws not found exception`() {
        every { propertyAssetsApi.findById(any()) } throws UnirestException("property not found")

        assertThrows<NotFoundException> { propertyGateway.findByPropertyKeywayId("asd-123") }
    }

    private fun getPropertyKeyscoreResponse() = GetPropertyKeyscoreResponse(
        score = 9,
        keywayOffer = BigDecimal.TEN,
        offerPrice = BigDecimal.valueOf(2000000),
        offerNoi = BigDecimal.valueOf(15000)
    )
}
