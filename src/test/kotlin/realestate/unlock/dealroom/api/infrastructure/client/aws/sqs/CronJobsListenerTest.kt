package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs

import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealsByClosingDate
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.CronJobsListener
import realestate.unlock.dealroom.api.utils.extensions.anyString
import software.amazon.awssdk.services.sqs.model.Message

@ExtendWith(MockKExtension::class)
class CronJobsListenerTest {

    @MockK
    private lateinit var changeDealsByClosingDate: ChangeDealsByClosingDate

    private lateinit var cronJobsListener: CronJobsListener

    @BeforeEach
    fun setUp() {
        cronJobsListener = CronJobsListener(
            changeDealsByClosingDate = changeDealsByClosingDate
        )
    }

    @Test
    fun `can execute cron job`() {
        // given
        val sqsMessage = givenSqsMessage("CLOSING_DEAL")
        every { changeDealsByClosingDate.findAndUpdate() } returns Unit

        // when
        val result = cronJobsListener.processMessage(sqsMessage)

        // then
        assertThat(result, equalTo(true))
        verify {
            changeDealsByClosingDate.findAndUpdate()
        }
    }

    @Test
    fun `can handle an error on cron job not succesfull`() {
        // given
        val sqsMessage = givenSqsMessage("NO EXISTO")
        every { changeDealsByClosingDate.findAndUpdate() } returns Unit

        // when
        val result = cronJobsListener.processMessage(sqsMessage)

        // then
        assertThat(result, equalTo(false))
    }

    private fun givenSqsMessage(job: String) =
        Message.builder().messageId(anyString())
            .body(givenMessageBody(job)).build()

    private fun givenMessageBody(job: String) = """
        {"job": "$job"}
    """.trimIndent()
}
