package realestate.unlock.dealroom.api.infrastructure.client.sendgrid

import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.email.TaskChangedEmailToSend
import realestate.unlock.dealroom.api.core.gateway.email.EmailGateway
import realestate.unlock.dealroom.api.core.gateway.email.TemplatedEmailData
import realestate.unlock.dealroom.api.core.usecase.email.task.changed.SendTaskChangedEmail
import realestate.unlock.dealroom.api.core.usecase.exception.email.SendTaskChangedEmailException

@ExtendWith(MockKExtension::class)
class SendTaskChangedEmailTest {

    companion object {
        private const val TEMPLATE = "task_changed_email"
    }

    private lateinit var sendTaskChangedEmail: SendTaskChangedEmail

    private val emailGateway: EmailGateway = mockk()

    @BeforeEach
    fun setUp() {
        sendTaskChangedEmail = SendTaskChangedEmail(
            emailGateway = emailGateway
        )
    }

    @Test
    fun `if sendgrid returns an error, throws an exception`() {
        // Given
        val mail = givenTaskChangedEmailToSend()
        every { emailGateway.sendTemplated(any()) } throws RuntimeException("boom")

        // When
        val action = { sendTaskChangedEmail.send(mail) }

        // Then
        assertThrows<SendTaskChangedEmailException>(action)
    }

    @Test
    fun `can send an email`() {
        // Given
        val mail = givenTaskChangedEmailToSend()
        every { emailGateway.sendTemplated(any()) } returns Unit

        // When
        sendTaskChangedEmail.send(mail)

        // Then
        verify {
            emailGateway.sendTemplated(mailFor(mail))
        }
    }

    private fun givenTaskChangedEmailToSend(): TaskChangedEmailToSend {
        return TaskChangedEmailToSend(
            emailTo = "<EMAIL>",
            fullName = "subject",
            taskStatus = "done",
            taskName = "Financial statements",
            categoryName = "Financial",
            propertyAddress = "Wall Street 345",
            memberAssigned = "assigned",
            link = "link to app"
        )
    }

    private fun mailFor(taskChangedEmailToSend: TaskChangedEmailToSend) =
        TemplatedEmailData(
            toEmailAddress = setOf(taskChangedEmailToSend.emailTo),
            templateKey = TEMPLATE,
            templateParams = mapOf(
                "full_name" to taskChangedEmailToSend.fullName,
                "status" to taskChangedEmailToSend.taskStatus,
                "assigned_to" to taskChangedEmailToSend.memberAssigned,
                "category" to taskChangedEmailToSend.categoryName,
                "task" to taskChangedEmailToSend.taskName,
                "deal" to taskChangedEmailToSend.propertyAddress,
                "app_link" to taskChangedEmailToSend.link,
                "subject" to "Task changed on deal ${taskChangedEmailToSend.propertyAddress}"
            )
        )
}
