package realestate.unlock.dealroom.api.infrastructure.client.rest

import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kong.unirest.Headers
import kong.unirest.HttpRequest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.client.rest.Interceptors.OriginHeaderInterceptor
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Configuration

class OriginHeaderInterceptorTest {

    private val configMock = mockk<Configuration>()

    private val target = OriginHeaderInterceptor(configMock)

    @BeforeEach
    fun clearMocks() {
        clearAllMocks()
    }

    @Test
    fun `A request without headers should be decorated with Origin`() {
        val headersMock = mockk<Headers>(relaxed = true) {
            every { containsKey(any()) } returns false
        }
        val givenRequest = mockk<HttpRequest<*>> {
            every { headers } returns headersMock
        }

        every { configMock.serviceName } returns "deal-room-api"

        target.onRequest(givenRequest, null)

        verify(exactly = 1) {
            headersMock.add("Origin", "deal-room-api")
        }
    }
}
