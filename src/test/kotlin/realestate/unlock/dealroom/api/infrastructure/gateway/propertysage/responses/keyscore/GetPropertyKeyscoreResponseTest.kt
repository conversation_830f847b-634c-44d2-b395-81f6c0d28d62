package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.keyscore

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.math.BigDecimal

class GetPropertyKeyscoreResponseTest {

    @Test
    fun `it serializes the response properly`() {
        val response = JsonMapper.decode(givenJson(), GetPropertyKeyscoreResponse::class.java)

        assertThat(response.score, equalTo(97))
        assertThat(response.keywayOffer, equalTo(BigDecimal.valueOf(3424820)))
        assertThat(response.listedNoi, equalTo(BigDecimal.valueOf(202887)))
        assertThat(response.listedPrice, equalTo(BigDecimal.valueOf(2898386)))
        assertThat(response.listedCapRate, equalTo(BigDecimal.valueOf(7)))
        assertThat(response.offerNoi, equalTo(BigDecimal.valueOf(202887)))
        assertThat(response.offerPrice, equalTo(BigDecimal.valueOf(2898386)))
        assertThat(response.offerCapRate, equalTo(BigDecimal.valueOf(7)))
        assertThat(response.imageUrl, equalTo("imageUrl"))
    }

    fun givenJson() = """
        {
            "score": 97,
            "keywayOffer": 3424820,
            "listedNoi": 202887,
            "listedPrice": 2898386,
            "listedCapRate": 7,
            "offerNoi": 202887,
            "offerPrice": 2898386,
            "offerCapRate": 7,
            "imageUrl": "imageUrl"
        }
    """.trimIndent()
}
