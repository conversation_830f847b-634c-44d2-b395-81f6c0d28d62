package realestate.unlock.dealroom.api.infrastructure.gateway.jwt

import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.exception.InvalidTokenException
import realestate.unlock.dealroom.api.infrastructure.configuration.model.JwtGatewayConfig
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Clock
import java.time.Instant
import java.time.ZoneId

@ExtendWith(MockKExtension::class)
class JWTAuthGatewayTest {

    companion object {
        private val now = Instant.now()
        private const val issuer = "issuer"
        private const val secret = "secret"
        private const val expiredToken =
            "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJteUNsYWltIjoidmFsdWUiLCJteUNsYWltMiI6InZhbHVlMiIsImlzcyI6Imlzc3VlciIsImV4cCI6MTY2MDgyODk2OH0.r1BlADHNkVuSU8n6vC_vyKvqVUgjpdVLMhCdN7J6oeI"
    }

    private lateinit var jwtAuthGateway: JWTAuthGateway

    @BeforeEach
    fun setUp() {
        jwtAuthGateway = JWTAuthGateway(
            clock = Clock.fixed(now, ZoneId.systemDefault()),
            jwtGatewayConfig = JwtGatewayConfig(
                issuer = issuer,
                secret = secret,
                tokenExpirationInSeconds = 100
            )
        )
    }

    @Test
    fun `can generate and read a valid token`() {
        val claims = mapOf(Pair("myClaim", "value"), Pair("myClaim2", "value2"))

        val token = jwtAuthGateway.generateToken(claims)

        val claimsFromToken = jwtAuthGateway.getClaimsFromToken(token)

        assertEquals(issuer, claimsFromToken["iss"])
        claims.entries.forEach {
            assertEquals(it.value, claimsFromToken[it.key])
        }
    }

    @Test
    fun `fails when token is not valid`() {
        assertThrows<InvalidTokenException> {
            jwtAuthGateway.getClaimsFromToken(anyString())
        }
    }

    @Test
    fun `fails when token is expired`() {
        assertThrows<InvalidTokenException> {
            jwtAuthGateway.getClaimsFromToken(expiredToken)
        }
    }
}
