package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.industry

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper

class GetPropertyIndustryResponseTest {

    @Test
    fun `it serializes the response properly`() {
        val response = JsonMapper.decode(givenJson(), GetPropertyIndustryResponse::class.java)

        assertThat(response.industry, equalTo("Eye doctors"))
        assertThat(response.score, equalTo(4))
    }

    fun givenJson() = """
        {
            "industry": "Eye doctors",
            "score": 4,
            "industryStatsGroups": []
        }
    """.trimIndent()
}
