package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs

import com.keyway.security.domain.authentication.AuthenticationSdk
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileToken
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileTokenStatus
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.repository.file.FileTokenRepository
import realestate.unlock.dealroom.api.core.usecase.file.gpt.RunFindings
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SetFileAsReady
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.ChatGptFileReadyListener
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import software.amazon.awssdk.services.sqs.model.Message

@ExtendWith(MockKExtension::class)
class ChatGptFileReadyListenerTest {

    @MockK
    private lateinit var setFileAsReady: SetFileAsReady

    @MockK
    private lateinit var fileTokenRepository: FileTokenRepository

    @MockK
    private lateinit var authenticationSdk: AuthenticationSdk

    private lateinit var listener: ChatGptFileReadyListener

    @MockK
    private lateinit var runFindings: RunFindings

    private val authToken = "token"

    @BeforeEach
    fun setUp() {
        every { authenticationSdk.accessTokenFor(any()) } returns authToken

        listener = ChatGptFileReadyListener(setFileAsReady, fileTokenRepository, runFindings, authenticationSdk, mockk(relaxed = true))
    }

    @Test
    fun `can set a file as ready`() {
        val fileToken = FileToken(
            kFileId = anyString(),
            token = anyString(),
            status = FileTokenStatus.PROCESSING,
            fileType = TokenFileType.REPORT,
            dealId = anyId()
        )
        val givenSqsMessage = buildSqsMessage(fileToken.kFileId, fileToken.token, "SUCCESS")
        every { fileTokenRepository.findByFileId(fileToken.kFileId) } returns fileToken
        every { setFileAsReady(SetFileAsReady.Input(fileId = fileToken.kFileId, token = fileToken.token, authToken = authToken)) } returns Unit
        every { runFindings(fileToken, authToken = authToken) } returns Unit

        val result = listener.processMessage(givenSqsMessage)

        assertTrue(result)
        verify {
            setFileAsReady(SetFileAsReady.Input(fileToken.kFileId, fileToken.token, authToken = authToken))
        }
        verify(exactly = 1) {
            runFindings(fileToken, authToken)
        }
    }

    @Test
    fun `if the file has already been set as ready it doesnt attempt to set it as ready again`() {
        val fileToken = FileToken(
            kFileId = anyString(),
            token = anyString(),
            status = FileTokenStatus.READY,
            fileType = TokenFileType.REPORT,
            dealId = anyId()
        )
        val givenSqsMessage = buildSqsMessage(fileToken.kFileId, fileToken.token, "SUCCESS")
        every { fileTokenRepository.findByFileId(fileToken.kFileId) } returns fileToken
        every { setFileAsReady(SetFileAsReady.Input(fileId = fileToken.kFileId, token = fileToken.token, authToken = authToken)) } returns Unit

        val result = listener.processMessage(givenSqsMessage)
        assertTrue(result)
        verify(exactly = 0) {
            setFileAsReady(any())
        }
    }

    @Test
    fun `if the file has already been set as initial_questions_ready it doesnt attempt to set it as ready again`() {
        val fileToken = FileToken(
            kFileId = anyString(),
            token = anyString(),
            status = FileTokenStatus.INITIAL_QUESTIONS_READY,
            fileType = TokenFileType.REPORT,
            dealId = anyId()
        )
        val givenSqsMessage = buildSqsMessage(fileToken.kFileId, fileToken.token, "SUCCESS")
        every { fileTokenRepository.findByFileId(fileToken.kFileId) } returns fileToken
        every { setFileAsReady(SetFileAsReady.Input(fileId = fileToken.kFileId, token = fileToken.token, authToken = authToken)) } returns Unit

        val result = listener.processMessage(givenSqsMessage)
        assertTrue(result)
        verify(exactly = 0) {
            setFileAsReady(any())
        }
    }

    @Test
    fun `if it cannot set the file as ready it returns false`() {
        val givenFileId = anyString()
        val givenToken = anyString()
        val givenSqsMessage = buildSqsMessage(givenFileId, givenToken, "SUCCESS")
        every { setFileAsReady(SetFileAsReady.Input(fileId = givenFileId, token = givenToken, authToken = authToken)) } throws Exception()

        assertFalse(listener.processMessage(givenSqsMessage))
    }

    @Test
    fun `it can update the status to failed`() {
        val fileToken = FileToken(
            kFileId = anyString(),
            token = anyString(),
            status = FileTokenStatus.PROCESSING,
            fileType = TokenFileType.REPORT,
            dealId = anyId()
        )
        val givenSqsMessage = buildSqsMessage(fileToken.kFileId, fileToken.token, "FAILED")

        every { fileTokenRepository.findByFileId(fileToken.kFileId) } returns fileToken
        every { fileTokenRepository.update(any()) } returns mockk()

        val result = listener.processMessage(givenSqsMessage)

        assertTrue(result)
        verify {
            fileTokenRepository.update(fileToken.copy(status = FileTokenStatus.FAILED))
        }
    }

    @Test
    fun `it can update the status to CONTENT_NOT_PROCESSABLE`() {
        val fileToken = FileToken(
            kFileId = anyString(),
            token = anyString(),
            status = FileTokenStatus.PROCESSING,
            fileType = TokenFileType.REPORT,
            dealId = anyId()
        )
        val givenSqsMessage = buildSqsMessage(fileToken.kFileId, fileToken.token, "CONTENT_NOT_PROCESSABLE")

        every { fileTokenRepository.findByFileId(fileToken.kFileId) } returns fileToken
        every { fileTokenRepository.update(any()) } returns mockk()

        val result = listener.processMessage(givenSqsMessage)

        assertTrue(result)
        verify {
            fileTokenRepository.update(fileToken.copy(status = FileTokenStatus.CONTENT_NOT_PROCESSABLE))
        }
    }

    @Test
    fun `setting a loi as ready should execute the findings`() {
        val fileToken = FileToken(
            kFileId = anyString(),
            token = anyString(),
            status = FileTokenStatus.PROCESSING,
            fileType = TokenFileType.LOI,
            dealId = anyId()
        )
        val givenSqsMessage = buildSqsMessage(fileToken.kFileId, fileToken.token, "SUCCESS")
        every { fileTokenRepository.findByFileId(fileToken.kFileId) } returns fileToken
        every { setFileAsReady(SetFileAsReady.Input(fileId = fileToken.kFileId, token = fileToken.token, authToken = authToken)) } returns Unit
        every { runFindings(any(), authToken = authToken) } returns Unit

        val result = listener.processMessage(givenSqsMessage)

        assertTrue(result)
        verify {
            setFileAsReady(SetFileAsReady.Input(fileToken.kFileId, fileToken.token, authToken = authToken))
        }
        verify {
            runFindings(fileToken, authToken = authToken)
        }
    }

    private fun buildSqsMessage(fileId: String, token: String, status: String) =
        Message.builder().messageId(anyString())
            .body("""{"file_id": "$fileId", "token": "$token", "status": "$status"}""")
            .build()
}
