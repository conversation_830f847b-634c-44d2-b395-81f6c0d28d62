package realestate.unlock.dealroom.api.functional.task

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.bulk.BulkUpdateResultType
import realestate.unlock.dealroom.api.entrypoint.rest.contract.bulk.BulkUpdateSuccessfulResult
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.patch.request.BulkUpdateTaskRequest
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.entity.deal.getAllTasks
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class BulkPatchTaskTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task",
        method = Method.PATCH.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - seller member should return 403`() {
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )

        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        // When
        HttpClient.patch(
            url = "$localUrl/task",
            body = getUpdatePayload(
                ids = listOf(givenTaskToUpdate.id),
                dueDate = LocalDate.of(2021, 7, 15)
            ),
            token = token,
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
    }

    @Test
    fun `Task should be correctly updated`() {
        // Given
        val tasks = DealCreator.createDealByRest().getAllTasks()
            .filter { it.visibility == Task.Visibility.ALL }
        val dueDate = LocalDate.of(2021, 7, 15)

        val response = HttpClient.patch(
            url = "$localUrl/task",
            body = getUpdatePayload(
                ids = tasks.map { it.id },
                dueDate = dueDate,
                status = TaskStatus.IN_REVIEW
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<BulkUpdateSuccessfulResult>>() {}) }
        )

        assertEquals(tasks.size, response.size)
    }

    @Test
    fun `can handle errors`() {
        // Given
        val task = createDealAndGetOneTask()
        val dueDate = LocalDate.of(2021, 7, 15)

        val invalidId = anyId()

        val response = HttpClient.patch(
            url = "$localUrl/task",
            body = getUpdatePayload(
                ids = listOf(task.id, invalidId),
                dueDate = dueDate,
                status = TaskStatus.IN_REVIEW
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, JsonNode::class.java) }
        )

        val resultList = response.toList()
        assertEquals(2, resultList.size)
        val taskWithError = resultList.first { it.get("type").asText() == BulkUpdateResultType.failure }
        val taskSuccessful = resultList.first { it.get("type").asText() == BulkUpdateResultType.successful }
        assertEquals(task.id, taskSuccessful.get("id").asLong())
        assertEquals(invalidId, taskWithError.get("id").asLong())
    }

    private fun createDealAndGetOneTask(templateKey: String? = null): Task =
        DealCreator.createDealByRest().findOneTask(templateKey)

    private fun getUpdatePayload(
        ids: List<Long>,
        status: TaskStatus? = null,
        dueDate: LocalDate? = null
    ): String =
        JsonMapper.encode(
            BulkUpdateTaskRequest(
                ids = ids,
                status = status?.key,
                dueDate = dueDate
            )
        )
}
