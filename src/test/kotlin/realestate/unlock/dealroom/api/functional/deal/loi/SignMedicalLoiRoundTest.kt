package realestate.unlock.dealroom.api.functional.deal.loi

import io.mockk.every
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentSign
import realestate.unlock.dealroom.api.core.entity.loi.LoiSignStatus
import realestate.unlock.dealroom.api.core.entity.loi.RespondLetterOfIntentInput
import realestate.unlock.dealroom.api.core.entity.loi.SignLetterOfIntentInput
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.gateway.sign.BeginSignFlowOutput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentSignRepository
import realestate.unlock.dealroom.api.core.usecase.loi.BeginLetterOfIntentSigning
import realestate.unlock.dealroom.api.core.usecase.loi.RespondLetterOfIntent
import realestate.unlock.dealroom.api.core.usecase.loi.SignLetterOfIntent
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalRound
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalWithoutRestTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.MemberSignBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import kotlin.io.path.ExperimentalPathApi

class SignMedicalLoiRoundTest : BaseFunctionalWithoutRestTest() {

    companion object {
        private const val buyerToken = "buyer-token"
    }

    private lateinit var letterOfIntentSignRepository: LetterOfIntentSignRepository
    private lateinit var beginLetterOfIntentSigning: BeginLetterOfIntentSigning
    private lateinit var respondLetterOfIntent: RespondLetterOfIntent
    private lateinit var signLetterOfIntent: SignLetterOfIntent
    private lateinit var buyer: Member

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        letterOfIntentSignRepository = Context.injector.getInstance(LetterOfIntentSignRepository::class.java)
        beginLetterOfIntentSigning = Context.injector.getInstance(BeginLetterOfIntentSigning::class.java)
        respondLetterOfIntent = Context.injector.getInstance(RespondLetterOfIntent::class.java)
        signLetterOfIntent = Context.injector.getInstance(SignLetterOfIntent::class.java)
        buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = buyerToken, email = "<EMAIL>", uid = anyString())
        Context.injector.getInstance(SignGateway::class.java).let { signGateway ->
            every { signGateway.beginFlow(any()) } returns BeginSignFlowOutput("signingId")
        }
    }

    @Test
    fun `can sign a loi`() {
        // given
        val loiSing = givenLoiSign()

        // when
        signLetterOfIntent.sign(
            input = SignLetterOfIntentInput(
                envelopeId = loiSing.signingId,
                recipientId = loiSing.sellerSign.id
            )
        )

        // then
        Assertions.assertEquals(LoiSignStatus.COMPLETED, letterOfIntentSignRepository.findByLoiId(loiSing.loiId)!!.status())
    }

    private fun givenLoiSign(): LetterOfIntentSign {
        val (loiResponse, dealResponse) = givenLoi()
        respondLetterOfIntent.respond(
            input = RespondLetterOfIntentInput(
                memberId = dealResponse.members.first { it.typeKey == "seller" }.id,
                dealId = dealResponse.id,
                loiId = loiResponse.id,
                accepted = true,
                comments = "no comments",
                files = listOf(),
                sellerSigner = MemberSignBuilder().build()
            )
        )
        return letterOfIntentSignRepository.findByLoiId(loiResponse.id)!!
    }

    @OptIn(ExperimentalPathApi::class)
    private fun givenLoi(): Pair<LoiMedicalRound, CompleteDeal> {
        val dealResponse = DealCreator.createDealByRest()
        val loiCreationRequest = PostMedicalLoiRequestBuilder().build()
        val loi = Unirest.post("$localUrl/deal/${dealResponse.id}/loi/round")
            .headers(AuthMock.getAuthHeader(buyerToken))
            .body(JsonMapper.encode(loiCreationRequest))
            .asJson()
            .let { JsonMapper.decode(it.body.toPrettyString(), LoiMedicalRound::class.java) }
        return Pair(loi, dealResponse)
    }
}
