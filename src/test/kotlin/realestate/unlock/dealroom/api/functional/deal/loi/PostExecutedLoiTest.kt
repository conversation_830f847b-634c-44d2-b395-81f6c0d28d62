package realestate.unlock.dealroom.api.functional.deal.loi

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.summary.LoiDocumentStatus
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.DocumentResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.DocumentType
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalRound
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMultifamilyRound
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.ExecutedLoiInput
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.PostMultifamilyLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class PostExecutedLoiTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser(memberType = "buyer")
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/loi/executed",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/executed",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/executed",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/executed",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `can create a multifamily executed loi`() {
        postExecutedLoiTestByType(
            PostMultifamilyLoiRequestBuilder().buildExecuted(),
            CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY }
        )
    }

    @Test
    fun `can create a medical executed loi`() {
        postExecutedLoiTestByType(
            PostMedicalLoiRequestBuilder().buildExecuted(),
            CreateDealInputBuilder().apply { propertyType = PropertyType.MEDICAL }
        )
    }

    private fun postExecutedLoiTestByType(request: ExecutedLoiInput, dealCreationRequestBuilder: CreateDealInputBuilder) {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = dealCreationRequestBuilder)
        val createLoiUrl = "$localUrl/deal/${deal.id}/loi/executed"

        checkStatusInMainDocuments(deal.id, LoiDocumentStatus.TO_DO)
        checkCurrentLoiRound(deal.id, null, dealCreationRequestBuilder.propertyType)

        // When
        val result = Unirest.post(createLoiUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(request))
            .asJson()

        // Then
        Assertions.assertEquals(HttpStatus.CREATED, result.status)
        checkCurrentLoiRound(deal.id, LetterOfIntentRoundStatus.EXECUTED, dealCreationRequestBuilder.propertyType)
        checkStatusInMainDocuments(deal.id, LoiDocumentStatus.EXECUTED)
    }

    private fun checkStatusInMainDocuments(dealId: Long, status: LoiDocumentStatus) {
        val givenUrl = "$localUrl/deal/$dealId/documents"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<DocumentResponse>>() {}
        )
        val loiDocument = response.first { it.type == DocumentType.LOI }
        assertThat(loiDocument.status, equalTo(status.toString()))
    }

    private fun checkCurrentLoiRound(dealId: Long, status: LetterOfIntentRoundStatus?, propertyType: PropertyType) {
        val loiUrl = "$localUrl/deal/$dealId/loi/round/current"

        // When
        val result = Unirest.get(loiUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        if (status == null) {
            assertThat(result.status, equalTo(204))
        } else {
            val status: LetterOfIntentRoundStatus = when (propertyType) {
                PropertyType.MEDICAL -> JsonMapper.decode(result.body.toPrettyString(), LoiMedicalRound::class.java).status
                PropertyType.MULTIFAMILY -> JsonMapper.decode(result.body.toPrettyString(), LoiMultifamilyRound::class.java).status
            }
            assertThat(status, IsEqual(status))
        }
    }
}
