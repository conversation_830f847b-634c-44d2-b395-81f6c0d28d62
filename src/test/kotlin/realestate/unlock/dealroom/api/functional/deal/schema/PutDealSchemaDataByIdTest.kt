package realestate.unlock.dealroom.api.functional.deal.schema

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.SchemaData
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.schema.DealSchemaData
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class PutDealSchemaDataByIdTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/schema-data",
        method = Method.PUT.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `should update deal schema successfully`() {
        // Given
        val deal = DealCreator.createDealByRest()
        Context.injector.getInstance(DealRepository::class.java).updateDealSchemaData(
            dealId = deal.id,
            dealSchemaData = SchemaData(
                schemaId = 1L,
                data = emptyMap()
            )
        )
        val newData = mapOf(
            "uno" to 1,
            "dos" to "dos"
        )
        // When
        val dealSchema = HttpClient.put(
            url = "$localUrl/deal/${deal.id}/schema-data",
            body = JsonMapper.encode(DealSchemaData(newData)),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        // Then
        Assertions.assertEquals(newData, dealSchema.data)
    }

    @Test
    fun `should replace all schema data`() {
        // Given
        val oldData = mapOf(
            "uno" to 1,
            "dos" to "dos"
        )
        val deal = DealCreator.createDealByRest()
        Context.injector.getInstance(DealRepository::class.java).updateDealSchemaData(
            dealId = deal.id,
            dealSchemaData = SchemaData(
                schemaId = 1L,
                data = oldData
            )
        )

        val newData = mapOf(
            "uno-nuevo" to 1,
            "dos-nuevo" to "dos"
        )
        // When
        val dealSchema = HttpClient.put(
            url = "$localUrl/deal/${deal.id}/schema-data",
            body = JsonMapper.encode(DealSchemaData(newData)),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        // Then
        Assertions.assertEquals(newData, dealSchema.data)
        oldData.forEach {
            Assertions.assertFalse(
                dealSchema.data.containsKey(it.key)
            )
        }
    }
}
