package realestate.unlock.dealroom.api.functional.member

import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.keyway.security.domain.user.User
import com.keyway.security.domain.user.UserSdk
import io.mockk.every
import io.mockk.verify
import io.swagger.models.Method
import kong.unirest.HttpResponse
import kong.unirest.HttpStatus
import kong.unirest.JsonNode
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.email.EmailGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ConflictException
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.member.post.PostMemberRequestBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.EmailGatewayStub

class PostMemberTest : BaseFunctionalTest() {

    private val logger by LoggerDelegate()
    private val emailGateway = Context.injector.getInstance(EmailGateway::class.java) as EmailGatewayStub

    @BeforeEach
    fun setUp() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/member",
        method = Method.POST.name,
        permissions = listOf(Permission.CREATE_MEMBERS)
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/member",
            memberType = MemberTypeEnum.SELLER.name,
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `Permissions - with needUser=true should ask for CREATE_USERS`() {
        // Given
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = MemberTypeEnum.BUYER.name,
            token = token,
            permissions = listOf(Permission.CREATE_MEMBERS)
        )

        val givenRequestBody = PostMemberRequestBuilder()
            .apply { needUser = true }
            .build()

        // When
        val givenUrl = "$localUrl/member"
        val result = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .body(JsonMapper.encode(givenRequestBody))
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
        assertThat(result.body.toPrettyString(), CoreMatchers.containsString("Permissions needed: [deal_room:create:users]"))
    }

    @Test
    fun `should save member successfully`() {
        // Given
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val givenUrl = "$localUrl/member"

        val givenRequestBody = PostMemberRequestBuilder()
            .apply { memberType = MemberTypeEnum.BUYER.name }
            .apply { phoneNumber = givenPhone }
            .build()

        // When
        val result = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenRequestBody))
            .asJson()

        // Then
        assertThat(result.status, equalTo(201))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            MemberDto::class.java
        )

        assertThat(response.address, equalTo(givenRequestBody.address))
        assertThat(response.type.name, equalTo(givenRequestBody.memberType))
        assertThat(response.lastName, equalTo(givenRequestBody.lastName))
        assertThat(response.firstName, equalTo(givenRequestBody.firstName))
        assertThat(response.companyName, equalTo(givenRequestBody.companyName))
        assertThat(response.phoneNumber, equalTo(givenRequestBody.phoneNumber))
        assertThat(response.email, equalTo(givenRequestBody.email))
    }

    @Test
    fun `should save member without address successfully`() {
        // Given
        val givenUrl = "$localUrl/member"

        val givenRequestBody = PostMemberRequestBuilder()
            .apply { memberType = MemberTypeEnum.SELLER.name }
            .apply { address = null }
            .build()

        // When
        val result = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenRequestBody))
            .asJson()

        // Then
        assertThat(result.status, equalTo(201))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            MemberDto::class.java
        )

        assertThat(response.address, nullValue())
        assertThat(response.type.name, equalTo(givenRequestBody.memberType))
        assertThat(response.lastName, equalTo(givenRequestBody.lastName))
        assertThat(response.firstName, equalTo(givenRequestBody.firstName))
        assertThat(response.companyName, equalTo(givenRequestBody.companyName))
        assertThat(response.phoneNumber, equalTo(givenRequestBody.phoneNumber))
        assertThat(response.email, equalTo(givenRequestBody.email))
        verify(exactly = 0) {
            emailGateway.sendTemplated(
                match { it.toEmailAddress == setOf(givenRequestBody.email) && it.templateKey == "welcome_email" }
            )
        }
    }

    @Test
    fun `if member type does not exists should return bad request`() {
        // Given
        val givenUrl = "$localUrl/member"

        val givenRequestBody = PostMemberRequestBuilder()
            .apply { memberType = "non-existent-type" }
            .build()

        // When
        val result = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenRequestBody))
            .asJson()

        // Then
        assertThat(result.status, equalTo(400))
    }

    @Test
    fun `should save member and user successfully`() {
        // Given
        val givenUrl = "$localUrl/member"

        val givenRequestBody = PostMemberRequestBuilder()
            .apply { needUser = true }
            .build()

        val givenUser = User(
            id = "test-user-creation-uid",
            email = givenRequestBody.email,
            name = "${givenRequestBody.firstName} ${givenRequestBody.lastName}",
            emailVerified = true,
            blocked = false
        )

        Context.injector.getInstance(UserSdk::class.java).let { userSdk ->
            every { userSdk.create(any()) } returns givenUser
        }

        // When
        val result = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenRequestBody))
            .asJson()

        // Then
        assertThat(result.status, equalTo(201))

        logger.info(result.body.toPrettyString())

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            MemberDto::class.java
        )

        assertThat(response.address, equalTo(givenRequestBody.address))
        assertThat(response.type.name, equalTo(givenRequestBody.memberType))
        assertThat(response.lastName, equalTo(givenRequestBody.lastName))
        assertThat(response.firstName, equalTo(givenRequestBody.firstName))
        assertThat(response.companyName, equalTo(givenRequestBody.companyName))
        assertThat(response.phoneNumber, equalTo(givenRequestBody.phoneNumber))
        assertThat(response.email, equalTo(givenRequestBody.email))
        assertThat(response.email, equalTo(givenUser.email))
    }

    @Test
    fun `should restrict access if user is not admin`() {
        // Given
        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val givenUrl = "$localUrl/member"

        // When
        val result = Unirest.post(givenUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
    }

    @Test
    fun `should save member without phone successfully`() {
        // Given
        val givenUrl = "$localUrl/member"

        val givenRequestBody = PostMemberRequestBuilder().build()

        // When
        val result = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenRequestBody))
            .asJson()

        // Then
        assertThat(result.status, equalTo(201))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            MemberDto::class.java
        )

        assertThat(response.address, equalTo(givenRequestBody.address))
        assertThat(response.type.name, equalTo(givenRequestBody.memberType))
        assertThat(response.lastName, equalTo(givenRequestBody.lastName))
        assertThat(response.firstName, equalTo(givenRequestBody.firstName))
        assertThat(response.companyName, equalTo(givenRequestBody.companyName))
        assertThat(response.phoneNumber, equalTo(givenRequestBody.phoneNumber))
        assertThat(response.email, equalTo(givenRequestBody.email))
    }

    @Test
    fun `Creating a member with an already used email should not be possible`() {
        // Given
        val givenEmail = "<EMAIL>"

        // when
        val firstMemberCreationResponse = givenMemberWithEmail(givenEmail)
        val secondMemberCreationResponse = givenMemberWithEmail(givenEmail)

        // then
        assertThat(firstMemberCreationResponse.status, equalTo(HttpStatus.CREATED))
        assertThat(secondMemberCreationResponse.status, equalTo(HttpStatus.CONFLICT))
        val expectedException = JsonMapper.decode(secondMemberCreationResponse.body.toString(), ConflictException::class.java)
        assertThat(expectedException.message, equalTo("Member with email [$givenEmail] already exists"))
    }

    private fun givenMemberWithEmail(email: String): HttpResponse<JsonNode> {
        val requestBody = PostMemberRequestBuilder()
            .apply { this.email = email }
            .build()

        return Unirest.post("$localUrl/member")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(requestBody))
            .asJson()
    }

    @Test
    fun `can create member for other organization`() {
        // Given
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = MemberTypeEnum.BUYER.name,
            token = token,
            permissions = listOf(Permission.CREATE_MEMBERS, Permission.CREATE_EXTERNAL_ORG_MEMBER)
        )

        val givenRequestBody = PostMemberRequestBuilder()
            .apply { address = null }
            .apply { organizationId = anyString() }
            .build()

        val response = HttpClient.post(
            url = "$localUrl/member",
            body = JsonMapper.encode(givenRequestBody),
            token = token,
            responseHandler = { JsonMapper.decode(it, MemberDto::class.java) },
            expectedStatus = 201
        )

        assertThat(response.organizationId, equalTo(givenRequestBody.organizationId))
    }

    @Test
    fun `creating a member for another organization without permission should fail due to permissions`() {
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = MemberTypeEnum.BUYER.name,
            token = token,
            permissions = listOf(Permission.CREATE_MEMBERS)
        )

        val givenRequestBody = PostMemberRequestBuilder()
            .apply { address = null }
            .apply { organizationId = anyString() }
            .build()

        val response = HttpClient.post(
            url = "$localUrl/member",
            body = JsonMapper.encode(givenRequestBody),
            token = token,
            responseHandler = { it },
            expectedStatus = 403
        )

        assertThat(response, containsString("Permissions needed: [keyway:users:create_in_other_org]"))
    }
}
