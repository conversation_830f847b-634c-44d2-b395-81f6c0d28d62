package realestate.unlock.dealroom.api.functional.deal.file

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.file.DealDirectoryFilter
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.core.entity.search.StringOperator
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.*
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableEnum
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableString
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DealFilesAndFoldersConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealDirectoryTest : BaseDealDirectoryTest() {

    private lateinit var dealFilesAndFoldersConfig: DealFilesAndFoldersConfig

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        dealFilesAndFoldersConfig = Context.injector.getInstance(DealFilesAndFoldersConfig::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/directory",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/directory",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/directory",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return == 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/directory"

        // Then
        HttpClient.get(
            url = givenUrl,
            token = token,
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
    }

    @Test
    fun `without path should return root repository`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "some-folder")
        createFile(deal.id, "some-folder", "file1.json")
        createFile(deal.id, "some-folder", "file2.json")
        createFile(deal.id, "", "some.pdf")

        // When
        val result = getDirectory(deal.id, "")

        // Then
        assertEquals(2, result.size)
        assertEquals("some-folder", result[0].name)
        assertEquals("some.pdf", result[1].name)
    }

    @Test
    fun `with path should return only directories in that path`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "Some Folder")
        createFile(deal.id, "Some Folder", "file1.json")
        createFile(deal.id, "Some Folder", "file2.json")
        createFile(deal.id, "", "some.pdf")

        // When
        val result = getDirectory(deal.id, "Some Folder")

        // Then
        assertEquals(2, result.size)
        assertEquals("file1.json", result[0].name)
        assertEquals("file2.json", result[1].name)
    }

    @Test
    fun `should filter by name`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "Some Folder")
        createFile(deal.id, "Some Folder", "red.json")
        createFile(deal.id, "Some Folder", "blue.json")
        createFile(deal.id, "Some Folder", "light-blue.json")
        createFile(deal.id, "Some Folder", "green.json")
        createFolder(deal.id, "Some Folder", "a nice BLue folder")
        createFile(deal.id, "Some Folder/a nice BLue folder", "some BLUE file.pdf")
        createFile(deal.id, "Some Folder/a nice BLue folder", "some GREEN file.pdf")
        createFile(deal.id, "", "some.pdf")

        // When
        val result = getDirectory(
            deal.id,
            "Some Folder",
            DealDirectoryFilter(name = SearchableString(StringOperator.LIKE, listOf("blue")))
        )

        // Then
        assertEquals(4, result.size)
        assertEquals("a nice BLue folder", result[0].name)
        assertEquals("blue.json", result[1].name)
        assertEquals("light-blue.json", result[2].name)
        assertEquals("some BLUE file.pdf", result[3].name)
    }

    @Test
    fun `should filter by name very deep folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "folder1")
        createFolder(deal.id, "folder1", "folder2")
        createFolder(deal.id, "folder1/folder2", "folder3-target")
        createFile(deal.id, "folder1/folder2/folder3-target", "target.pdf")
        createFolder(deal.id, "folder1/folder2/folder3-target", "folder4")
        createFolder(deal.id, "folder1/folder2/folder3-target/folder4", "folder5-target")

        // When
        val result = getDirectory(
            deal.id,
            "",
            DealDirectoryFilter(name = SearchableString(StringOperator.LIKE, listOf("target")))
        )

        // Then
        assertEquals(3, result.size)
        assertEquals("folder3-target", result[0].name)
        assertEquals("folder5-target", result[1].name)
        assertEquals("target.pdf", result[2].name)
    }

    @Test
    fun `should filter by type folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "Some Folder")
        createFile(deal.id, "Some Folder", "red.json")
        createFile(deal.id, "Some Folder", "blue.json")
        createFile(deal.id, "Some Folder", "light-blue.json")
        createFile(deal.id, "Some Folder", "green.json")
        createFolder(deal.id, "Some Folder", "a nice BLue folder")
        createFile(deal.id, "Some Folder/a nice BLue folder", "some BLUE file.pdf")
        createFile(deal.id, "Some Folder/a nice BLue folder", "some GREEN file.pdf")
        createFolder(deal.id, "", "empty folder")
        createFile(deal.id, "", "some.pdf")

        // When
        val result = getDirectory(
            deal.id,
            path = "",
            filter = DealDirectoryFilter(type = SearchableEnum(EnumOperator.EQ, listOf(DirectoryItemTypeEnum.FOLDER)))
        )

        // Then
        assertEquals(2, result.size)
        assertEquals("Some Folder", result[0].name)
        assertEquals("empty folder", result[1].name)
    }

    @Test
    fun `should filter by type file`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "Some Folder")
        createFile(deal.id, "Some Folder", "red.json")
        createFile(deal.id, "Some Folder", "blue.json")
        createFile(deal.id, "Some Folder", "light-blue.json")
        createFile(deal.id, "Some Folder", "green.json")
        createFolder(deal.id, "Some Folder", "a nice BLue folder")
        createFile(deal.id, "Some Folder/a nice BLue folder", "some BLUE file.pdf")
        createFile(deal.id, "Some Folder/a nice BLue folder", "some GREEN file.pdf")
        createFolder(deal.id, "", "empty folder")
        createFile(deal.id, "", "some.pdf")

        // When
        val result = getDirectory(
            deal.id,
            path = "Some Folder",
            filter = DealDirectoryFilter(type = SearchableEnum(EnumOperator.EQ, listOf(DirectoryItemTypeEnum.FILE)))
        )

        // Then
        assertEquals(4, result.size)
        assertEquals("blue.json", result[0].name)
        assertEquals("green.json", result[1].name)
        assertEquals("light-blue.json", result[2].name)
        assertEquals("red.json", result[3].name)
    }

    @Test
    fun `given a file that is in a folder with the same name that repository main folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val baseFolder = FileUtils.buildDealRepositoryPath(deal.id, "")
        createFile(deal.id, baseFolder, "file.pdf")

        // Then
        getDirectory(deal.id, "").also {
            assertEquals(1, it.size)
            assertEquals("deal", it[0].name)
            assertEquals(DirectoryItemType.folder, it[0].type)
        }
        getDirectory(deal.id, "deal").also {
            assertEquals(1, it.size)
            assertEquals(deal.id.toString(), it[0].name)
            assertEquals(DirectoryItemType.folder, it[0].type)
        }
        getDirectory(deal.id, "deal/${deal.id}").also {
            assertEquals(1, it.size)
            assertEquals("repository", it[0].name)
            assertEquals(DirectoryItemType.folder, it[0].type)
        }
        getDirectory(deal.id, "deal/${deal.id}/repository").also {
            assertEquals(1, it.size)
            assertEquals("file.pdf", it[0].name)
            assertEquals(DirectoryItemType.file, it[0].type)
        }
        getDirectory(deal.id, "deal/${deal.id}/repository/").also {
            assertEquals(1, it.size)
            assertEquals("file.pdf", it[0].name)
            assertEquals(DirectoryItemType.file, it[0].type)
        }
    }

    @Test
    fun `should return folder parentFolderPath`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "folder1")
        createFolder(deal.id, "folder1", "folder2")
        createFolder(deal.id, "folder1/folder2", "folder3-target")
        createFile(deal.id, "folder1/folder2/folder3-target", "target.pdf")
        createFolder(deal.id, "folder1/folder2/folder3-target", "folder4")
        createFolder(deal.id, "folder1/folder2/folder3-target/folder4", "folder5-target")

        // When
        getDirectory(deal.id, "").let {
            val folder = it.first { f -> f.name == "folder1" && f is DirectoryFolder } as DirectoryFolder
            assertEquals("", folder.parentFolderPath)
        }

        getDirectory(deal.id, "folder1").let {
            val folder = it.first { f -> f.name == "folder2" && f is DirectoryFolder } as DirectoryFolder
            assertEquals("folder1", folder.parentFolderPath)
        }

        getDirectory(deal.id, "folder1/folder2").let {
            val folder = it.first { f -> f.name == "folder3-target" && f is DirectoryFolder } as DirectoryFolder
            assertEquals("folder1/folder2", folder.parentFolderPath)
        }
    }
}
