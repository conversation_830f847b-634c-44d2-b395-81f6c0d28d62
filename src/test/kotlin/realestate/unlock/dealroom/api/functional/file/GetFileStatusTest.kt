package realestate.unlock.dealroom.api.functional.file

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileToken
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileTokenStatus
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.gpt.FileStatusResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.repository.database.file.FileTokenDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetFileStatusTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/file/456/status",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `it retrieves the status of the given file`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenFileId = anyString()
        val givenToken = anyString()
        val givenStatus = FileTokenStatus.READY
        saveFileTokenStatus(givenFileId, givenToken, givenStatus, givenDeal.id)

        val givenUrl = "$localUrl/deal/${givenDeal.id}/file/$givenFileId/status"

        val response = HttpClient.get(
            url = givenUrl,
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, FileStatusResponse::class.java) }
        )

        assertThat(response.status.name, equalTo(givenStatus.name))
    }

    private fun saveFileTokenStatus(fileId: String, token: String, status: FileTokenStatus, dealId: Long) {
        Context.injector.getInstance(FileTokenDatabaseRepository::class.java).let { repository ->
            repository.save(FileToken(kFileId = fileId, token = token, status = status, fileType = TokenFileType.REPORT, dealId = dealId))
        }
    }
}
