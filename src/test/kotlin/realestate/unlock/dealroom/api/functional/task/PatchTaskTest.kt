package realestate.unlock.dealroom.api.functional.task

import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.team.AddDealTeamMemberRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.patch.request.ChangeTaskStatusRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.patch.request.UpdateTaskRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.post.request.CreateTaskRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.entity.deal.getAllTasks
import realestate.unlock.dealroom.api.utils.extensions.*
import realestate.unlock.dealroom.api.utils.json.IncludeNullJsonMapper
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class PatchTaskTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/${anyString()}",
        method = Method.PATCH.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "buyer",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )

        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        // When
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                dueDate = LocalDate.of(2021, 7, 15)
            ),
            token = token,
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
        assertThat(
            response,
            CoreMatchers.containsString("In order to access to the task you need to be member of the related deal")
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "buyer",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS)

        )

        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        // When
        HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                status = givenTaskToUpdate.statusKey,
                dueDate = LocalDate.of(2021, 7, 15),
                attachedForm = mapOf(
                    "test_key_1" to 3001,
                    "test_key_2" to "2021-12-07"
                )
            ),
            token = token,
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
    }

    @Test
    fun `Permissions - seller member should return 403`() {
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )

        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        // When
        HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                status = givenTaskToUpdate.statusKey,
                dueDate = LocalDate.of(2021, 7, 15),
                attachedForm = mapOf(
                    "test_key_1" to 3001,
                    "test_key_2" to "2021-12-07"
                )
            ),
            token = token,
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
    }

    @Test
    fun `Task should be correctly updated`() {
        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        val duedate = LocalDate.of(2021, 7, 15)
        val form = mapOf(
            "test_key_1" to 3001,
            "test_key_2" to "2021-12-07"
        )

        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                status = givenTaskToUpdate.statusKey,
                dueDate = duedate,
                attachedForm = form
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, not(nullValue()))
        assertThat(response.attachedFormData, not(emptyMap()))
        assertThat(response.dueDate, equalTo(duedate))
        assertThat(response.attachedFormData, equalTo(form))
        assertThat(response.status, equalTo(givenTaskToUpdate.statusKey.key))
        assertThat(response.title, equalTo(givenTaskToUpdate.title))
    }

    @Test
    fun `Task without form should be correctly updated`() {
        // Then
        val givenTaskToUpdate = createDealAndGetOneTask()
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                status = givenTaskToUpdate.statusKey
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, nullValue())
        assertThat(response.dueDate, equalTo(givenTaskToUpdate.dueDate))
        assertThat(response.attachedFormData, equalTo(givenTaskToUpdate.attachedFormData))
    }

    @Test
    fun `Existent task with form should not delete the form if do not send it in the next update`() {
        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        val givenUpdatedTaskWithForm = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                attachedForm = mapOf(
                    "test_key_1" to 3001,
                    "test_key_2" to "2021-12-07"
                )
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                dueDate = LocalDate.of(2021, 7, 15)
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, not(nullValue()))
        assertThat(response.attachedFormData, not(emptyMap()))
        assertThat(response.attachedFormData, equalTo(givenUpdatedTaskWithForm.attachedFormData))
    }

    @Test
    fun `Task with files should be correctly updated`() {
        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        val files = listOf(anyString()).getFileParam()
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                attachedForm = mapOf(
                    "test_key_1" to 3001,
                    "test_key_2" to "2021-12-07",
                    "file" to files
                )
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, not(nullValue()))
        assertThat(response.attachedFormData, not(emptyMap()))
        response.assertFiles(files)
    }

    @Test
    fun `Update task with different files with the same name should save files with the same name but different path`() {
        // Given
        val givenFileName = "test-file.txt"
        val givenTaskToUpdate = createDealAndGetOneTask("financial_statements")
        val files = listOf(givenFileName, givenFileName).getFileParam()
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                attachedForm = mapOf(
                    "test_key_1" to 3001,
                    "test_key_2" to "2021-12-07",
                    "file" to files
                )
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.files.size, equalTo(2))
        assertThat(response.files.first().name, equalTo(response.files[1].name))
        response.assertFiles(files)
    }

    @Test
    fun `Back and forth task with new file version should switch the assigned user`() {
        // When
        val fileName = anyString()
        val givenTaskToUpdate = createDealAndGetOneTask("survey")
        val fileParam = listOf(fileName).getFileParam()
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                attachedForm = mapOf(
                    "test_key_1" to 3001,
                    "test_key_2" to "2021-12-07",
                    "file" to fileParam
                )
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData?.get("file"), equalTo(fileParam))
        response.assertFiles(fileParam)
    }

    @Test
    fun `Task should be correctly updated if the user is not admin but the task is assigned to given member`() {
        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        val givenToken = "not-admin-token"
        val givenNotAdminMember = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = PhoneNumberUtil.getInstance().let { it.format(it.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164) },
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        Context.injector.getInstance(TaskRepository::class.java)
            .update(
                TaskToUpdate(
                    id = givenTaskToUpdate.id,
                    dealCategoryId = givenTaskToUpdate.dealCategoryId,
                    assignedBuyerId = givenTaskToUpdate.assignedBuyer.id,
                    assignedTeam = MemberDealTeam.SELLER,
                    statusKey = givenTaskToUpdate.statusKey.key,
                    title = givenTaskToUpdate.title,
                    description = givenTaskToUpdate.description,
                    dueDate = givenTaskToUpdate.dueDate,
                    attachedForm = givenTaskToUpdate.attachedFormData,
                    enabled = givenTaskToUpdate.enabled
                )
            )

        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                dueDate = LocalDate.of(2021, 7, 15)
            ),
            token = givenToken,
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, nullValue())
        assertThat(response.dueDate, not(nullValue()))
    }

    @Test
    fun `Updating a rejected task should remove the rejectionReason`() {
        // Given
        val rejectedTaskId = givenARejectedTask()
        val response = HttpClient.patch(
            url = rejectedTaskId.getPatchURL(),
            body = getUpdatePayload(
                status = TaskStatus.IN_REVIEW
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
        assertThat(TaskStatusTranslator.valueOf(response.status.uppercase()).taskStatus, equalTo(TaskStatus.IN_REVIEW))
        assertThat(response.rejectionReason, nullValue())
    }

    @Test
    fun `Update task should be restricted if the user is not admin and the task is not assigned to given member`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)
        )

        val givenTaskToUpdate = dealResponse.findOneTask()

        // Then
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = getUpdatePayload(
                status = TaskStatus.DONE,
                dueDate = LocalDate.of(2021, 7, 15)
            ),
            expectedStatus = HttpStatus.FORBIDDEN,
            token = givenToken,
            responseHandler = { it }
        )
        assertThat(response, CoreMatchers.containsString("In order to access to the task you need to be member of the related deal"))
    }

    @Test
    fun `Task should be correctly updated if the user belongs to the assigned member team`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val seller = dealResponse.members.first { member -> member.typeKey.startsWith(prefix = "seller") }

        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val newTeamMember = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller_counsel",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        HttpClient.post(
            url = "$localUrl/deal/${dealResponse.id}/team",
            body = JsonMapper.encode(AddDealTeamMemberRequest(listOf(newTeamMember.id))),
            responseHandler = { it }
        )

        val givenTaskToUpdate = dealResponse.categories
            .flatMap { category -> category.tasksWithFilesAndHistory.filter { task -> task.task.assignedTeam == MemberDealTeam.SELLER } }
            .first()

        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.task.id}",
            body = getUpdatePayload(
                status = givenTaskToUpdate.task.statusKey,
                dueDate = LocalDate.of(2021, 7, 15)
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.task.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.task.dealCategoryId))
        assertThat(response.attachedFormData, nullValue())
        assertThat(response.dueDate, not(nullValue()))
    }

    @Test
    fun `Should return bad request if task do not exists`() {
        HttpClient.patch(
            url = "$localUrl/task/99999",
            body = getUpdatePayload(),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { it }
        )
    }

    @Test
    fun `Should update only task status`() {
        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                status = TaskStatus.DONE
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, equalTo(givenTaskToUpdate.attachedFormData))
        assertThat(response.status, equalTo(TaskStatus.DONE.key))
        assertThat(response.dueDate, equalTo(givenTaskToUpdate.dueDate))
    }

    @Test
    fun `Should update only attached form`() {
        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        val attachedForm = mapOf(
            "test_key_1" to 3001,
            "test_key_2" to "2021-12-07"
        )
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                attachedForm = attachedForm
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, equalTo(attachedForm))
        assertThat(response.status, equalTo(givenTaskToUpdate.statusKey.key))
        assertThat(response.dueDate, equalTo(givenTaskToUpdate.dueDate))
    }

    @Test
    fun `Should update only due date`() {
        // Given
        val givenTaskToUpdate = createDealAndGetOneTask()
        val dueDate = LocalDate.now().minusDays(32)
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                dueDate = dueDate
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.title, equalTo(givenTaskToUpdate.title))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, equalTo(givenTaskToUpdate.attachedFormData))
        assertThat(response.status, equalTo(givenTaskToUpdate.statusKey.key))
        assertThat(response.dueDate, equalTo(dueDate))
    }

    @Test
    fun `Should update all fields`() {
        val givenTaskToUpdate = createDealAndGetOneTask()
        val dueDate = LocalDate.now().minusDays(32)
        val attachedForm = mapOf(
            "test_key_1" to 3001,
            "test_key_2" to "2021-12-07"
        )
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                dueDate = dueDate,
                attachedForm = attachedForm,
                status = TaskStatus.DONE
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.title, equalTo(givenTaskToUpdate.title))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, equalTo(attachedForm))
        assertThat(response.status, equalTo(TaskStatus.DONE.key))
        assertThat(response.dueDate, equalTo(dueDate))
    }

    @Test
    fun `Should clear due date && attachedForm`() {
        val givenTaskToUpdate = createDealAndGetOneTask()
        val dueDate = LocalDate.now().minusDays(32)
        val attachedForm = mapOf(
            "test_key_1" to 3001,
            "test_key_2" to "2021-12-07"
        )
        val responseData = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                dueDate = dueDate,
                attachedForm = attachedForm
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        Assertions.assertEquals(responseData.dueDate, dueDate)
        Assertions.assertEquals(responseData.attachedFormData, attachedForm)

        val responseClearData = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = IncludeNullJsonMapper.encode(
                UpdateTaskRequest(
                    status = null,
                    dueDate = null,
                    attachedForm = emptyMap()
                )
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        Assertions.assertEquals(responseData.id, responseClearData.id)
        Assertions.assertEquals(responseData.dealCategoryId, responseClearData.dealCategoryId)
        Assertions.assertEquals(responseData.status, responseClearData.status)
        Assertions.assertNotEquals(responseData.dueDate, responseClearData.dueDate)
        Assertions.assertNotEquals(responseData.attachedFormData, responseClearData.attachedFormData)
    }

    @Test
    fun `should update deal om file`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskToUpdate = givenDeal.findOneTask("nnn_upload_om")
        val givenFileId = anyString()
        val attachedForm = mapOf(
            "om" to listOf(
                mapOf(
                    "name" to "om-file.pdf",
                    "uid" to givenFileId
                )
            )
        )

        HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = getUpdatePayload(
                attachedForm = attachedForm,
                status = TaskStatus.DONE
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        val updatedDeal = HttpClient.get(
            url = "$localUrl/deal/${givenDeal.id}",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.omFileId, equalTo(givenFileId))
    }

    private fun createDealAndGetOneTask(templateKey: String? = null): Task =
        DealCreator.createDealByRest().findOneTask(templateKey)

    private fun Long.getPatchURL() = "$localUrl/task/$this"

    private fun getUpdatePayload(
        status: TaskStatus? = null,
        dueDate: LocalDate? = null,
        attachedForm: Map<String, Any>? = null
    ): String =
        JsonMapper.encode(
            UpdateTaskRequest(
                status = status?.key,
                dueDate = dueDate,
                attachedForm = attachedForm
            )
        )

    private fun List<String>.getFileParam() =
        this.map {
            mapOf(
                "uid" to anyString(),
                "name" to it
            )
        }

    private fun GetTaskResponse.assertFiles(request: List<Map<String, String>>) {
        request.forEach { requestFile ->
            this.files.any { file -> file.uid == requestFile["uid"] }
        }
    }

    private fun givenARejectedTask(): Long {
        val dealResponse = DealCreator.createDealByRest()
        val taskToReject = dealResponse.categories.first { it.categoryKey != "purchase_and_sale_contract" }.tasksWithFilesAndHistory.first().task
        HttpClient.patch(
            url = taskToReject.id.getPatchURL(),
            body = getUpdatePayload(
                status = TaskStatus.IN_REVIEW
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
        val rejectTaskUrl = "$localUrl/task/${taskToReject.id}/status"
        val rejectionReason = "this is the reason why the task was rejected"
        val rejectResponse = HttpClient.put(
            url = rejectTaskUrl,
            body = JsonMapper.encode(
                ChangeTaskStatusRequest(
                    transition = TaskStatusTransitions.REJECT,
                    reason = rejectionReason
                )
            ),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(rejectResponse.status, equalTo(TaskStatus.REJECTED.key))
        assertThat(rejectResponse.rejectionReason, equalTo(rejectionReason))
        return taskToReject.id
    }

    @Test
    fun `updating title,description and dealCategoryId in a custom task should work`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val initialCategoryId = deal.findOneTask().dealCategoryId
        val otherCategoryId = deal.getAllTasks().map { it.dealCategoryId }.first { it != initialCategoryId }
        val givenTaskToUpdate = HttpClient.post(
            url = "$localUrl/task",
            body = JsonMapper.encode(
                CreateTaskRequest(
                    assignedBuyerId = deal.members.first { it.type == MemberTypeEnum.BUYER }.id,
                    dealCategoryId = initialCategoryId,
                    status = TaskStatus.TO_DO,
                    title = "Old title",
                    description = "Old description",
                    dueDate = null,
                    priority = Priority(PriorityValue.HIGH, 0)
                )
            ),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // When
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = JsonMapper.encode(
                UpdateTaskRequest(
                    title = "New title",
                    description = "New description",
                    dealCategoryId = otherCategoryId
                )
            ),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.status, equalTo(givenTaskToUpdate.status))
        assertThat(response.dealCategoryId, equalTo(otherCategoryId))
        assertThat(response.title, equalTo("New title"))
        assertThat(response.description, equalTo("New description"))
    }

    @Test
    fun `updating title in a non custom task should be bad request`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val givenTaskToUpdate = deal.findOneTask()

        // When
        val response = HttpClient.patch(
            url = givenTaskToUpdate.id.getPatchURL(),
            body = JsonMapper.encode(
                UpdateTaskRequest(
                    title = "New title"
                )
            ),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertThat(response.message, equalTo("Some fields are only allowed to be edited for custom tasks"))
    }
}
