package realestate.unlock.dealroom.api.functional.deal.file

import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.directory.UrlType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.CreateDirectoryItemInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.FileDirectoryUrl
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetFileDirectoryUrlHandlerTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/file-urls",
        method = Method.GET.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/file-urls",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/file-urls",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return == 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/directory"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
    }

    private fun getInput(): CreateDirectoryItemInput =
        CreateDirectoryItemInput(
            path = anyString(),
            name = anyString()
        )

    @Test
    fun `can get url upload for file`() {
        val givenDeal = DealCreator.createDealByRest()
        val fileName = anyString()
        val path = "reports/pepe/filsito"
        val urlUpload = HttpClient.get(
            url = buildUrl(givenDeal.id, fileName, path, UrlType.UPLOAD),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, FileDirectoryUrl::class.java) }
        )

        Assertions.assertEquals("www.keyway.com/deal/${givenDeal.id}/repository/$path/$fileName", urlUpload.url)
    }

    @Test
    fun `can get url upload for file with empty path`() {
        val givenDeal = DealCreator.createDealByRest()
        val fileName = anyString()
        val path = ""
        val urlUpload = HttpClient.get(
            url = buildUrl(givenDeal.id, fileName, path, UrlType.UPLOAD),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, FileDirectoryUrl::class.java) }
        )

        Assertions.assertEquals("www.keyway.com/deal/${givenDeal.id}/repository/$fileName", urlUpload.url)
    }

    @Test
    fun `can get url download for file with complete path`() {
        val givenDeal = DealCreator.createDealByRest()
        val fileName = anyString()
        val path = "reports/pepe/$fileName"
        val urlUpload = HttpClient.get(
            url = buildUrl(givenDeal.id, fileName, path, UrlType.DOWNLOAD),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, FileDirectoryUrl::class.java) }
        )

        Assertions.assertEquals("www.keyway.com/deal/${givenDeal.id}/repository/$path", urlUpload.url)
    }

    @Test
    fun `can get url download for file`() {
        val givenDeal = DealCreator.createDealByRest()
        val fileName = anyString()
        val path = "reports/pepe"
        val urlUpload = HttpClient.get(
            url = buildUrl(givenDeal.id, fileName, path, UrlType.DOWNLOAD),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, FileDirectoryUrl::class.java) }
        )

        Assertions.assertEquals("www.keyway.com/deal/${givenDeal.id}/repository/$path/$fileName", urlUpload.url)
    }

    private fun buildUrl(dealId: Long, fileName: String, path: String, urlType: UrlType) =
        """$localUrl/deal/$dealId/file-urls?fileName=$fileName&filePath=$path&urlType=${urlType.name}"""
}
