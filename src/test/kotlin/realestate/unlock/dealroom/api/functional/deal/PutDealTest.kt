package realestate.unlock.dealroom.api.functional.deal

import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.nullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.UpdateDates
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.create.CreateSaleLeasebackDealInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.member.post.PostMemberRequestBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FeatureFlagsStub
import java.time.LocalDate

class PutDealTest : BaseFunctionalTest() {

    private val featureFlags = Context.injector.getInstance(FeatureFlags::class.java) as FeatureFlagsStub

    @BeforeEach
    fun setUp() {
        featureFlags.configureFeature(Feature.ORG_CAN_CREATE_NNN, true)
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/dates",
        method = Method.PUT.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${givenDeal.id}/dates",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${givenDeal.id}/dates",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${givenDeal.id}/dates",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should update deal successfully`() {
        // Given
        val givenSeller = givenSeller()
        val givenBuyer = givenBuyer()

        val givenUrl = "$localUrl/deal"
        val givenRequestCreationBody = givenDealCreationRequestBody(givenSeller, givenBuyer)

        val deal = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenRequestCreationBody))
            .asJson().body.toPrettyString().let { JsonMapper.decode(it, CompleteDeal::class.java) }

        val givenLoiExecutedDate = LocalDate.now()
        val givenUpdateDates = UpdateDates(loiExecutedDate = givenLoiExecutedDate)

        // When
        val result = Unirest.put("$givenUrl/${deal.id}/dates")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenUpdateDates))
            .asJson()

        // Then
        assertThat(result.status, equalTo(202))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            DealDto::class.java
        )

        assertThat(response.loiExecutedDate, equalTo(givenLoiExecutedDate))
        assertThat(response.id, equalTo(deal.id))
        assertThat(response.propertyId, equalTo(deal.propertyId))
        assertThat(response.contractExecutedDate, nullValue())
        assertThat(response.outsideClosingDate, nullValue())
        assertThat(response.initialClosingDate, nullValue())
        assertThat(response.diligenceExpirationDate, nullValue())
    }

    @Test
    fun `should update deal without LOI execution date`() {
        // Given
        val newDate = LocalDate.now()
        val givenSeller = givenSeller()
        val givenBuyer = givenBuyer()

        val givenUrl = "$localUrl/deal"
        val givenRequestCreationBody = givenDealCreationRequestBody(givenSeller, givenBuyer)

        val deal = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenRequestCreationBody))
            .asJson().body.toPrettyString().let { JsonMapper.decode(it, CompleteDeal::class.java) }

        val givenUpdateDates = UpdateDates(
            contractExecutedDate = newDate
        )

        // When
        val result = Unirest.put("$givenUrl/${deal.id}/dates")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenUpdateDates))
            .asJson()

        // Then
        assertThat(result.status, equalTo(202))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            DealDto::class.java
        )

        assertThat(response.contractExecutedDate, equalTo(newDate))
        assertThat(response.id, equalTo(deal.id))
        assertThat(response.propertyId, equalTo(deal.propertyId))
        assertThat(response.loiExecutedDate, nullValue())
        assertThat(response.outsideClosingDate, nullValue())
        assertThat(response.initialClosingDate, nullValue())
        assertThat(response.diligenceExpirationDate, nullValue())
    }

    @Test
    fun `should fail if deal not exists`() {
        // Given
        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val givenUrl = "$localUrl/deal/-1"

        val givenUpdateDates = UpdateDates(
            loiExecutedDate = LocalDate.now()
        )

        // When
        val result = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenUpdateDates))
            .asJson()

        // Then
        assertThat(result.status, equalTo(404))
    }

    private fun givenSeller(): MemberDto = givenMemberCreation("seller", "<EMAIL>")
    private fun givenBuyer(): MemberDto = givenMemberCreation("buyer", "<EMAIL>")

    private fun givenMemberCreation(memberType: String, email: String): MemberDto {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val givenMemberRequestCreationBody = PostMemberRequestBuilder()
            .apply { this.memberType = memberType }
            .apply { this.phoneNumber = givenPhone }
            .apply { this.email = email }
            .build()

        val givenMemberUrl = "$localUrl/member"
        return Unirest.post(givenMemberUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenMemberRequestCreationBody))
            .asString()
            .let { JsonMapper.decode(it.body, MemberDto::class.java) }
    }

    private fun givenDealCreationRequestBody(givenSeller: MemberDto, givenBuyer: MemberDto): CreateSaleLeasebackDealInput =
        CreateDealInputBuilder()
            .apply { propertyKeywayId = null }
            .apply { sellerMemberId = givenSeller.id }
            .apply { buyerMemberId = givenBuyer.id }
            .buildSaleLeaseback()
}
