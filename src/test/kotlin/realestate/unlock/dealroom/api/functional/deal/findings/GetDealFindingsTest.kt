package realestate.unlock.dealroom.api.functional.deal.findings

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.findings.EntityType
import realestate.unlock.dealroom.api.core.entity.deal.findings.Finding
import realestate.unlock.dealroom.api.core.entity.deal.findings.FindingStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.repository.database.deal.finding.FindingDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.OffsetDateTime

class GetDealFindingsTest : BaseFunctionalTest() {

    private lateinit var findingDatabaseRepository: FindingDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        findingDatabaseRepository = Context.injector.getInstance(FindingDatabaseRepository::class.java)
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/findings",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `it retrieves the findings of the deal`() {
        val givenDeal = DealCreator.createDealByRest()
        createFinding(givenDeal.id)
        createFinding(givenDeal.id)

        val response = HttpClient.get(
            url = "$localUrl/deal/${givenDeal.id}/findings",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<Finding>>() {}) }
        )

        assertThat(response, hasSize(2))
    }

    private fun createFinding(dealId: Long) = findingDatabaseRepository.save(givenFindingForDeal(dealId))

    private fun givenFindingForDeal(dealId: Long) = Finding(
        id = findingDatabaseRepository.nextId(),
        dealId = dealId,
        entity = EntityType.REPORT,
        entityType = "ZONING",
        kFileId = "an-amazing-id",
        message = anyString(),
        status = FindingStatus.OPEN,
        createdAt = OffsetDateTime.now(),
        updatedByMemberId = null
    )
}
