package realestate.unlock.dealroom.api.functional.deal.file

import com.keyway.kommons.http.exception.BadRequestException
import com.keyway.kommons.http.exception.NotFoundException
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.file.DeleteDirectoriesInput
import realestate.unlock.dealroom.api.core.entity.deal.file.DeleteDirectoriesInputDirectory
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.CreateLinkedFileInput
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileData
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.*
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DealFilesAndFoldersConfig
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.getRandomTask
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class DeleteDirectoriesTest : BaseDealDirectoryTest() {

    private lateinit var dealFilesAndFoldersConfig: DealFilesAndFoldersConfig

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        dealFilesAndFoldersConfig = Context.injector.getInstance(DealFilesAndFoldersConfig::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/directory",
        method = Method.DELETE.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.DELETE.name,
            url = "$localUrl/deal/${givenDeal.id}/directory",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.DELETE.name,
            url = "$localUrl/deal/${givenDeal.id}/directory",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return == 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/directory"

        // Then
        HttpClient.delete(
            url = givenUrl,
            token = token,
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
    }

    @Test
    fun `delete a single file inside a folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "some-folder")
        createFile(deal.id, "some-folder", "file.json")
        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FILE,
                    path = "some-folder/file.json"
                )
            )
        )

        // When
        deleteDirectories(deal.id, input)

        // Then
        getDirectory(deal.id, "some-folder").let {
            assertEquals(0, it.size)
        }
        getDirectory(deal.id, "").let {
            assertEquals(1, it.size)
            assertEquals(DirectoryItemType.folder, it[0].type)
        }
    }

    @Test
    fun `delete a file with a folder that has the same name`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "good-name")
        createFile(deal.id, "", "good-name")
        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FILE,
                    path = "good-name"
                )
            )
        )

        // When
        deleteDirectories(deal.id, input)

        // Then
        getDirectory(deal.id, "").let {
            assertEquals(1, it.size)
            assertEquals("good-name", it[0].name)
            assertEquals(DirectoryItemType.folder, it[0].type)
        }
    }

    @Test
    fun `delete a folder with a file that has the same name`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "good-name")
        createFile(deal.id, "", "good-name")
        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FOLDER,
                    path = "good-name"
                )
            )
        )

        // When
        deleteDirectories(deal.id, input)

        // Then
        getDirectory(deal.id, "").let {
            assertEquals(1, it.size)
            assertEquals("good-name", it[0].name)
            assertEquals(DirectoryItemType.file, it[0].type)
        }
    }

    @Test
    fun `delete a folder that does not exists returns 404`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "abc")
        createFile(deal.id, "abc", "file.pdf")
        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FOLDER,
                    path = "ab"
                )
            )
        )

        // When
        val result = HttpClient.delete(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NOT_FOUND,
            responseHandler = { JsonMapper.decode(it, NotFoundException::class.java) }
        )

        // Then
        assertEquals("Folder not found with path: ab", result.message)
        getDirectory(deal.id, "").let {
            assertEquals(1, it.size)
            assertEquals("abc", it[0].name)
            assertEquals(DirectoryItemType.folder, it[0].type)
        }
    }

    @Test
    fun `delete a file that does not exists returns 404`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "file")
        createFile(deal.id, "", "file2")
        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FILE,
                    path = "file"
                )
            )
        )

        // When
        val result = HttpClient.delete(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NOT_FOUND,
            responseHandler = { JsonMapper.decode(it, NotFoundException::class.java) }
        )

        // Then
        assertEquals("File not found with path: file", result.message)
        getDirectory(deal.id, "").let {
            assertEquals(2, it.size)
            assertEquals("file", it[0].name)
            assertEquals(DirectoryItemType.folder, it[0].type)
            assertEquals("file2", it[1].name)
            assertEquals(DirectoryItemType.file, it[1].type)
        }
    }

    @Test
    fun `delete a file given another file with the same prefix name`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFile(deal.id, "", "file")
        createFile(deal.id, "", "file and stuff")
        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FILE,
                    path = "file"
                )
            )
        )

        // When
        deleteDirectories(deal.id, input)

        // Then
        getDirectory(deal.id, "").let {
            assertEquals(1, it.size)
            assertEquals("file and stuff", it[0].name)
            assertEquals(DirectoryItemType.file, it[0].type)
        }
    }

    @Test
    fun `delete an empty folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "some-folder")
        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FOLDER,
                    path = "some-folder"
                )
            )
        )

        // When
        deleteDirectories(deal.id, input)

        // Then
        getDirectory(deal.id, "").let {
            assertEquals(0, it.size)
        }
    }

    @Test
    fun `delete multiples folders and files`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "folder1")
        createFolder(deal.id, "folder1", "f1-inside-folder1")
        createFile(deal.id, "folder1/f1-inside-folder1", "file-0.pdf")
        createFolder(deal.id, "folder1", "f2-inside-folder1")
        createFile(deal.id, "folder1/f2-inside-folder1", "file-a.pdf")
        createFile(deal.id, "folder1/f2-inside-folder1", "file-b.pdf")
        createFile(deal.id, "folder1", "file-c.pdf")
        createFile(deal.id, "folder1", "file-d.pdf")
        createFolder(deal.id, "", "folder2")
        createFile(deal.id, "folder2", "file-e.pdf")
        createFile(deal.id, "folder2", "file-f.pdf")
        createFolder(deal.id, "", "folder3")
        createFile(deal.id, "", "file-g.pdf")
        createFile(deal.id, "", "file-h.pdf")
        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(DirectoryItemTypeEnum.FOLDER, "folder1/f1-inside-folder1"),
                DeleteDirectoriesInputDirectory(DirectoryItemTypeEnum.FILE, "folder1/f2-inside-folder1/file-a.pdf"),
                DeleteDirectoriesInputDirectory(DirectoryItemTypeEnum.FILE, "folder1/file-c.pdf"),
                DeleteDirectoriesInputDirectory(DirectoryItemTypeEnum.FOLDER, "folder2"),
                DeleteDirectoriesInputDirectory(DirectoryItemTypeEnum.FILE, "file-h.pdf")
            )
        )

        // When
        deleteDirectories(deal.id, input)

        // Then
        getDirectory(deal.id, "").let {
            assertEquals(3, it.size)
            assert(it.any { it.type == DirectoryItemType.folder && it.name == "folder1" })
            assert(it.any { it.type == DirectoryItemType.folder && it.name == "folder3" })
            assert(it.any { it.type == DirectoryItemType.file && it.name == "file-g.pdf" })
        }
        getDirectory(deal.id, "folder1").let {
            assertEquals(2, it.size)
            assert(it.any { it.type == DirectoryItemType.folder && it.name == "f2-inside-folder1" })
            assert(it.any { it.type == DirectoryItemType.file && it.name == "file-d.pdf" })
        }
        getDirectory(deal.id, "folder1/f2-inside-folder1").let {
            assertEquals(1, it.size)
            assert(it.any { it.type == DirectoryItemType.file && it.name == "file-b.pdf" })
        }
    }

    @Test
    fun `delete a file linked returns bad request`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val file = createFile(deal.id, "", "file")
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = file.uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = deal.getRandomTask().id.toString(),
                filePath = null
            )
        )

        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FILE,
                    path = "file"
                )
            )
        )

        // When
        val result = HttpClient.delete(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertEquals("Cannot delete linked directory with path: file", result.message)
        getDirectory(deal.id, "").let {
            assertEquals(1, it.size)
            assertEquals("file", it[0].name)
        }
    }

    @Test
    fun `delete a folder with linked files returns bad request`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "folder")
        createFile(deal.id, "folder", "no-linked")
        val file = createFile(deal.id, "folder", "linked")
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = file.uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = deal.getRandomTask().id.toString(),
                filePath = null
            )
        )

        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FOLDER,
                    path = "folder"
                )
            )
        )

        // When
        val result = HttpClient.delete(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertEquals("Cannot delete linked directory with path: folder", result.message)
        getDirectory(deal.id, "folder").let {
            assertEquals(2, it.size)
            assertEquals("linked", it[0].name)
            assertEquals("no-linked", it[1].name)
        }
    }

    @Test
    fun `delete a file that was linked moves it to history`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFile(deal.id, "", "file.pdf")
            .let {
                createLinkedFile(
                    deal.id,
                    CreateLinkedFileInput(
                        fileId = it.uid,
                        entityType = LinkedFileEntityType.TASK,
                        entityId = deal.getRandomTask().id.toString(),
                        filePath = null
                    )
                )
            }
            .also { deleteLinkedFile(deal.id, it.id) }

        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FILE,
                    path = "file.pdf"
                )
            )
        )

        // When
        deleteDirectories(deal.id, input)

        // Then
        getDirectory(deal.id, "").let {
            assertEquals(0, it.size)
        }
        fileGateway.getFiles(FileUtils.dealHistoryPath(deal.id)).let {
            assertEquals(1, it.size)
            assertEquals(FileUtils.buildDealHistoryPath(deal.id, "file.pdf"), it[0].path)
        }
    }

    @Test
    fun `delete a folder with a file that was linked moves it to history`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "folder")
        createFile(deal.id, "folder", "non-linked.pdf")
        createFile(deal.id, "folder", "file.pdf")
            .let {
                createLinkedFile(
                    deal.id,
                    CreateLinkedFileInput(
                        fileId = it.uid,
                        entityType = LinkedFileEntityType.TASK,
                        entityId = deal.getRandomTask().id.toString(),
                        filePath = null
                    )
                )
            }
            .also { deleteLinkedFile(deal.id, it.id) }

        val input = DeleteDirectoriesInput(
            directories = setOf(
                DeleteDirectoriesInputDirectory(
                    type = DirectoryItemTypeEnum.FOLDER,
                    path = "folder"
                )
            )
        )

        // When
        deleteDirectories(deal.id, input)

        // Then
        getDirectory(deal.id, "").let {
            assertEquals(0, it.size)
        }
        fileGateway.getFiles(FileUtils.dealHistoryPath(deal.id)).let {
            assertEquals(1, it.size)
            assertEquals(FileUtils.buildDealHistoryPath(deal.id, "folder/file.pdf"), it[0].path)
        }
    }

    @Test
    fun `delete a file that was linked and path already exists in history, should change name`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val input = DeleteDirectoriesInput(
            directories = setOf(DeleteDirectoriesInputDirectory(DirectoryItemTypeEnum.FILE, "file.pdf"))
        )

        // Create, link, unlink and delete first file
        createFile(deal.id, "", "file.pdf")
            .let {
                createLinkedFile(
                    deal.id,
                    CreateLinkedFileInput(
                        fileId = it.uid,
                        entityType = LinkedFileEntityType.TASK,
                        entityId = deal.getRandomTask().id.toString(),
                        filePath = null
                    )
                )
            }
            .also { deleteLinkedFile(deal.id, it.id) }
            .also { deleteDirectories(deal.id, input) }

        // Create, link and unlink second file with same name
        createFile(deal.id, "", "file.pdf")
            .let {
                createLinkedFile(
                    deal.id,
                    CreateLinkedFileInput(
                        fileId = it.uid,
                        entityType = LinkedFileEntityType.TASK,
                        entityId = deal.getRandomTask().id.toString(),
                        filePath = null
                    )
                )
            }
            .also { deleteLinkedFile(deal.id, it.id) }

        // When
        deleteDirectories(deal.id, input)

        // Then
        getDirectory(deal.id, "").let {
            assertEquals(0, it.size)
        }
        fileGateway.getFiles(FileUtils.dealHistoryPath(deal.id)).let {
            assertEquals(2, it.size)
            assertEquals(FileUtils.buildDealHistoryPath(deal.id, "file.pdf"), it[0].path)
            assertEquals(FileUtils.buildDealHistoryPath(deal.id, "file (1).pdf"), it[1].path)
        }
    }

    private fun createLinkedFile(dealId: Long, input: CreateLinkedFileInput) = HttpClient.post(
        url = "$localUrl/deal/$dealId/linked-files",
        body = JsonMapper.encode(input),
        expectedStatus = HttpStatus.CREATED,
        responseHandler = { JsonMapper.decode(it, LinkedFileData::class.java) }
    )

    private fun deleteLinkedFile(dealId: Long, linkedFileId: Long): Unit = HttpClient.delete(
        url = "$localUrl/deal/$dealId/linked-files/$linkedFileId",
        expectedStatus = HttpStatus.NO_CONTENT,
        responseHandler = { }
    )
}
