package realestate.unlock.dealroom.api.functional.deal.loi

import io.swagger.models.Method
import kong.unirest.HttpResponse
import kong.unirest.JsonNode
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.DealFile
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMultifamilyRound
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.PostMultifamilyLoiRoundRequest
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.PostMultifamilyLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class PostMultifamilyLoiTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser(memberType = "buyer")
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/loi/round",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `can create a loi offer`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val createLoiUrl = "$localUrl/deal/${deal.id}/loi/round"
        val request = givenLoiCreationRequest()

        // When
        val result = Unirest.post(createLoiUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(request))
            .asJson()

        // Then
        assertLoiCreation(result, request)
    }

    @Test
    fun `succeeds to create a loi offer when there is other in negotiation`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val createLoiUrl = "$localUrl/deal/${deal.id}/loi/round"
        givenLoiFor(createLoiUrl, givenLoiCreationRequest())

        // When
        val result = Unirest.post(createLoiUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenLoiCreationRequest()))
            .asJson()

        // Then
        assertThat(result.status, equalTo(201))
    }

    private fun givenLoiFor(
        createLoiUrl: String,
        request: PostMultifamilyLoiRoundRequest
    ) {
        Unirest.post(createLoiUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(request))
            .asEmpty()
    }

    private fun givenLoiCreationRequest() = PostMultifamilyLoiRequestBuilder().build()

    private fun assertLoiCreation(
        result: HttpResponse<JsonNode>,
        request: PostMultifamilyLoiRoundRequest
    ) {
        assertThat(result.status, equalTo(201))
        val response = JsonMapper.decode(result.body.toPrettyString(), LoiMultifamilyRound::class.java)
        assertThat(response.status, IsEqual(LetterOfIntentRoundStatus.IN_NEGOTIATION))
        assertThat(response.offerClosingExtensionDeposit, IsEqual(request.offerClosingExtensionDeposit))
        assertThat(response.offerFiles, IsEqual(request.offerFiles.map { DealFile(uid = it.uid, name = it.name) }))
    }
}
