package realestate.unlock.dealroom.api.functional.deal.findings

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.findings.EntityType
import realestate.unlock.dealroom.api.core.entity.deal.findings.Finding
import realestate.unlock.dealroom.api.core.entity.deal.findings.FindingStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.finding.UpdateDealFindingRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.repository.database.deal.finding.FindingDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.OffsetDateTime

class UpdateDealFindingTest : BaseFunctionalTest() {

    private lateinit var findingDatabaseRepository: FindingDatabaseRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        findingDatabaseRepository = Context.injector.getInstance(FindingDatabaseRepository::class.java)
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/findings/123",
        method = Method.PUT.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `it updates the finding status`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenFinding = givenFindingForDeal(dealId = givenDeal.id)
        findingDatabaseRepository.save(givenFinding)

        val updatedFinding = HttpClient.put(
            url = "$localUrl/deal/${givenDeal.id}/findings/${givenFinding.id}",
            body = JsonMapper.encode(UpdateDealFindingRequest(status = FindingStatus.DISMISSED)),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, Finding::class.java) }
        )

        assertThat(givenFinding.status, equalTo(FindingStatus.OPEN))
        assertThat(updatedFinding.status, equalTo(FindingStatus.DISMISSED))
        assertThat(updatedFinding.id, equalTo(givenFinding.id))
    }

    private fun givenFindingForDeal(dealId: Long) = Finding(
        id = findingDatabaseRepository.nextId(),
        dealId = dealId,
        entity = EntityType.REPORT,
        entityType = "ZONING",
        kFileId = "another-amazing-id",
        message = anyString(),
        status = FindingStatus.OPEN,
        createdAt = OffsetDateTime.now(),
        updatedByMemberId = null
    )
}
