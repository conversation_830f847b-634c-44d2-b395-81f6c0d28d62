package realestate.unlock.dealroom.api.functional.deal

import com.fasterxml.jackson.core.type.TypeReference
import io.mockk.every
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.gateway.property.PropertyDetailsGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.get.DealTemplateResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.entity.property.PropertyDetailsBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealTemplateTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/prospect/123/template",
        method = Method.GET.name,
        permissions = listOf()
    )

    @Test
    fun `buyer cant get sign url of a not in process signing loi`() {
        // Given
        val propertyId = anyString()

        val propertyDetails = givenPropertyDetails(propertyId)

        // When
        val response = Unirest.get("$localUrl/prospect/$propertyId/template")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(response.status, IsEqual(HttpStatus.OK))

        val details = JsonMapper.decode(
            response.body.toPrettyString(),
            object : TypeReference<DealTemplateResponse>() {}
        )

        assertThat(details.keywayId, IsEqual(propertyDetails.keywayId))
        assertThat(details.address, IsEqual(propertyDetails.address))
        assertThat(details.city, IsEqual(propertyDetails.city))
        assertThat(details.state, IsEqual(propertyDetails.state))
        assertThat(details.squareFootage, IsEqual(propertyDetails.squareFootage))
        assertThat(details.constructionYear, IsEqual(propertyDetails.constructionYear))
        assertThat(details.coordinates, IsEqual(propertyDetails.coordinates))
        assertThat(details.vertical, IsEqual(propertyDetails.vertical))
        assertThat(details.tenant, IsEqual(propertyDetails.tenant))
    }

    private fun givenPropertyDetails(propertyId: String) =
        PropertyDetailsBuilder()
            .apply { this.keywayId = propertyId }
            .build()
            .apply {
                Context.injector.getInstance(PropertyDetailsGateway::class.java).let {
                    every { it.findByPropertyKeywayId(propertyId) } returns this
                }
            }
}
