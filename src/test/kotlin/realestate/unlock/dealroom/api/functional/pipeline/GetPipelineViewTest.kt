package realestate.unlock.dealroom.api.functional.pipeline

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.pipeline.PipelineView
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.pipeline.PipelineViewInput
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetPipelineViewTest : BaseFunctionalTest() {

    private lateinit var member: Member

    @BeforeEach
    fun setUp() {
        member = AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/pipeline-views/123",
        method = Method.GET.name,
        permissions = listOf()
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/pipeline-views/123",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `it can retrieve a pipeline view`() {
        // Given
        val input = PipelineViewInput(
            name = "Pipeline View 1",
            propertyType = PropertyType.MULTIFAMILY,
            data = mapOf("some" to "data")
        )
        val created = createPipelineView(input)

        // When
        val response = HttpClient.get(
            url = "$localUrl/pipeline-views/${created.id}",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, PipelineView::class.java) }
        )

        // Then
        assertThat(response.name, equalTo(input.name))
        assertThat(response.propertyType, equalTo(input.propertyType))
        assertThat(response.data, equalTo(input.data))
        assertThat(response.organizationId, equalTo(member.organizationId))
    }

    private fun createPipelineView(input: PipelineViewInput) = HttpClient.post(
        url = "$localUrl/pipeline-views",
        body = JsonMapper.encode(input),
        expectedStatus = HttpStatus.CREATED,
        responseHandler = { JsonMapper.decode(it, PipelineView::class.java) }
    )
}
