package realestate.unlock.dealroom.api.functional.task.comment

import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.task.comment.TaskCommentRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.post.request.TaskCommentRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class SaveTaskCommentTest : BaseFunctionalTest() {

    private lateinit var givenMember: Member
    private lateinit var taskCommentRepository: TaskCommentRepository

    @BeforeEach
    fun setUp() {
        givenMember = AuthMock.createMemberWithUser()
        taskCommentRepository = Context.injector.getInstance(TaskCommentRepository::class.java)
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/123/comment",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/task/$givenTaskId/comment",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/task/$givenTaskId/comment",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/task/$givenTaskId/comment",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `It adds a new comment as expected`() {
        // given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id
        val givenMessage = "this is a new message"

        val taskCommentRequest = TaskCommentRequest(comment = givenMessage)
        val result = Unirest.post("$localUrl/task/$givenTaskId/comment")
            .body(JsonMapper.encode(taskCommentRequest))
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(201))
        verifyCommentWasAddedToTask(taskId = givenTaskId, expectedComment = givenMessage)
    }

    private fun verifyCommentWasAddedToTask(taskId: Long, expectedComment: String) {
        val comments = taskCommentRepository.findByTaskId(taskId)

        assertThat(comments, hasSize(1))
        assertThat(comments[0].comment, equalTo(expectedComment))
    }
}
