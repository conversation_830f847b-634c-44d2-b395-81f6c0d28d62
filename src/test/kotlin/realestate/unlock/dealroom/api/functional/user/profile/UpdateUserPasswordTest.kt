package realestate.unlock.dealroom.api.functional.user.profile

import com.keyway.security.domain.user.NewUserPasswordInput
import com.keyway.security.domain.user.User
import com.keyway.security.domain.user.UserImpersonationSdk
import io.mockk.every
import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.CoreMatchers.containsString
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.entrypoint.rest.contract.user.profile.patch.PatchUserPasswordRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class UpdateUserPasswordTest : BaseFunctionalTest() {

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/user/profile/password",
        method = Method.PATCH.name,
        permissions = listOf()
    )

    @Test
    fun `should update user password successfully`() {
        // Given
        val member = AuthMock.createMemberWithUser()
        val givenUrl = "$localUrl/user/profile/password"
        val givenPatchUserPasswordRequest = PatchUserPasswordRequest(
            currentPassword = "current-password",
            newPassword = "new-password"
        )

        Context.injector.getInstance(UserImpersonationSdk::class.java).let { userImpersonationSdk ->
            every { userImpersonationSdk.login(any()) } returns mockk()

            val givenUser = User(
                id = member.authId!!,
                email = member.email,
                name = member.fullName,
                emailVerified = true,
                blocked = false
            )
            every { userImpersonationSdk.updatePassword(eq(NewUserPasswordInput(givenUser.id, givenPatchUserPasswordRequest.newPassword))) } returns givenUser
        }

        // When
        val result = Unirest.patch(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenPatchUserPasswordRequest))
            .asJson()

        // Then
        assertThat(result.status, equalTo(HttpStatus.ACCEPTED))
    }

    @Test
    fun `should fail if send invalid credentials`() {
        // Given
        val member = AuthMock.createMemberWithUser()
        val givenUrl = "$localUrl/user/profile/password"
        val givenPatchUserPasswordRequest = PatchUserPasswordRequest(
            currentPassword = "current-password",
            newPassword = "new-password"
        )

        Context.injector.getInstance(UserImpersonationSdk::class.java).let { userImpersonationSdk ->
            every { userImpersonationSdk.login(any()) } throws Exception()
        }

        // When
        val result = Unirest.patch(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenPatchUserPasswordRequest))
            .asJson()

        // Then
        assertThat(result.status, equalTo(HttpStatus.UNAUTHORIZED))
        assertThat(result.body.toPrettyString(), containsString("\"message\": \"Invalid credentials\""))
    }

    @Test
    fun `should fail if update user password fails`() {
        // Given
        val member = AuthMock.createMemberWithUser()
        val givenUrl = "$localUrl/user/profile/password"
        val givenPatchUserPasswordRequest = PatchUserPasswordRequest(
            currentPassword = "current-password",
            newPassword = "new-password"
        )

        Context.injector.getInstance(UserImpersonationSdk::class.java).let { userImpersonationSdk ->
            every { userImpersonationSdk.login(any()) } returns mockk()

            every { userImpersonationSdk.updatePassword(any()) } throws Exception()
        }

        // When
        val result = Unirest.patch(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenPatchUserPasswordRequest))
            .asJson()

        // Then
        assertThat(result.status, equalTo(HttpStatus.INTERNAL_SERVER_ERROR))
        assertThat(result.body.toPrettyString(), containsString("\"message\": \"Server Error\""))
    }
}
