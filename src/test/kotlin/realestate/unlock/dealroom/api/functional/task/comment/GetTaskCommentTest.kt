package realestate.unlock.dealroom.api.functional.task.comment

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.task.comment.TaskComment
import realestate.unlock.dealroom.api.core.entity.task.comment.TaskCommentToSave
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.task.comment.TaskCommentRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetTaskCommentTest : BaseFunctionalTest() {

    private lateinit var givenMember: Member
    private lateinit var taskCommentRepository: TaskCommentRepository

    @BeforeEach
    fun setUp() {
        givenMember = AuthMock.createMemberWithUser()
        taskCommentRepository = Context.injector.getInstance(TaskCommentRepository::class.java)
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/123/comment",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/task/$givenTaskId/comment",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/task/$givenTaskId/comment",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/task/$givenTaskId/comment",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `It retrieves the comments ordered by descending creation date`() {
        // given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id
        val firstCommentMessage = "this is the first comment"
        val secondCommentMessage = "this is the second comment"
        val thirdCommentMessage = "this is the third comment"

        addCommentToTask(taskId = givenTaskId, comment = firstCommentMessage)
        addCommentToTask(taskId = givenTaskId, comment = secondCommentMessage)
        addCommentToTask(taskId = givenTaskId, comment = thirdCommentMessage)

        // when
        val result = Unirest.get("$localUrl/task/$givenTaskId/comment")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<TaskComment>>() {}
        )

        assertThat(response, hasSize(3))
        assertThat(response[0].comment, equalTo(thirdCommentMessage))
        assertThat(response[1].comment, equalTo(secondCommentMessage))
        assertThat(response[2].comment, equalTo(firstCommentMessage))
    }

    private fun addCommentToTask(taskId: Long, comment: String) {
        taskCommentRepository.save(
            TaskCommentToSave(
                memberId = givenMember.id,
                taskId = taskId,
                comment = comment
            )
        )
    }
}
