package realestate.unlock.dealroom.api.functional.deal.file.linked

import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.CreateLinkedFileInput
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.NotFoundException
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.getRandomTask
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class DeleteLinkedFileTest : BaseLinkedFilesTest() {

    private lateinit var fileGateway: FileGateway

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/linked-files/123",
        method = Method.DELETE.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.DELETE.name,
            url = "$localUrl/deal/${givenDeal.id}/linked-files/123",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.DELETE.name,
            url = "$localUrl/deal/${givenDeal.id}/linked-files/123",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return == 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/linked-files/123"

        // Then
        HttpClient.delete(
            url = givenUrl,
            token = token,
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
    }

    @Test
    fun `delete a linked file that does not exists returns 404`() {
        // Given
        val deal = DealCreator.createDealByRest()

        // When
        val result = deleteLinkedFileWithResponse(deal.id, 123, HttpStatus.NOT_FOUND, NotFoundException::class.java)

        // Then
        assertEquals("Resource [LinkedFile] with ID [123] not found", result.message)
    }

    @Test
    fun `delete a linked file that was already deleted returns 404`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val linkedFile = createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = createFile(deal.id, "", "file.pdf").uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = deal.getRandomTask().id.toString(),
                filePath = null
            )
        )
        deleteLinkedFile(deal.id, linkedFile.id)

        // When
        val result = deleteLinkedFileWithResponse(deal.id, linkedFile.id, HttpStatus.NOT_FOUND, NotFoundException::class.java)

        // Then
        assertEquals("Resource [LinkedFile] with ID [${linkedFile.id}] not found", result.message)
    }

    @Test
    fun `delete a linked file`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val linkedFile = createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = createFile(
                    deal.id,
                    "",
                    "file.pdf"
                ).uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = deal.getRandomTask().id.toString(),
                filePath = null
            )
        )

        // When
        deleteLinkedFile(deal.id, linkedFile.id)

        // Then
        val result = getLinkedFiles(deal.id)
        assertEquals(0, result.size)
    }

    private fun createFile(dealId: Long, path: String, name: String) = fileGateway.uploadFile(
        FileToUpload(
            content = mockk(),
            contentType = "application/pdf",
            path = FileUtils.buildDealRepositoryPath(dealId, path),
            filename = name
        )
    )
}
