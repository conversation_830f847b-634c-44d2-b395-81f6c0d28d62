package realestate.unlock.dealroom.api.functional.task.file

import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.GetFileResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub
import java.net.URL
import kotlin.io.path.ExperimentalPathApi

class GetTaskWithFilesAndHistoryFileStagingTest : BaseFunctionalTest() {

    private lateinit var taskFileGatewayStub: FileGatewayStub

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        taskFileGatewayStub = Context.injector.getInstance(FileGateway::class.java) as FileGatewayStub
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/123/file/123",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskToUpdate = givenDeal.findTaskByTemplateKey("financial_statements")

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/task/${givenTaskToUpdate.id}/file/123",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskToUpdate = givenDeal.findTaskByTemplateKey("financial_statements")

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/task/${givenTaskToUpdate.id}/file/123",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val dealResponse = DealCreator.createDealByRest(seller = seller)
        val givenKFileId = anyString()
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")
        val givenGetTaskFileUrl = "$localUrl/task/${givenTaskToUpdate.id}/file/$givenKFileId"

        // When
        val result = Unirest.get(givenGetTaskFileUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, not(equalTo(403)))
    }

    @OptIn(ExperimentalPathApi::class)
    @Test
    fun `Task file (staging) should be correctly retrieved`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val givenKFileId = anyString()
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")
        val givenGetTaskFileUrl = "$localUrl/task/${givenTaskToUpdate.id}/file/$givenKFileId"

        // When
        val result = Unirest.get(givenGetTaskFileUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(result.body.toPrettyString(), GetFileResponse::class.java)

        assertThat(response.uid, equalTo(givenKFileId))
        assertThat(response.url, not(emptyString()))
        assertThat(response.url, equalTo(URL(response.url).toString()))
    }

    @OptIn(ExperimentalPathApi::class)
    @Test
    fun `Task file (staging) should be correctly retrieved if the user is not admin but is member of the related deal`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val givenSeller = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val dealResponse = DealCreator.createDealByRest(seller = givenSeller)
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")
        val givenKFileId = anyString()
        val givenGetTaskFileUrl = "$localUrl/task/${givenTaskToUpdate.id}/file/$givenKFileId"

        // When
        val result = Unirest.get(givenGetTaskFileUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(result.body.toPrettyString(), GetFileResponse::class.java)

        assertThat(response.uid, equalTo(givenKFileId))
        assertThat(response.url, not(emptyString()))
        assertThat(response.url, equalTo(URL(response.url).toString()))
    }

    @OptIn(ExperimentalPathApi::class)
    @Test
    fun `Task file (staging) should be restricted if the user is not admin and is not member of the related deal`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken,
            permissions = listOf(Permission.READ_OWN_DEALS)
        )

        val dealResponse = DealCreator.createDealByRest()
        val givenKFileId = anyString()
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")
        val givenGetTaskFileUrl = "$localUrl/task/${givenTaskToUpdate.id}/file/$givenKFileId"

        // When
        val result = Unirest.get(givenGetTaskFileUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
        assertThat(result.body.toPrettyString(), CoreMatchers.containsString("In order to access to the task you need to be member of the related deal"))
    }

    @Test
    fun `Should return bad request if task file (staging) cannot be retrieved`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")
        val givenNonExistentTaskFileId = 99999
        val givenTaskFileUrl = "$localUrl/task/${givenTaskToUpdate.id}/file/$givenNonExistentTaskFileId"
        taskFileGatewayStub.throwsNotFoundOnNextGet(true)

        // When
        val result = Unirest.get(givenTaskFileUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(404))
    }
}
