package realestate.unlock.dealroom.api.functional.deal.update

import io.mockk.verify
import io.swagger.models.Method
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.nullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.SourceType
import realestate.unlock.dealroom.api.core.entity.deal.team.AddDealTeamMembersInput
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileTokenStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.deal.AddDealTeamMembers
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.UpdateDates
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.repository.database.file.FileTokenDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal
import java.time.LocalDate

class UpdateDealTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123",
        method = Method.PATCH.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PATCH.name,
            url = "$localUrl/deal/${givenDeal.id}",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PATCH.name,
            url = "$localUrl/deal/${givenDeal.id}",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `it updates only guarantee type`() {
        val initialLoiExecutedDate = LocalDate.now().minusDays(1)
        val initialGuaranteeType = GuaranteeType.CORPORATE
        val initialVertical = "Medical"
        val givenDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                guaranteeType = initialGuaranteeType
                vertical = initialVertical
            },
            updateDates = UpdateDates(loiExecutedDate = initialLoiExecutedDate)
        )

        val updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"guarantee_type": "PERSONAL"}""",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.id, equalTo(givenDeal.id))
        assertThat(updatedDeal.guaranteeType, equalTo(GuaranteeType.PERSONAL))
        assertThat(updatedDeal.vertical, equalTo(initialVertical))
        assertThat(updatedDeal.loiExecutedDate, equalTo(initialLoiExecutedDate))
    }

    @Test
    fun `it updates only vertical`() {
        val initialLoiExecutedDate = LocalDate.now().minusDays(1)
        val initialGuaranteeType = GuaranteeType.CORPORATE
        val initialVertical = "Medical"
        val newVertical = "Pediatrics"
        val givenDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                guaranteeType = initialGuaranteeType
                vertical = initialVertical
            },
            updateDates = UpdateDates(loiExecutedDate = initialLoiExecutedDate)
        )

        val updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"vertical": "$newVertical"}""",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.id, equalTo(givenDeal.id))
        assertThat(updatedDeal.guaranteeType, equalTo(initialGuaranteeType))
        assertThat(updatedDeal.vertical, equalTo(newVertical))
        assertThat(updatedDeal.loiExecutedDate, equalTo(initialLoiExecutedDate))
    }

    @Test
    fun `it updates only loi executed date`() {
        val initialLoiExecutedDate = LocalDate.now().minusDays(1)
        val initialGuaranteeType = GuaranteeType.CORPORATE
        val initialVertical = "Medical"
        val givenDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                guaranteeType = initialGuaranteeType
                vertical = initialVertical
            },
            updateDates = UpdateDates(loiExecutedDate = initialLoiExecutedDate)
        )

        val newLoiExecutedDate = LocalDate.now().minusDays(5)
        val updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"loi_executed_date": "$newLoiExecutedDate"}""",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.id, equalTo(givenDeal.id))
        assertThat(updatedDeal.guaranteeType, equalTo(initialGuaranteeType))
        assertThat(updatedDeal.vertical, equalTo(initialVertical))
        assertThat(updatedDeal.loiExecutedDate, equalTo(newLoiExecutedDate))
    }

    @Test
    fun `it updates only tags`() {
        val initialLoiExecutedDate = LocalDate.now().minusDays(1)
        val initialGuaranteeType = GuaranteeType.CORPORATE
        val initialVertical = "Medical"
        val givenDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                guaranteeType = initialGuaranteeType
                vertical = initialVertical
            },
            updateDates = UpdateDates(loiExecutedDate = initialLoiExecutedDate)
        )

        val updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"tags": ["tag1", "tag2"]}""",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.id, equalTo(givenDeal.id))
        assertThat(updatedDeal.guaranteeType, equalTo(initialGuaranteeType))
        assertThat(updatedDeal.vertical, equalTo(initialVertical))
        assertThat(updatedDeal.loiExecutedDate, equalTo(initialLoiExecutedDate))
        assertThat(updatedDeal.tags, equalTo(setOf("tag1", "tag2")))
    }

    @Test
    fun `duplicate tag with different caps should fail and rollback transaction`() {
        val givenDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                tags = setOf("tag1", "tag2")
            }
        )

        // When
        val error = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"tags": ["Tag1"]}""",
            expectedStatus = 400,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertThat(error.message, equalTo("Can not add tag 'Tag1' because already exists as 'tag1'"))
        val dealDto = HttpClient.get(
            url = "$localUrl/deal/${givenDeal.id}",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )
        assertThat(dealDto.tags, equalTo(setOf("tag1", "tag2")))
    }

    @Test
    fun `it updates only leaderId`() {
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)

        val newBuyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer2-token", email = "<EMAIL>", uid = anyString())
        val newLeaderId = newBuyer.id
        Context.injector.getInstance(AddDealTeamMembers::class.java).also {
            it.add(AddDealTeamMembersInput(dealId = givenDeal.id, memberIds = listOf(newLeaderId)))
        }

        val updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"leader_id": $newLeaderId}""",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.id, equalTo(givenDeal.id))
        assertThat(updatedDeal.leaderId, equalTo(newLeaderId))
    }

    @Test
    fun `it can't update leaderId using a seller id`() {
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val newLeaderId = seller.id

        val error = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"leader_id": $newLeaderId}""",
            expectedStatus = 400,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        assertThat(error.message, equalTo("Leader must be a buyer member"))
    }

    @Test
    fun `it can't update leaderId using a non member`() {
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val buyerNonMember = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer2-token", email = "<EMAIL>", uid = anyString())
        val givenDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        val newLeaderId = buyerNonMember.id

        val error = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"leader_id": $newLeaderId}""",
            expectedStatus = 400,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        assertThat(error.message, equalTo("Leader must be a buyer member"))
    }

    @Test
    fun `it can't delete leaderId`() {
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenDeal = DealCreator.createDealByRest(seller = seller, buyer = buyer)

        val error = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"leader_id": null}""",
            expectedStatus = 400,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        assertThat(error.message, equalTo("leader can't be deleted"))
    }

    @Test
    fun `it can update nullable values to null`() {
        val initialLoiExecutedDate = LocalDate.now().minusDays(1)
        val initialGuaranteeType = GuaranteeType.CORPORATE
        val initialVertical = "Medical"
        val givenDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                guaranteeType = initialGuaranteeType
                vertical = initialVertical
            },
            updateDates = UpdateDates(loiExecutedDate = initialLoiExecutedDate)
        )

        val updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = """{"guarantee_type": null, "vertical": null, "loi_executed_date": null}""",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.guaranteeType, nullValue())
        assertThat(updatedDeal.vertical, nullValue())
        assertThat(updatedDeal.loiExecutedDate, nullValue())
    }

    @Test
    fun `it updates and confirms omFileId, but it doesn't if is not changed`() {
        val givenDeal = DealCreator.createDealByRest(updateDates = UpdateDates(loiExecutedDate = LocalDate.now().minusDays(1)))
        val fileTokenRepository = Context.injector.getInstance(FileTokenDatabaseRepository::class.java)
        val omFileId = anyString()
        var updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = JsonMapper.encode(mapOf("om_file_id" to omFileId)),
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )
        assertThat(updatedDeal.omFileId, equalTo(omFileId))
        assertThat(fileTokenRepository.findByFileId(omFileId)!!.status, equalTo(FileTokenStatus.PROCESSING))

        updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = JsonMapper.encode(mapOf("tags" to setOf("tag1", "tag2"))),
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )
        val fileGateway = Context.injector.getInstance(FileGateway::class.java)
        verify(exactly = 1) {
            fileGateway.confirmStagingFile(any())
        }

        assertThat(updatedDeal.omFileId, equalTo(omFileId))
        assertThat(updatedDeal.tags, equalTo(setOf("tag1", "tag2")))
    }

    @Test
    fun `it updates source type`() {
        val givenDeal = DealCreator.createDealByRest(updateDates = UpdateDates(loiExecutedDate = LocalDate.now().minusDays(1)))
        val updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = JsonMapper.encode(mapOf("source_type" to SourceType.OFF_MARKET)),
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.id, equalTo(givenDeal.id))
        assertThat(updatedDeal.sourceType, equalTo(SourceType.OFF_MARKET))
    }

    @Test
    fun `it updates deposits`() {
        val givenDeal = DealCreator.createDealByRest(updateDates = UpdateDates(loiExecutedDate = LocalDate.now().minusDays(1)))
        val newEarnestMoneyDeposit = BigDecimal.valueOf(1213)
        val newExtensionDeposit = BigDecimal.valueOf(8976)
        val updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = JsonMapper.encode(
                mapOf(
                    "earnest_money_deposit" to newEarnestMoneyDeposit,
                    "extension_deposit" to newExtensionDeposit
                )
            ),
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.id, equalTo(givenDeal.id))
        assertThat(updatedDeal.earnestMoneyDeposit, equalTo(newEarnestMoneyDeposit))
        assertThat(updatedDeal.extensionDeposit, equalTo(newExtensionDeposit))
    }

    @Test
    fun `it updates company names`() {
        val givenDeal = DealCreator.createDealByRest(updateDates = UpdateDates(loiExecutedDate = LocalDate.now().minusDays(1)))
        val newBuyerCompanyName = "new buyer company name"
        val newBrokerCompanyName = "new broker company name"
        val newSellerCompanyName = "new seller company name"
        val updatedDeal = HttpClient.patch(
            url = "$localUrl/deal/${givenDeal.id}",
            body = JsonMapper.encode(
                mapOf(
                    "buyer_company_name" to newBuyerCompanyName,
                    "broker_company_name" to newBrokerCompanyName,
                    "seller_company_name" to newSellerCompanyName
                )
            ),
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.id, equalTo(givenDeal.id))
        assertThat(updatedDeal.buyerCompanyName, equalTo(newBuyerCompanyName))
        assertThat(updatedDeal.brokerCompanyName, equalTo(newBrokerCompanyName))
        assertThat(updatedDeal.sellerCompanyName, equalTo(newSellerCompanyName))
    }
}
