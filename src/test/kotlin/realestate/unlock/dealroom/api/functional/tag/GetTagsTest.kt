package realestate.unlock.dealroom.api.functional.tag

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.core.entity.search.SortOrder
import realestate.unlock.dealroom.api.core.entity.tag.Tag
import realestate.unlock.dealroom.api.core.entity.tag.TagType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.pagination.PaginationUrlBuilder

class GetTagsTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/tags",
        method = Method.GET.name,
        permissions = listOf()
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/tags",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should get deals successfully`() {
        // Given
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        DealCreator.createDealByRest(buyer = buyer, seller = seller, dealCreationRequestBuilder = CreateDealInputBuilder().apply { tags = setOf("bcd", "abc") })
        DealCreator.createDealByRest(buyer = buyer, seller = seller, dealCreationRequestBuilder = CreateDealInputBuilder().apply { tags = setOf("blue", "test") })
        DealCreator.createDealByRest(buyer = buyer, seller = seller, dealCreationRequestBuilder = CreateDealInputBuilder().apply { tags = setOf("test") })
        DealCreator.createDealByRest(buyer = buyer, seller = seller, dealCreationRequestBuilder = CreateDealInputBuilder().apply { tags = setOf("tag1", "test", "red") })

        // When
        val response = HttpClient.get(
            url = PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "type" to mapOf(
                            "operator" to EnumOperator.EQ.name,
                            "values" to listOf(TagType.DEAL.name)
                        )
                    )
                )
                .withOrderBy("NAME")
                .withOrder(SortOrder.ASC)
                .getUrl("$localUrl/tags"),
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<Tag>>() {}) }
        )

        // Then
        assertThat(response.size, equalTo(6))
        assertThat(response.data[0].name, equalTo("abc"))
        assertThat(response.data[1].name, equalTo("bcd"))
        assertThat(response.data[2].name, equalTo("blue"))
        assertThat(response.data[3].name, equalTo("red"))
        assertThat(response.data[4].name, equalTo("tag1"))
        assertThat(response.data[5].name, equalTo("test"))
    }
}
