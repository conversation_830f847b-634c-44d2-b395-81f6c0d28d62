package realestate.unlock.dealroom.api.functional.deal

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.PendingTasksResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class GetDealPendingTasksTest : BaseFunctionalTest() {

    private val sellerToken = "seller-token"
    private val buyerToken = "buyer-token"

    private lateinit var dealRepository: DealRepository
    private lateinit var buyer: Member
    private lateinit var seller: Member

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = buyerToken, email = "<EMAIL>", uid = anyString())
        seller = AuthMock.createMemberWithUser(memberType = "seller", token = sellerToken, email = "<EMAIL>", uid = anyString())
        dealRepository = Context.injector.getInstance(DealRepository::class.java)
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/pending-tasks",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/pending-tasks",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/pending-tasks",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should not return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/pending-tasks",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = true
        )
    }

    @Test
    fun `Given a new deal it should return empty values`() {
        val deal = DealCreator.createDealByRest()
        val givenUrl = "$localUrl/deal/${deal.id}/pending-tasks"

        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<PendingTasksResponse>() {}
        )

        assertThat(response.top, empty())
        assertThat(response.upcoming, empty())
        assertThat(response.additional, empty())
    }

    @Test
    fun `Given a deal in in contract negotiation should returns tasks`() {
        val deal = givenDealInStage(Stage.NEGOTIATION)
        val givenUrl = "$localUrl/deal/${deal.id}/pending-tasks"

        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<PendingTasksResponse>() {}
        )

        assertThat(response.top.size, `is`(6))
        assertThat(response.upcoming, not(empty()))
        assertThat(response.additional, not(empty()))
    }

    private fun givenDealInStage(stage: Stage, initialClosingDate: LocalDate? = LocalDate.now()): Deal {
        val deal = DealCreator.createDealByRest(seller = seller, buyer = buyer)
        return dealRepository.findById(deal.id).let {
            dealRepository.update(
                it.copy(
                    stage = stage,
                    initialClosingDate = initialClosingDate
                )
            )
        }
    }
}
