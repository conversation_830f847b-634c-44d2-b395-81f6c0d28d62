package realestate.unlock.dealroom.api.functional.deal.documents.psa

import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.UploadFileToStagingResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.file.FileCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub

class UploadPSAFileTest : BaseFunctionalTest() {

    private lateinit var taskFileGatewayStub: FileGatewayStub

    @BeforeEach
    fun setUp() {
        taskFileGatewayStub = Context.injector.getInstance(FileGateway::class.java) as FileGatewayStub
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/documents/psa/files",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/files",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/files",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/files",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `can upload a file`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val file = FileCreator.file()
        val fileId = givenSuccessfullyGatewayResponse()

        // When
        val result = Unirest.post(givenUploadFileUrl(dealResponse.id))
            .headers(AuthMock.getAuthHeader())
            .field("file", file)
            .asJson()

        // Then
        assertThat(result.status, equalTo(201))
        val response = JsonMapper.decode(result.body.toPrettyString(), UploadFileToStagingResponse::class.java)
        assertThat(response.uid, equalTo(fileId))
    }

    @Test
    fun `cannot upload more than a file`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val file = FileCreator.file()

        // When
        val result = Unirest.post(givenUploadFileUrl(dealResponse.id))
            .headers(AuthMock.getAuthHeader())
            .field("file1", file)
            .field("file2", file)
            .asJson()

        // Then
        assertThat(result.status, equalTo(400))
    }

    private fun givenSuccessfullyGatewayResponse() = anyString().apply {
        taskFileGatewayStub.nextUploadFileUid(this)
    }

    private fun givenUploadFileUrl(dealId: Long) = "$localUrl/deal/$dealId/documents/psa/files"
}
