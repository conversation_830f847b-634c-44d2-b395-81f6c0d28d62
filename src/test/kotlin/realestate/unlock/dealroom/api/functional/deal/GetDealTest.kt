package realestate.unlock.dealroom.api.functional.deal

import io.swagger.models.Method
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `get deal by id`() {
        val givenDeal = DealCreator.createDealByRest()

        val response = HttpClient.get(
            url = "$localUrl/deal/${givenDeal.id}",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(response.id, equalTo(givenDeal.id))
    }

    @Test
    fun `cannot get deals of another organizations`() {
        val givenDeal = DealCreator.createDealByRest()
        val memberFromAnotherOrgToken = "another-org-token"
        AuthMock.createMemberWithUser(
            organizationId = "another-org-id",
            email = "<EMAIL>",
            token = memberFromAnotherOrgToken
        )

        val response = HttpClient.get(
            url = "$localUrl/deal/${givenDeal.id}",
            token = memberFromAnotherOrgToken,
            expectedStatus = 403,
            responseHandler = { it }
        )

        assertThat(response, containsString("In order to access to the deal you must belong to the organization"))
    }
}
