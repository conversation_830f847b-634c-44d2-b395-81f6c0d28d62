package realestate.unlock.dealroom.api.functional.base

import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest

class HealthCheckTest : BaseFunctionalTest() {

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/health",
        method = Method.GET.name,
        permissions = listOf()
    )

    @Test
    fun `Health check test`() {
        // Given
        val givenUrl = "$localUrl/health"

        // When
        val result = Unirest.get(givenUrl).asString()

        // Then
        JsonMapper.getMapper().readValue(result.body, Map::class.java).let { response ->
            assertThat(response["status"], equalTo("ok"))
        }
        assertThat(result.headers.getFirst("access-control-allow-private-network"), IsEqual("true"))
    }
}
