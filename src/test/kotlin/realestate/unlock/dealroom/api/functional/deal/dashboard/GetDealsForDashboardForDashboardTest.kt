package realestate.unlock.dealroom.api.functional.deal.dashboard

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.dashboard.DealDashboard
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.UpdateDates
import realestate.unlock.dealroom.api.entrypoint.rest.handler.dashboard.GetDealsForDashboardHandler
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.pagination.PaginationUrlBuilder

class GetDealsForDashboardForDashboardTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/dashboard/deals",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DASHBOARD)
    )

    @Test
    fun `should only get member deals if doesn't have READ_ALL_DASHBOARD`() {
        // Given
        val dealCreationRequestBuilder = CreateDealInputBuilder().apply { guaranteeType = GuaranteeType.CORPORATE }
        val dealDates = UpdateDates()
        val token = anyString()
        val buyer = AuthMock.createMemberWithUser(
            uid = token,
            token = token,
            memberType = "buyer",
            email = "$<EMAIL>",
            permissions = listOf(Permission.READ_OWN_DASHBOARD)
        )
        val dealFromAnotherMember = DealCreator.createDealByRest(dealCreationRequestBuilder = dealCreationRequestBuilder, updateDates = dealDates)
        val deal = DealCreator.createDealByRest(buyer = buyer, sellerEmail = "<EMAIL>")

        // When
        val result = Unirest.get(
            PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "phases" to mapOf(
                            "operator" to EnumOperator.IN.name,
                            "values" to listOf(Stage.DILIGENCE, Stage.CLOSING, Stage.OFFER, Stage.NEGOTIATION, Stage.UNDERWRITING, Stage.EVALUATION).map { it.name }
                        )
                    )
                )
                .withSize(15)
                .getUrl("$localUrl/dashboard/deals")
        )
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(200))

        val data = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<Map<String, *>>() {
            }
        )["data"]

        val listResponse = JsonMapper.decode(
            JsonMapper.encode(data!!),
            object : TypeReference<List<DealDashboard>>() {}
        )

        assertEquals(1, listResponse.size)
    }

    @Test
    fun `should filter by property type`() {
        // Given
        val multifamilyDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY }
        )
        val medicalDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MEDICAL },
            buyerEmail = "<EMAIL>",
            sellerEmail = "<EMAIL>"
        )

        // When
        val result = Unirest.get(
            PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "property_type" to mapOf(
                            "operator" to EnumOperator.EQ.name,
                            "values" to listOf(PropertyType.MULTIFAMILY).map { it.name }
                        )
                    )
                )
                .withSize(15)
                .getUrl("$localUrl/dashboard/deals")
        )
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(200))

        val data = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<Map<String, *>>() {
            }
        )["data"]

        val listResponse = JsonMapper.decode(
            JsonMapper.encode(data!!),
            object : TypeReference<List<DealDashboard>>() {}
        )

        assertEquals(1, listResponse.size)
        assertEquals(multifamilyDeal.id, listResponse.first().deal.id)
    }

    @Test
    fun `should get deals for dashboard successfully with READ_ALL_DASHBOARD`() {
        // Given
        val dealCreationRequestBuilder = CreateDealInputBuilder().apply { guaranteeType = GuaranteeType.CORPORATE }
        val dealDates = UpdateDates()
        val token = anyString()
        val buyer = AuthMock.createMemberWithUser(
            uid = token,
            token = token,
            memberType = "buyer",
            email = "$<EMAIL>",
            permissions = listOf(Permission.READ_OWN_DASHBOARD, Permission.READ_ALL_DASHBOARD)
        )
        val dealFromAnotherMember = DealCreator.createDealByRest(dealCreationRequestBuilder = dealCreationRequestBuilder, updateDates = dealDates)
        val deal = DealCreator.createDealByRest(buyer = buyer, sellerEmail = "<EMAIL>")

        // When
        val result = Unirest.get("$localUrl/dashboard/deals")
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(200))

        val data = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<Map<String, *>>() {
            }
        )["data"]

        val listResponse = JsonMapper.decode(
            JsonMapper.encode(data!!),
            object : TypeReference<List<DealDashboard>>() {}
        )

        assertEquals(2, listResponse.size)
        val response = listResponse.first()
        assertEquals(response.address.street, dealCreationRequestBuilder.propertyStreet)
        assertEquals(response.address.city, dealCreationRequestBuilder.propertyCity)
        assertEquals(response.address.state, dealCreationRequestBuilder.propertyState)
        assertEquals(response.deal.id, deal.id)
        assertEquals(response.deal.stage, Stage.EVALUATION)
        assertEquals(response.deal.contractExecutedDate, dealDates.contractExecutedDate)
        assertEquals(response.deal.loiExecutedDate, dealDates.loiExecutedDate)
        assertEquals(response.deal.diligenceExpirationDate, dealDates.diligenceExpirationDate)
        assertEquals(response.deal.initialClosingDate, dealDates.initialClosingDate)
        assertEquals(response.deal.outsideClosingDate, dealDates.outsideClosingDate)
        assertEquals(response.deal.tenantName, dealCreationRequestBuilder.tenantName)
        assertEquals(response.deal.vertical, dealCreationRequestBuilder.vertical)
    }

    @Test
    fun `should execute with all order by without errors`() {
        // Given
        val orders = GetDealsForDashboardHandler.DealsDashboardOrderBy.values()

        // When
        orders.forEach {
            val result = Unirest.get(
                PaginationUrlBuilder()
                    .withOrderBy(it.name)
                    .getUrl("$localUrl/dashboard/deals")
            )
                .headers(AuthMock.getAuthHeader())
                .asJson()

            MatcherAssert.assertThat(result.status, Matchers.equalTo(200))
        }
    }
}
