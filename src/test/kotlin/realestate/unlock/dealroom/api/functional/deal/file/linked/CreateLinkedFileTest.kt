package realestate.unlock.dealroom.api.functional.deal.file.linked

import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.CreateLinkedFileInput
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.functional.deal.report.ReportRequestBuilder
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.getRandomTask
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class CreateLinkedFileTest : BaseLinkedFilesTest() {
    private val fileGateway: FileGateway = Context.injector.getInstance(FileGateway::class.java)

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/linked-files",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/linked-files",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/linked-files",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return == 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/linked-files"

        // Then
        HttpClient.post(
            url = givenUrl,
            body = "",
            token = token,
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
    }

    @Test
    fun `must fail if send fileId and filePath`() {
        val deal = DealCreator.createDealByRest()
        val exception = HttpClient.post(
            url = "$localUrl/deal/${deal.id}/linked-files",
            body = """{
                "fileId": "${anyString()}",
                "entityType": "${LinkedFileEntityType.TASK}",
                "entityId": "1234",
                "filePath": "${anyString()}"
                }
            """.trimMargin(),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        assertEquals("YOU MUST PROVIDE fileId OR filePath NOT BOTH", exception.message)
    }

    @Test
    fun `must fail if don't send fileId or filePath`() {
        val deal = DealCreator.createDealByRest()
        val exception = HttpClient.post(
            url = "$localUrl/deal/${deal.id}/linked-files",
            body = """{
                "fileId": null,
                "entityType": "${LinkedFileEntityType.TASK}",
                "entityId": "1234",
                "filePath": null
                }
            """.trimMargin(),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        assertEquals("YOU MUST PROVIDE fileId OR filePath", exception.message)
    }

    @Test
    fun `create a linked file by id`() {
        // Given
        val deal = DealCreator.createDealByRest()

        val input = CreateLinkedFileInput(
            fileId = createFile(deal.id, "", "file.pdf").uid,
            entityType = LinkedFileEntityType.TASK,
            entityId = deal.getRandomTask().id.toString(),
            filePath = null
        )

        // When
        val result = createLinkedFile(deal.id, input)

        // Then
        assertEquals(input.fileId, result.fileId)
        assertEquals(input.entityType, result.entityType)
        assertEquals(input.entityId, result.entityId)
    }

    @Test
    fun `create a linked file by path`() {
        // Given
        val deal = DealCreator.createDealByRest()

        val input = CreateLinkedFileInput(
            fileId = null,
            entityType = LinkedFileEntityType.TASK,
            entityId = deal.getRandomTask().id.toString(),
            filePath = createFile(deal.id, "", "file.pdf").name
        )

        // When
        val result = createLinkedFile(deal.id, input)

        // Then
        assertNotNull(result.fileId)
        assertEquals(input.entityType, result.entityType)
        assertEquals(input.entityId, result.entityId)
    }

    @Test
    fun `can create a linked file if entity is already linked`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val entityId = deal.getRandomTask().id.toString()
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = createFile(deal.id, "", "file1.pdf").uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = entityId,
                filePath = null
            )
        )
        val input = CreateLinkedFileInput(
            fileId = createFile(deal.id, "", "file2.pdf").uid,
            entityType = LinkedFileEntityType.TASK,
            entityId = entityId,
            filePath = null

        )

        // When
        val result = createLinkedFile(deal.id, input)

        // Then
        assertEquals(input.fileId, result.fileId)
        assertEquals(input.entityType, result.entityType)
        assertEquals(input.entityId, result.entityId)
    }

    @Test
    fun `can create a linked file if entity was linked and unlinked`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val entityId = deal.getRandomTask().id.toString()

        val linkedFile = createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = createFile(deal.id, "", "file.pdf").uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = entityId,
                filePath = null
            )
        )
        deleteLinkedFile(deal.id, linkedFile.id)
        val input = CreateLinkedFileInput(
            fileId = createFile(deal.id, "", "file 2.pdf").uid,
            entityType = LinkedFileEntityType.TASK,
            entityId = entityId,
            filePath = null
        )

        // When
        val result = createLinkedFile(deal.id, input)

        // Then
        assertEquals(input.fileId, result.fileId)
        assertEquals(input.entityType, result.entityType)
        assertEquals(input.entityId, result.entityId)
    }

    @Test
    fun `cannot create linked file if fileId was already linked`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val fileId = createFile(deal.id, "", "file.pdf").uid
        val entityId = deal.getRandomTask().id
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = fileId,
                entityType = LinkedFileEntityType.TASK,
                entityId = entityId.toString(),
                filePath = null
            )
        )
        val input = CreateLinkedFileInput(
            fileId = fileId,
            entityType = LinkedFileEntityType.TASK,
            entityId = deal.getRandomTask(entityId).id.toString(),
            filePath = null
        )

        // When
        val result = createLinkedFileWithResponse(deal.id, input, HttpStatus.BAD_REQUEST, BadRequestException::class.java)

        // Then
        assertEquals("fileId: $fileId is already linked", result.message)
    }

    @Test
    fun `link file to a report and get report with file`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val kFile = fileGateway.uploadFile(
            FileToUpload(
                content = mockk(),
                contentType = "application/pdf",
                path = FileUtils.buildDealRepositoryPath(dealId = deal.id, "deal/${deal.id}/pepe.pdf"),
                filename = "pepe.pdf"
            )
        )

        val report = HttpClient.post(
            url = "$localUrl/deal/${deal.id}/reports",
            body = JsonMapper.encode(ReportRequestBuilder().apply { reportName = "ALTO REPORTE" }.build()),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, ReportResponse::class.java) }
        )

        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = kFile.uid,
                entityType = LinkedFileEntityType.REPORT,
                entityId = report.id.toString(),
                filePath = null
            )
        )

        val finalReport = HttpClient.get(
            url = "$localUrl/deal/${deal.id}/reports/${report.id}",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, ReportResponse::class.java) }
        )

        assertEquals(1, finalReport.documents.size)
        assertEquals(kFile.name, finalReport.documents.first().name)
        assertEquals(kFile.uid, finalReport.documents.first().uid)
    }

    private fun createFile(dealId: Long, path: String, name: String) = fileGateway.uploadFile(
        FileToUpload(
            content = mockk(),
            contentType = "application/pdf",
            path = FileUtils.buildDealRepositoryPath(dealId, path),
            filename = name
        )
    )
}
