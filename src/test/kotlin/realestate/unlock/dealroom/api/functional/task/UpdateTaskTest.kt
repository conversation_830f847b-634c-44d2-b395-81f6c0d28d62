package realestate.unlock.dealroom.api.functional.task

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileData
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.core.entity.search.SearchString
import realestate.unlock.dealroom.api.core.entity.search.StringOperator
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.entity.task.TaskStatusTransitions
import realestate.unlock.dealroom.api.core.entity.task.TaskStatusTranslator
import realestate.unlock.dealroom.api.core.entity.task.TaskToUpdate
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.team.AddDealTeamMemberRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableEnum
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.patch.request.ChangeTaskStatusRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.entity.deal.getAllTasks
import realestate.unlock.dealroom.api.utils.extensions.*
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.net.URLEncoder

class UpdateTaskTest : BaseFunctionalTest() {

    private lateinit var fileGateway: FileGateway

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/333",
        method = Method.PATCH.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "buyer",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )

        // Given
        val dealResponse = DealCreator.createDealByRest()
        val givenTaskToUpdate = dealResponse.findOneTask()

        // When
        val result =
            HttpClient.patch(
                url = "$localUrl/task/${givenTaskToUpdate.id}",
                token = token,
                body = """{"status": "${givenTaskToUpdate.statusKey.key}", "attachedForm": {"test_key_1":3001,"test_key_2":"2021-12-07"}}""",
                expectedStatus = HttpStatus.FORBIDDEN,
                responseHandler = { it }
            )

        // Then
        assertThat(result, CoreMatchers.containsString("In order to access to the task you need to be member of the related deal"))
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "buyer",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS)

        )

        // Given
        val dealResponse = DealCreator.createDealByRest()
        val givenTaskToUpdate = dealResponse.findOneTask()

        // When
        HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            token = token,
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "attachedForm": {"test_key_1":3001,"test_key_2":"2021-12-07"}}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { it }
        )
    }

    @Test
    fun `Permissions - seller member should return 403`() {
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )

        // Given
        val dealResponse = DealCreator.createDealByRest(seller = seller)
        val givenTaskToUpdate = dealResponse.findOneTask()

        // When

        HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            token = token,
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "attachedForm": {"test_key_1":3001,"test_key_2":"2021-12-07"}}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { it }
        )
    }

    @Test
    fun `Task should be correctly updated`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenTaskToUpdate = dealResponse.findOneTask()

        // When
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "attachedForm": {"test_key_1":3001,"test_key_2":"2021-12-07"}}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, not(nullValue()))
        assertThat(response.attachedFormData, not(emptyMap()))
    }

    @Test
    fun `Task without form should be correctly updated`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenTaskToUpdate = dealResponse.findOneTask()

        // When
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}"}"""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, nullValue())
    }

    @Test
    fun `Existent task with form should not delete the form if do not send it in the next update`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenTaskToUpdate = dealResponse.findOneTask()

        val givenUpdatedTaskWithForm = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "attachedForm": {"test_key_1":3001,"test_key_2":"2021-12-07"} }"""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // When
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}"}"""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then

        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, not(nullValue()))
        assertThat(response.attachedFormData, not(emptyMap()))
        assertThat(response.attachedFormData, equalTo(givenUpdatedTaskWithForm.attachedFormData))
    }

    @Test
    fun `Task with files should be correctly updated`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenFile = fileInStringJsonFormatValue()

        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")

        // When
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}",  "attachedForm": {"test_key_1":3001,"test_key_2":"2021-12-07","file": $givenFile} }"""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
        // Then
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, not(nullValue()))
        assertThat(response.attachedFormData, not(emptyMap()))
    }

    @Test
    fun `Task with files should create a linked file`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val fileId1 = createFile(dealResponse.id, "", "file1.pdf").uid
        val fileId2 = createFile(dealResponse.id, "", "file2.pdf").uid
        val givenFile = filesInStringJsonFormatValue(listOf(fileId1, fileId2))

        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")

        // When
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}",  "attachedForm": {"test_key_1":3001,"test_key_2":"2021-12-07","file": $givenFile} }"""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        val linkedFiles = getLinkedFiles(
            dealResponse.id,
            mapOf(
                "entity_type" to SearchableEnum(EnumOperator.EQ, listOf(LinkedFileEntityType.TASK)),
                "entity_id" to SearchString(StringOperator.EQ, listOf(givenTaskToUpdate.id.toString()))
            )
        )
        assertEquals(2, linkedFiles.size)
        assertEquals(fileId2, linkedFiles[0].fileId)
        assertEquals(fileId1, linkedFiles[1].fileId)
    }

    @Test
    fun `overriding or deleting a file should unlinked it`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val fileId1 = createFile(dealResponse.id, "", "file1.pdf").uid
        val fileId2 = createFile(dealResponse.id, "", "file2.pdf").uid
        val givenFile = filesInStringJsonFormatValue(listOf(fileId1, fileId2))

        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")
        HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}",  "attachedForm": {"test_key_1":3001,"test_key_2":"2021-12-07","file": $givenFile} }"""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // When
        val fileId3 = createFile(dealResponse.id, "", "file3.pdf").uid
        val givenNewFile = filesInStringJsonFormatValue(listOf(fileId2, fileId3))
        HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}",  "attachedForm": {"test_key_1":3001,"test_key_2":"2021-12-07","file": $givenNewFile} }"""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        val linkedFiles = getLinkedFiles(
            dealResponse.id,
            mapOf(
                "entity_type" to SearchableEnum(EnumOperator.EQ, listOf(LinkedFileEntityType.TASK)),
                "entity_id" to SearchString(StringOperator.EQ, listOf(givenTaskToUpdate.id.toString()))
            )
        )
        assertEquals(2, linkedFiles.size)
        assertEquals(fileId3, linkedFiles[0].fileId)
        assertEquals(fileId2, linkedFiles[1].fileId)
    }

    @Test
    fun `Update task with different files with the same name should save files with the same name but different path`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val givenFileName = "test-file.txt"
        val givenFiles = filesInStringJsonFormat(listOf(givenFileName, givenFileName))
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")

        // When
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${TaskStatus.TO_DO.key}",  "attachedForm": $givenFiles }"""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))

        assertThat(response.files.size, equalTo(2))
        assertThat(response.files.first().name, equalTo(response.files[1].name))
    }

    @Test
    fun `Back and forth task with new file version should switch the assigned user`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val fileName = anyString()
        val uid = anyString()
        val givenFile = fileInStringJsonFormat(fileName = fileName, fileUID = uid)

        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("survey")

        // When
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${TaskStatus.TO_DO.key}", "attachedForm": $givenFile}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData?.get("file"), equalTo(listOf(mapOf("uid" to uid, "name" to fileName))))
    }

    @Test
    fun `Due_date should be correctly updated`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenTaskToUpdate = dealResponse.findOneTask()

        // When
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "due_date": "2021-07-15"}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, nullValue())
    }

    @Test
    fun `Task should be correctly updated if the user is not admin but the task is assigned to given member`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val givenTaskToUpdate = dealResponse.findOneTask()

        Context.injector.getInstance(TaskRepository::class.java)
            .update(
                TaskToUpdate(
                    id = givenTaskToUpdate.id,
                    dealCategoryId = givenTaskToUpdate.dealCategoryId,
                    assignedTeam = MemberDealTeam.SELLER,
                    assignedBuyerId = givenTaskToUpdate.assignedBuyer.id,
                    statusKey = givenTaskToUpdate.statusKey.key,
                    title = givenTaskToUpdate.title,
                    description = givenTaskToUpdate.description,
                    dueDate = givenTaskToUpdate.dueDate,
                    attachedForm = givenTaskToUpdate.attachedFormData,
                    enabled = givenTaskToUpdate.enabled
                )
            )

        // When
        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            token = givenToken,
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "dueDate": "2021-07-15"}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // then
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.attachedFormData, nullValue())
        assertThat(response.dueDate, not(nullValue()))
    }

    @Test
    fun `Updating a rejected task should remove the rejectionReason`() {
        // Given
        val rejectedTaskId = givenARejectedTask()

        val response = HttpClient.patch(
            url = "$localUrl/task/$rejectedTaskId",
            body = """{"status": "${TaskStatus.IN_REVIEW.key}"}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        assertThat(TaskStatusTranslator.valueOf(response.status.uppercase()).taskStatus, equalTo(TaskStatus.IN_REVIEW))
        assertThat(response.rejectionReason, nullValue())
    }

    @Test
    fun `Update task should be restricted if the user is not admin and the task is not assigned to given member`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)
        )

        val givenTaskToUpdate = dealResponse.findOneTask()

        // When
        val result = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            token = givenToken,
            body = """{"status": "${TaskStatus.DONE.key}"}""",
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )

        // Then
        assertThat(result, CoreMatchers.containsString("In order to access to the task you need to be member of the related deal"))
    }

    @Test
    fun `Task should be correctly updated if the user belongs to the assigned member team`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val seller = dealResponse.members.first { member -> member.typeKey.startsWith(prefix = "seller") }

        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val newTeamMember = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller_counsel",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        Unirest.post("$localUrl/deal/${dealResponse.id}/team")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(AddDealTeamMemberRequest(listOf(newTeamMember.id))))
            .asString()

        val givenTaskToUpdate = dealResponse.getAllTasks().first { task -> task.assignedTeam == MemberDealTeam.SELLER }

        // When

        val response = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "dueDate": "2021-07-15"}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        assertThat(response.id, equalTo(givenTaskToUpdate.id))
        assertThat(response.dealCategoryId, equalTo(givenTaskToUpdate.dealCategoryId))
        assertThat(response.dueDate, not(nullValue()))
    }

    @Test
    fun `Should return bad request if do not send status`() {
        // Given

        // When
        HttpClient.patch(
            url = "$localUrl/task/123",
            body = """{ "dueDate": "2021-07-15"}""",
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { it }
        )
    }

    @Test
    fun `Should return bad request if task do not exists`() {
        HttpClient.patch(
            url = "$localUrl/task/123",
            body = """{ "status": "${TaskStatus.DONE.key}", "dueDate": "2021-07-15"}""",
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { it }
        )
    }

    private fun givenARejectedTask(): Long {
        val dealResponse = DealCreator.createDealByRest()
        val taskToReject = dealResponse.categories
            .first { it.categoryKey != "purchase_and_sale_contract" }
            .tasksWithFilesAndHistory.first().task
        HttpClient.patch(
            url = "$localUrl/task/${taskToReject.id}",
            body = """{"status": "${TaskStatus.IN_REVIEW.key}"}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        val rejectTaskUrl = "$localUrl/task/${taskToReject.id}/status"
        val rejectionReason = "this is the reason why the task was rejected"

        val rejectResult = Unirest.put(rejectTaskUrl)
            .headers(AuthMock.getAuthHeader())
            .body(
                JsonMapper.encode(
                    ChangeTaskStatusRequest(
                        transition = TaskStatusTransitions.REJECT,
                        reason = rejectionReason
                    )
                )
            )
            .asString()

        val rejectResponse = JsonMapper.decode(rejectResult.body, GetTaskResponse::class.java)

        assertThat(rejectResult.status, equalTo(200))
        assertThat(rejectResponse.status, equalTo(TaskStatus.REJECTED.key))
        assertThat(rejectResponse.rejectionReason, equalTo(rejectionReason))
        return taskToReject.id
    }

    private fun getLinkedFiles(dealId: Long, filter: Map<String, Any>? = null) = HttpClient.get(
        url = (
            "$localUrl/deal/$dealId/linked-files?" +
                "&filter=${filter?.let { URLEncoder.encode(JsonMapper.encode(it), "UTF-8") } ?: ""}"
            ),
        expectedStatus = HttpStatus.OK,
        responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<LinkedFileData>>() {}) }
    ).data

    private fun createFile(dealId: Long, path: String, name: String) = fileGateway.uploadFile(
        FileToUpload(
            content = mockk(),
            contentType = "application/pdf",
            path = FileUtils.buildDealRepositoryPath(dealId, path),
            filename = name
        )
    )
}
