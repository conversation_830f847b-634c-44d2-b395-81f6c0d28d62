package realestate.unlock.dealroom.api.functional.vertical

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.entity.vertical.Vertical
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetVerticalsTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/verticals",
        method = Method.GET.name,
        permissions = listOf()
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/verticals",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `Should retrieve verticals successfully`() {
        val response = HttpClient.get(
            url = "$localUrl/verticals",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<Vertical>>() {}) }
        )

        assertThat(response, not(emptyList()))
        assertTrue(response.any { it.name == "Other" })
        assertTrue(response.any { it.name == "Pediatrics" })
        assertTrue(response.any { it.name == "Dentistry" })
    }
}
