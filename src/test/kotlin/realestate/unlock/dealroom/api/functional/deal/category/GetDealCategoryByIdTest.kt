package realestate.unlock.dealroom.api.functional.deal.category

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.task.PriorityValue
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.category.GetDealCategoryResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.category.summary.GetDealCategoriesSummaryResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.extensions.fileInStringJsonFormat
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealCategoryByIdTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/categories/123",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/categories/123",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/categories/123",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val postDealResponse = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories/123"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, not(equalTo(403)))
    }

    @Test
    fun `should get deal category successfully`() {
        // Given
        val postDealResponse = DealCreator.createDealByRest()

        val givenCategorySummaryUrl = "$localUrl/deal/${postDealResponse.id}/categories-summary"

        val getCategorySummaryResult = Unirest.get(givenCategorySummaryUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        val getCategorySummaryResponse = JsonMapper.decode(
            getCategorySummaryResult.body.toPrettyString(),
            object : TypeReference<List<GetDealCategoriesSummaryResponse>>() {}
        )

        val insuranceCategoryId = getCategorySummaryResponse.first { it.category.key == "insurance" }.id
        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories/$insuranceCategoryId"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            GetDealCategoryResponse::class.java
        )

        assertThat(response.tasks, hasSize(5))
        assertTrue(response.tasks[0].required)
        assertThat(response.tasks[0].priority.value, equalTo(PriorityValue.HIGHEST))
        assertThat(response.tasks[0].priority.sorting, equalTo(4))
        assertThat(response.tasks[1].priority.value, equalTo(PriorityValue.MEDIUM))
        assertThat(response.tasks[1].priority.sorting, equalTo(5))
        assertThat(response.tasks[2].priority.value, equalTo(PriorityValue.MEDIUM))
        assertThat(response.tasks[2].priority.sorting, equalTo(7))
        assertThat(response.tasks[3].priority.value, equalTo(PriorityValue.MEDIUM))
        assertThat(response.tasks[3].priority.sorting, equalTo(15))
        assertThat(response.tasks[4].priority.value, equalTo(PriorityValue.LOW))
        assertThat(response.tasks[4].priority.sorting, equalTo(1))
    }

    @Test
    fun `should get deal category successfully with files`() {
        // Given
        val postDealResponse = DealCreator.createDealByRest()
        val givenTaskToUpdate = postDealResponse.findTaskByTemplateKey("financial_statements")
        val givenTaskUpdateUrl = "$localUrl/task/${givenTaskToUpdate.id}"
        val givenFile = fileInStringJsonFormat()
        HttpClient.patch(
            url = givenTaskUpdateUrl,
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "attachedForm": $givenFile }""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { it }
        )
        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories/${givenTaskToUpdate.dealCategoryId}"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            GetDealCategoryResponse::class.java
        )

        assertThat(response.tasks, not(empty()))
    }

    @Test
    fun `should restrict access if user is not admin and is not member of the related deal`() {
        // Given
        val postDealResponse = DealCreator.createDealByRest()

        val givenToken = "not-admin--without-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        val member = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken,
            permissions = listOf(Permission.READ_OWN_DEALS)
        )

        val dealCategoryId = postDealResponse.categories.first().id

        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories/$dealCategoryId"

        // When
        val result = Unirest.get(givenUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
        assertThat(result.body.toPrettyString(), CoreMatchers.containsString("In order to access to the deal you must be a member"))
    }
}
