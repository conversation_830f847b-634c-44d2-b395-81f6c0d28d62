package realestate.unlock.dealroom.api.functional.deal.datasync

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.datasync.DataSyncStatus
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.field.DataFieldInstance.*
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.datasync.CreateDataSyncInput
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal

class ProcessDataSyncTest : BaseDataSyncWithoutRest() {

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        AuthMock.createMemberWithUser()
    }

    @Test
    fun `should read file and save changes`() {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY })
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = listOf(
                buildExcelFileRow(OFFER_PRICE.dataField.label, 20.2)
            )
        ).let(::processDataSync)

        // Then
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            assertThat(it.dataSync.status, equalTo(DataSyncStatus.READY_TO_APPLY))
            assertThat(it.dataSync.data.changes?.get(0)?.fieldId, equalTo("deal.offerPrice"))
            assertThat(it.dataSync.data.changes?.get(0)?.value, equalTo(BigDecimal.valueOf(20.2)))
        }
    }

    @Test
    fun `should read blank cells as null value`() {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY })
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = listOf(
                mapOf(0 to OFFER_PRICE.dataField.label)
            )
        ).let(::processDataSync)

        // Then
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            assertThat(it.dataSync.status, equalTo(DataSyncStatus.READY_TO_APPLY))
            assertThat(it.dataSync.data.changes?.get(0)?.fieldId, equalTo("deal.offerPrice"))
            assertThat(it.dataSync.data.changes?.get(0)?.value, nullValue())
        }
    }

    @Test
    fun `should ignore header row`() {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY })
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        // When
        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = listOf(
                buildExcelFileRow("Field", "Value"),
                buildExcelFileRow(OFFER_PRICE.dataField.label, 20.2)
            )
        ).let(::processDataSync)
        // Then
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            assertThat(it.dataSync.status, equalTo(DataSyncStatus.READY_TO_APPLY))
            assertThat(it.dataSync.data.changes?.size, equalTo(1))
            assertThat(it.dataSync.data.changes?.get(0)?.fieldId, equalTo("deal.offerPrice"))
            assertThat(it.dataSync.data.changes?.get(0)?.value, equalTo(BigDecimal.valueOf(20.2)))
        }
    }

    @Test
    fun `null cell should be considered empty`() {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY })
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = listOf(
                buildExcelFileRow(OFFER_PRICE.dataField.label, null)
            )
        ).let(::processDataSync)

        // Then
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            assertThat(it.dataSync.status, equalTo(DataSyncStatus.READY_TO_APPLY))
            assertThat(it.dataSync.data.changes?.size, equalTo(1))
            assertThat(it.dataSync.data.changes?.get(0)?.fieldId, equalTo("deal.offerPrice"))
            assertThat(it.dataSync.data.changes?.get(0)?.value, nullValue())
        }
    }

    @Test
    fun `uploading a file without the specified sheet, should change status to ERROR with a message`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "ERROR",
            rows = null,
            errorType = "MISSING_SHEET"
        ).let(::processDataSync)

        // Then
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            assertThat(it.dataSync.status, equalTo(DataSyncStatus.ERROR))
            assertThat(it.dataSync.data.error?.message, equalTo("MISSING_SHEET"))
        }
    }

    @Test
    fun `numeric value that ends with ,0 in string data field`() {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY })
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = listOf(
                buildExcelFileRow(SUPPLEMENTAL.dataField.label, 9.0)
            )
        ).let(::processDataSync)

        // Then
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            assertThat(it.dataSync.status, equalTo(DataSyncStatus.READY_TO_APPLY))
            assertThat(it.dataSync.data.changes?.size, equalTo(1))
            assertThat(it.dataSync.data.changes?.get(0)?.fieldId, equalTo("deal.schemaData.data.supplemental"))
            assertThat(it.dataSync.data.changes?.get(0)?.value, equalTo("9"))
        }
    }

    @Test
    fun `numeric value that doesnt end with ,0 in string data field`() {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY })
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = listOf(
                buildExcelFileRow(SUPPLEMENTAL.dataField.label, 9.12)
            )
        ).let(::processDataSync)

        // Then
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            assertThat(it.dataSync.status, equalTo(DataSyncStatus.READY_TO_APPLY))
            assertThat(it.dataSync.data.changes?.size, equalTo(1))
            assertThat(it.dataSync.data.changes?.get(0)?.fieldId, equalTo("deal.schemaData.data.supplemental"))
            assertThat(it.dataSync.data.changes?.get(0)?.value, equalTo("9.12"))
        }
    }
}
