package realestate.unlock.dealroom.api.functional.deal.documents.psa

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.DraftDocumentsResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.creator.PSACreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.documents.psa.InteractionRequestFactory
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetPSARoundsTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/documents/psa/rounds",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/rounds",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/rounds",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/rounds",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `get PSA rounds`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val interactionRequest = InteractionRequestFactory.draft()
        PSACreator.createByRest(dealId = deal.id, interactionRequest = interactionRequest)

        val interactionsUrl = "$localUrl/deal/${deal.id}/documents/psa/rounds"

        // When
        val response = Unirest.get(interactionsUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertEquals(200, response.status)

        val rounds = JsonMapper.decode(
            response.body.toPrettyString(),
            object : TypeReference<List<DraftDocumentsResponse>>() {}
        )

        assertEquals(1, rounds.size)
        assertTrue(rounds.first() is DraftDocumentsResponse)
        val round = rounds.first()
        assertNotNull(round.cleanVersionFileId)
        assertNotNull(round.redLineVersionFileId)
        assertEquals(round.comment, round.comment)
    }
}
