package realestate.unlock.dealroom.api.functional.deal.report

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.DealFile
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.extensions.fileInStringJsonFormat
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub

class GetDealReportTaskFilesTest : BaseFunctionalTest() {

    private lateinit var taskFileGatewayStub: FileGatewayStub

    @BeforeEach
    fun setUp() {
        taskFileGatewayStub = Context.injector.getInstance(FileGateway::class.java) as FileGatewayStub
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/reports/123/task-files",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/reports/123/task-files",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/reports/123/task-files",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/reports/123/task-files",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `It retrieves the files of the right task`() {
        // given
        val givenDeal = DealCreator.createDealByRest()
        val givenFile = fileInStringJsonFormat()
        val givenTaskToUpdate = givenDeal.findTaskByTemplateKey("asbestos_survey")
        val updatedTask = uploadTaskToFile(taskId = givenTaskToUpdate.id, attachedForm = givenFile)

        val asbestosReport = givenReport(dealId = givenDeal.id, reportType = "ASBESTOS_SURVEY")

        // when
        val result = Unirest.get("$localUrl/deal/${givenDeal.id}/reports/${asbestosReport.id}/task-files")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        assertThat(result.status, equalTo(HttpStatus.OK))
        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<DealFile>>() {}
        )

        assertThat(response, hasSize(1))
        assertThat(response[0].uid, equalTo(updatedTask.files[0].uid))
        assertThat(response[0].name, equalTo(updatedTask.files[0].name))
    }

    private fun givenReport(dealId: Long, reportType: String): ReportResponse {
        val input = ReportRequestBuilder().apply { this.type = reportType }.build()
        val result = Unirest.post("$localUrl/deal/$dealId/reports")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(input))
            .asJson()
        return JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
    }

    private fun uploadTaskToFile(taskId: Long, attachedForm: String) =
        HttpClient.patch(
            url = "$localUrl/task/$taskId",
            body = """{"status": "to_do", "attachedForm": $attachedForm}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
}
