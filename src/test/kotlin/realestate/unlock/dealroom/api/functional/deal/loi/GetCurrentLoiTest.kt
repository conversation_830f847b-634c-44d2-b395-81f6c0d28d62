package realestate.unlock.dealroom.api.functional.deal.loi

import io.swagger.models.Method
import kong.unirest.HttpResponse
import kong.unirest.JsonNode
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.DealFile
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalRound
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetCurrentLoiTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/loi/round/current",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/current",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/current",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `can get the deal loi`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val loiUrl = "$localUrl/deal/${deal.id}/loi/round/current"
        val loi = givenLoiFor("$localUrl/deal/${deal.id}/loi/round")

        // When
        val result = Unirest.get(loiUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertLoi(result, loi)
    }

    @Test
    fun `return empty if there is not loi`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val loiUrl = "$localUrl/deal/${deal.id}/loi/round/current"

        // When
        val result = Unirest.get(loiUrl)
            .headers(AuthMock.getAuthHeader())
            .asEmpty()

        // Then
        assertThat(result.status, equalTo(204))
    }

    private fun givenLoiFor(
        createLoiUrl: String
    ): LoiMedicalRound {
        return Unirest.post(createLoiUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenLoiCreationRequest()))
            .asJson().let { JsonMapper.decode(it.body.toPrettyString(), LoiMedicalRound::class.java) }
    }

    private fun givenLoiCreationRequest() = PostMedicalLoiRequestBuilder().build()

    private fun assertLoi(
        result: HttpResponse<JsonNode>,
        loi: LoiMedicalRound
    ) {
        assertThat(result.status, equalTo(200))
        val response = JsonMapper.decode(result.body.toPrettyString(), LoiMedicalRound::class.java)
        assertThat(response.status, IsEqual(LetterOfIntentRoundStatus.IN_NEGOTIATION))
        assertThat(response.tenantName, IsEqual(loi.tenantName))
        assertThat(response.propertySquareFootage, IsEqual(loi.propertySquareFootage))
        assertThat(response.vertical, IsEqual(loi.vertical))
        assertThat(response.broker, IsEqual(loi.broker))
        assertThat(response.brokerageCompanyName, IsEqual(loi.brokerageCompanyName))
        assertThat(response.offerPrice, IsEqual(loi.offerPrice))
        assertThat(response.offerLeaseRent, IsEqual(loi.offerLeaseRent))
        assertThat(response.offerLeaseType, IsEqual(loi.offerLeaseType))
        assertThat(response.offerLeaseCondition, IsEqual(loi.offerLeaseCondition))
        assertThat(response.offerLeaseRentIncrease, IsEqual(loi.offerLeaseRentIncrease))
        assertThat(response.offerLeaseIncreaseEveryYear, IsEqual(loi.offerLeaseIncreaseEveryYear))
        assertThat(response.offerLeaseLength, IsEqual(loi.offerLeaseLength))
        assertThat(response.offerLeaseExpirationYear, IsEqual(loi.offerLeaseExpirationYear))
        assertThat(response.offerLeaseNumberOfOptions, IsEqual(loi.offerLeaseNumberOfOptions))
        assertThat(response.offerLeaseOptionLengths, IsEqual(loi.offerLeaseOptionLengths))
        assertThat(response.offerClosingPeriod, IsEqual(loi.offerClosingPeriod))
        assertThat(response.offerClosingPeriodExtension, IsEqual(loi.offerClosingPeriodExtension))
        assertThat(response.offerClosingExtensionDeposit, IsEqual(loi.offerClosingExtensionDeposit))
        assertThat(response.offerContractTermination, IsEqual(loi.offerContractTermination))
        assertThat(response.offerEarnestMoneyDeposit, IsEqual(loi.offerEarnestMoneyDeposit))
        assertThat(response.offerDueDiligenceNumber, IsEqual(loi.offerDueDiligenceNumber))
        assertThat(response.offerFiles, IsEqual(loi.offerFiles.map { DealFile(uid = it.uid, name = it.name) }))
        assertThat(response.offerRentCpi, IsEqual(loi.offerRentCpi))
        assertThat(response.offerRentStepType, IsEqual(loi.offerRentStepType))
    }
}
