package realestate.unlock.dealroom.api.functional.deal

import io.swagger.models.Method
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.UpdateDealStageRequest
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class UpdateDealStageTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/stage",
        method = Method.PUT.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${givenDeal.id}/stage",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${givenDeal.id}/stage",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${givenDeal.id}/stage",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `It updates the stage and its dates as expected`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenRequestBody = UpdateDealStageRequest(
            stage = Stage.DILIGENCE,
            evaluationDueDate = LocalDate.now().minusDays(10),
            underwritingDueDate = LocalDate.now().minusDays(8),
            offerDueDate = LocalDate.now().minusDays(6),
            negotiationDueDate = LocalDate.now().minusDays(5),
            diligenceDueDate = LocalDate.now().minusDays(4),
            closingDueDate = LocalDate.now().minusDays(2),
            postClosingDueDate = LocalDate.now().minusDays(1)
        )

        val updatedDeal = HttpClient.put(
            url = "$localUrl/deal/${givenDeal.id}/stage",
            body = JsonMapper.encode(givenRequestBody),
            expectedStatus = 202,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertThat(updatedDeal.id, equalTo(givenDeal.id))
        assertThat(updatedDeal.stage, equalTo(givenRequestBody.stage))
        assertThat(updatedDeal.evaluationDueDate, equalTo(givenRequestBody.evaluationDueDate))
        assertThat(updatedDeal.underwritingDueDate, equalTo(givenRequestBody.underwritingDueDate))
        assertThat(updatedDeal.loiExecutedDate, equalTo(givenRequestBody.offerDueDate))
        assertThat(updatedDeal.contractExecutedDate, equalTo(givenRequestBody.negotiationDueDate))
        assertThat(updatedDeal.diligenceExpirationDate, equalTo(givenRequestBody.diligenceDueDate))
        assertThat(updatedDeal.initialClosingDate, equalTo(givenRequestBody.closingDueDate))
        assertThat(updatedDeal.outsideClosingDate, equalTo(givenRequestBody.postClosingDueDate))
    }
}
