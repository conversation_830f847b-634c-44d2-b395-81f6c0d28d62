package realestate.unlock.dealroom.api.functional.deal.file

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.GetDealFilesResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.extensions.fileInStringJsonFormat
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealFilesTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/file",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/file",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/file",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/file"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, Matchers.not(equalTo(403)))
    }

    @Test
    fun `Deal files should be correctly retrieved`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val givenFile = fileInStringJsonFormat()

        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")
        val givenTaskUpdateUrl = "$localUrl/task/${givenTaskToUpdate.id}"

        val updatedTaskResponse = HttpClient.patch(
            url = givenTaskUpdateUrl,
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "attachedForm": $givenFile }""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        val givenGetDealFilesUrl = "$localUrl/deal/${dealResponse.id}/file"

        // When
        val result = Unirest.get(givenGetDealFilesUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<GetDealFilesResponse>>() {}
        )

        assertThat(response.first().id, equalTo(updatedTaskResponse.files.first().id))
    }

    @Test
    fun `Task file should be correctly retrieved if the user is not admin but is member of the related deal`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        val givenSeller = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val dealResponse = DealCreator.createDealByRest(
            seller = givenSeller
        )

        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")

        val updatedTaskResponse =
            HttpClient.patch(
                url = "$localUrl/task/${givenTaskToUpdate.id}",
                body = """{"status": "${givenTaskToUpdate.statusKey.key}", "attachedForm": ${fileInStringJsonFormat()}}""",
                expectedStatus = HttpStatus.ACCEPTED,
                responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
            )

        val givenGetDealFileUrl = "$localUrl/deal/${dealResponse.id}/file"

        // When
        val result = Unirest.get(givenGetDealFileUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<GetDealFilesResponse>>() {}
        )

        assertThat(response.first().id, equalTo(updatedTaskResponse.files.first().id))
    }

    @Test
    fun `Task file should be restricted if the user is not admin and is not member of the related deal`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken,
            permissions = listOf(Permission.READ_OWN_DEALS)
        )

        val dealResponse = DealCreator.createDealByRest()
        val givenFile = fileInStringJsonFormat()

        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")

        HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "form": $givenFile}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { it }
        )

        val givenGetDealFileUrl = "$localUrl/deal/${dealResponse.id}/file"

        // When
        val result = Unirest.get(givenGetDealFileUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
    }
}
