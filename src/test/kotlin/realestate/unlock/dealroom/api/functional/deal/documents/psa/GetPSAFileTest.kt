package realestate.unlock.dealroom.api.functional.deal.documents.psa

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.DraftDocumentsResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.GetFileResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.creator.PSACreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.documents.psa.InteractionRequestFactory
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetPSAFileTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/documents/psa/files/123",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/files/123",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/files/123",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/files/123",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `upload new draft version`() {
        val deal = DealCreator.createDealByRest()
        val interactionRequest = InteractionRequestFactory.draft()
        PSACreator.createByRest(dealId = deal.id, interactionRequest = interactionRequest)

        val round = getPSARound(dealId = deal.id)

        val fileId = round.cleanVersionFileId!!

        val psaFilesUrl = "$localUrl/deal/${deal.id}/documents/psa/files/$fileId"

        // When
        val response = Unirest.get(psaFilesUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertEquals(200, response.status)

        val psaFile = JsonMapper.decode(
            response.body.toPrettyString(),
            object : TypeReference<GetFileResponse>() {}
        )

        assertNotNull(psaFile)
        assertEquals(interactionRequest.cleanVersion?.uid, psaFile.uid)
    }

    private fun getPSARound(dealId: Long): DraftDocumentsResponse {
        val documentsUrl = "$localUrl/deal/$dealId/documents/psa/rounds"

        val documents = Unirest.get(documentsUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson().let {
                JsonMapper.decode(
                    it.body.toPrettyString(),
                    object : TypeReference<List<DraftDocumentsResponse>>() {}
                )
            }
        return documents.first()
    }
}
