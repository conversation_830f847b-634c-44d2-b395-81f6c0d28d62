package realestate.unlock.dealroom.api.functional.dashboard

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.dashboard.ActiveDealsSummary
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealsStatusSummaryForDashboardTest : BaseFunctionalTest() {

    private lateinit var dealRepository: DealRepository

    @BeforeEach
    fun setUp() {
        dealRepository = Context.injector.getInstance(DealRepository::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/dashboard/deals-status-summary",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DASHBOARD)
    )

    @Test
    fun `should get deals status for dashboard`() {
        // Given
        val d1 = DealCreator.createDealByRest()
        markAsNegotiation(d1.id)

        // When
        val result = Unirest.get("$localUrl/dashboard/deals-status-summary")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<ActiveDealsSummary>() {}
        )

        assertEquals(1, response.activeDealsFor(Stage.NEGOTIATION).activeDeals)
        assertEquals(0, response.activeDealsFor(Stage.CLOSING).activeDeals)
        assertEquals(0, response.activeDealsFor(Stage.OFFER).activeDeals)
        assertEquals(0, response.activeDealsFor(Stage.DILIGENCE).activeDeals)
    }

    @Test
    fun `should get deals status for dashboard filter by property type`() {
        // Given
        val d1 = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MEDICAL },
            buyerEmail = "<EMAIL>",
            sellerEmail = "<EMAIL>"
        )
        markAsNegotiation(d1.id)
        val d2 = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY },
            buyerEmail = "<EMAIL>",
            sellerEmail = "<EMAIL>"
        )
        markAsNegotiation(d2.id)
        val d3 = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY },
            buyerEmail = "<EMAIL>",
            sellerEmail = "<EMAIL>"
        )
        markAsNegotiation(d3.id)

        // When
        val result = Unirest.get("$localUrl/dashboard/deals-status-summary?propertyType=MULTIFAMILY")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<ActiveDealsSummary>() {}
        )

        assertEquals(2, response.activeDealsFor(Stage.NEGOTIATION).activeDeals)
        assertEquals(0, response.activeDealsFor(Stage.CLOSING).activeDeals)
        assertEquals(0, response.activeDealsFor(Stage.OFFER).activeDeals)
        assertEquals(0, response.activeDealsFor(Stage.DILIGENCE).activeDeals)
    }

    private fun markAsNegotiation(dealId: Long) =
        dealRepository.findById(dealId).also {
            dealRepository.update(it.copy(stage = Stage.NEGOTIATION))
        }
}
