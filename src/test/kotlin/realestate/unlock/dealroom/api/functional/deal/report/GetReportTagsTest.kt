package realestate.unlock.dealroom.api.functional.deal.report

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportTag
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetReportTagsTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/report-types/ZONING/tags",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/report-types/ZONING/tags",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `It retrieves the report types by report type`() {
        // given
        val givenReportType = "ZONING"

        val givenUrl = "$localUrl/report-types/$givenReportType/tags"

        // when
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // then
        assertThat(result.status, Matchers.equalTo(200))

        val tags = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<ReportTag>>() {}
        )

        assertThat(tags, hasSize(11))
        assertTrue(tags.any { it.name == "Legal Conforming" })
        assertTrue(tags.any { it.name == "No outstanding zoning code violations" })
        assertTrue(tags.any { it.name == "No outstanding building code violations" })
        assertTrue(tags.any { it.name == "No outstanding fire code violations" })
        assertTrue(tags.any { it.name == "Certificate of Occupancy available" })
        assertTrue(tags.any { it.name == "Legal Non-Conforming" })
        assertTrue(tags.any { it.name == "Non-Conforming" })
        assertTrue(tags.any { it.name == "Outstanding zoning code violations" })
        assertTrue(tags.any { it.name == "Outstanding building code violations" })
        assertTrue(tags.any { it.name == "Outstanding fire code violations" })
        assertTrue(tags.any { it.name == "Certificate of Occupancy unavailable" })
    }
}
