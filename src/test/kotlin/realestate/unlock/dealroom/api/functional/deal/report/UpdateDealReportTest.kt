package realestate.unlock.dealroom.api.functional.deal.report

import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportStatus
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportType
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportVendor
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ConflictException
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.repository.database.deal.reports.ReportTagsDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub
import java.math.BigDecimal
import java.nio.file.Files.createFile
import java.time.LocalDate

class UpdateDealReportTest : BaseFunctionalTest() {

    private lateinit var fileGatewayStub: FileGatewayStub
    private lateinit var reportTagsDatabaseRepository: ReportTagsDatabaseRepository

    @BeforeEach
    fun setUp() {
        fileGatewayStub = Context.injector.getInstance(FileGateway::class.java) as FileGatewayStub
        reportTagsDatabaseRepository = Context.injector.getInstance(ReportTagsDatabaseRepository::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/reports/123",
        method = Method.PUT.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val createdReport = givenCreatedReport(dealId = deal.id)

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${deal.id}/reports/${createdReport.id}",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val createdReport = givenCreatedReport(dealId = deal.id)

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${deal.id}/reports/${createdReport.id}",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `can add a new file to an existing report`() {
        val deal = DealCreator.createDealByRest()
        val createdReport = givenCreatedReport(dealId = deal.id)
        val newFile = FileInput(uid = createFile(deal.id, "", "new-file-name.pdf").uid, name = "new-file-name.pdf")

        val updateReportInput = createdReport.toReportRequest().copy(
            documents = listOf(newFile)
        )

        val result = Unirest.put("$localUrl/deal/${deal.id}/reports/${createdReport.id}")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(updateReportInput))
            .asJson()

        assertThat(result.status, equalTo(HttpStatus.ACCEPTED))
        val updatedReport = JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
        assertThat(createdReport.id, equalTo(updatedReport.id))
        assertThat(createdReport.documents, hasSize(0))
        assertThat(updatedReport.documents, hasSize(1))
    }

    @Test
    fun `it can update all fields`() {
        val deal = DealCreator.createDealByRest()
        val reportFile = FileInput(uid = createFile(deal.id, "", "report-file-name.pdf").uid, name = "report-file-name.pdf")
        val reportFileToBeDeleted = FileInput(uid = createFile(deal.id, "", "name.docx").uid, name = "name.docx")
        val createdReport = givenCreatedReport(dealId = deal.id, files = listOf(reportFile, reportFileToBeDeleted))

        val newTag = reportTagsDatabaseRepository.findByReportType(createdReport.type).first().id
        val newFile = FileInput(uid = createFile(deal.id, "", "new-file-name.pdf").uid, name = "new-file-name.pdf")
        val anotherNewFile = FileInput(uid = createFile(deal.id, "", "another-new-file-name.pdf").uid, name = "another-new-file-name.pdf")

        val updateReportInput = createdReport.toReportRequest().copy(
            type = "MOLD_SURVEY",
            reportName = "new report name",
            vendorKey = "EBI",
            vendorName = "new vendor name",
            status = ReportStatus.RECEIVED,
            expectedDate = LocalDate.now(),
            costEstimate = BigDecimal.TEN,
            findings = "those are the findings",
            tags = listOf(newTag),
            documents = listOf(reportFile, newFile, anotherNewFile)
        )

        val result = Unirest.put("$localUrl/deal/${deal.id}/reports/${createdReport.id}")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(updateReportInput))
            .asJson()

        assertThat(result.status, equalTo(HttpStatus.ACCEPTED))
        val updatedReport = JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
        assertThat(createdReport.id, equalTo(updatedReport.id))
        assertThat(updatedReport.type, equalTo(updateReportInput.type))
        assertThat(updatedReport.reportName, nullValue()) // null since report type is not 'Other'
        assertThat(updatedReport.vendorKey, equalTo(updateReportInput.vendorKey))
        assertThat(updatedReport.vendorName, nullValue()) // null since vendor is not 'Other'
        assertThat(updatedReport.status, equalTo(updateReportInput.status))
        assertThat(updatedReport.expectedDate, equalTo(updateReportInput.expectedDate))
        assertThat(updatedReport.costEstimate, equalTo(updateReportInput.costEstimate))
        assertThat(updatedReport.findings, equalTo(updateReportInput.findings))
        assertThat(updatedReport.tags, hasSize(1))
        assertThat(updatedReport.tags[0], equalTo(newTag))

        assertThat(createdReport.documents, hasSize(2)) // we created the report with two files
        assertThat(updatedReport.documents, hasSize(3)) // then removed one file and added two new

        assertTrue(updatedReport.documents.none { it.uid == reportFileToBeDeleted.uid })
        assertTrue(updatedReport.documents.any { it.uid == reportFile.uid })
        assertTrue(updatedReport.documents.any { it.uid == newFile.uid })
        assertTrue(updatedReport.documents.any { it.uid == anotherNewFile.uid })
    }

    @Test
    fun `it updates report name and vendor name if type and vendor are OTHER`() {
        val deal = DealCreator.createDealByRest()
        val createdReport = givenCreatedReport(dealId = deal.id)

        val updateReportInput = createdReport.toReportRequest().copy(
            type = ReportType.OTHER,
            reportName = "new report name",
            vendorKey = "OTHER",
            vendorName = "new vendor name"
        )

        val result = Unirest.put("$localUrl/deal/${deal.id}/reports/${createdReport.id}")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(updateReportInput))
            .asJson()

        assertThat(result.status, equalTo(HttpStatus.ACCEPTED))
        val updatedReport = JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
        assertThat(updatedReport.type, equalTo(ReportType.OTHER))
        assertThat(updatedReport.reportName, equalTo(updateReportInput.reportName))
        assertThat(updatedReport.vendorKey, equalTo(ReportVendor.OTHER))
        assertThat(updatedReport.vendorName, equalTo(updateReportInput.vendorName))
    }

    @Test
    fun `it cannot update a report with same type and vendor`() {
        // given
        val deal = DealCreator.createDealByRest()
        val seismicReport = givenCreatedReport(dealId = deal.id) // create a new report of a given type
        val asbestosReport = createReport(
            dealId = deal.id,
            request = ReportRequestBuilder().apply { this.type = "ASBESTOS_SURVEY" }.build()
        )

        val updateInput = asbestosReport.copy(type = seismicReport.type)

        // when
        val result = Unirest.put("$localUrl/deal/${deal.id}/reports/${asbestosReport.id}")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(updateInput))
            .asJson()

        assertThat(result.status, equalTo(HttpStatus.CONFLICT))
        val expectedError = JsonMapper.decode(result.body.toString(), ConflictException::class.java)
        assertThat(expectedError.message, equalTo("Report of type [${seismicReport.type}] with vendor [${seismicReport.vendorKey}] already exists."))
    }

    private fun givenCreatedReport(dealId: Long, files: List<FileInput> = listOf()): ReportResponse {
        val givenInput = ReportRequestBuilder()
            .apply { this.documents = files }
            .apply { this.type = "PHASE_I" }
            .build()

        return createReport(dealId, givenInput)
    }

    private fun createReport(dealId: Long, request: ReportRequest) =
        Unirest.post("$localUrl/deal/$dealId/reports")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(request))
            .asJson()
            .let { JsonMapper.decode(it.body.toPrettyString(), ReportResponse::class.java) }

    private fun ReportResponse.toReportRequest() = ReportRequest(
        type = this.type,
        reportName = this.reportName,
        vendorKey = this.vendorKey,
        vendorName = this.vendorName,
        status = this.status,
        expectedDate = this.expectedDate,
        costEstimate = this.costEstimate,
        cost = this.cost,
        findings = this.findings,
        tags = this.tags,
        documents = this.documents.map { FileInput(uid = it.uid, name = it.name) }
    )

    private fun createFile(dealId: Long, path: String, name: String) = fileGatewayStub.uploadFile(
        FileToUpload(
            content = mockk(),
            contentType = "application/pdf",
            path = FileUtils.buildDealRepositoryPath(dealId, path),
            filename = name
        )
    )
}
