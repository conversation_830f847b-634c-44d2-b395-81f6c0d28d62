package realestate.unlock.dealroom.api.functional.calendar

import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventType
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.dashboard.CalendarEvents
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.UpdateDates
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.entrypoint.rest.handler.dashboard.GetCalendarEventsHandler
import realestate.unlock.dealroom.api.functional.deal.report.ReportRequestBuilder
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class GetCalendarEventsTest : BaseFunctionalTest() {

    private lateinit var dealRepository: DealRepository
    private lateinit var buyerAdmin: Member

    @BeforeEach
    fun setUp() {
        dealRepository = Context.injector.getInstance(DealRepository::class.java)
        buyerAdmin = AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/calendar/events?untilDays=7",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DASHBOARD)
    )

    @Test
    fun `it retrieves the calendar events`() {
        val diligenceExpirationDate = LocalDate.now().plusDays(2)
        val reportExpirationDate = LocalDate.now().plusDays(3)
        val givenReportCreationRequest = ReportRequestBuilder().apply { this.expectedDate = reportExpirationDate }.build()
        val givenDealCreationRequestBuilder = CreateDealInputBuilder()
        val givenDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = givenDealCreationRequestBuilder,
            updateDates = UpdateDates(diligenceExpirationDate = diligenceExpirationDate)
        )
        val givenReport = createReport(givenDeal.id, givenReportCreationRequest)

        // When
        val result = Unirest.get("$localUrl/calendar/events?untilDays=7")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        assertThat(result.status, equalTo(200))

        val data = JsonMapper.decode(
            result.body.toPrettyString(),
            CalendarEvents::class.java
        )

        assertThat(data.dates.entries, hasSize(2))
        assertThat(data.dates[diligenceExpirationDate.toString()], notNullValue())
        assertTrue(
            data.dates[diligenceExpirationDate.toString()]!!.any {
                it.type == CalendarEventType.DUE_DILIGENCE
            }
        )

        assertThat(data.dates[reportExpirationDate.toString()], notNullValue())
        assertTrue(
            data.dates[reportExpirationDate.toString()]!!.any {
                it.type == CalendarEventType.REPORT && it.title == givenReport.type.replace("_", " ")
            }
        )
    }

    @Test
    fun `it retrieves the calendar events using from`() {
        val diligenceExpirationDate = LocalDate.parse("2023-05-04")
        val givenDealCreationRequestBuilder = CreateDealInputBuilder()
        val givenDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = givenDealCreationRequestBuilder,
            updateDates = UpdateDates(diligenceExpirationDate = diligenceExpirationDate)
        )

        val report1 = createReport(
            givenDeal.id,
            ReportRequestBuilder().apply {
                this.expectedDate = LocalDate.parse("2023-05-01")
            }.build()
        )
        val deal2 = DealCreator.createDealByRest(buyerEmail = "<EMAIL>", sellerEmail = "<EMAIL>")
        val report2 = createReport(
            deal2.id,
            ReportRequestBuilder().apply {
                this.expectedDate = LocalDate.parse("2023-05-10")
            }.build()
        )
        val deal3 = DealCreator.createDealByRest(buyerEmail = "<EMAIL>", sellerEmail = "<EMAIL>")
        val report3 = createReport(
            deal3.id,
            ReportRequestBuilder().apply {
                this.expectedDate = LocalDate.parse("2023-05-11")
            }.build()
        )
        val deal4 = DealCreator.createDealByRest(buyerEmail = "<EMAIL>", sellerEmail = "<EMAIL>")
        val report4 = createReport(
            deal4.id,
            ReportRequestBuilder().apply {
                this.expectedDate = LocalDate.parse("2023-04-30")
            }.build()
        )

        // When
        val data = HttpClient.get(
            url = "$localUrl/calendar/events?from=2023-05-01&untilDays=10",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, CalendarEvents::class.java) }
        )

        // Then
        assertThat(data.dates.entries, hasSize(3))
        assertTrue(
            data.dates[diligenceExpirationDate.toString()]!!.all {
                it.type == CalendarEventType.DUE_DILIGENCE
            }
        )
        assertTrue(
            data.dates[report1.expectedDate.toString()]!!.all {
                it.type == CalendarEventType.REPORT && it.title == report1.type.replace("_", " ")
            }
        )
        assertTrue(
            data.dates[report2.expectedDate.toString()]!!.all {
                it.type == CalendarEventType.REPORT && it.title == report2.type.replace("_", " ")
            }
        )
        assertThat(data.dates[report3.expectedDate.toString()], nullValue())
        assertThat(data.dates[report4.expectedDate.toString()], nullValue())
    }

    @Test
    fun `it retrieves overdue events`() {
        val diligenceExpirationDate = LocalDate.now().minusDays(2)
        val givenDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder(),
            updateDates = UpdateDates(diligenceExpirationDate = diligenceExpirationDate)
        )
        val reportExpirationDate = LocalDate.now().minusDays(3)
        val givenReportCreationRequest = ReportRequestBuilder().apply { this.expectedDate = reportExpirationDate }.build()
        val givenReport = createReport(givenDeal.id, givenReportCreationRequest)

        // When
        val result = Unirest.get("$localUrl/calendar/events?untilDays=7")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        assertThat(result.status, equalTo(200))

        val data = JsonMapper.decode(
            result.body.toPrettyString(),
            CalendarEvents::class.java
        )

        assertThat(data.dates.entries, hasSize(2))
        assertThat(data.dates[diligenceExpirationDate.toString()], notNullValue())
        assertTrue(
            data.dates[diligenceExpirationDate.toString()]!!.any {
                it.type == CalendarEventType.DUE_DILIGENCE
            }
        )

        assertThat(data.dates[reportExpirationDate.toString()], notNullValue())
        assertTrue(
            data.dates[reportExpirationDate.toString()]!!.any {
                it.type == CalendarEventType.REPORT && it.title == givenReport.type.replace("_", " ")
            }
        )
    }

    @Test
    fun `it retrieves events filtering by diligence expiration`() {
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())

        val d1 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2))
        )
        changeStage(d1.id, Stage.POST_CLOSING)

        val d2 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2))
        )
        changeStage(d2.id, Stage.CLOSING)

        val d3 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().plusDays(2))
        )
        changeStage(d3.id, Stage.DILIGENCE)

        val d4 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(diligenceExpirationDate = LocalDate.now().minusDays(2))
        )
        changeStage(d4.id, Stage.DILIGENCE)

        val d5 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(diligenceExpirationDate = null)
        )
        changeStage(d5.id, Stage.DILIGENCE)

        // When
        val result = Unirest.get("$localUrl/calendar/events?untilDays=7")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val data = JsonMapper.decode(
            result.body.toPrettyString(),
            CalendarEvents::class.java
        )

        assertThat(data.dates.values.flatten(), hasSize(2))
        assertTrue(data.dates.values.all { it.any { it.type == CalendarEventType.DUE_DILIGENCE } })
        assertTrue(data.dates[d3.diligenceExpirationDate.toString()]!!.any { it.dealId == d3.id })
        assertTrue(data.dates[d4.diligenceExpirationDate.toString()]!!.any { it.dealId == d4.id })
    }

    @Test
    fun `it retrieves events filtering by initial closing date`() {
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())

        val d1 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(initialClosingDate = LocalDate.now().plusDays(2))
        )
        changeStage(d1.id, Stage.POST_CLOSING)

        val d2 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(initialClosingDate = LocalDate.now().plusDays(2))
        )
        changeStage(d2.id, Stage.CLOSING)

        val d3 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(initialClosingDate = LocalDate.now().plusDays(2))
        )
        changeStage(d3.id, Stage.DILIGENCE)

        val d4 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(initialClosingDate = LocalDate.now().minusDays(2))
        )
        changeStage(d4.id, Stage.DILIGENCE)

        val d5 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(initialClosingDate = null)
        )
        changeStage(d5.id, Stage.DILIGENCE)

        // When
        val data = HttpClient.get(
            url = "$localUrl/calendar/events?untilDays=7",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, CalendarEvents::class.java) }
        )

        // Then
        assertThat(data.dates.values.flatten(), hasSize(3))
        assertTrue(data.dates.values.flatten().all { it.type == CalendarEventType.CLOSING_DATE })
        assertTrue(data.dates[d2.initialClosingDate.toString()]!!.any { it.dealId == d2.id })
        assertTrue(data.dates[d3.initialClosingDate.toString()]!!.any { it.dealId == d3.id })
        assertTrue(data.dates[d4.initialClosingDate.toString()]!!.any { it.dealId == d4.id })
    }

    @Test
    fun `it retrieves events filtering by property type`() {
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())

        val d1 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(initialClosingDate = LocalDate.now().plusDays(2)),
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { this.propertyType = PropertyType.MULTIFAMILY }
        )

        val d2 = DealCreator.createDealByRest(
            seller = seller,
            buyer = buyer,
            updateDates = UpdateDates(initialClosingDate = LocalDate.now().plusDays(2)),
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { this.propertyType = PropertyType.MEDICAL }
        )

        // When
        val data = HttpClient.get(
            url = "$localUrl/calendar/events?untilDays=7&propertyType=MULTIFAMILY",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, CalendarEvents::class.java) }
        )

        // Then
        assertThat(data.dates.values.flatten(), hasSize(1))
        assertTrue(data.dates.values.flatten().any { it.dealId == d1.id })
    }

    @Test
    fun `it retrieves the calendar events by dealVisibility`() {
        val givenDeal = DealCreator.createDealByRest(buyer = buyerAdmin, sellerEmail = "<EMAIL>")
        val buyer = AuthMock.createMemberWithUser(
            memberType = "buyer",
            token = "buyer-token",
            email = "<EMAIL>",
            uid = anyString(),
            permissions = Permission.values().filter { it != Permission.READ_ALL_DASHBOARD }
        )
        val dealWithoutRequestMember = DealCreator.createDealByRest(buyer = buyer, sellerEmail = "<EMAIL>")
        val expectedDate = LocalDate.now().plusDays(3)
        createReport(
            givenDeal.id,
            ReportRequestBuilder().apply { this.expectedDate = expectedDate }.build()
        )
        createReport(
            dealWithoutRequestMember.id,
            ReportRequestBuilder().apply { this.expectedDate = expectedDate }.build()
        )

        // When
        val myDeals = HttpClient.get(
            url = "$localUrl/calendar/events?dealVisibility=${GetCalendarEventsHandler.DealVisibility.MY_DEALS}&untilDays=7",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, CalendarEvents::class.java) }
        )

        // Then
        assertThat(myDeals.dates.entries, hasSize(1))
        assertThat(myDeals.dates[expectedDate.toString()], hasSize(1))

        val allDeals = HttpClient.get(
            url = "$localUrl/calendar/events?dealVisibility=${GetCalendarEventsHandler.DealVisibility.ALL_DEALS}&untilDays=7",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, CalendarEvents::class.java) }
        )

        assertThat(allDeals.dates.entries, hasSize(1))
        assertThat(allDeals.dates[expectedDate.toString()], hasSize(2))

        // Haven't permission to see all deals
        val buyerDeals = HttpClient.get(
            url = "$localUrl/calendar/events?dealVisibility=${GetCalendarEventsHandler.DealVisibility.ALL_DEALS}&untilDays=7",
            token = "buyer-token",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, CalendarEvents::class.java) }
        )

        assertThat(buyerDeals.dates.entries, hasSize(1))
        assertThat(buyerDeals.dates[expectedDate.toString()], hasSize(1))
    }

    @Test
    fun `it retrieves the calendar events by deal`() {
        val givenDeal = DealCreator.createDealByRest()

        // TODO: add task and documents to return

        val givenReport = createReport(
            givenDeal.id,
            ReportRequestBuilder().apply { this.expectedDate = LocalDate.now().plusDays(3) }.build()
        )

        // When
        val data = HttpClient.get(
            url = "$localUrl/calendar/events?dealId=${givenDeal.id}&untilDays=7",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, CalendarEvents::class.java) }
        )

        // Then
        assertThat(data.dates.entries, hasSize(1))

        assertThat(data.dates[givenReport.expectedDate.toString()], notNullValue())
        assertTrue(
            data.dates[givenReport.expectedDate.toString()]!!.any {
                it.type == CalendarEventType.REPORT && it.title == givenReport.type.replace("_", " ")
            }
        )
    }

    private fun createReport(dealId: Long, input: ReportRequest): ReportResponse =
        Unirest.post("$localUrl/deal/$dealId/reports")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(input))
            .asJson()
            .let { JsonMapper.decode(it.body.toPrettyString(), ReportResponse::class.java) }

    private fun changeStage(dealId: Long, stage: Stage) =
        dealRepository.update(
            dealRepository.findById(dealId).copy(stage = stage)
        )
}
