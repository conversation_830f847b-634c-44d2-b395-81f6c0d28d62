package realestate.unlock.dealroom.api.functional.deal.documents.psa

import io.swagger.models.Method
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.DocumentType
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.creator.PSACreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.documents.psa.InteractionRequestFactory
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.rest.DocumentFinder

class BusinessReviewPSATest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/documents/psa",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `can request business review for a psa`() {
        val deal = DealCreator.createDealByRest()
        givenPSAInLegalReviewFor(deal)

        val givenUrl = "$localUrl/deal/${deal.id}/documents/psa"

        val request = InteractionRequestFactory.businessReview()

        val response = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(request))
            .asEmpty()

        assertEquals(201, response.status)
        val documentResponse = DocumentFinder.get(DocumentType.PSA, deal.id, localUrl)
        assertEquals(DocumentStatus.BUYER_REVIEW.name, documentResponse.status)
    }

    private fun givenPSAInLegalReviewFor(deal: CompleteDeal) {
        PSACreator.createByRest(deal.id)

        val response = Unirest.post("$localUrl/deal/${deal.id}/documents/psa")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(InteractionRequestFactory.legalReview()))
            .asEmpty()

        assertEquals(201, response.status)
    }
}
