package realestate.unlock.dealroom.api.functional.pipeline

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.pipeline.PipelineView
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.pipeline.PipelineViewInput
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class UpdatePipelineViewTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/pipeline-views/123",
        method = Method.PUT.name,
        permissions = listOf()
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/pipeline-views/123",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `it can update a pipeline view`() {
        // Given
        val input = PipelineViewInput(
            name = "Pipeline View 1",
            propertyType = PropertyType.MULTIFAMILY,
            data = mapOf("some" to "data")
        )
        val created = createPipelineView(input)
        val updatedInput = input.copy(data = mapOf("other" to "thing"))

        // When
        val response = HttpClient.put(
            url = "$localUrl/pipeline-views/${created.id}",
            body = JsonMapper.encode(updatedInput),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, PipelineView::class.java) }
        )

        // Then
        assertThat(response.name, equalTo(updatedInput.name))
        assertThat(response.propertyType, equalTo(updatedInput.propertyType))
        assertThat(response.data, equalTo(updatedInput.data))
    }

    private fun createPipelineView(input: PipelineViewInput) = HttpClient.post(
        url = "$localUrl/pipeline-views",
        body = JsonMapper.encode(input),
        expectedStatus = HttpStatus.CREATED,
        responseHandler = { JsonMapper.decode(it, PipelineView::class.java) }
    )
}
