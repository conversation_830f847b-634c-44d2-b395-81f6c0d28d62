package realestate.unlock.dealroom.api.functional.deal.file

import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.CreateDirectoryItemInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.DirectoryFolder
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class FolderDirectoryCreationTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/directory",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/directory",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/directory",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return == 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/directory"

        // When
        val result = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
    }

    private fun getInput(): CreateDirectoryItemInput =
        CreateDirectoryItemInput(
            path = anyString(),
            name = anyString()
        )

    @Test
    fun `can get create a folder`() {
        val givenDeal = DealCreator.createDealByRest()
        val input = getInput()
        val response = HttpClient.post(
            url = "$localUrl/deal/${givenDeal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, DirectoryFolder::class.java) }
        )

        Assertions.assertEquals(response.name, input.name)
        Assertions.assertEquals(response.path, "${input.path}/${input.name}")
    }
}
