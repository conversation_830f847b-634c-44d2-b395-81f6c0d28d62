package realestate.unlock.dealroom.api.functional.deal.datasync

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.datasync.ChangeToApply
import realestate.unlock.dealroom.api.core.entity.deal.datasync.DataSyncStatus
import realestate.unlock.dealroom.api.core.entity.deal.datasync.UpdatedChange
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.field.DataFieldInstance
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.datasync.ApplyDataSyncInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.datasync.CreateDataSyncInput
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal

class ApplyDataSyncTest : BaseDataSyncTest() {

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/data-sync/345/apply",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/data-sync/345/apply",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/data-sync/345/apply",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/data-sync/345/apply",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should return bad request if status is not READY_TO_APPLY`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))
        val input = ApplyDataSyncInput(
            changesToApply = listOf(
                ChangeToApply("deal.offerPrice", BigDecimal.valueOf(20.2))
            )
        )

        // Then
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/data-sync/${dataSyncResponse.dataSync.id}/apply",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = {}
        )
    }

    @Test
    fun `should apply changes`() {
        // Given
        val offerPriceValue = BigDecimal.valueOf(10.1)
        val deal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                offerPrice = offerPriceValue
                propertyType = PropertyType.MULTIFAMILY
            }
        )
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = listOf(
                buildExcelFileRow(DataFieldInstance.OFFER_PRICE.dataField.label, 20.2)
            )
        ).let(::processDataSync)

        // When
        applyDataSync(
            deal.id,
            dataSyncResponse.dataSync.id,
            ApplyDataSyncInput(
                changesToApply = listOf(
                    ChangeToApply(DataFieldInstance.OFFER_PRICE.dataField.fieldId, BigDecimal.valueOf(30.3))
                )
            )
        )

        // Then
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            assertThat(it.dataSync.status, equalTo(DataSyncStatus.APPLIED))
            assertThat(
                it.dataSync.data.updated?.get(0),
                equalTo(
                    UpdatedChange(
                        fieldId = "deal.offerPrice",
                        oldValue = BigDecimal.valueOf(10.1),
                        newValue = BigDecimal.valueOf(30.3)
                    )
                )
            )
        }
        getDeal(deal.id).also {
            assertThat(it.offerPrice, equalTo(BigDecimal.valueOf(30.3)))
        }
    }
}
