package realestate.unlock.dealroom.api.functional.deal.team

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealMemberTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/members",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/members",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/members",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val postDealResponse = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${postDealResponse.id}/members"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.not(Matchers.equalTo(403)))
    }

    @Test
    fun `should get deal members successfully`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenUrl = "$localUrl/deal/${givenDeal.id}/members"
        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<MemberDto>>() {}
        )

        Assertions.assertEquals(response.size, 2)
    }

    @Test
    fun `should get deal members filtered successfully`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenUrl = "$localUrl/deal/${givenDeal.id}/members?team=buyer"
        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<MemberDto>>() {}
        )

        Assertions.assertEquals(response.size, 1)
    }
}
