package realestate.unlock.dealroom.api.functional.user.profile

import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsNull
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.entrypoint.rest.contract.user.profile.patch.PatchUserProfileRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.user.profile.patch.PatchUserProfileResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class UpdateUserProfileTest : BaseFunctionalTest() {

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/user/profile",
        method = Method.PATCH.name,
        permissions = listOf()
    )

    @Test
    fun `should update user profile successfully`() {
        // Given
        AuthMock.createMemberWithUser()
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.getExampleNumber("US")
        val givenUrl = "$localUrl/user/profile"
        val givenPatchUserProfileRequest = PatchUserProfileRequest(
            firstName = "firstName",
            lastName = "lastName",
            phoneNumber = phoneUtil.format(givenPhone, PhoneNumberFormat.E164)
        )

        // When
        val result = Unirest.patch(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenPatchUserProfileRequest))
            .asJson()

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            PatchUserProfileResponse::class.java
        )

        assertThat(response.fullName, equalTo("${givenPatchUserProfileRequest.firstName} ${givenPatchUserProfileRequest.lastName}"))
        assertThat(response.phoneNumber, equalTo(givenPatchUserProfileRequest.phoneNumber))
    }

    @Test
    fun `should fail if not exist Authenticated user`() {
        // Given
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.getExampleNumber("US")
        val givenUrl = "$localUrl/user/profile"
        val givenPatchUserProfileRequest = PatchUserProfileRequest(
            firstName = "firstName",
            lastName = "lastName",
            phoneNumber = phoneUtil.format(givenPhone, PhoneNumberFormat.E164)
        )
        // When
        val result = Unirest.patch(givenUrl)
            .body(JsonMapper.encode(givenPatchUserProfileRequest))
            .asJson()

        // Then
        assertThat(result.status, equalTo(HttpStatus.UNAUTHORIZED))
    }

    @Test
    fun `should update user profile without phone successfully`() {
        // Given
        AuthMock.createMemberWithUser()
        val givenUrl = "$localUrl/user/profile"
        val givenPatchUserProfileRequest = PatchUserProfileRequest(
            firstName = "firstName",
            lastName = "lastName"
        )

        // When
        val result = Unirest.patch(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenPatchUserProfileRequest))
            .asJson()

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            PatchUserProfileResponse::class.java
        )

        assertThat(response.fullName, equalTo("${givenPatchUserProfileRequest.firstName} ${givenPatchUserProfileRequest.lastName}"))
        assertThat(response.phoneNumber, equalTo(givenPatchUserProfileRequest.phoneNumber))
    }

    @Test
    fun `when update user profile with empty phone should save member without phone number`() {
        // Given
        AuthMock.createMemberWithUser()
        val givenUrl = "$localUrl/user/profile"
        val givenPatchUserProfileRequest = PatchUserProfileRequest(
            firstName = "firstName",
            lastName = "lastName",
            phoneNumber = ""
        )

        // When
        val result = Unirest.patch(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenPatchUserProfileRequest))
            .asJson()

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            PatchUserProfileResponse::class.java
        )

        assertThat(response.fullName, equalTo("${givenPatchUserProfileRequest.firstName} ${givenPatchUserProfileRequest.lastName}"))
        assertThat(response.phoneNumber, IsNull())
    }
}
