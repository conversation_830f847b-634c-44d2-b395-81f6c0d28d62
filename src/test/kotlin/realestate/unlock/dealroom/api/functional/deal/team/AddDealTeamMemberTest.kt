package realestate.unlock.dealroom.api.functional.deal.team

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.team.AddDealTeamMemberRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.member.buyerTeam
import realestate.unlock.dealroom.api.utils.entity.member.sellerTeam
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class AddDealTeamMemberTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/members",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/members",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/members",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/members",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `Adding a new seller to a deal should be possible`() {
        // Given a deal with a seller and a buyer
        val deal = dealWithSellerAndBuyer()
        val newMember = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "another-uid",
            memberType = "seller",
            token = anyString()
        )

        // when we add the new member (seller)
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/members",
            body = JsonMapper.encode(AddDealTeamMemberRequest(memberIds = listOf(newMember.id))),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )

        // then verify the new seller has not been added to the deal
        val getDealTeam = getDealMembers(deal.id)
        assertThat(getDealTeam.buyerTeam(), hasSize(1))
        assertThat(getDealTeam.sellerTeam(), hasSize(2))
    }

    @Test
    fun `Adding a new buyer to a deal should be possible`() {
        // given a deal with a seller and a buyer
        val deal = dealWithSellerAndBuyer()

        val newMember = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "another-uid",
            memberType = "buyer",
            token = anyString()
        )

        // when we add the new member (buyer)
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/members",
            body = JsonMapper.encode(AddDealTeamMemberRequest(memberIds = listOf(newMember.id))),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )

        // then verify the new buyer has not been added to the deal
        val getDealTeam = getDealMembers(deal.id)
        assertThat(getDealTeam.buyerTeam(), hasSize(2))
        assertThat(getDealTeam.sellerTeam(), hasSize(1))
    }

    @Test
    fun `Adding a new seller_broker to a deal without any other broker should add it as expected`() {
        // given a deal with a seller and a buyer
        val deal = dealWithSellerAndBuyer()

        val newSellerBroker = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "seller-broker-uid",
            memberType = "seller_broker",
            token = anyString()
        )

        // when we add the new member (seller_broker)
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/members",
            body = JsonMapper.encode(AddDealTeamMemberRequest(memberIds = listOf(newSellerBroker.id))),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )

        // verify the deal now has two sellers (the seller and seller_broker)
        val getDealTeam = getDealMembers(deal.id)
        val sellerTeam = getDealTeam.sellerTeam()
        assertThat(sellerTeam, hasSize(2))
        assertTrue(sellerTeam.any { it.id == newSellerBroker.id })
    }

    @Test
    fun `Adding more than one seller_broker should be possible`() {
        // given a deal with a seller and a buyer
        val deal = dealWithSellerAndBuyer()

        val newSellerBroker = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "seller-broker-uid",
            memberType = "seller_broker",
            token = anyString()
        )
        val anotherSellerBroker = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "another-seller-broker-uid",
            memberType = "seller_broker",
            token = anyString()
        )

        // when we add the new members (two seller_broker)
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/members",
            body = JsonMapper.encode(
                AddDealTeamMemberRequest(
                    memberIds = listOf(
                        newSellerBroker.id,
                        anotherSellerBroker.id
                    )
                )
            ),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )

        // verify the deal has added the two seller_broker
        val getDealTeam = getDealMembers(deal.id)
        val sellerTeam = getDealTeam.sellerTeam()
        assertThat(sellerTeam, hasSize(3))
        assertTrue(sellerTeam.any { it.id == newSellerBroker.id })
        assertTrue(sellerTeam.any { it.id == anotherSellerBroker.id })
    }

    @Test
    fun `Adding a seller_broker if there is another one should be possible`() {
        // given a deal with a seller and a buyer
        val deal = dealWithSellerAndBuyer()

        val newSellerBroker = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "seller-broker-uid",
            memberType = "seller_broker",
            token = anyString()
        )

        // when we add the new member (seller_broker)
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/members",
            body = JsonMapper.encode(AddDealTeamMemberRequest(memberIds = listOf(newSellerBroker.id))),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )
        // verify the deal now has the two sellers: the seller and the seller_broker
        val getDealTeam = getDealMembers(deal.id)
        val sellerTeam = getDealTeam.sellerTeam()
        assertThat(sellerTeam, hasSize(2))
        assertTrue(sellerTeam.any { it.id == newSellerBroker.id })

        // now we need validate that we can add another seller_broker
        val anotherSellerBroker = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "another-seller-broker-uid",
            memberType = "seller_broker",
            token = anyString()
        )
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/members",
            body = JsonMapper.encode(AddDealTeamMemberRequest(memberIds = listOf(anotherSellerBroker.id))),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )

        // verify the deal has not added the second seller_broker
        val secondGetDealTeam = getDealMembers(deal.id)
        val secondSellerTeam = secondGetDealTeam.sellerTeam()
        assertThat(secondSellerTeam, hasSize(3))
        assertTrue(secondSellerTeam.any { it.id == newSellerBroker.id })
        assertTrue(secondSellerTeam.any { it.id == anotherSellerBroker.id })
    }

    @Test
    fun `Adding multiple members with different types should work as expected`() {
        // given a deal with a seller and a broker
        val deal = dealWithSellerAndBuyer()

        val newSellerCounsel = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "seller-counsel-uid",
            memberType = "seller_counsel",
            token = anyString()
        )
        val anotherSellerCounsel = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "another-seller-counsel-uid",
            memberType = "seller_counsel",
            token = anyString()
        )
        val newBuyerCounsel = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "buyer-counsel-uid",
            memberType = "buyer_counsel",
            token = anyString()
        )
        val anotherBuyerCounsel = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "another-buyer-counsel-uid",
            memberType = "buyer_counsel",
            token = anyString()
        )

        // when we add the new members (two buyer counsels and two seller counsel)
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/members",
            body = JsonMapper.encode(
                AddDealTeamMemberRequest(
                    memberIds = listOf(
                        newSellerCounsel.id,
                        anotherSellerCounsel.id,
                        newBuyerCounsel.id,
                        anotherBuyerCounsel.id
                    )
                )
            ),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )

        // verify the deal has the new members
        val getDealTeam = getDealMembers(deal.id)
        val buyerTeam = getDealTeam.buyerTeam()
        assertThat(buyerTeam, hasSize(3))
        assertTrue(buyerTeam.any { it.id == newBuyerCounsel.id })
        assertTrue(buyerTeam.any { it.id == anotherBuyerCounsel.id })

        val sellerTeam = getDealTeam.sellerTeam()
        assertThat(sellerTeam, hasSize(3))
        assertTrue(sellerTeam.any { it.id == newSellerCounsel.id })
        assertTrue(sellerTeam.any { it.id == anotherSellerCounsel.id })
    }

    @Test
    fun `Adding an empty list of members should keep the original members of the deal`() {
        // given a deal with a seller and a buyer
        val deal = dealWithSellerAndBuyer()

        // when we add an empty list of members
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/members",
            body = JsonMapper.encode(AddDealTeamMemberRequest(memberIds = listOf())),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )

        // verify the deal still has the original buyer and seller
        val getDealTeam = getDealMembers(deal.id)
        assertThat(getDealTeam.sellerTeam(), hasSize(1))
        assertThat(getDealTeam.buyerTeam(), hasSize(1))
    }

    private fun dealWithSellerAndBuyer(): CompleteDeal =
        DealCreator.createDealByRest()
            .also { deal ->
                assertThat(deal.members, hasSize(2))
                assertThat(deal.members.count { it.typeKey == MemberTypeEnum.SELLER.key }, equalTo(1))
                assertThat(deal.members.count { it.typeKey == MemberTypeEnum.BUYER.key }, equalTo(1))
            }

    private fun getDealMembers(dealId: Long): List<MemberDto> {
        val getDealTeamResponse = Unirest.get("$localUrl/deal/$dealId/members")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        return JsonMapper.decode(
            getDealTeamResponse.body.toPrettyString(),
            object : TypeReference<List<MemberDto>>() {}
        )
    }
}
