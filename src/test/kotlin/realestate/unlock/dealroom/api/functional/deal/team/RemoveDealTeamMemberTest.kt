package realestate.unlock.dealroom.api.functional.deal.team

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.team.AddDealTeamMemberRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.member.buyerTeam
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class RemoveDealTeamMemberTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/members/22",
        method = Method.DELETE.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Removing a member with unassigned tasks should be possible`() {
        val givenDeal = DealCreator.createDealByRest()
        val newMember = AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            uid = "buyer-counsel-uid",
            memberType = "buyer_counsel"
        )

        val body = AddDealTeamMemberRequest(memberIds = listOf(newMember.id))
        Unirest.post("$localUrl/deal/${givenDeal.id}/members")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(body))
            .asString()

        val dealTeamBeforeRemoval = getDealTeam(givenDeal.id)

        val removeMemberResponse = Unirest.delete("$localUrl/deal/${givenDeal.id}/members/${newMember.id}")
            .headers(AuthMock.getAuthHeader())
            .asString()

        val dealTeamAfterRemoval = getDealTeam(givenDeal.id)

        assertThat(removeMemberResponse.status, equalTo(202))

        // before removal
        assertThat(dealTeamBeforeRemoval.buyerTeam(), hasSize(2))
        assertTrue(dealTeamBeforeRemoval.buyerTeam().any { it.id == newMember.id })

        // after removal
        assertThat(dealTeamAfterRemoval.buyerTeam(), hasSize(1))
        assertTrue(dealTeamAfterRemoval.buyerTeam().none { it.id == newMember.id })
    }

    @Test
    fun `Removing a member with assigned tasks should not be possible`() {
        val givenDeal = DealCreator.createDealByRest()
        val buyer = givenDeal.members.first { it.typeKey == "buyer" }

        val removeBuyerResponse = Unirest.delete("$localUrl/deal/${givenDeal.id}/members/${buyer.id}")
            .headers(AuthMock.getAuthHeader())
            .asString()

        assertThat(removeBuyerResponse.status, equalTo(400))
    }

    private fun getDealTeam(dealId: Long): List<MemberDto> {
        val getDealTeamResponse = Unirest.get("$localUrl/deal/$dealId/members")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        return JsonMapper.decode(
            getDealTeamResponse.body.toPrettyString(),
            object : TypeReference<List<MemberDto>>() {}
        )
    }
}
