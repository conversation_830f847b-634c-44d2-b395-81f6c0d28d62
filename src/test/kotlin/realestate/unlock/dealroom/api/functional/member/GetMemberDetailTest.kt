package realestate.unlock.dealroom.api.functional.member

import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.DealStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.GetMemberDetailResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetMemberDetailTest : BaseFunctionalTest() {

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/member/123",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_MEMBERS)
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/member/123",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should get member detail successfully`() {
        // Given
        val givenUrl = "$localUrl/member/{member-id}"

        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val member = AuthMock.createMemberWithUser(phoneNumber = givenPhone)
        AuthMock.mockMemberUserSdk(member)
        val createdDeal = DealCreator.createDealByRest(
            buyer = member
        )
        val response: GetMemberDetailResponse = HttpClient
            .get(
                givenUrl.replace("{member-id}", member.id.toString()),
                expectedStatus = HttpStatus.OK,
                responseHandler = { JsonMapper.decode(it, GetMemberDetailResponse::class.java) }
            )
        assertThat(response.member.id, equalTo(member.id))
        assertThat(response.member.address, equalTo(member.address))
        assertThat(response.member.companyName, equalTo(member.companyName))
        assertThat(response.member.firstName, equalTo(member.firstName))
        assertThat(response.member.lastName, equalTo(member.lastName))
        assertThat(response.member.email, equalTo(member.email))
        assertThat(response.member.phoneNumber, equalTo(member.phoneNumber))
        assertThat(response.member.type, equalTo(member.type))
        assertThat(response.deals, hasSize(1))
        assertThat(response.deals[0].id, equalTo(createdDeal.id))
        assertThat(response.deals[0].stage, equalTo(createdDeal.stage))
    }

    @Test
    fun `should find member by email`() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val member = AuthMock.createMemberWithUser(phoneNumber = givenPhone)
        AuthMock.mockMemberUserSdk(member)
        val createdDeal = DealCreator.createDealByRest(
            buyer = member
        )

        val response = HttpClient.get(
            url = "$localUrl/member-by-email/${member.email}",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, GetMemberDetailResponse::class.java) }
        )

        assertThat(response.member.id, equalTo(member.id))
        assertThat(response.member.address, equalTo(member.address))
        assertThat(response.member.companyName, equalTo(member.companyName))
        assertThat(response.member.firstName, equalTo(member.firstName))
        assertThat(response.member.lastName, equalTo(member.lastName))
        assertThat(response.member.email, equalTo(member.email))
        assertThat(response.member.phoneNumber, equalTo(member.phoneNumber))
        assertThat(response.member.type, equalTo(member.type))
        assertThat(response.deals, hasSize(1))
        assertThat(response.deals[0].id, equalTo(createdDeal.id))
        assertThat(response.deals[0].stage, equalTo(createdDeal.stage))
    }

    @Test
    fun `should fail if member not exists`() {
        // Given
        val givenUrl = "$localUrl/member/{member-id}"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(phoneNumber = givenPhone)

        HttpClient
            .get(
                givenUrl.replace("{member-id}", "-5"),
                expectedStatus = HttpStatus.BAD_REQUEST,
                responseHandler = { it }
            )
    }

    @Test
    fun `should fail if finding member by email not exists`() {
        AuthMock.createMemberWithUser(email = anyString())
        HttpClient
            .get(
                url = "$localUrl/member-by-email/<EMAIL>",
                expectedStatus = HttpStatus.BAD_REQUEST,
                responseHandler = { it }
            )
    }

    @Test
    fun `Should return not archived deals`() {
        // given
        val givenSeller = AuthMock.createMemberWithUser(memberType = "seller", email = "<EMAIL>", uid = anyString())
        val givenBuyer = AuthMock.createMemberWithUser(memberType = "buyer", email = "<EMAIL>", uid = anyString())
        val givenDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer)
        val dealToBeArchived = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer)
        deleteDeal(dealToBeArchived.id)

        // When
        val response: GetMemberDetailResponse = HttpClient
            .get(
                "$localUrl/member/${givenSeller.id}",
                expectedStatus = HttpStatus.OK,
                responseHandler = { JsonMapper.decode(it, GetMemberDetailResponse::class.java) }
            )

        assertThat(response.deals, hasSize(1))
        assertThat(response.deals[0].id, equalTo(givenDeal.id))
        assertThat(response.deals[0].lease, notNullValue())
        assertThat(response.deals[0].lease.type, equalTo("NNN"))
    }

    private fun deleteDeal(dealId: Long) {
        Context.injector.getInstance(DealRepository::class.java).let { dealRepository ->
            dealRepository.findById(dealId)
                .let { dealRepository.update(it.copy(status = DealStatus.DELETED)) }
        }
    }
}
