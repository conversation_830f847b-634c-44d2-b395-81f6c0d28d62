package realestate.unlock.dealroom.api.functional.deal.report

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.keypilot.DocumentQuestion
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetReportGptFaqTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/report-types/ZONING/frequently-questions",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `it retrieves the frequently questions`() {
        val response = HttpClient.get(
            url = "$localUrl/report-types/APPRAISAL/frequently-questions",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<DocumentQuestion>>() {}) }
        )

        assertThat(response, hasSize(9))
        assertTrue(response.any { it.question == "What is the purpose of the appraisal report?" })
    }
}
