package realestate.unlock.dealroom.api.functional.deal.file.linked

import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.CreateLinkedFileInput
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportType
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.core.entity.search.StringOperator
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.*
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableEnum
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableString
import realestate.unlock.dealroom.api.functional.deal.report.ReportRequestBuilder
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.getRandomTask
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetLinkedFilesTest : BaseLinkedFilesTest() {

    private lateinit var taskRepository: TaskRepository
    private lateinit var fileGateway: FileGateway

    @BeforeEach
    fun setUp() {
        taskRepository = Context.injector.getInstance(TaskRepository::class.java)
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/linked-files",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/linked-files",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/linked-files",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return == 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/linked-files"

        // Then
        HttpClient.get(
            url = givenUrl,
            token = token,
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
    }

    @Test
    fun `should filter by entity type`() {
        // Given
        val deal = DealCreator.createDealByRest()

        val taskFile = createFile(deal.id, "folder", "task file name.pdf")
        val task = taskRepository.findByDeal(deal.id).first()
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = taskFile.uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = task.id.toString(),
                filePath = null
            )
        )

        val reportFile = createFile(deal.id, "folder", "report file name.pdf")
        val report = HttpClient.post(
            url = "$localUrl/deal/${deal.id}/reports",
            body = JsonMapper.encode(ReportRequestBuilder().apply { reportName = "ALTO REPORTE" }.build()),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, ReportResponse::class.java) }
        )
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = reportFile.uid,
                entityType = LinkedFileEntityType.REPORT,
                entityId = report.id.toString(),
                filePath = null
            )
        )

        // When
        val result = getLinkedFiles(
            deal.id,
            mapOf(
                "entity_type" to SearchableEnum(EnumOperator.EQ, listOf(LinkedFileEntityType.TASK))
            )
        )

        // Then
        assertEquals(1, result.size)
        assertEquals(LinkedFileEntityType.TASK, result[0].entityType)
    }

    @Test
    fun `should return entity name`() {
        // Given
        val deal = DealCreator.createDealByRest()

        val taskFile = createFile(deal.id, "folder", "task file name.pdf")
        val task = taskRepository.findByDeal(deal.id).first()
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = taskFile.uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = task.id.toString(),
                filePath = null
            )
        )

        val reportFile = createFile(deal.id, "folder", "report file name.pdf")
        val report = HttpClient.post(
            url = "$localUrl/deal/${deal.id}/reports",
            body = JsonMapper.encode(
                ReportRequestBuilder().apply {
                    type = ReportType.OTHER
                    reportName = "Great report name"
                }.build()
            ),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, ReportResponse::class.java) }
        )
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = reportFile.uid,
                entityType = LinkedFileEntityType.REPORT,
                entityId = report.id.toString(),
                filePath = null
            )
        )

        // When
        val result = getLinkedFiles(deal.id)

        // Then
        assertEquals(2, result.size)
        assertEquals(LinkedFileEntityType.REPORT, result[0].entityType)
        assertEquals("Great report name", result[0].entityName)
        assertEquals("report file name.pdf", result[0].fileName)
        assertEquals(LinkedFileEntityType.TASK, result[1].entityType)
        assertEquals("Title", result[1].entityName)
        assertEquals("task file name.pdf", result[1].fileName)
    }

    @Test
    fun `should filter by entity id`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val taskFile1 = createFile(deal.id, "folder", "task file name 1.pdf")
        val taskFile2 = createFile(deal.id, "folder", "task file name 2.pdf")
        val entityId = deal.getRandomTask().id
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = taskFile1.uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = entityId.toString(),
                filePath = null
            )
        )
        createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = taskFile2.uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = deal.getRandomTask(entityId).id.toString(),
                filePath = null
            )
        )

        // When
        val result = getLinkedFiles(
            deal.id,
            mapOf(
                "entity_id" to SearchableString(StringOperator.EQ, listOf(entityId.toString()))
            )
        )

        // Then
        assertEquals(1, result.size)
        assertEquals(entityId.toString(), result[0].entityId)
    }

    @Test
    fun `should not return a linked file that was deleted`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val taskFile = createFile(deal.id, "folder", "task file name.pdf")
        val linkedFile = createLinkedFile(
            deal.id,
            CreateLinkedFileInput(
                fileId = taskFile.uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = deal.getRandomTask().id.toString(),
                filePath = null
            )
        )
        assertEquals(1, getLinkedFiles(deal.id).size)
        deleteLinkedFile(deal.id, linkedFile.id)

        // When
        val result = getLinkedFiles(deal.id)

        // Then
        assertEquals(0, result.size)
    }

    private fun createFile(dealId: Long, path: String, name: String) = fileGateway.uploadFile(
        FileToUpload(
            content = mockk(),
            contentType = "application/pdf",
            path = FileUtils.buildDealRepositoryPath(dealId, path),
            filename = name
        )
    )
}
