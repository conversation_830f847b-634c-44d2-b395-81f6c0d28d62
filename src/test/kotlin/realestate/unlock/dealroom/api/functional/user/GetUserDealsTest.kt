package realestate.unlock.dealroom.api.functional.user

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.get.GetDealResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetUserDealsTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/user/deal",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val givenUrl = "$localUrl/user/deal"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, Matchers.not(equalTo(403)))
    }

    @Test
    fun `should get empty array if the user has not deals`() {
        // Given
        val givenUrl = "$localUrl/user/deal"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<GetDealResponse>>() {}
        )

        assertThat(response.size, equalTo(0))
    }

    @Test
    fun `should get user deals successfully`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val givenSeller = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val givenCreatedDeal = DealCreator.createDealByRest(
            seller = givenSeller
        )

        val givenUrl = "$localUrl/user/deal"

        // When
        val result = Unirest.get(givenUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .queryString("complete", "true")
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<GetDealResponse>>() {}
        )

        assertThat(response.size, equalTo(1))
        assertThat(response.first().id, equalTo(givenCreatedDeal.id))
    }
}
