package realestate.unlock.dealroom.api.functional.deal.category.summary

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.category.summary.GetDealCategoriesSummaryResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.post.request.EnableTaskRequest
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealSummaryTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/categories-summary",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/categories-summary",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/categories-summary",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val postDealResponse = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories-summary"

        HttpClient.get(url = givenUrl, token = token, expectedStatus = HttpStatus.OK, responseHandler = { it })
    }

    @Test
    fun `should get deal categories summary successfully if user is admin`() {
        // Given
        val postDealResponse = DealCreator.createDealByRest()

        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories-summary"

        // When
        val response = HttpClient.get(url = givenUrl, expectedStatus = HttpStatus.OK, responseHandler = { JsonMapper.decode(it, object : TypeReference<List<GetDealCategoriesSummaryResponse>>() {}) })

        assertThat(response.size, equalTo(24))
        assertTrue(response.any { it.category.key == "permits_contracts_and_warranties" })
        assertTrue(response.any { it.category.key == "lease_documents" })
        assertTrue(response.any { it.category.key == "insurance" })
        assertTrue(response.any { it.category.key == "building_documents" })
        assertTrue(response.any { it.category.key == "closing_documents" })
        assertTrue(response.any { it.category.key == "payment_and_invoicing" })
        assertTrue(response.any { it.category.key == "point_of_contact" })
        assertTrue(response.any { it.category.key == "insurance_checklist" })
        assertTrue(response.any { it.category.key == "survey" })
        assertTrue(response.any { it.category.key == "physical_and_environmental" })
        assertTrue(response.any { it.category.key == "property_taxes" })
        assertTrue(response.any { it.category.key == "title" })
        assertTrue(response.any { it.category.key == "financial" })
        assertTrue(response.any { it.category.key == "nnn_evaluation" })
        assertTrue(response.any { it.category.key == "nnn_business_and_finances" })
        assertTrue(response.any { it.category.key == "nnn_pipeline" })
        assertTrue(response.any { it.category.key == "nnn_icm1" })
        assertTrue(response.any { it.category.key == "nnn_psa_elaboration" })
        assertTrue(response.any { it.category.key == "nnn_icm2" })
        assertTrue(response.any { it.category.key == "nnn_legal" })
        assertTrue(response.any { it.category.key == "nnn_wiring" })
        assertTrue(response.any { it.category.key == "nnn_verification" })
        assertTrue(response.any { it.category.key == "nnn_welcome_letter" })
        assertTrue(response.any { it.category.key == "nnn_notifications" })
    }

    @Test
    fun `should get deal categories summary successfully filtering by status`() {
        // Given
        val postDealResponse = DealCreator.createDealByRest()
        val task = postDealResponse.categories.first { it.categoryKey == "building_documents" }.tasksWithFilesAndHistory.first { it.task.templateKey == "personal_property_inventory" }

        HttpClient.patch(url = "$localUrl/task/${task.task.id}", body = """{"status": "DONE", "form": "{}"}""", expectedStatus = HttpStatus.ACCEPTED, responseHandler = { it })

        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories-summary?task_status=DONE"

        // When
        val response = HttpClient.get(url = givenUrl, expectedStatus = HttpStatus.OK, responseHandler = { JsonMapper.decode(it, object : TypeReference<List<GetDealCategoriesSummaryResponse>>() {}) })

        // Then
        assertThat(response.size, equalTo(1))
        assertTrue(response.any { it.category.key == "building_documents" })
        assertTrue(response.any { it.tasksSummary.done == 1 })
    }

    @Test
    fun `should get deal categories summary successfully filtering by stage`() {
        // Given
        val postDealResponse = DealCreator.createDealByRest()
        val task = postDealResponse.categories.first { it.categoryKey == "property_taxes" }.tasksWithFilesAndHistory.first()

        HttpClient.patch(url = "$localUrl/task/${task.task.id}", body = """{"status": "DONE", "form": "{}"}""", expectedStatus = HttpStatus.ACCEPTED, responseHandler = { it })

        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories-summary?stage=CLOSING"

        // When
        val response = HttpClient.get(url = givenUrl, expectedStatus = HttpStatus.OK, responseHandler = { JsonMapper.decode(it, object : TypeReference<List<GetDealCategoriesSummaryResponse>>() {}) })

        // Then
        assertThat(response.size, equalTo(22))
        assertTrue(response.any { it.category.key == "permits_contracts_and_warranties" })
        assertTrue(response.any { it.category.key == "lease_documents" })
        assertTrue(response.any { it.category.key == "insurance" })
        assertTrue(response.any { it.category.key == "building_documents" })
        assertTrue(response.any { it.category.key == "closing_documents" })
        assertTrue(response.any { it.category.key == "payment_and_invoicing" })
        assertTrue(response.any { it.category.key == "point_of_contact" })
        assertTrue(response.any { it.category.key == "insurance_checklist" })
        assertTrue(response.any { it.category.key == "survey" })
        assertTrue(response.any { it.category.key == "physical_and_environmental" })
        assertTrue(response.any { it.category.key == "title" })
        assertTrue(response.any { it.category.key == "financial" })
        assertTrue(response.any { it.category.key == "nnn_evaluation" })
        assertTrue(response.any { it.category.key == "nnn_business_and_finances" })
        assertTrue(response.any { it.category.key == "nnn_pipeline" })
        assertTrue(response.any { it.category.key == "nnn_icm1" })
        assertTrue(response.any { it.category.key == "nnn_psa_elaboration" })
        assertTrue(response.any { it.category.key == "nnn_icm2" })
        assertTrue(response.any { it.category.key == "nnn_legal" })
        assertTrue(response.any { it.category.key == "nnn_wiring" })
        assertTrue(response.any { it.category.key == "nnn_verification" })
        assertTrue(response.any { it.category.key == "nnn_welcome_letter" })
    }

    @Test
    fun `should get deal categories summary successfully if user is not admin but is member of the related deal`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        val givenSeller = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken
        )

        val postDealResponse = DealCreator.createDealByRest(
            seller = givenSeller
        )

        // disable one random task to verify the summary
        val givenCategoryToDisableTask = postDealResponse.categories.first()
        val givenTaskToDisable = givenCategoryToDisableTask.tasksWithFilesAndHistory.first().task.id
        disableTask(givenTaskToDisable)

        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories-summary"

        // When
        val response = HttpClient.get(
            url = givenUrl,
            expectedStatus = HttpStatus.OK,
            token = givenToken,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<GetDealCategoriesSummaryResponse>>() {}) }
        )

        assertThat(response.size, equalTo(15))
        assertTrue(response.any { it.category.key == "permits_contracts_and_warranties" })
        assertTrue(response.any { it.category.key == "lease_documents" })
        assertTrue(response.any { it.category.key == "insurance" })
        assertTrue(response.any { it.category.key == "building_documents" })
        assertTrue(response.any { it.category.key == "closing_documents" })
        assertTrue(response.any { it.category.key == "payment_and_invoicing" })
        assertTrue(response.any { it.category.key == "point_of_contact" })
        assertTrue(response.any { it.category.key == "insurance_checklist" })
        assertTrue(response.any { it.category.key == "survey" })
        assertTrue(response.any { it.category.key == "physical_and_environmental" })
        assertTrue(response.any { it.category.key == "purchase_and_sale_contract" })
        assertTrue(response.any { it.category.key == "lease" })
        assertTrue(response.any { it.category.key == "property_taxes" })
        assertTrue(response.any { it.category.key == "title" })
        assertTrue(response.any { it.category.key == "financial" })
        assertThat(response.find { it.id == givenCategoryToDisableTask.id }!!.tasksSummary.disabled, equalTo(1))
    }

    @Test
    fun `should restrict access if user is not admin and is not member of the related deal`() {
        // Given
        val postDealResponse = DealCreator.createDealByRest()

        val givenToken = "not-admin-without-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken,
            permissions = listOf(Permission.READ_OWN_DEALS)
        )

        val givenUrl = "$localUrl/deal/${postDealResponse.id}/categories-summary"

        val response = HttpClient.get(url = givenUrl, token = givenToken, expectedStatus = HttpStatus.FORBIDDEN, responseHandler = { it })
        // Then
        assertThat(response, CoreMatchers.containsString("In order to access to the deal you must be a member"))
    }

    private fun disableTask(taskId: Long) = HttpClient.put(
        url = "$localUrl/task/$taskId/enabled",
        expectedStatus = HttpStatus.ACCEPTED,
        responseHandler = { it },
        body = JsonMapper.encode(EnableTaskRequest(enabled = false))
    )
}
