package realestate.unlock.dealroom.api.functional.deal.datasync

import com.fasterxml.jackson.core.type.TypeReference
import io.mockk.every
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers
import realestate.unlock.dealroom.api.core.entity.deal.datasync.*
import realestate.unlock.dealroom.api.core.entity.kfile.KFileWithUrl
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.datasync.ApplyDataSyncInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.datasync.CreateDataSyncInput
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.client.SQSReadExcelFileClient
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.ExcelFileResultListener
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapperCC
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.pagination.PaginationUrlBuilder
import software.amazon.awssdk.services.sqs.model.Message
import java.math.BigDecimal

abstract class BaseDataSyncWithoutRest : BaseDataSyncTest() {
    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig.dontTestPermissions()
}

abstract class BaseDataSyncTest : BaseFunctionalTest() {

    protected lateinit var fileGateway: FileGateway
    protected val sqsReadExcelFileClient: SQSReadExcelFileClient = Context.injector.getInstance(SQSReadExcelFileClient::class.java)
    protected val excelFileResultListener = Context.injector.getInstance(ExcelFileResultListener::class.java)

    protected fun createDataSync(dealId: Long, input: CreateDataSyncInput, fileName: String = "the_file_name.xlsx"): DataSyncResponse {
        every { fileGateway.getFileWithUrl(input.fileId) } returns KFileWithUrl(input.fileId, "url", fileName)
        every { sqsReadExcelFileClient.send(any()) } returns Unit
        return HttpClient.post(
            url = "$localUrl/deal/$dealId/data-sync",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, DataSyncResponse::class.java) }
        )
    }

    protected fun getPendingChanges(dealId: Long, dataSyncId: Long) = HttpClient.get(
        url = "$localUrl/deal/$dealId/data-sync/$dataSyncId/pending-changes",
        expectedStatus = HttpStatus.OK,
        responseHandler = { JsonMapper.decode(it, DataSyncPendingChangesResponse::class.java) }
    ).pendingChanges

    protected fun processDataSync(excelResult: ExcelFileResult): Boolean =
        Message.builder().body(JsonMapperCC.encode(excelResult)).build()
            .let(excelFileResultListener::processMessage)

    protected fun applyDataSync(dealId: Long, dataSyncId: Long, input: ApplyDataSyncInput): Unit = HttpClient.post(
        url = "$localUrl/deal/$dealId/data-sync/$dataSyncId/apply",
        body = JsonMapper.encode(input),
        expectedStatus = HttpStatus.NO_CONTENT,
        responseHandler = {}
    )

    protected fun getDataSync(dealId: Long, dataSyncId: Long): DataSyncResponse = HttpClient.get(
        url = "$localUrl/deal/$dealId/data-sync/$dataSyncId",
        expectedStatus = HttpStatus.OK,
        responseHandler = { JsonMapper.decode(it, DataSyncResponse::class.java) }
    )

    protected fun listDataSyncs(dealId: Long, filter: Map<String, Any>? = null): List<DataSyncResponse> = HttpClient.get(
        url = PaginationUrlBuilder()
            .withFilter(filter ?: emptyMap())
            .withOrderBy("CREATED_AT")
            .getUrl("$localUrl/deal/$dealId/data-sync"),
        expectedStatus = HttpStatus.OK,
        responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<DataSyncResponse>>() {}) }
    ).data

    protected fun getDeal(dealId: Long): DealDto = HttpClient.get(
        url = "$localUrl/deal/$dealId",
        expectedStatus = HttpStatus.OK,
        responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
    )

    protected fun buildExcelFileResult(
        dataSync: DataSync,
        status: String = "OK",
        rows: List<Map<Int, Any?>>?,
        errorType: String? = null
    ): ExcelFileResult =
        ExcelFileResult(
            referenceId = DataSyncHelper.buildReferenceId(dataSyncId = dataSync.id, dealId = dataSync.dealId),
            status = status,
            rows = rows,
            errorType = errorType
        )

    protected fun buildExcelFileRow(name: String, value: Any?) =
        mapOf(0 to name, 1 to value)

    protected fun assertBigDecimal(value: Any?, expected: String) = MatcherAssert.assertThat(value as BigDecimal, Matchers.comparesEqualTo(BigDecimal(expected)))
}
