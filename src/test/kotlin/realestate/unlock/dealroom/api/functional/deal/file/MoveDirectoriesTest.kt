package realestate.unlock.dealroom.api.functional.deal.file

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.file.MoveDirectoriesInput
import realestate.unlock.dealroom.api.core.entity.deal.file.MoveDirectoriesInputDirectory
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.*
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DealFilesAndFoldersConfig
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class MoveDirectoriesTest : BaseDealDirectoryTest() {

    private lateinit var dealFilesAndFoldersConfig: DealFilesAndFoldersConfig

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        dealFilesAndFoldersConfig = Context.injector.getInstance(DealFilesAndFoldersConfig::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/directory",
        method = Method.PUT.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${givenDeal.id}/directory",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.PUT.name,
            url = "$localUrl/deal/${givenDeal.id}/directory",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return == 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/directory"

        // Then
        HttpClient.put(
            url = givenUrl,
            body = "",
            token = token,
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
    }

    @Test
    fun `move a file that has the same name that a folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "good")
        createFile(deal.id, "", "good")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "good",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "",
                    destinationName = "file1.pdf"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "").also {
            assertEquals(2, it.size)
            assertEquals("good", it[0].name)
            assertEquals(DirectoryItemType.folder, it[0].type)
            assertEquals("file1.pdf", it[1].name)
            assertEquals(DirectoryItemType.file, it[1].type)
        }
    }

    @Test
    fun `move a file to a destination that already exists`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFile(deal.id, "", "file.pdf")
        createFolder(deal.id, "", "folder")
        createFile(deal.id, "folder", "another.pdf")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "file.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "folder",
                    destinationName = "another.pdf"
                )
            )
        )

        // When
        val result = HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertEquals("Destination already exist for path: folder/another.pdf", result.message)
        getDirectory(deal.id, "").also {
            assertEquals(2, it.size)
            assertEquals("folder", it[0].name)
            assertEquals("file.pdf", it[1].name)
        }
        getDirectory(deal.id, "folder").also {
            assertEquals(1, it.size)
            assertEquals("another.pdf", it[0].name)
        }
    }

    @Test
    fun `move a folder to a destination that already exists`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "folder")
        createFile(deal.id, "folder", "file.pdf")
        createFolder(deal.id, "", "another-folder")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "folder",
                    sourceType = DirectoryItemTypeEnum.FOLDER,
                    destinationFolderPath = "",
                    destinationName = "another-folder"
                )
            )
        )

        // When
        val result = HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertEquals("Destination already exist for path: another-folder", result.message)
        getDirectory(deal.id, "").also {
            assertEquals(2, it.size)
            assertEquals("another-folder", it[0].name)
            assertEquals("folder", it[1].name)
        }
    }

    @Test
    fun `move a folder to a destination that already exists without pixel file`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "folder")
        createFile(deal.id, "folder", "file.pdf")
        createFile(deal.id, "another-folder", "file2.pdf")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "folder",
                    sourceType = DirectoryItemTypeEnum.FOLDER,
                    destinationFolderPath = "",
                    destinationName = "another-folder"
                )
            )
        )

        // When
        val result = HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertEquals("Destination already exist for path: another-folder", result.message)
        getDirectory(deal.id, "").also {
            assertEquals(2, it.size)
            assertEquals("another-folder", it[0].name)
            assertEquals("folder", it[1].name)
        }
    }

    @Test
    fun `move multiples files with same destination should return bad request`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFile(deal.id, "", "file1.pdf")
        createFile(deal.id, "", "file2.pdf")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "file1.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "",
                    destinationName = "new.pdf"
                ),
                MoveDirectoriesInputDirectory(
                    sourcePath = "file2.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "",
                    destinationName = "new.pdf"
                )
            )
        )

        // When
        val result = HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertEquals("Cannot move more than one directory to the same destination", result.message)
        getDirectory(deal.id, "").also {
            assertEquals(2, it.size)
            assertEquals("file1.pdf", it[0].name)
            assertEquals("file2.pdf", it[1].name)
        }
    }

    @Test
    fun `sending a folder and his content in the same request returns bad request`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "stuff")
        createFile(deal.id, "stuff", "file.pdf")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "stuff",
                    sourceType = DirectoryItemTypeEnum.FOLDER,
                    destinationFolderPath = "b",
                    destinationName = "stuff"
                ),
                MoveDirectoriesInputDirectory(
                    sourcePath = "stuff/file.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "c",
                    destinationName = "file.pdf"
                )
            )
        )

        // When
        val result = HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.BAD_REQUEST,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertEquals("Sending a folder and his content in the same request is not allowed", result.message)
        getDirectory(deal.id, "").also {
            assertEquals(1, it.size)
            assertEquals("stuff", it[0].name)
        }
        getDirectory(deal.id, "stuff").also {
            assertEquals(1, it.size)
            assertEquals("file.pdf", it[0].name)
        }
    }

    @Test
    fun `move a file that does not exist returns 404`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "files and stuff")
        createFile(deal.id, "", "file 1")
        createFile(deal.id, "", "file 2")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "file 1",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "",
                    destinationName = "file 1 renamed.pdf"
                ),
                MoveDirectoriesInputDirectory(
                    sourcePath = "file",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "",
                    destinationName = "file renamed.pdf"
                ),
                MoveDirectoriesInputDirectory(
                    sourcePath = "file 2",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "",
                    destinationName = "file 2 renamed.pdf"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NOT_FOUND,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "").also {
            assertEquals(3, it.size)
            assertEquals("files and stuff", it[0].name)
            assertEquals("file 1", it[1].name)
            assertEquals("file 2", it[2].name)
        }
    }

    @Test
    fun `move a folder that does not exist returns 404`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "files and stuff")
        createFile(deal.id, "", "file 1")
        createFile(deal.id, "", "file 2")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "file 1",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "",
                    destinationName = "file 1 renamed.pdf"
                ),
                MoveDirectoriesInputDirectory(
                    sourcePath = "file",
                    sourceType = DirectoryItemTypeEnum.FOLDER,
                    destinationFolderPath = "",
                    destinationName = "file renamed.pdf"
                ),
                MoveDirectoriesInputDirectory(
                    sourcePath = "file 2",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "",
                    destinationName = "file 2 renamed.pdf"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NOT_FOUND,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "").also {
            assertEquals(3, it.size)
            assertEquals("files and stuff", it[0].name)
            assertEquals("file 1", it[1].name)
            assertEquals("file 2", it[2].name)
        }
    }

    @Test
    fun `rename a file`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "some-folder")
        createFile(deal.id, "some-folder", "file.pdf")
        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "some-folder/file.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "some-folder",
                    destinationName = "better-name.pdf"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "some-folder").also {
            assertEquals(1, it.size)
            assertEquals("better-name.pdf", it[0].name)
        }
    }

    @Test
    fun `rename a folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "some-folder")
        createFolder(deal.id, "some-folder", "subfolder-old")
        createFile(deal.id, "some-folder/subfolder-old", "file1.pdf")
        createFile(deal.id, "some-folder/subfolder-old", "file2.pdf")
        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "some-folder/subfolder-old",
                    sourceType = DirectoryItemTypeEnum.FOLDER,
                    destinationFolderPath = "some-folder",
                    destinationName = "subfolder-new"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "some-folder").also {
            assertEquals(1, it.size)
            assertEquals("subfolder-new", it[0].name)
        }
        getDirectory(deal.id, "some-folder/subfolder-new").also {
            assertEquals(2, it.size)
            assertEquals("file1.pdf", it[0].name)
            assertEquals("file2.pdf", it[1].name)
        }
    }

    @Test
    fun `move a file that is in a folder with the same name that repository main folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val baseFolder = FileUtils.buildDealRepositoryPath(deal.id, "")
        createFile(deal.id, baseFolder, "file.pdf")
        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "$baseFolder/file.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "new-folder",
                    destinationName = "file.pdf"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "").also {
            assertEquals(1, it.size)
            assertEquals("new-folder", it[0].name)
        }
        getDirectory(deal.id, "new-folder").also {
            assertEquals(1, it.size)
            assertEquals("file.pdf", it[0].name)
        }
    }

    @Test
    fun `move a file that has the same prefix than another one`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "a")
        createFile(deal.id, "a", "file.pdf")
        createFile(deal.id, "a", "file.pdf (1).pdf")
        createFolder(deal.id, "", "b")
        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "a/file.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "b",
                    destinationName = "renamed.pdf"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "a").also {
            assertEquals(1, it.size)
            assertEquals("file.pdf (1).pdf", it[0].name)
        }
        getDirectory(deal.id, "b").also {
            assertEquals(1, it.size)
            assertEquals("renamed.pdf", it[0].name)
        }
    }

    @Test
    fun `move a folder that has the same prefix than other file`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "good-name")
        createFile(deal.id, "", "good-name")
        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "good-name",
                    sourceType = DirectoryItemTypeEnum.FOLDER,
                    destinationFolderPath = "",
                    destinationName = "better folder name"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "").also {
            assertEquals(2, it.size)
            assertEquals("better folder name", it[0].name)
            assertEquals(DirectoryItemType.folder, it[0].type)
            assertEquals("good-name", it[1].name)
            assertEquals(DirectoryItemType.file, it[1].type)
        }
    }

    @Test
    fun `move a file to a folder that does not exist, it is ok`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "a")
        createFile(deal.id, "a", "file1.pdf")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "a/file1.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "b",
                    destinationName = "file1.pdf"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "a").also {
            assertEquals(0, it.size)
        }
        getDirectory(deal.id, "b").also {
            assertEquals(1, it.size)
            assertEquals("file1.pdf", it[0].name)
        }
    }

    @Test
    fun `move multiples files to a folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "a")
        createFolder(deal.id, "", "b")
        createFile(deal.id, "a", "file1.pdf")
        createFile(deal.id, "a", "file2.pdf")
        createFile(deal.id, "a", "file3.pdf")
        createFile(deal.id, "a", "file4.pdf")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "a/file1.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "b",
                    destinationName = "file1.pdf"
                ),
                MoveDirectoriesInputDirectory(
                    sourcePath = "a/file3.pdf",
                    sourceType = DirectoryItemTypeEnum.FILE,
                    destinationFolderPath = "b",
                    destinationName = "file3.pdf"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "a").also {
            assertEquals(2, it.size)
            assertEquals("file2.pdf", it[0].name)
            assertEquals("file4.pdf", it[1].name)
        }
        getDirectory(deal.id, "b").also {
            assertEquals(2, it.size)
            assertEquals("file1.pdf", it[0].name)
            assertEquals("file3.pdf", it[1].name)
        }
    }

    @Test
    fun `move a folder`() {
        // Given
        val deal = DealCreator.createDealByRest()
        createFolder(deal.id, "", "abc")
        createFolder(deal.id, "abc", "subfolder")
        createFile(deal.id, "abc/subfolder", "file1.pdf")
        createFile(deal.id, "abc/subfolder", "file2.pdf")
        createFolder(deal.id, "abc/subfolder", "inner")
        createFile(deal.id, "abc/subfolder/inner", "file-inner.pdf")

        val input = MoveDirectoriesInput(
            directories = listOf(
                MoveDirectoriesInputDirectory(
                    sourcePath = "abc/subfolder",
                    sourceType = DirectoryItemTypeEnum.FOLDER,
                    destinationFolderPath = "new",
                    destinationName = "subfolder"
                )
            )
        )

        // When
        HttpClient.put(
            url = "$localUrl/deal/${deal.id}/directory",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        getDirectory(deal.id, "abc").also {
            assertEquals(0, it.size)
        }
        getDirectory(deal.id, "new").also {
            assertEquals(1, it.size)
            assertEquals("subfolder", it[0].name)
        }
        getDirectory(deal.id, "new/subfolder").also {
            assertEquals(3, it.size)
            assertEquals("inner", it[0].name)
            assertEquals("file1.pdf", it[1].name)
            assertEquals("file2.pdf", it[2].name)
        }
        getDirectory(deal.id, "new/subfolder/inner").also {
            assertEquals(1, it.size)
            assertEquals("file-inner.pdf", it[0].name)
        }
    }
}
