package realestate.unlock.dealroom.api.functional.deal.file.linked

import com.fasterxml.jackson.core.type.TypeReference
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.CreateLinkedFileInput
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileData
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import java.net.URLEncoder

abstract class BaseLinkedFilesTest : BaseFunctionalTest() {

    protected fun createLinkedFile(dealId: Long, input: CreateLinkedFileInput) = createLinkedFileWithResponse(
        dealId,
        input,
        HttpStatus.CREATED,
        LinkedFileData::class.java
    )

    protected fun <T> createLinkedFileWithResponse(dealId: Long, input: CreateLinkedFileInput, expectedStatus: Int, clazz: Class<T>): T = HttpClient.post(
        url = "$localUrl/deal/$dealId/linked-files",
        body = JsonMapper.encode(input),
        expectedStatus = expectedStatus,
        responseHandler = { JsonMapper.decode(it, clazz) }
    )

    protected fun getLinkedFiles(dealId: Long, filter: Map<String, Any>? = null) = HttpClient.get(
        url = (
            "$localUrl/deal/$dealId/linked-files?" +
                "&filter=${filter?.let { URLEncoder.encode(JsonMapper.encode(it), "UTF-8") } ?: ""}"
            ),
        expectedStatus = HttpStatus.OK,
        responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<LinkedFileData>>() {}) }
    ).data

    protected fun deleteLinkedFile(dealId: Long, linkedFileId: Long): Unit = HttpClient.delete(
        url = "$localUrl/deal/$dealId/linked-files/$linkedFileId",
        expectedStatus = HttpStatus.NO_CONTENT,
        responseHandler = { }
    )

    protected fun <T> deleteLinkedFileWithResponse(dealId: Long, linkedFileId: Long, expectedStatus: Int, clazz: Class<T>): T = HttpClient.delete(
        url = "$localUrl/deal/$dealId/linked-files/$linkedFileId",
        expectedStatus = expectedStatus,
        responseHandler = { JsonMapper.decode(it, clazz) }
    )
}
