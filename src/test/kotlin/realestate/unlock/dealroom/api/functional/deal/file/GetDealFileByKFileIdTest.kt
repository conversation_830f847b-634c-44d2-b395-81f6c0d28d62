package realestate.unlock.dealroom.api.functional.deal.file

import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.GetFileResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.functional.deal.report.ReportRequestBuilder
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.extensions.fileInStringJsonFormat
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub
import java.net.URL

class GetDealFileByKFileIdTest : BaseFunctionalTest() {
    private lateinit var fileGatewayStub: FileGatewayStub

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        fileGatewayStub = Context.injector.getInstance(FileGateway::class.java) as FileGatewayStub
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/files/123",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/files/123",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/files/123",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val dealResponse = DealCreator.createDealByRest(seller = seller)
        val givenTask = createTask(dealResponse.findTaskByTemplateKey("financial_statements"))

        // When
        val result = Unirest.get("$localUrl/deal/${dealResponse.id}/files/${givenTask.files.first().uid}")
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, not(equalTo(403)))
    }

    @Test
    fun `Task file should be correctly retrieved`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val givenTask = createTask(dealResponse.findTaskByTemplateKey("financial_statements"))

        val response = HttpClient.get(
            "$localUrl/deal/${dealResponse.id}/files/${givenTask.files.first().uid}",
            expectedStatus = HttpStatus.OK,
            responseHandler = {
                JsonMapper.decode(it, GetFileResponse::class.java)
            }
        )

        assertThat(response.uid, equalTo(givenTask.files.first().uid))
        assertThat(response.url, not(emptyString()))
        assertThat(response.url, equalTo(URL(response.url).toString()))
    }

    @Test
    fun `report file should be correctly retrieved`() {
        val givenDeal = DealCreator.createDealByRest()
        val file = createFile(givenDeal.id, "", "amazing.pdf")
        val givenInput = ReportRequestBuilder()
            .apply { this.documents = listOf(FileInput(uid = file.uid, name = "amazing.pdf")) }
            .build()
        val report = createReport(dealId = givenDeal.id, input = givenInput)

        // When
        val result = Unirest.get("$localUrl/deal/${givenDeal.id}/files/${report.documents[0].uid}")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(result.body.toPrettyString(), GetFileResponse::class.java)
        assertThat(response.uid, equalTo(report.documents[0].uid))
        assertThat(response.url, not(emptyString()))
    }

    @Test
    fun `get url with file name param`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val file = createFile(dealResponse.id, "", "amazing.pdf")

        val response = HttpClient.get(
            "$localUrl/deal/${dealResponse.id}/files/${file.uid}?fileName=another.pdf",
            expectedStatus = HttpStatus.OK,
            responseHandler = {
                JsonMapper.decode(it, GetFileResponse::class.java)
            }
        )

        assertThat(response.uid, equalTo(file.uid))
        assertThat(response.url, endsWith("another.pdf"))
    }

    private fun createTask(taskToUpdate: Task) = HttpClient.patch(
        url = "$localUrl/task/${taskToUpdate.id}",
        body = """{"status": "${taskToUpdate.statusKey.key}", "attachedForm": ${fileInStringJsonFormat()}}""",
        expectedStatus = HttpStatus.ACCEPTED,
        responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
    )

    private fun createReport(dealId: Long, input: ReportRequest): ReportResponse =
        Unirest.post("$localUrl/deal/$dealId/reports")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(input))
            .asJson()
            .let { JsonMapper.decode(it.body.toPrettyString(), ReportResponse::class.java) }

    private fun createFile(dealId: Long, path: String, name: String) = fileGatewayStub.uploadFile(
        FileToUpload(
            content = mockk(),
            contentType = "application/pdf",
            path = FileUtils.buildDealRepositoryPath(dealId, path),
            filename = name
        )
    )
}
