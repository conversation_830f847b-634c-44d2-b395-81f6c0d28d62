package realestate.unlock.dealroom.api.functional.deal

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.DealStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.UpdateStatus
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class UpdateDealStatusTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/status",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/123/status",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `it updates the status correctly`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenNewStatus = DealStatus.ON_HOLD

        // When
        val result = Unirest.post("$localUrl/deal/${givenDeal.id}/status")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(UpdateStatus(givenNewStatus)))
            .asJson()

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<DealDto>() {}
        )

        // Then
        assertThat(result.status, equalTo(HttpStatus.ACCEPTED))
        assertThat(response.status, equalTo(givenNewStatus))
    }
}
