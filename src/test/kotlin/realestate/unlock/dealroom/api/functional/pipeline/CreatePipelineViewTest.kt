package realestate.unlock.dealroom.api.functional.pipeline

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.pipeline.PipelineView
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.pipeline.PipelineViewInput
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class CreatePipelineViewTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/pipeline-views",
        method = Method.POST.name,
        permissions = listOf()
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/pipeline-views",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `it can create a pipeline view`() {
        // Given
        val input = PipelineViewInput(
            name = "Pipeline View 1",
            propertyType = PropertyType.MULTIFAMILY,
            data = mapOf("some" to "data")
        )

        // When
        val response = HttpClient.post(
            url = "$localUrl/pipeline-views",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, PipelineView::class.java) }
        )

        // Then
        assertThat(response.name, equalTo(input.name))
        assertThat(response.propertyType, equalTo(input.propertyType))
        assertThat(response.data, equalTo(input.data))
    }

    @Test
    fun `duplicated pipeline view returns bad request`() {
        // Given
        val input = PipelineViewInput(
            name = "Pipeline View 1",
            propertyType = PropertyType.MULTIFAMILY,
            data = mapOf("some" to "data")
        )
        HttpClient.post(
            url = "$localUrl/pipeline-views",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, PipelineView::class.java) }
        )

        // When
        val response = HttpClient.post(
            url = "$localUrl/pipeline-views",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.CONFLICT,
            responseHandler = { JsonMapper.decode(it, BadRequestException::class.java) }
        )

        // Then
        assertThat(response.message, equalTo("Pipeline view with name 'Pipeline View 1' already exists"))
    }
}
