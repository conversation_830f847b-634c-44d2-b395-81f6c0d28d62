package realestate.unlock.dealroom.api.functional.deal.datasync

import io.swagger.models.Method
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.datasync.*
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.field.DataFieldInstance.*
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.datasync.CreateDataSyncInput
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal

class GetDataSyncPendingChangesTest : BaseDataSyncTest() {

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/data-sync/456/pending-changes",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/data-sync/456/pending-changes",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/data-sync/456/pending-changes",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/data-sync/456/pending-changes",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should return pending-changes without errors`() {
        // Given
        val currentValue = BigDecimal.valueOf(10.1)
        val deal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                offerPrice = currentValue
                propertyType = PropertyType.MULTIFAMILY
            }
        )
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput("fileId"))

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = listOf(
                buildExcelFileRow(OFFER_PRICE.dataField.label, 20.2)
            )
        ).let(::processDataSync)

        // When
        val pendingChanges = getPendingChanges(deal.id, dataSyncResponse.dataSync.id)

        // Then
        assertThat(pendingChanges.size, equalTo(1))
        assertThat(pendingChanges[0].change.fieldId, equalTo("deal.offerPrice"))
        assertThat(pendingChanges[0].change.value, equalTo(BigDecimal.valueOf(20.2)))
        assertThat(pendingChanges[0].currentValue, equalTo(currentValue))
    }

    @Test
    fun `should return pending-changes with errors`() {
        // Given
        val offerPriceValue = BigDecimal.valueOf(10.1)
        val deal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                offerPrice = offerPriceValue
                propertyType = PropertyType.MULTIFAMILY
            }
        )
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput("fileId"))

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = listOf(
                buildExcelFileRow("Field", "Value"),
                buildExcelFileRow(OFFER_PRICE.dataField.label, 20.2),
                buildExcelFileRow("invalid value", 20.2),
                buildExcelFileRow(CAP_RATE_FY3.dataField.label, "should not be a string"),
                buildExcelFileRow(LENDER.dataField.label, false),
                buildExcelFileRow("912.18", "hello")
            )
        ).let(::processDataSync)
        val pendingChanges = getPendingChanges(deal.id, dataSyncResponse.dataSync.id)

        // Then
        assertThat(
            pendingChanges[0],
            equalTo(
                PendingChange(
                    change = Change(
                        fieldId = OFFER_PRICE.dataField.fieldId,
                        value = BigDecimal("20.2")
                    ),
                    field = FieldDefinition(
                        label = OFFER_PRICE.dataField.label,
                        type = OFFER_PRICE.dataField.type
                    ),
                    currentValue = offerPriceValue
                )
            )
        )

        assertThat(
            pendingChanges[1],
            equalTo(
                PendingChange(
                    change = Change(
                        error = DataSyncError(
                            code = DataSyncErrorCode.UNKNOWN_FIELD,
                            message = "Unknown field",
                            details = mapOf("cell" to "A2")
                        )
                    )
                )
            )
        )

        assertThat(
            pendingChanges[2],
            equalTo(
                PendingChange(
                    change = Change(
                        fieldId = CAP_RATE_FY3.dataField.fieldId,
                        error = DataSyncError(
                            code = DataSyncErrorCode.NUMERIC_PARSE,
                            message = "Cannot parse value to number",
                            details = mapOf("cell" to "B3")
                        )
                    ),
                    field = FieldDefinition(
                        label = CAP_RATE_FY3.dataField.label,
                        type = CAP_RATE_FY3.dataField.type
                    )
                )
            )
        )

        assertThat(
            pendingChanges[3],
            equalTo(
                PendingChange(
                    change = Change(
                        fieldId = LENDER.dataField.fieldId,
                        value = "false"
                    ),
                    field = FieldDefinition(
                        label = LENDER.dataField.label,
                        type = LENDER.dataField.type
                    )
                )
            )
        )

        assertThat(
            pendingChanges[4],
            equalTo(
                PendingChange(
                    change = Change(
                        error = DataSyncError(
                            code = DataSyncErrorCode.UNKNOWN_FIELD,
                            message = "Unknown field",
                            details = mapOf("cell" to "A5")
                        )
                    )
                )
            )
        )
    }
}
