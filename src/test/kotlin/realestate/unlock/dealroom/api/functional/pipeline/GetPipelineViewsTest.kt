package realestate.unlock.dealroom.api.functional.pipeline

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.core.entity.pipeline.PipelineView
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.core.entity.search.StringOperator
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.pipeline.PipelineViewInput
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.pagination.PaginationUrlBuilder

class GetPipelineViewsTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/pipeline-views",
        method = Method.GET.name,
        permissions = listOf()
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/pipeline-views",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `it can filter by propertyType`() {
        // Given
        val pipelineView1 = createPipelineView(
            PipelineViewInput(
                name = "Pipeline View 1",
                propertyType = PropertyType.MULTIFAMILY,
                data = mapOf("some" to "data")
            )
        )
        val pipelineView2 = createPipelineView(
            PipelineViewInput(
                name = "Pipeline View 2",
                propertyType = PropertyType.MEDICAL,
                data = mapOf("some" to "data")
            )
        )

        // When
        val response = HttpClient.get(
            url = PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "property_type" to mapOf(
                            "operator" to EnumOperator.EQ,
                            "values" to listOf(PropertyType.MULTIFAMILY.name)
                        )
                    )
                )
                .getUrl("$localUrl/pipeline-views"),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<PipelineView>>() {}) }
        )

        // Then
        assertThat(response.size, equalTo(1))
        assertThat(response.data[0].id, equalTo(pipelineView1.id))
        assertThat(response.data[0].propertyType, equalTo(pipelineView1.propertyType))
    }

    @Test
    fun `it can filter by name like`() {
        // Given
        val pipelineView1 = createPipelineView(
            PipelineViewInput(
                name = "sarasa",
                propertyType = PropertyType.MULTIFAMILY,
                data = mapOf("some" to "data")
            )
        )
        val pipelineView2 = createPipelineView(
            PipelineViewInput(
                name = "view2",
                propertyType = PropertyType.MULTIFAMILY,
                data = mapOf("some" to "data")
            )
        )

        // When
        val response = HttpClient.get(
            url = PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "name" to mapOf(
                            "operator" to StringOperator.LIKE,
                            "values" to listOf("sar")
                        )
                    )
                )
                .getUrl("$localUrl/pipeline-views"),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<PipelineView>>() {}) }
        )

        // Then
        assertThat(response.size, equalTo(1))
        assertThat(response.data[0].id, equalTo(pipelineView1.id))
    }

    private fun createPipelineView(input: PipelineViewInput) = HttpClient.post(
        url = "$localUrl/pipeline-views",
        body = JsonMapper.encode(input),
        expectedStatus = HttpStatus.CREATED,
        responseHandler = { JsonMapper.decode(it, PipelineView::class.java) }
    )
}
