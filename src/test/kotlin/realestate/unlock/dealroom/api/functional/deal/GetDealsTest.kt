package realestate.unlock.dealroom.api.functional.deal

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.DealStatus
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.core.entity.search.JsonArrayOperator
import realestate.unlock.dealroom.api.core.entity.search.SortOrder
import realestate.unlock.dealroom.api.core.entity.search.StringOperator
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealStage
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.get.GetDealResponse
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.GetDealsHandler
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.pagination.PaginationUrlBuilder

class GetDealsTest : BaseFunctionalTest() {

    private lateinit var changeDealStage: ChangeDealStage

    @BeforeEach
    fun setUp() {
        changeDealStage = Context.injector.getInstance(ChangeDealStage::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_ALL_DEALS)
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should get deals successfully`() {
        // Given
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val givenDealCreationRequestBuilder = CreateDealInputBuilder().apply { tags = setOf("tag1", "tag2") }
        val givenDeal = DealCreator.createDealByRest(buyer = buyer, dealCreationRequestBuilder = givenDealCreationRequestBuilder)
        val givenUrl = "$localUrl/deal"

        // When
        val response = HttpClient.get(
            url = givenUrl,
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<GetDealResponse>>() {}) }
        )

        // Then
        val deal = response.data.first()
        assertThat(deal.id, equalTo(givenDeal.id))
        assertThat(deal.propertyId, equalTo(givenDeal.propertyId))
        assertThat(deal.stage, equalTo(Stage.EVALUATION))
        assertThat(deal.tags, equalTo(setOf("tag1", "tag2")))
        assertThat(deal.leaderId, equalTo(buyer.id))
        assertNotNull(deal.property.fullAddress.coordinates)
    }

    @Test
    fun `should order by without errors`() {
        // Given
        val orders = GetDealsHandler.GetDealsOrderBy.values()

        // When
        orders.forEach {
            val result = Unirest.get(PaginationUrlBuilder().withOrderBy(it.name).getUrl("$localUrl/deal"))
                .headers(AuthMock.getAuthHeader())
                .asJson()
            assertThat(result.body.toString(), result.status, equalTo(200))
        }
    }

    @Test
    fun `should filter by property type`() {
        // Given
        val givenSeller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenBuyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val firstDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer, dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MEDICAL })
        val secondDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer, dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY })

        // When
        val result = Unirest.get(
            PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "property_type" to mapOf(
                            "operator" to EnumOperator.EQ.name,
                            "values" to listOf(PropertyType.MULTIFAMILY.name)
                        )
                    )
                )
                .withSize(15)
                .getUrl("$localUrl/deal")
        )
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<PaginatedOutput<GetDealResponse>>() {}
        )

        assertThat(response.data.size, equalTo(1))
        assertThat(response.total, equalTo(1))
        assertThat(response.data.first().propertyId, equalTo(secondDeal.propertyId))
    }

    @Test
    fun `should filter by stage`() {
        // Given
        val givenSeller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenBuyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val firstDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer)
        changeDealStage.invoke(firstDeal.id, Stage.NEGOTIATION)
        val secondDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer)
        changeDealStage.invoke(secondDeal.id, Stage.CLOSING)

        // When
        val result = Unirest.get(
            PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "stage" to mapOf(
                            "operator" to EnumOperator.EQ.name,
                            "values" to listOf(Stage.CLOSING.name)
                        )
                    )
                )
                .withSize(15)
                .getUrl("$localUrl/deal")
        )
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<PaginatedOutput<GetDealResponse>>() {}
        )

        assertThat(response.data.size, equalTo(1))
        assertThat(response.total, equalTo(1))
        assertThat(response.data.first().id, equalTo(secondDeal.id))
        assertThat(response.data.first().stage, equalTo(Stage.CLOSING))
    }

    @Test
    fun `should filter by city`() {
        // Given
        val givenSeller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenBuyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val firstDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer, dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyCity = "Manhattan" })
        val secondDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer, dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyCity = "Miami" })

        // When
        val response = HttpClient.get(
            url = PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "address_city" to mapOf(
                            "operator" to StringOperator.LIKE.name,
                            "values" to listOf("Man")
                        )
                    )
                )
                .getUrl("$localUrl/deal"),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<GetDealResponse>>() {}) }
        )

        assertThat(response.data.size, equalTo(1))
        assertThat(response.total, equalTo(1))
        assertThat(response.data.first().id, equalTo(firstDeal.id))
    }

    @Test
    fun `should filter by state`() {
        // Given
        val givenSeller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenBuyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val firstDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer, dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyState = "NY" })
        val secondDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer, dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyState = "FL" })

        // When
        val response = HttpClient.get(
            url = PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "address_state" to mapOf(
                            "operator" to StringOperator.EQ.name,
                            "values" to listOf("NY")
                        )
                    )
                )
                .getUrl("$localUrl/deal"),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<GetDealResponse>>() {}) }
        )

        assertThat(response.data.size, equalTo(1))
        assertThat(response.total, equalTo(1))
        assertThat(response.data.first().id, equalTo(firstDeal.id))
    }

    @Test
    fun `should filter by status`() {
        // Given
        val givenSeller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenBuyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val firstDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer)
        val secondDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer)
        changeStatus(secondDeal.id, DealStatus.CLOSED)

        // When
        val response = HttpClient.get(
            url = PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "status" to mapOf(
                            "operator" to EnumOperator.EQ.name,
                            "values" to listOf(DealStatus.CLOSED.name)
                        )
                    )
                )
                .getUrl("$localUrl/deal"),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<GetDealResponse>>() {}) }
        )

        assertThat(response.data.size, equalTo(1))
        assertThat(response.total, equalTo(1))
        assertThat(response.data.first().id, equalTo(secondDeal.id))
    }

    @Test
    fun `should filter by tags`() {
        // Given
        val givenSeller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenBuyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val firstDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer, dealCreationRequestBuilder = CreateDealInputBuilder().apply { tags = setOf("tag1", "tag2") })
        val secondDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer, dealCreationRequestBuilder = CreateDealInputBuilder().apply { tags = setOf("tag3") })
        val thirdDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer, dealCreationRequestBuilder = CreateDealInputBuilder().apply { tags = setOf("tag4") })

        // When
        val response = HttpClient.get(
            url = PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "tags" to mapOf(
                            "operator" to JsonArrayOperator.IN,
                            "values" to listOf("tag2", "tag4")
                        )
                    )
                )
                .withOrderBy(GetDealsHandler.GetDealsOrderBy.CREATED_AT.name)
                .withOrder(SortOrder.ASC)
                .getUrl("$localUrl/deal"),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<GetDealResponse>>() {}) }
        )

        assertThat(response.data.size, equalTo(2))
        assertThat(response.total, equalTo(2))
        assertThat(response.data.map { it.id }, contains(firstDeal.id, thirdDeal.id))
    }

    @Test
    fun `should restrict access if user is not admin`() {
        // Given
        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val givenUrl = "$localUrl/deal"

        // When
        val result = Unirest.get(givenUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
    }

    @Test
    fun `Should retrieve all non-archived deals`() {
        // given
        val givenSeller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenBuyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val firstDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer)
        val secondDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer)
        val thirdDeal = DealCreator.createDealByRest(seller = givenSeller, buyer = givenBuyer)

        changeStatus(secondDeal.id, DealStatus.DELETED)

        // when
        val result = Unirest.get("$localUrl/deal")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<PaginatedOutput<GetDealResponse>>() {}
        )

        assertThat(response.data, hasSize(2))
        assertTrue(response.data.any { it.id == firstDeal.id })
        assertTrue(response.data.any { it.id == thirdDeal.id })
        assertTrue(response.data.none { it.id == secondDeal.id })
        assertTrue(response.data.none { it.status == DealStatus.DELETED })
    }

    private fun changeStatus(dealId: Long, status: DealStatus) {
        Context.injector.getInstance(DealRepository::class.java).let { dealRepository ->
            dealRepository.findById(dealId)
                .let { dealRepository.update(it.copy(status = status)) }
        }
    }
}
