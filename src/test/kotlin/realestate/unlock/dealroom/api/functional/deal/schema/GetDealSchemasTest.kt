package realestate.unlock.dealroom.api.functional.deal.schema

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.notNullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.schema.DealSchema
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.core.entity.search.SortOrder
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.pagination.PaginationUrlBuilder

class GetDealSchemasTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal-schemas",
        method = Method.GET.name,
        permissions = listOf()
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal-schemas",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should get deal schema successfully for MEDICAL`() {
        // Given
        val propertyType = PropertyType.MEDICAL

        // When
        val output = HttpClient.get(
            url = PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "property_type" to mapOf(
                            "operator" to EnumOperator.EQ.name,
                            "values" to listOf(propertyType)
                        )
                    )
                )
                .withOrderBy("CREATED_AT")
                .withOrder(SortOrder.DESC)
                .getUrl("$localUrl/deal-schemas"),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<DealSchema>>() {}) }
        )

        // Then
        assertThat(output.size, equalTo(1))
        val dealSchema = output.data.first()
        assertThat(dealSchema.id, notNullValue())
        assertThat(dealSchema.propertyType, equalTo(propertyType))
        assertThat(dealSchema.viewSchema, notNullValue())
        assertThat(dealSchema.jsonSchema, notNullValue())
        assertThat(dealSchema.createdAt, notNullValue())
        assertThat(dealSchema.fieldDefinitions, notNullValue())
    }

    @Test
    fun `should get deal schema successfully for MULTIFAMILY`() {
        // Given
        val propertyType = PropertyType.MULTIFAMILY

        // When
        val output = HttpClient.get(
            url = PaginationUrlBuilder()
                .withFilter(
                    mapOf(
                        "property_type" to mapOf(
                            "operator" to EnumOperator.EQ.name,
                            "values" to listOf(propertyType)
                        )
                    )
                )
                .withOrderBy("CREATED_AT")
                .withOrder(SortOrder.DESC)
                .getUrl("$localUrl/deal-schemas"),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<PaginatedOutput<DealSchema>>() {}) }
        )

        // Then
        assertThat(output.size, equalTo(1))
        val dealSchema = output.data.first()
        assertThat(dealSchema.id, notNullValue())
        assertThat(dealSchema.propertyType, equalTo(propertyType))
        assertThat(dealSchema.viewSchema, notNullValue())
        assertThat(dealSchema.jsonSchema, notNullValue())
        assertThat(dealSchema.createdAt, notNullValue())
        assertThat(dealSchema.fieldDefinitions, notNullValue())
    }
}
