package realestate.unlock.dealroom.api.functional.task.history

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.CreateLinkedFileInput
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileData
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.entity.task.TaskStatusTransitions
import realestate.unlock.dealroom.api.core.entity.task.history.HistoryAnalysisResult
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.history.TaskHistoryItem
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.patch.request.ChangeTaskStatusRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.entity.deal.getRandomTask
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.extensions.fileInStringJsonFormat
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetTaskWithFilesAndHistoryHistoryTest : BaseFunctionalTest() {

    private val fileGateway: FileGateway = Context.injector.getInstance(FileGateway::class.java)

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/123/history",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/task/$givenTaskId/history",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/task/$givenTaskId/history",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenTaskId = givenDeal.findOneTask().id

        // When
        val result = Unirest.get("$localUrl/task/$givenTaskId/history")
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, not(equalTo(403)))
    }

    @Test
    fun `Task history should be correctly retrieved`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")

        val attachedForm = """{ "test_key_1": "Test value 1","test_key_2": "Test value 2","test_list_key": "Test list value 1","test_list_key": "Test list value 2"}"""

        val updatedTaskSnap1 = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${TaskStatus.IN_REVIEW.key}", "attachedForm": $attachedForm}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        val updatedTaskSnap2 = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${TaskStatus.REJECTED.key}"}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        val givenFile = fileInStringJsonFormat()

        val updatedTaskSnap3 = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${TaskStatus.IN_REVIEW.key}", "attachedForm": $givenFile}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        val response = getTaskHistory(givenTaskToUpdate.id)

        assertThat(response.size, equalTo(3))

        // First Snapshot
        response.first().let {
            assertThat(it.snapshot.beforeUpdate.attachedFormData, `is`(nullValue()))
            assertThat(it.snapshot.beforeUpdate.statusKey, not(equalTo(it.snapshot.updated.statusKey)))
            assertThat(it.snapshot.beforeUpdate.assignedTeam, equalTo(it.snapshot.beforeUpdate.assignedTeam))

            assertThat(it.snapshot.updated.assignedTeam, equalTo(it.snapshot.updated.assignedTeam))
            assertThat(it.snapshot.updated.statusKey, equalTo(updatedTaskSnap1.statusKey))
            assertThat(it.snapshot.updated.attachedFormData, equalTo(updatedTaskSnap1.attachedFormData))
            assertThat(it.snapshot.updates.files, `is`(emptyList()))
        }

        response[1].let {
            assertThat(it.snapshot.beforeUpdate.attachedFormData, `is`(not(emptyMap())))
            assertThat(it.snapshot.beforeUpdate.statusKey, not(equalTo(it.snapshot.updated.statusKey)))
            assertThat(it.snapshot.beforeUpdate.assignedTeam, equalTo(it.snapshot.beforeUpdate.assignedTeam))

            assertThat(it.snapshot.updated.assignedTeam, equalTo(it.snapshot.updated.assignedTeam))
            assertThat(it.snapshot.updated.statusKey, equalTo(updatedTaskSnap2.status))
            assertThat(it.snapshot.updated.attachedFormData, equalTo(updatedTaskSnap2.attachedFormData))
            assertThat(it.snapshot.updates.files, `is`(emptyList()))
        }

        response[2].let {
            assertThat(it.snapshot.beforeUpdate.attachedFormData, `is`(not(emptyMap())))
            assertThat(it.snapshot.beforeUpdate.statusKey, not(equalTo(it.snapshot.updated.statusKey)))
            assertThat(it.snapshot.beforeUpdate.assignedTeam, equalTo(it.snapshot.beforeUpdate.assignedTeam))

            assertThat(it.snapshot.updated.assignedTeam, equalTo(it.snapshot.updated.assignedTeam))
            assertThat(it.snapshot.updated.statusKey, equalTo(updatedTaskSnap3.statusKey))
            assertThat(it.snapshot.updated.attachedFormData, equalTo(updatedTaskSnap3.attachedFormData))
            assertThat(it.snapshot.updates.files, not(emptyList()))
            assertThat(it.snapshot.updates.files.all { file -> file.uid.isEmpty().not() }, `is`(true))
        }
    }

    @Test
    fun `Task history with rejected tasks should store the reasons correctly`() {
        // given a set of updates in the task status
        val dealResponse = DealCreator.createDealByRest()
        val givenTaskToUpdate = dealResponse.categories.first { it.categoryKey != "purchase_and_sale_contract" }.tasksWithFilesAndHistory.first().task
        val rejectionReason = "this is the reason why the task was rejected"

        // first snapshot

        HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${TaskStatus.IN_REVIEW.key}"}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { it }
        )

        // second snapshot
        Unirest.put("$localUrl/task/${givenTaskToUpdate.id}/status")
            .headers(AuthMock.getAuthHeader())
            .body(
                JsonMapper.encode(
                    ChangeTaskStatusRequest(
                        transition = TaskStatusTransitions.REJECT,
                        reason = rejectionReason
                    )
                )
            )
            .asString()

        // third snapshot
        HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${TaskStatus.IN_REVIEW.key}"}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { it }
        )

        // fourth snapshot
        Unirest.put("$localUrl/task/${givenTaskToUpdate.id}/status")
            .headers(AuthMock.getAuthHeader())
            .body(
                JsonMapper.encode(
                    ChangeTaskStatusRequest(
                        transition = TaskStatusTransitions.APPROVE
                    )
                )
            )
            .asString()

        // when we retrieve the task history
        val historyResult = Unirest.get("$localUrl/task/${givenTaskToUpdate.id}/history")
            .headers(AuthMock.getAuthHeader())
            .asString()

        val taskHistory = historyResult.body.let {
            JsonMapper.decode(it, object : TypeReference<List<TaskHistoryItem>>() {})
        }

        assertThat(historyResult.status, equalTo(200))
        assertThat(taskHistory, hasSize(4))

        // the first update changes from TO_DO to IN_REVIEW, so it should not contain any rejectionReason
        taskHistory[0].let {
            // updates
            assertThat(it.snapshot.updates.rejectionReason, nullValue())
            assertThat(it.snapshot.updates.statusKey, equalTo(TaskStatus.IN_REVIEW.key))

            // before update
            assertThat(it.snapshot.beforeUpdate.statusKey, equalTo(TaskStatus.TO_DO.key))
            assertThat(it.snapshot.beforeUpdate.rejectionReason, nullValue())

            // after update
            assertThat(it.snapshot.updated.statusKey, equalTo(TaskStatus.IN_REVIEW.key))
            assertThat(it.snapshot.updated.rejectionReason, nullValue())
        }

        // the second update rejects the task, so it goes from IN_REVIEW to TO_DO again, therefore a rejectionReason has to be set
        taskHistory[1].let {
            // updates
            assertThat(it.snapshot.updates.rejectionReason, equalTo(rejectionReason))
            assertThat(it.snapshot.updates.statusKey, equalTo(TaskStatus.REJECTED.key))

            // before update
            assertThat(it.snapshot.beforeUpdate.statusKey, equalTo(TaskStatus.IN_REVIEW.key))
            assertThat(it.snapshot.beforeUpdate.rejectionReason, nullValue())

            // after update
            assertThat(it.snapshot.updated.statusKey, equalTo(TaskStatus.REJECTED.key))
            assertThat(it.snapshot.updated.rejectionReason, equalTo(rejectionReason))
        }

        // the third update sends the task again to review, it goes from TO_DO to IN_REVIEW, so the rejectionReason has to be blanked
        taskHistory[2].let {
            // updates
            assertThat(it.snapshot.updates.rejectionReason, nullValue())
            assertThat(it.snapshot.updates.statusKey, equalTo(TaskStatus.IN_REVIEW.key))

            // before update
            assertThat(it.snapshot.beforeUpdate.statusKey, equalTo(TaskStatus.REJECTED.key))
            assertThat(it.snapshot.beforeUpdate.rejectionReason, equalTo(rejectionReason))

            // after update
            assertThat(it.snapshot.updated.statusKey, equalTo(TaskStatus.IN_REVIEW.key))
            assertThat(it.snapshot.updated.rejectionReason, nullValue())
        }

        // the fourth update approves the task, so it goes from IN_REVIEW to DONE, so no rejectionReason has to be added
        taskHistory[3].let {
            // updates
            assertThat(it.snapshot.updates.rejectionReason, nullValue())
            assertThat(it.snapshot.updates.statusKey, equalTo(TaskStatus.DONE.key))

            // before update
            assertThat(it.snapshot.beforeUpdate.statusKey, equalTo(TaskStatus.IN_REVIEW.key))
            assertThat(it.snapshot.beforeUpdate.rejectionReason, nullValue())

            // after update
            assertThat(it.snapshot.updated.statusKey, equalTo(TaskStatus.DONE.key))
            assertThat(it.snapshot.updated.rejectionReason, nullValue())
        }
    }

    @Test
    fun `Task history should be correctly retrieved if the user is not admin but is member of the related deal`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val givenSeller = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val dealResponse = DealCreator.createDealByRest(
            seller = givenSeller
        )
        val givenTaskToUpdate = dealResponse.categories.first { it.categoryKey != "purchase_and_sale_contract" }.tasksWithFilesAndHistory.first()

        val attachedForm = """{ "test_key_1": "Test value 1","test_key_2": "Test value 2","test_list_key": "Test list value 1","test_list_key": "Test list value 2"}"""

        val updatedTaskSnap1 = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.task.id}",
            body = """{"status": "${TaskStatus.IN_REVIEW.key}", "attachedForm": $attachedForm}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        val givenHistoryUrl = "$localUrl/task/${updatedTaskSnap1.id}/history"

        // When
        val result = Unirest.get(givenHistoryUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asString()

        // Then
        assertThat(result.status, equalTo(200))

        val response = result.body.let {
            JsonMapper.decode(it, object : TypeReference<List<TaskHistoryItem>>() {})
        }

        assertThat(response.size, equalTo(1))

        // First Snapshot
        response.first().let {
            assertThat(it.snapshot.beforeUpdate.attachedFormData, `is`(nullValue()))
            assertThat(it.snapshot.beforeUpdate.statusKey, not(equalTo(it.snapshot.updated.statusKey)))
            assertThat(it.snapshot.beforeUpdate.assignedTeam, equalTo(it.snapshot.beforeUpdate.assignedTeam))

            assertThat(it.snapshot.updated.assignedTeam, equalTo(it.snapshot.updated.assignedTeam))
            assertThat(it.snapshot.updated.statusKey, equalTo(updatedTaskSnap1.statusKey))
            assertThat(it.snapshot.updated.attachedFormData, equalTo(updatedTaskSnap1.attachedFormData))
            assertThat(it.snapshot.updates.files, `is`(emptyList()))
        }
    }

    @Test
    fun `Task history should be restricted if the user is not admin and is not member of the related deal`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken,
            permissions = listOf(Permission.READ_OWN_DEALS)
        )

        val dealResponse = DealCreator.createDealByRest()
        val givenTaskToUpdate = dealResponse.findOneTask()

        val attachedForm = """{ "test_key_1": "Test value 1","test_key_2": "Test value 2","test_list_key": "Test list value 1","test_list_key": "Test list value 2"}"""

        val updatedTaskSnap1 = HttpClient.patch(
            url = "$localUrl/task/${givenTaskToUpdate.id}",
            body = """{"status": "${givenTaskToUpdate.statusKey.key}", "attachedForm": $attachedForm}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        val givenHistoryUrl = "$localUrl/task/${updatedTaskSnap1.id}/history"

        // When
        val result = Unirest.get(givenHistoryUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
        assertThat(result.body.toPrettyString(), CoreMatchers.containsString("In order to access to the task you need to be member of the related deal"))
    }

    @Test
    fun `Should return bad request if do not send task id`() {
        // Given
        val givenTaskHistoryUrl = "$localUrl/task/asd/history"

        // When
        val result = Unirest.get(givenTaskHistoryUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        // Then
        assertThat(result.status, equalTo(400))
    }

    @Test
    fun `Should return bad request if task do not exists`() {
        // Given
        val givenNonExistentTaskId = 99999
        val givenTaskHistoryUrl = "$localUrl/task/$givenNonExistentTaskId/history"

        // When
        val result = Unirest.get(givenTaskHistoryUrl)
            .headers(AuthMock.getAuthHeader())
            .asString()

        // Then
        assertThat(result.status, equalTo(400))
    }

    @Test
    fun `should retrieve task history of linked file`() {
        val givenDeal = DealCreator.createDealByRest()
        val task = givenDeal.getRandomTask()

        val file = linkFileToTask(givenDeal.id, taskId = task.id)

        val response = getTaskHistory(taskId = task.id)

        val item = response.first()
        assertTrue(item.actions.contains(HistoryAnalysisResult.CHANGE_LINKED_FILES))
        assertTrue(item.addedFiles.any { it.id == file.fileId })
        assertTrue(item.addedFiles.any { it.name == file.fileName })
        assertEquals(1, item.actions.size)
        assertEquals(1, item.addedFiles.size)
        assertEquals(0, item.removedFiles.size)
    }

    @Test
    fun `should retrieve task history of unlinked file`() {
        val givenDeal = DealCreator.createDealByRest()
        val task = givenDeal.getRandomTask()

        val file = linkFileToTask(givenDeal.id, taskId = task.id)

        HttpClient.delete(
            url = "$localUrl/deal/${givenDeal.id}/linked-files/${file.id}",
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { }
        )

        val response = getTaskHistory(taskId = task.id)

        assertEquals(2, response.size)
        val item = response.first { it.removedFiles.isNotEmpty() }
        assertTrue(item.actions.contains(HistoryAnalysisResult.CHANGE_LINKED_FILES))
        assertEquals(1, item.actions.size)
        assertTrue(item.removedFiles.any { it.id == file.fileId })
        assertTrue(item.removedFiles.any { it.name == file.fileName })
        assertEquals(0, item.addedFiles.size)
        assertEquals(1, item.removedFiles.size)
    }

    private fun getTaskHistory(taskId: Long) = HttpClient.get(
        url = "$localUrl/task/$taskId/history",
        expectedStatus = HttpStatus.OK,
        responseHandler = { JsonMapper.decode(it, object : TypeReference<List<TaskHistoryItem>>() {}) }
    )

    private fun linkFileToTask(dealId: Long, taskId: Long): LinkedFileData = HttpClient.post(
        url = "$localUrl/deal/$dealId/linked-files",
        body = JsonMapper.encode(
            CreateLinkedFileInput(
                fileId = fileGateway.uploadFile(
                    FileToUpload(
                        content = mockk(),
                        contentType = "application/pdf",
                        path = FileUtils.buildDealRepositoryPath(dealId, ""),
                        filename = "file.pdf"
                    )
                ).uid,
                entityType = LinkedFileEntityType.TASK,
                entityId = taskId.toString(),
                filePath = null
            )
        ),
        expectedStatus = HttpStatus.CREATED,
        responseHandler = { JsonMapper.decode(it, LinkedFileData::class.java) }
    )
}
