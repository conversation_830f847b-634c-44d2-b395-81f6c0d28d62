package realestate.unlock.dealroom.api.functional.deal.team

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.member.buyerTeam
import realestate.unlock.dealroom.api.utils.entity.member.sellerTeam
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealTeamTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/members",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `should get deal team successfully`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenUrl = "$localUrl/deal/${givenDeal.id}/members"
        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<MemberDto>>() {}
        )

        MatcherAssert.assertThat(response.sellerTeam().size, Matchers.equalTo(1))
        MatcherAssert.assertThat(response.buyerTeam().size, Matchers.equalTo(1))
    }

    @Test
    fun `should get deal team successfully if user is not admin but is member of the related deal`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        val givenSeller = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val givenDeal = DealCreator.createDealByRest(
            seller = givenSeller
        )

        val givenUrl = "$localUrl/deal/${givenDeal.id}/members"

        // When
        val result = Unirest.get(givenUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<MemberDto>>() {}
        )

        MatcherAssert.assertThat(response.sellerTeam().size, Matchers.equalTo(1))
        MatcherAssert.assertThat(response.buyerTeam().size, Matchers.equalTo(1))
    }

    @Test
    fun `should restrict access if user is not admin and is not member of the related deal`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        val givenToken = "not-admin--without-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken,
            permissions = listOf(Permission.READ_OWN_DEALS)
        )

        val givenUrl = "$localUrl/deal/${givenDeal.id}/members"

        // When
        val result = Unirest.get(givenUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        MatcherAssert.assertThat(result.status, Matchers.equalTo(403))
        MatcherAssert.assertThat(result.body.toPrettyString(), CoreMatchers.containsString("In order to access to the deal you must be a member"))
    }
}
