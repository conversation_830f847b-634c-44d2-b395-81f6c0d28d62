package realestate.unlock.dealroom.api.functional.deal.documents.psa

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.document.DocumentInteractionRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.creator.PSACreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.documents.psa.InteractionRequestFactory
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class ChangeExpectedDateTest : BaseFunctionalTest() {

    private lateinit var documentRepository: DocumentRepository
    private lateinit var documentInteractionRepository: DocumentInteractionRepository

    @BeforeEach
    fun setUp() {
        documentRepository = Context.injector.getInstance(DocumentRepository::class.java)
        documentInteractionRepository = Context.injector.getInstance(DocumentInteractionRepository::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/documents/psa",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `can change expected date a psa`() {
        val deal = DealCreator.createDealByRest()
        PSACreator.createByRest(deal.id)
        val newExpectedDate = LocalDate.now().plusDays(10)
        HttpClient.post(
            url = "$localUrl/deal/${deal.id}/documents/psa",
            body = JsonMapper.encode(InteractionRequestFactory.changeExpectedDate(newExpectedDate)),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )
        val psa = documentRepository.findByDealIdAndDocumentType(deal.id, DocumentType.PSA)!!
        val interactions = documentInteractionRepository.findByRoundIdAndDocumentId(documentId = psa.id, roundId = psa.currentRoundId!!)
        val interaction = interactions.first { it.type == Interaction.Type.CHANGE_EXPECTED_DATE }
        assertEquals("Change date to $newExpectedDate", interaction.comment)
        assertEquals(newExpectedDate, psa.expectedBy?.toLocalDate())
    }
}
