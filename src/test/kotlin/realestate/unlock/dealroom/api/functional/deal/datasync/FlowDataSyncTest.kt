package realestate.unlock.dealroom.api.functional.deal.datasync

import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.datasync.ChangeToApply
import realestate.unlock.dealroom.api.core.entity.deal.datasync.DataSyncCell
import realestate.unlock.dealroom.api.core.entity.deal.datasync.FieldType
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.repository.deal.schema.DealSchemaRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealData
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.field.DataFieldInstance
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.field.DataFieldInstance.*
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.datasync.ApplyDataSyncInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.datasync.CreateDataSyncInput
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class FlowDataSyncTest : BaseDataSyncTest() {

    private lateinit var getDealData: GetDealData
    private lateinit var dealSchemaRepository: DealSchemaRepository

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        getDealData = Context.injector.getInstance(GetDealData::class.java)
        dealSchemaRepository = Context.injector.getInstance(DealSchemaRepository::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig.dontTestPermissions()

    @Test
    fun `updating all fields successfully MULTIFAMILY`() {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY })
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        val rows = values().filter { it.propertyType == PropertyType.MULTIFAMILY }.associateWith {
            when (it.dataField.type) {
                FieldType.NUMBER -> 9.12
                FieldType.PERCENTAGE -> 0.0912
                FieldType.STRING -> it.dataField.label
                FieldType.CURRENCY -> 9.12
            }
        }

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = rows.map {
                buildExcelFileRow(it.key.dataField.label, it.value)
            }
        ).let(::processDataSync)

        // When
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            val changesToApply = it.dataSync.data.changes?.map { change ->
                ChangeToApply(change.fieldId!!, change.value!!)
            }
            applyDataSync(deal.id, dataSyncResponse.dataSync.id, ApplyDataSyncInput(changesToApply!!))
        }

        // Then
        getDealData.get(deal.id).also { dealData ->
            DataFieldInstance.values().filter { it.propertyType == PropertyType.MULTIFAMILY }.forEach {
                Assertions.assertEquals(
                    it.dataField.parseValue(DataSyncCell(coordinates = "AA", value = rows[it])),
                    it.dataField.getValue(dealData),
                    "check value in ${it.dataField.label}"
                )
            }
        }
    }

    @Test
    fun `updating all fields successfully MEDICAL`() {
        // Given
        val deal = DealCreator.createDealByRest(dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MEDICAL })
        val dataSyncResponse = createDataSync(deal.id, CreateDataSyncInput(fileId = "fileId"))

        val rows = values().filter { it.propertyType == PropertyType.MEDICAL }.associateWith {
            when (it.dataField.type) {
                FieldType.NUMBER -> 9.12
                FieldType.PERCENTAGE -> 0.0912
                FieldType.STRING -> it.dataField.label
                FieldType.CURRENCY -> 9.12
            }
        }

        buildExcelFileResult(
            dataSync = dataSyncResponse.dataSync,
            status = "OK",
            rows = rows.map {
                buildExcelFileRow(it.key.dataField.label, it.value)
            }
        ).let(::processDataSync)

        // When
        getDataSync(deal.id, dataSyncResponse.dataSync.id).also {
            val changesToApply = it.dataSync.data.changes?.map { change ->
                ChangeToApply(change.fieldId!!, change.value!!)
            }
            applyDataSync(deal.id, dataSyncResponse.dataSync.id, ApplyDataSyncInput(changesToApply!!))
        }

        // Then
        getDealData.get(deal.id).also { dealData ->
            DataFieldInstance.values().filter { it.propertyType == PropertyType.MEDICAL }.forEach {
                Assertions.assertEquals(
                    it.dataField.parseValue(DataSyncCell(coordinates = "AA", value = rows[it])),
                    it.dataField.getValue(dealData),
                    "check value in ${it.dataField.label}"
                )
            }
        }
    }

    @Test
    fun `schemaData fields should exist in schema MULTIFAMILY`() {
        val propertyType = PropertyType.MULTIFAMILY
        val dealSchema = dealSchemaRepository.findLastByPropertyType(propertyType)

        DataFieldInstance.values()
            .filter { it.propertyType == propertyType }
            .filter { it.dataField.fieldId.startsWith("deal.schemaData.data.") }
            .map { it.dataField.fieldId.removePrefix("deal.schemaData.data.") }
            .forEach {
                Assertions.assertTrue(
                    dealSchema.jsonSchema.properties.containsKey(it),
                    "check field $it exists in schema"
                )
            }
    }

    @Test
    fun `schemaData fields should exist in schema MEDICAL`() {
        val propertyType = PropertyType.MEDICAL
        val dealSchema = dealSchemaRepository.findLastByPropertyType(propertyType)

        DataFieldInstance.values()
            .filter { it.propertyType == propertyType }
            .filter { it.dataField.fieldId.startsWith("deal.schemaData.data.") }
            .map { it.dataField.fieldId.removePrefix("deal.schemaData.data.") }
            .forEach {
                Assertions.assertTrue(
                    dealSchema.jsonSchema.properties.containsKey(it),
                    "check field $it exists in schema"
                )
            }
    }
}
