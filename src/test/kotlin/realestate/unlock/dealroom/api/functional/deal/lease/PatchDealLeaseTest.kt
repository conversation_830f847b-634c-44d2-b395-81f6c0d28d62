package realestate.unlock.dealroom.api.functional.deal.lease

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealLeaseDto
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.json.IncludeNullJsonMapper
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal
import java.time.LocalDate
import java.time.Month

class PatchDealLeaseTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/lease",
        method = Method.PATCH.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `should update deal lease successfully`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val rentNewValue = BigDecimal.valueOf(123456)
        val newDate = LocalDate.of(2023, Month.AUGUST, 12)
        val leaseUpdate = DealLeaseDto(
            rent = rentNewValue,
            type = null,
            rentIncrease = null,
            increaseEveryYear = null,
            length = null,
            expirationYear = null,
            numberOfOptions = null,
            optionLengths = null,
            rentCpi = null,
            rentStepType = null,
            dueDiligenceNumber = null,
            closingPeriod = null,
            closingPeriodExtension = null,
            date = newDate
        )

        // When
        val dealLease = HttpClient.patch(
            url = "$localUrl/deal/${deal.id}/lease",
            body = JsonMapper.encode(leaseUpdate),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealLeaseDto::class.java) }
        )

        val expectedDto = DealLeaseDto(deal.lease.copy(rent = rentNewValue, date = newDate))
        // Then
        Assertions.assertEquals(expectedDto, dealLease)
    }

    @Test
    fun `should set null on value of deal data`() {
        // Given
        val newData = mapOf(
            "rent" to null
        )
        val deal = DealCreator.createDealByRest()
        val dealLease = HttpClient.patch(
            url = "$localUrl/deal/${deal.id}/lease",
            body = IncludeNullJsonMapper.encode(newData),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealLeaseDto::class.java) }
        )

        // Then
        val expectedDto = DealLeaseDto(deal.lease.copy(rent = null))
        // Then
        Assertions.assertEquals(expectedDto, dealLease)
    }
}
