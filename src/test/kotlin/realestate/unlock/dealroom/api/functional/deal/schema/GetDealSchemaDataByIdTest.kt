package realestate.unlock.dealroom.api.functional.deal.schema

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.SchemaData
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.schema.DealSchemaData
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealSchemaDataByIdTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/schema-data",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `should get deal empty schema data successfully`() {
        // Given
        val deal = DealCreator.createDealByRest()

        // When
        val dealSchemaData = HttpClient.get(
            url = "$localUrl/deal/${deal.id}/schema-data",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        // Then
        Assertions.assertTrue(dealSchemaData.data.isEmpty())
    }

    @Test
    fun `should get deal schema data successfully`() {
        // Given
        val deal = DealCreator.createDealByRest()

        val dealRepo = Context.injector.getInstance(DealRepository::class.java)

        val dealUpdated = dealRepo.updateDealSchemaData(
            dealId = deal.id,
            dealSchemaData = SchemaData(
                schemaId = 1L,
                data = mapOf(
                    "uno" to 1,
                    "dos" to "dos"
                )
            )
        )

        // When
        val dealSchemaData = HttpClient.get(
            url = "$localUrl/deal/${deal.id}/schema-data",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        // Then
        Assertions.assertEquals(dealUpdated.schemaData.data, dealSchemaData.data)
    }
}
