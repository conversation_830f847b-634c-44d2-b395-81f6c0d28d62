package realestate.unlock.dealroom.api.functional.deal

import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealByPropertyKeywayIdTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/keyway-property/${anyString()}",
        method = Method.GET.name,
        permissions = listOf() // TODO should have permissions?
    )

    @Test
    fun `if deals does not exist returns not found`() {
        // Given
        val givenUrl = "$localUrl/deal/keyway-property/${anyString()}"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asEmpty()

        // Then
        MatcherAssert.assertThat(result.status, equalTo(404))
    }
}
