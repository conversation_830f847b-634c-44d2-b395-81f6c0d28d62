package realestate.unlock.dealroom.api.functional.deal.schema

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.notNullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.schema.DealSchema
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealSchemaByIdTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal-schemas/123",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal-schemas/123",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should get deal schema successfully`() {
        // Given
        val schemaId = 1L

        // When
        val dealSchema = HttpClient.get(
            url = "$localUrl/deal-schemas/$schemaId",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchema::class.java) }
        )

        // Then
        assertThat(dealSchema.id, equalTo(schemaId))
        assertThat(dealSchema.propertyType, notNullValue())
        assertThat(dealSchema.viewSchema, notNullValue())
        assertThat(dealSchema.jsonSchema, notNullValue())
        assertThat(dealSchema.createdAt, notNullValue())
        assertThat(dealSchema.fieldDefinitions, notNullValue())
    }

    @Test
    fun `should return 404 if a schema doesn't exist`() {
        // Given
        val schemaId = 0L

        // When
        val dealSchema = HttpClient.get(
            url = "$localUrl/deal-schemas/$schemaId",
            expectedStatus = HttpStatus.NOT_FOUND,
            responseHandler = { it }
        )
    }
}
