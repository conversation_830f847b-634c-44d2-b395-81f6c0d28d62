package realestate.unlock.dealroom.api.functional.deal.report

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.not
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealReportTest : BaseFunctionalTest() {

    private lateinit var buyer: Member

    @BeforeEach
    fun setUp() {
        buyer = AuthMock.createMemberWithUser(memberType = "buyer")
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/reports/123",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val firstReport = ReportRequestBuilder().apply { this.type = "HAZARDOUS_MATERIALS_RECORDS" }.build()
        val report = createDealReport(givenDeal.id, firstReport)

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/reports/${report.id}",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val firstReport = ReportRequestBuilder().apply { this.type = "HAZARDOUS_MATERIALS_RECORDS" }.build()
        val report = createDealReport(givenDeal.id, firstReport)

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/reports/${report.id}",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `can retrieve the report by id`() {
        // given
        val givenDeal = DealCreator.createDealByRest(buyer = buyer)
        val firstReport = ReportRequestBuilder().apply { this.type = "HAZARDOUS_MATERIALS_RECORDS" }.build()
        val report = createDealReport(givenDeal.id, firstReport)

        // when
        val result = Unirest.get("$localUrl/deal/${givenDeal.id}/reports/${report.id}")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<ReportResponse>() {}
        )

        assertThat(response.type, equalTo("HAZARDOUS_MATERIALS_RECORDS"))
    }

    private fun createDealReport(dealId: Long, input: ReportRequest) =
        Unirest.post("$localUrl/deal/$dealId/reports")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(input))
            .asJson()
            .let { JsonMapper.decode(it.body.toPrettyString(), ReportResponse::class.java) }
}
