package realestate.unlock.dealroom.api.functional.deal.loi

import io.mockk.every
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.loi.*
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.sign.BeginSignFlowOutput
import realestate.unlock.dealroom.api.core.gateway.sign.DocumentOutput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.repository.loi.LetterOfIntentSignRepository
import realestate.unlock.dealroom.api.core.usecase.loi.BeginLetterOfIntentSigning
import realestate.unlock.dealroom.api.core.usecase.loi.ExecuteLetterOfIntentFromSign
import realestate.unlock.dealroom.api.core.usecase.loi.RespondLetterOfIntent
import realestate.unlock.dealroom.api.core.usecase.loi.SignLetterOfIntent
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.ExecutedLoiResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalRound
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.io.InputStream

class GetExecutedLoiTest : BaseFunctionalTest() {

    companion object {
        private const val buyerToken = "buyer-token"
    }

    private lateinit var letterOfIntentSignRepository: LetterOfIntentSignRepository
    private lateinit var beginLetterOfIntentSigning: BeginLetterOfIntentSigning
    private lateinit var respondLetterOfIntent: RespondLetterOfIntent
    private lateinit var signLetterOfIntent: SignLetterOfIntent
    private lateinit var executeLetterOfIntentFromSign: ExecuteLetterOfIntentFromSign
    private lateinit var buyer: Member
    private lateinit var sellerSigner: MemberSign

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        letterOfIntentSignRepository = Context.injector.getInstance(LetterOfIntentSignRepository::class.java)
        beginLetterOfIntentSigning = Context.injector.getInstance(BeginLetterOfIntentSigning::class.java)
        respondLetterOfIntent = Context.injector.getInstance(RespondLetterOfIntent::class.java)
        signLetterOfIntent = Context.injector.getInstance(SignLetterOfIntent::class.java)
        executeLetterOfIntentFromSign = Context.injector.getInstance(ExecuteLetterOfIntentFromSign::class.java)
        buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = buyerToken, email = "<EMAIL>", uid = anyString())
        sellerSigner = MemberSign(id = "ESTO_ES_UN_ID", email = "<EMAIL>", firstName = "churros", lastName = "el_topo", completedAt = null, memberType = MemberTypeEnum.SELLER)
        Context.injector.getInstance(SignGateway::class.java).let { signGateway ->
            every { signGateway.beginFlow(any()) } returns BeginSignFlowOutput("signingId")
            every { signGateway.retrieveDocument(any()) } returns DocumentOutput(documentId = "100", content = InputStream.nullInputStream())
        }
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/loi",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/loi",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/loi",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val postDealResponse = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${postDealResponse.id}/loi"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, Matchers.not(Matchers.equalTo(403)))
    }

    @Test
    fun `return empty if there is not executed loi`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val executedLoiUrl = "$localUrl/deal/${deal.id}/loi"

        // When
        val result = Unirest.get(executedLoiUrl)
            .headers(AuthMock.getAuthHeader())
            .asEmpty()

        // Then
        assertThat(result.status, Matchers.equalTo(204))
    }

    @Test
    fun `can get an executed loi`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val executedLoiUrl = "$localUrl/deal/${deal.id}/loi"
        val executedLoi = givenExecutedLoi(deal)

        // When
        val result = Unirest.get(executedLoiUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, IsEqual(200))
        val response = JsonMapper.decode(result.body.toPrettyString(), ExecutedLoiResponse::class.java)
        assertThat(response.dealId, IsEqual(executedLoi.dealId))
        assertThat(response.executedAt, IsEqual(executedLoi.executedAt))
        assertThat(response.loiId, IsEqual(executedLoi.loiId))
        assertThat(response.loiFile!!.uid, IsEqual(executedLoi.letterOfIntentSigned!!.kFileId))
        assertThat(response.loiFile!!.name, IsEqual(executedLoi.letterOfIntentSigned!!.name))
    }

    private fun givenExecutedLoi(deal: CompleteDeal): ExecutedLetterOfIntent {
        val loiRound = givenLoiRound(deal)
        val loiSign = giveSignLoi(dealResponse = deal, medicalLoiRoundResponse = loiRound)
        signLetterOfIntent.sign(
            SignLetterOfIntentInput(
                envelopeId = loiSign.signingId,
                recipientId = loiSign.sellerSign.id
            )
        )
        return executeLetterOfIntentFromSign(
            input = ExecuteLetterOfIntentInput(
                loiSign.signingId
            ),
            authToken = anyString()
        )
    }

    private fun giveSignLoi(medicalLoiRoundResponse: LoiMedicalRound, dealResponse: CompleteDeal): LetterOfIntentSign {
        respondLetterOfIntent.respond(
            input = RespondLetterOfIntentInput(
                memberId = dealResponse.members.first { f -> f.typeKey == MemberTypeEnum.SELLER.key }.id,
                dealId = dealResponse.id,
                loiId = medicalLoiRoundResponse.id,
                accepted = true,
                comments = "no comments",
                files = listOf(),
                sellerSigner = sellerSigner
            )
        )
        return letterOfIntentSignRepository.findByLoiId(medicalLoiRoundResponse.id)!!
    }

    private fun givenLoiRound(deal: CompleteDeal): LoiMedicalRound {
        val loiCreationRequest = PostMedicalLoiRequestBuilder().build()
        return Unirest.post("$localUrl/deal/${deal.id}/loi/round")
            .headers(AuthMock.getAuthHeader(buyerToken))
            .body(JsonMapper.encode(loiCreationRequest))
            .asJson()
            .let { JsonMapper.decode(it.body.toPrettyString(), LoiMedicalRound::class.java) }
    }

    private fun CompleteDeal.seller() = members.first { it.typeKey == "seller" }.id
    private fun CompleteDeal.buyer() = members.first { it.typeKey == "buyer" }.id
}
