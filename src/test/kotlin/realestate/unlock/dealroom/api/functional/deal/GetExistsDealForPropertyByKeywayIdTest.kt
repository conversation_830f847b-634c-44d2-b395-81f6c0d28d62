package realestate.unlock.dealroom.api.functional.deal

import io.swagger.models.Method
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.core.IsEqual
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.get.IsDealForKeywayIdResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetExistsDealForPropertyByKeywayIdTest : BaseFunctionalTest() {

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/prospect/123/deal",
        method = Method.GET.name,
        permissions = listOf()
    )

    @Test
    fun `can know if exists a deal by property keywayId`() {
        // Given
        val keywayId = anyString()
        AuthMock.createMemberWithUser()
        val deal = DealCreator.createDealByRest(keywayId = keywayId)

        // When
        val response = HttpClient.get(
            url = "$localUrl/prospect/$keywayId/deal",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, IsDealForKeywayIdResponse::class.java) }
        )

        // Then
        assertThat(response.isDeal, IsEqual(true))
        assertThat(response.redirectUrl, IsEqual("http://localhost:8081/deals/${deal.id}"))
    }

    @Test
    fun `if the property has a deal on another property should not return anything`() {
        // Given
        val keywayId = anyString()
        AuthMock.createMemberWithUser()
        val deal = DealCreator.createDealByRest(keywayId = keywayId)

        val memberOfAnotherOrgToken = anyString()
        AuthMock.createMemberWithUser(
            email = "<EMAIL>",
            organizationId = "another-org-id",
            uid = anyString(),
            token = memberOfAnotherOrgToken
        )

        // When
        val response = HttpClient.get(
            url = "$localUrl/prospect/$keywayId/deal",
            token = memberOfAnotherOrgToken,
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, IsDealForKeywayIdResponse::class.java) }
        )

        // Then
        assertFalse(response.isDeal)
    }

    @Test
    fun `if deals does not exist returns not found`() {
        // Given
        AuthMock.createMemberWithUser()
        val keywayId = anyString()

        // When
        val response = HttpClient.get(
            url = "$localUrl/prospect/$keywayId/deal",
            expectedStatus = 200,
            responseHandler = { JsonMapper.decode(it, IsDealForKeywayIdResponse::class.java) }
        )

        // Then
        assertFalse(response.isDeal)
    }
}
