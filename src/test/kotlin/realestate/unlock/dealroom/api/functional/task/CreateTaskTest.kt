package realestate.unlock.dealroom.api.functional.task

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.category.summary.GetDealCategoriesSummaryResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.post.request.CreateTaskRequest
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.*
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class CreateTaskTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "buyer",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )

        // Given
        val dealResponse = DealCreator.createDealByRest()
        val categories = getDealCategories(dealResponse.id)
        val input = buildBody(dealCategoryId = categories[0].id)

        // When
        val result = HttpClient.post(
            url = "$localUrl/task",
            token = token,
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )

        // Then
        assertThat(result, CoreMatchers.containsString("You must be a deal member"))
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "buyer",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS)

        )

        // Given
        val dealResponse = DealCreator.createDealByRest()
        val categories = getDealCategories(dealResponse.id)
        val input = buildBody(
            assignedBuyerId = dealResponse.members.first { it.type == MemberTypeEnum.BUYER }.id,
            dealCategoryId = categories[0].id
        )

        // When
        val result = HttpClient.post(
            url = "$localUrl/task",
            token = token,
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { it }
        )
    }

    @Test
    fun `Permissions - seller member should return 403`() {
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )

        // Given
        val dealResponse = DealCreator.createDealByRest()
        val categories = getDealCategories(dealResponse.id)
        val input = buildBody(dealCategoryId = categories[0].id)

        // When
        val result = HttpClient.post(
            url = "$localUrl/task",
            token = token,
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.FORBIDDEN,
            responseHandler = { it }
        )
    }

    @Test
    fun `should create custom task`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val categories = getDealCategories(dealResponse.id)
        val input = buildBody(
            assignedBuyerId = dealResponse.members.first { it.type == MemberTypeEnum.BUYER }.id,
            dealCategoryId = categories[0].id
        )

        // When
        val result = HttpClient.post(
            url = "$localUrl/task",
            body = JsonMapper.encode(input),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

        // Then
        assertThat(result.id, CoreMatchers.notNullValue())
        assertThat(result.assignedBuyer.id, CoreMatchers.equalTo(input.assignedBuyerId))
        assertThat(result.dealCategoryId, CoreMatchers.equalTo(input.dealCategoryId))
        assertThat(result.statusKey, CoreMatchers.equalTo(input.status.key))
        assertThat(result.custom, CoreMatchers.equalTo(true))
        assertThat(result.title, CoreMatchers.equalTo(input.title))
        assertThat(result.description, CoreMatchers.equalTo(input.description))
        assertThat(result.dueDate, CoreMatchers.equalTo(input.dueDate))
        assertThat(result.priority, CoreMatchers.equalTo(input.priority))
    }

    private fun getDealCategories(dealId: Long) = HttpClient.get(
        url = "$localUrl/deal/$dealId/categories-summary",
        expectedStatus = HttpStatus.OK,
        responseHandler = { JsonMapper.decode(it, object : TypeReference<List<GetDealCategoriesSummaryResponse>>() {}) }
    )

    private fun buildBody(assignedBuyerId: Long = 1, dealCategoryId: Long = 123) = CreateTaskRequest(
        assignedBuyerId = assignedBuyerId,
        dealCategoryId = dealCategoryId,
        status = TaskStatus.TO_DO,
        title = "some title",
        description = "some detail",
        dueDate = null,
        priority = Priority(PriorityValue.HIGHEST, 0)
    )
}
