package realestate.unlock.dealroom.api.functional.dashboard

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.search.EnumOperator
import realestate.unlock.dealroom.api.core.entity.search.SortOrder
import realestate.unlock.dealroom.api.core.entity.search.StringOperator
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.entity.task.TaskToUpdate
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.dashboard.TaskDashboard
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableEnum
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableString
import realestate.unlock.dealroom.api.entrypoint.rest.handler.dashboard.GetTaskSearchForDashboardHandler
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.pagination.PaginationUrlBuilder

class GetTaskDataForDashboardTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/dashboard/tasks",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DASHBOARD)
    )

    @Test
    fun `should get task for dashboard successfully with READ_ALL_DASHBOARD`() {
        // Given
        val token = anyString()
        val buyer = AuthMock.createMemberWithUser(
            uid = token,
            token = token,
            memberType = "buyer",
            email = "$<EMAIL>",
            permissions = listOf(Permission.READ_OWN_DASHBOARD, Permission.READ_ALL_DASHBOARD)
        )
        val givenDealCreationRequestBuilder = CreateDealInputBuilder().apply { guaranteeType = GuaranteeType.CORPORATE }
        val givenDeal = DealCreator.createDealByRest(dealCreationRequestBuilder = givenDealCreationRequestBuilder, buyer = buyer)
        val dealFromAnotherMember = DealCreator.createDealByRest(dealCreationRequestBuilder = givenDealCreationRequestBuilder, buyerEmail = "<EMAIL>", sellerEmail = "<EMAIL>")

        // When
        val result = Unirest.get("$localUrl/dashboard/tasks?size=1000")
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val data = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<Map<String, *>>() {
            }
        )["data"]

        val listResponse = JsonMapper.decode(
            JsonMapper.encode(data!!),
            object : TypeReference<List<TaskDashboard>>() {}
        )

        Assertions.assertEquals(134, listResponse.size)
        val response = listResponse.first()
        Assertions.assertEquals(response.address.street, givenDealCreationRequestBuilder.propertyStreet)
        Assertions.assertEquals(response.address.city, givenDealCreationRequestBuilder.propertyCity)
        Assertions.assertEquals(response.address.state, givenDealCreationRequestBuilder.propertyState)
        Assertions.assertEquals(response.dealId, givenDeal.id)
        Assertions.assertEquals(response.task.status, TaskStatus.TO_DO)
    }

    @Test
    fun `should get only tasks that are enabled`() {
        // given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskToDisable = givenDeal.findOneTask()
        disableTask(givenTaskToDisable.id)

        // when
        val result = HttpClient.get(
            url = "$localUrl/dashboard/tasks?size=1000",
            expectedStatus = 200,
            responseHandler = {
                val data = JsonMapper.decode(it, object : TypeReference<Map<String, *>>() {})["data"]
                JsonMapper.decode(
                    JsonMapper.encode(data!!),
                    object : TypeReference<List<TaskDashboard>>() {}
                )
            }
        )

        assertTrue(result.none { it.task.id == givenTaskToDisable.id })
    }

    @Test
    fun `should get only task from member deals if doesn't have READ_ALL_DASHBOARD`() {
        // Given
        val token = anyString()
        val buyer = AuthMock.createMemberWithUser(
            uid = token,
            token = token,
            memberType = "buyer",
            email = "$<EMAIL>",
            permissions = listOf(Permission.READ_OWN_DASHBOARD)
        )
        val givenDealCreationRequestBuilder = CreateDealInputBuilder().apply { guaranteeType = GuaranteeType.CORPORATE }
        val givenDeal = DealCreator.createDealByRest(dealCreationRequestBuilder = givenDealCreationRequestBuilder, buyer = buyer)
        val dealFromAnotherMember = DealCreator.createDealByRest(dealCreationRequestBuilder = givenDealCreationRequestBuilder, buyerEmail = "<EMAIL>", sellerEmail = "<EMAIL>")

        // When
        val result = Unirest.get("$localUrl/dashboard/tasks?size=1000")
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val data = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<Map<String, *>>() {
            }
        )["data"]

        val listResponse = JsonMapper.decode(
            JsonMapper.encode(data!!),
            object : TypeReference<List<TaskDashboard>>() {}
        )

        Assertions.assertEquals(67, listResponse.size)
    }

    @Test
    fun `should get task for dashboard successfully filter by memberTeam`() {
        // Given
        val givenDealCreationRequestBuilder = CreateDealInputBuilder().apply { guaranteeType = GuaranteeType.CORPORATE }
        val givenDeal = DealCreator.createDealByRest(dealCreationRequestBuilder = givenDealCreationRequestBuilder)
        val filterSeller = mapOf("assigned_team" to SearchableEnum<MemberDealTeam>(operator = EnumOperator.IN, values = listOf(MemberDealTeam.SELLER)))
        val filterBuyer = mapOf("assigned_team" to SearchableEnum<MemberDealTeam>(operator = EnumOperator.IN, values = listOf(MemberDealTeam.BUYER)))

        val sellerTasks = executeRequestForFilterMemberTeam(filterSeller, MemberDealTeam.SELLER)
        val buyerTasks = executeRequestForFilterMemberTeam(filterBuyer, MemberDealTeam.BUYER)

        buyerTasks.forEach { buyerTD ->
            sellerTasks.forEach { sellerTD ->
                Assertions.assertNotEquals(
                    buyerTD.task.id,
                    sellerTD.task.id,
                    JsonMapper.encode(listOf(buyerTD.task, sellerTD.task))
                )
            }
        }
    }

    @Test
    fun `should get task successfully filter by property type`() {
        // Given
        val multifamilyDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY }
        )
        val medicalDeal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MEDICAL },
            buyerEmail = "<EMAIL>",
            sellerEmail = "<EMAIL>"
        )

        val search = executeSearch(
            filter = mapOf("property_type" to SearchableEnum(operator = EnumOperator.EQ, values = listOf(PropertyType.MEDICAL)))
        )

        val deals = search.groupBy(TaskDashboard::dealId)
        Assertions.assertEquals(1, deals.size)
        Assertions.assertEquals(medicalDeal.id, deals.keys.first())
    }

    @Test
    fun `should get 200 from search with filters`() {
        // Given
        val givenDealCreationRequestBuilder = CreateDealInputBuilder().apply { guaranteeType = GuaranteeType.CORPORATE }
        val givenDeal = DealCreator.createDealByRest(dealCreationRequestBuilder = givenDealCreationRequestBuilder)
        val filters = mapOf(
            "assigned_team" to SearchableEnum<MemberDealTeam>(operator = EnumOperator.IN, values = listOf(MemberDealTeam.BUYER)),
            "task_title" to SearchableString(StringOperator.LIKE, values = listOf("ease")),
            "address_state" to SearchableString(StringOperator.EQ, values = listOf(givenDealCreationRequestBuilder.propertyState)),
            "address_city" to SearchableString(StringOperator.EQ, values = listOf(givenDealCreationRequestBuilder.propertyCity)),
            "task_stages" to SearchableEnum<Stage>(operator = EnumOperator.IN, values = listOf(Stage.CLOSING, Stage.DILIGENCE)),
            "deal_stages" to SearchableEnum<Stage>(operator = EnumOperator.IN, values = listOf(Stage.OFFER, Stage.UNDERWRITING, Stage.EVALUATION))
        )

        val results = executeSearch(filter = filters)

        assertTrue(results.none { td -> td.task.assignedTeam == MemberDealTeam.SELLER })
        assertTrue(results.none { td -> !listOf(Stage.CLOSING, Stage.DILIGENCE).contains(td.task.stage) })
        assertTrue(results.isNotEmpty())
    }

    private fun executeRequestForFilterMemberTeam(filter: Map<String, SearchableEnum<MemberDealTeam>>, team: MemberDealTeam): List<TaskDashboard> {
        val response = executeSearch(filter)
        assertTrue(response.all { it.task.assignedTeam == team })

        return response
    }

    private fun executeSearch(
        filter: Map<String, Any> = emptyMap(),
        orderBy: GetTaskSearchForDashboardHandler.TaskDashboardOrderBy = GetTaskSearchForDashboardHandler.TaskDashboardOrderBy.TASK_ID,
        order: SortOrder = SortOrder.DESC,
        size: Int = 1000,
        offset: Int = 0
    ): List<TaskDashboard> {
        val givenUrl = PaginationUrlBuilder()
            .withFilter(filter)
            .withOffset(offset)
            .withSize(size)
            .withOrder(order)
            .withOrderBy(orderBy.name)
            .getUrl("$localUrl/dashboard/tasks")

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val data = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<Map<String, *>>() {
            }
        )["data"]

        return JsonMapper.decode(
            JsonMapper.encode(data!!),
            object : TypeReference<List<TaskDashboard>>() {}
        )
    }

    @Test
    fun `should orderBy task id`() {
        // Given
        val givenDealCreationRequestBuilder = CreateDealInputBuilder().apply { guaranteeType = GuaranteeType.CORPORATE }
        val givenDeal = DealCreator.createDealByRest(dealCreationRequestBuilder = givenDealCreationRequestBuilder)

        val orderDesc = executeSearch(order = SortOrder.DESC)
        val orderAsc = executeSearch(order = SortOrder.ASC)

        Assertions.assertEquals(orderAsc.first().task.id, orderAsc.minOf { it.task.id })
        Assertions.assertEquals(orderDesc.first().task.id, orderDesc.maxOf { it.task.id })
        Assertions.assertNotEquals(orderAsc.first().task.id, orderDesc.first().task.id)
    }

    @Test
    fun `should orderBy task stage`() {
        // Given
        val buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = "buyer-token", email = "<EMAIL>", uid = anyString())
        val seller = AuthMock.createMemberWithUser(memberType = "seller", token = "seller-token", email = "<EMAIL>", uid = anyString())
        val givenDeal = DealCreator.createDealByRest(buyer = buyer, seller = seller)
        val anotherDeal = DealCreator.createDealByRest(buyer = buyer, seller = seller)

        val orderByStageAscendingSearch = executeSearch(order = SortOrder.ASC, orderBy = GetTaskSearchForDashboardHandler.TaskDashboardOrderBy.TASK_STAGE)
        val orderByStageDescendingSearch = executeSearch(order = SortOrder.DESC, orderBy = GetTaskSearchForDashboardHandler.TaskDashboardOrderBy.TASK_STAGE)

        val orderByStageAscending = orderByStageAscendingSearch.map { it.task.stage }.distinct()
        val orderByStageDescending = orderByStageDescendingSearch.map { it.task.stage }.distinct()
        assertThat(orderByStageAscending, hasSize(6))
        assertThat(orderByStageAscending[0], equalTo(Stage.CLOSING))
        assertThat(orderByStageAscending[1], equalTo(Stage.DILIGENCE))
        assertThat(orderByStageAscending[2], equalTo(Stage.EVALUATION))
        assertThat(orderByStageAscending[3], equalTo(Stage.NEGOTIATION))
        assertThat(orderByStageAscending[4], equalTo(Stage.POST_CLOSING))
        assertThat(orderByStageAscending[5], equalTo(Stage.UNDERWRITING))

        assertThat(orderByStageDescending, hasSize(6))
        assertThat(orderByStageDescending[0], equalTo(Stage.UNDERWRITING))
        assertThat(orderByStageDescending[1], equalTo(Stage.POST_CLOSING))
        assertThat(orderByStageDescending[2], equalTo(Stage.NEGOTIATION))
        assertThat(orderByStageDescending[3], equalTo(Stage.EVALUATION))
        assertThat(orderByStageDescending[4], equalTo(Stage.DILIGENCE))
        assertThat(orderByStageDescending[5], equalTo(Stage.CLOSING))
    }

    @Test
    fun `should paginate orderBy task id`() {
        // Given
        val givenDealCreationRequestBuilder = CreateDealInputBuilder().apply { guaranteeType = GuaranteeType.CORPORATE }
        val givenDeal = DealCreator.createDealByRest(dealCreationRequestBuilder = givenDealCreationRequestBuilder)

        val order1 = executeSearch(order = SortOrder.DESC, size = 10)
        val order2 = executeSearch(order = SortOrder.DESC, size = 10, offset = 10)

        Assertions.assertNotEquals(order1.first().task.id, order2.first().task.id)
        Assertions.assertNotEquals(order1.last().task.id, order2.last().task.id)
    }

    @Test
    fun `should return total`() {
        // Given
        val givenDealCreationRequestBuilder = CreateDealInputBuilder().apply { guaranteeType = GuaranteeType.CORPORATE }
        val givenDeal = DealCreator.createDealByRest(dealCreationRequestBuilder = givenDealCreationRequestBuilder)

        val givenUrl = PaginationUrlBuilder()
            .getUrl("$localUrl/dashboard/tasks")

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val totalCalculated = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<Map<String, *>>() {
            }
        )["total"] as Int

        assertTrue(totalCalculated > 0)

        val totalValue = 1
        val givenUrlWithTotal = PaginationUrlBuilder()
            .withTotal(totalValue)
            .getUrl("$localUrl/dashboard/tasks")

        // When
        val resultWithTotal = Unirest.get(givenUrlWithTotal)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val total = JsonMapper.decode(
            resultWithTotal.body.toPrettyString(),
            object : TypeReference<Map<String, *>>() {
            }
        )["total"] as Int

        Assertions.assertEquals(total, totalValue)
        Assertions.assertNotEquals(total, totalCalculated)
    }

    private fun disableTask(taskId: Long) {
        Context.injector.getInstance(TaskRepository::class.java).let { taskRepository ->
            taskRepository.findById(taskId)
                .let { task ->
                    taskRepository.update(TaskToUpdate(task.copy(enabled = false)))
                }
        }
    }
}
