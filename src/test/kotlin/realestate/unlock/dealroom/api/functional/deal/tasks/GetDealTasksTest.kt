package realestate.unlock.dealroom.api.functional.deal.tasks

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.search.NumberOperator
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableNumber
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.TaskResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.net.URLEncoder

class GetDealTasksTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/tasks",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `can fetch deal tasks without filters`() {
        val deal = DealCreator.createDealByRest()
        val tasks = HttpClient.get(
            url = "$localUrl/deal/${deal.id}/tasks",
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<TaskResponse>>() {}) }
        )

        assert(tasks.isNotEmpty())
    }

    @Test
    fun `can fetch deal tasks with assigned buyer filter`() {
        val deal = DealCreator.createDealByRest()
        val buyer = deal.members.first { it.isTeam(MemberDealTeam.BUYER) }
        val filter = mapOf(
            "assigned_buyer_id" to SearchableNumber(operator = NumberOperator.EQ, values = listOf(buyer.id))
        )
        val tasks = HttpClient.get(
            url = "$localUrl/deal/${deal.id}/tasks?filter=${URLEncoder.encode(JsonMapper.encode(filter), "UTF-8")}",
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<TaskResponse>>() {}) }
        )

        tasks.forEach {
            assertEquals(buyer.id, it.assignedBuyer.id)
        }
    }

    @Test
    fun `if any member match, returns empty task list`() {
        val deal = DealCreator.createDealByRest()
        val filter = mapOf(
            "assigned_buyer_id" to SearchableNumber(operator = NumberOperator.EQ, values = listOf(anyId()))
        )
        val tasks = HttpClient.get(
            url = "$localUrl/deal/${deal.id}/tasks?filter=${URLEncoder.encode(JsonMapper.encode(filter), "UTF-8")}",
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<TaskResponse>>() {}) }
        )

        assert(tasks.isEmpty())
    }
}
