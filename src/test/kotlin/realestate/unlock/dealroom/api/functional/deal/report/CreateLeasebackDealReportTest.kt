package realestate.unlock.dealroom.api.functional.deal.report

import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.HttpResponse
import kong.unirest.HttpStatus
import kong.unirest.JsonNode
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.nullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportType
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportVendor
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ConflictException
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub

class CreateLeasebackDealReportTest : BaseFunctionalTest() {

    private lateinit var fileGatewayStub: FileGatewayStub

    @BeforeEach
    fun setUp() {
        fileGatewayStub = Context.injector.getInstance(FileGateway::class.java) as FileGatewayStub
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/reports",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/reports",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/reports",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/reports",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `can create a deal report`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTag = 1L
        val givenInput = ReportRequestBuilder()
            .apply { this.tags = listOf(givenTag) }
            .apply { this.documents = listOf(FileInput(uid = createFile(givenDeal.id, "", "amazing.pdf").uid, name = "amazing.pdf")) }
            .build()

        // When
        val result = createReport(dealId = givenDeal.id, input = givenInput)

        // Then
        assertThat(result.status, equalTo(201))
        val response = JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
        assertThat(response.vendorKey, equalTo(givenInput.vendorKey))
        assertThat(response.type, equalTo(givenInput.type))
        assertThat(response.status, equalTo(givenInput.status))
        assertThat(response.documents[0].name, equalTo("amazing.pdf"))
        assertThat(response.tags[0], equalTo(givenTag))
    }

    @Test
    fun `report name is set only if report type is OTHER`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenInput = ReportRequestBuilder()
            .apply { this.type = ReportType.OTHER }
            .apply { this.reportName = "the report name" }
            .build()

        // When
        val result = createReport(dealId = givenDeal.id, input = givenInput)

        // Then
        assertThat(result.status, equalTo(201))
        val response = JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
        assertThat(response.type, equalTo(ReportType.OTHER))
        assertThat(response.reportName, equalTo(givenInput.reportName))
    }

    @Test
    fun `report name is not set if report type is not OTHER`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenInput = ReportRequestBuilder()
            .apply { this.type = "SEISMIC_REPORT" }
            .apply { this.reportName = "the report name" }
            .build()

        // When
        val result = createReport(dealId = givenDeal.id, input = givenInput)

        // Then
        assertThat(result.status, equalTo(201))
        val response = JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
        assertThat(response.type, equalTo("SEISMIC_REPORT"))
        assertThat(response.reportName, nullValue())
    }

    @Test
    fun `vendor name is set only if vendor is OTHER`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenInput = ReportRequestBuilder()
            .apply { this.vendorKey = ReportVendor.OTHER }
            .apply { this.vendorName = "the vendor name" }
            .build()

        // When
        val result = createReport(dealId = givenDeal.id, input = givenInput)

        // Then
        assertThat(result.status, equalTo(201))
        val response = JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
        assertThat(response.vendorKey, equalTo(ReportVendor.OTHER))
        assertThat(response.vendorName, equalTo(givenInput.vendorName))
    }

    @Test
    fun `vendor name is not set if vendor is not OTHER`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenInput = ReportRequestBuilder()
            .apply {
                type = "APPRAISAL"
                vendorKey = "BOWERY_VALUATION"
                vendorName = "the vendor name"
            }.build()

        // When
        val result = createReport(dealId = givenDeal.id, input = givenInput)

        // Then
        assertThat(result.status, equalTo(201))
        val response = JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
        assertThat(response.vendorKey, equalTo(givenInput.vendorKey))
        assertThat(response.vendorName, nullValue())
    }

    @Test
    fun `cannot create a report with the same type and vendor more than once`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenInput = ReportRequestBuilder()
            .apply { this.type = "SEISMIC_REPORT" }
            .apply { this.vendorKey = "CRETELLIGENT" }
            .build()

        createReport(dealId = givenDeal.id, input = givenInput)
        val duplicatedReportResponse = createReport(dealId = givenDeal.id, input = givenInput)

        assertThat(duplicatedReportResponse.status, equalTo(HttpStatus.CONFLICT))
        val expectedError = JsonMapper.decode(duplicatedReportResponse.body.toString(), ConflictException::class.java)
        assertThat(expectedError.message, equalTo("Report of type [${givenInput.type}] with vendor [${givenInput.vendorKey}] already exists."))
    }

    private fun createReport(dealId: Long, input: ReportRequest): HttpResponse<JsonNode> =
        Unirest.post("$localUrl/deal/$dealId/reports")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(input))
            .asJson()

    private fun createFile(dealId: Long, path: String, name: String) = fileGatewayStub.uploadFile(
        FileToUpload(
            content = mockk(),
            contentType = "application/pdf",
            path = FileUtils.buildDealRepositoryPath(dealId, path),
            filename = name
        )
    )
}
