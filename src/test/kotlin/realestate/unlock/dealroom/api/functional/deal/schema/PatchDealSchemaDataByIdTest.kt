package realestate.unlock.dealroom.api.functional.deal.schema

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.SchemaData
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.usecase.deal.schema.FieldsCalculatorUtils.defaultScale
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.schema.DealSchemaData
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal

class PatchDealSchemaDataByIdTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/schema-data",
        method = Method.PATCH.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `should update deal schema successfully`() {
        // Given
        val deal = DealCreator.createDealByRest()
        Context.injector.getInstance(DealRepository::class.java).updateDealSchemaData(
            dealId = deal.id,
            dealSchemaData = SchemaData(
                schemaId = 1L,
                data = emptyMap()
            )
        )

        val newData = mapOf(
            "uno" to 1,
            "dos" to "dos"
        )

        // When
        val dealSchema = HttpClient.patch(
            url = "$localUrl/deal/${deal.id}/schema-data",
            body = JsonMapper.encode(DealSchemaData(newData)),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        // Then
        Assertions.assertEquals(newData, dealSchema.data)
    }

    @Test
    fun `should replace schema data`() {
        // Given
        val oldData = mapOf(
            "uno" to 1,
            "dos" to "dos"
        )
        val deal = DealCreator.createDealByRest()
        Context.injector.getInstance(DealRepository::class.java).updateDealSchemaData(
            dealId = deal.id,
            dealSchemaData = SchemaData(
                schemaId = 1L,
                data = oldData
            )
        )

        val newData = mapOf(
            "uno" to 123
        )
        // When
        val dealSchema = HttpClient.patch(
            url = "$localUrl/deal/${deal.id}/schema-data",
            body = JsonMapper.encode(DealSchemaData(newData)),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        // Then
        Assertions.assertEquals(dealSchema.data["uno"], 123)
        Assertions.assertEquals(dealSchema.data["dos"], "dos")
    }

    @Test
    fun `should append schema data`() {
        // Given
        val oldData = mapOf(
            "uno" to 1,
            "dos" to "dos"
        )
        val deal = DealCreator.createDealByRest()
        Context.injector.getInstance(DealRepository::class.java).updateDealSchemaData(
            dealId = deal.id,
            dealSchemaData = SchemaData(
                schemaId = 1L,
                data = oldData
            )
        )

        val newData = mapOf(
            "uno-nuevo" to 1,
            "dos-nuevo" to "dos"
        )
        // When
        val dealSchema = HttpClient.patch(
            url = "$localUrl/deal/${deal.id}/schema-data",
            body = JsonMapper.encode(DealSchemaData(newData)),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        // Then
        oldData.forEach {
            Assertions.assertTrue(
                dealSchema.data.containsKey(it.key)
            )
        }
        newData.forEach {
            Assertions.assertTrue(
                dealSchema.data.containsKey(it.key)
            )
        }
    }

    @Test
    fun `can store medical custom fields`() {
        // Given
        val deal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                propertyAskingPrice = BigDecimal.valueOf(200)
                propertySquareFootage = BigDecimal.valueOf(100)
            }
        )
        Context.injector.getInstance(DealRepository::class.java).updateDealSchemaData(
            dealId = deal.id,
            dealSchemaData = SchemaData(
                schemaId = 1L,
                data = emptyMap()
            )
        )

        val newData = mapOf(
            "updateNotes" to anyString(),
            "pipelineNotes" to anyString(),
            "propertyNotes" to anyString(),
            "offerNoi" to BigDecimal.valueOf(50).defaultScale(),
            "offerCapRate" to BigDecimal.valueOf(25).defaultScale(),
            "askingNoi" to BigDecimal.valueOf(10).defaultScale()
        )
        // When
        val dealSchema = HttpClient.patch(
            url = "$localUrl/deal/${deal.id}/schema-data",
            body = JsonMapper.encode(DealSchemaData(newData)),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        // Then
        val expectedUpdatedData = newData
            .plus("askingCapRate" to BigDecimal.valueOf(5).defaultScale())
            .plus("offerNoiPsf" to BigDecimal(0.500).defaultScale())

        assertEquals(expectedUpdatedData, dealSchema.data)
    }

    @Test
    fun `can store multifamily custom fields`() {
        // Given
        val deal = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply {
                propertySquareFootage = BigDecimal.valueOf(1000)
                propertyAskingPrice = BigDecimal.valueOf(2200000)
                offerPrice = BigDecimal.valueOf(2000000)
                propertyType = PropertyType.MULTIFAMILY
            }
        )

        val newData = mapOf(
            "stabilizedNoi" to BigDecimal.valueOf(500000).defaultScale(),
            "units" to 10,
            "capexUnit" to BigDecimal.valueOf(8000).defaultScale(),
            "t12Noi" to BigDecimal.valueOf(400000).defaultScale()
        )

        // When
        val dealSchema = HttpClient.patch(
            url = "$localUrl/deal/${deal.id}/schema-data",
            body = JsonMapper.encode(DealSchemaData(newData)),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        val expectedData = mapOf(
            "stabilizedNoi" to BigDecimal.valueOf(500000).defaultScale(),
            "units" to 10,
            "capexUnit" to BigDecimal.valueOf(8000).defaultScale(),
            "t12Noi" to BigDecimal.valueOf(400000).defaultScale(),
            "stabilizedYoc" to BigDecimal.valueOf(24).defaultScale(),
            "askingPriceUnit" to 220000,
            "askingPriceSf" to 2200,
            "offerPriceUnit" to 200000,
            "offerPriceSf" to 2000,
            "goingInCap" to BigDecimal("20").defaultScale()
        )

        // Then
        assertEquals(expectedData, dealSchema.data)
    }
}
