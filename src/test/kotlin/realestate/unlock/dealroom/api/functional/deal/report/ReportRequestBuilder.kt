package realestate.unlock.dealroom.api.functional.deal.report

import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportStatus
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportRequest
import java.math.BigDecimal
import java.time.LocalDate

class ReportRequestBuilder {
    var type: String = "SEISMIC_REPORT"
    var reportName: String? = null
    var vendorKey: String = "EBI"
    var vendorName: String? = null
    var status: ReportStatus = ReportStatus.ORDERED
    var expectedDate: LocalDate? = null
    var costEstimate: BigDecimal? = null
    var cost: BigDecimal? = null
    var findings: String? = null
    var tags: List<Long> = listOf()
    var documents: List<FileInput> = listOf()

    fun build(): ReportRequest = ReportRequest(
        type = type,
        reportName = reportName,
        vendorKey = vendorKey,
        vendorName = vendorName,
        status = status,
        expectedDate = expectedDate,
        costEstimate = costEstimate,
        cost = cost,
        findings = findings,
        tags = tags,
        documents = documents
    )
}
