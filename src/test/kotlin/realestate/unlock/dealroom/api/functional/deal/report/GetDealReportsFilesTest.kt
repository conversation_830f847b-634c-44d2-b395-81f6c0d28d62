package realestate.unlock.dealroom.api.functional.deal.report

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileToken
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileTokenStatus
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.repository.file.FileTokenRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.gpt.FileTokenStatusResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.gpt.ReportFileResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.gpt.ReportFileSourceResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.repository.database.file.DocumentTypePromptDatabaseRepository
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.extensions.fileInStringJsonFormat
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub

class GetDealReportsFilesTest : BaseFunctionalTest() {

    private lateinit var taskFileGatewayStub: FileGatewayStub
    private val fileTypePromptDatabaseRepository = Context.injector.getInstance(DocumentTypePromptDatabaseRepository::class.java)

    @BeforeEach
    fun setUp() {
        taskFileGatewayStub = Context.injector.getInstance(FileGateway::class.java) as FileGatewayStub
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/reports/123/files",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `It retrieves the files of the right task`() {
        // given
        val givenDeal = DealCreator.createDealByRest()
        val givenFile = fileInStringJsonFormat()
        val givenTaskToUpdate = givenDeal.findTaskByTemplateKey("zoning_report")
        val updatedTask = uploadTaskToFile(taskId = givenTaskToUpdate.id, attachedForm = givenFile)
        val zoningPrompt = "this is the prompt for the zoning report"

        val zoningReport = givenReport(dealId = givenDeal.id, reportType = "ZONING")
        savePromptForReport(report = zoningReport, prompt = zoningPrompt)

        // when
        val result = Unirest.get("$localUrl/deal/${givenDeal.id}/reports/${zoningReport.id}/files")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        assertThat(result.status, equalTo(HttpStatus.OK))
        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<ReportFileResponse>>() {}
        )

        assertThat(response, hasSize(1))
        assertThat(response[0].name, equalTo(updatedTask.files[0].name))
        assertThat(response[0].status, equalTo(FileTokenStatusResponse.NOT_INITIALIZED))
        assertThat(response[0].token, equalTo(null))
        assertThat(response[0].prompt, equalTo(zoningPrompt))
    }

    @Test
    fun `it retrieves the token as expected`() {
        // given
        val givenDeal = DealCreator.createDealByRest()
        val givenFile = fileInStringJsonFormat()
        val givenToken = anyString()
        val givenTaskToUpdate = givenDeal.findTaskByTemplateKey("zoning_report")
        val updatedTask = uploadTaskToFile(taskId = givenTaskToUpdate.id, attachedForm = givenFile)
        val zoningPrompt = "this is the prompt for the zoning report"

        val zoningReport = givenReport(dealId = givenDeal.id, reportType = "ZONING")
        savePromptForReport(report = zoningReport, prompt = zoningPrompt)

        Context.injector.getInstance(FileTokenRepository::class.java)
            .save(
                FileToken(
                    kFileId = updatedTask.files[0].uid,
                    token = givenToken,
                    status = FileTokenStatus.READY,
                    fileType = TokenFileType.TASK,
                    dealId = givenDeal.id
                )
            )

        // when
        val response = HttpClient.get(
            url = "$localUrl/deal/${givenDeal.id}/reports/${zoningReport.id}/files",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<ReportFileResponse>>() {}) }
        )

        assertThat(response, hasSize(1))
        assertThat(response[0].name, equalTo(updatedTask.files[0].name))
        assertThat(response[0].status, equalTo(FileTokenStatusResponse.READY))
        assertThat(response[0].token, equalTo(givenToken))
        assertThat(response[0].source, equalTo(ReportFileSourceResponse.TASK))
        assertThat(response[0].prompt, equalTo(zoningPrompt))
    }

    private fun givenReport(dealId: Long, reportType: String): ReportResponse {
        val input = ReportRequestBuilder().apply { this.type = reportType }.build()
        val result = Unirest.post("$localUrl/deal/$dealId/reports")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(input))
            .asJson()
        return JsonMapper.decode(result.body.toPrettyString(), ReportResponse::class.java)
    }

    private fun uploadTaskToFile(taskId: Long, attachedForm: String) =
        HttpClient.patch(
            url = "$localUrl/task/$taskId",
            body = """{"status": "to_do", "attachedForm": $attachedForm}""",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )

    private fun savePromptForReport(report: ReportResponse, prompt: String) =
        fileTypePromptDatabaseRepository.save(document = TokenFileType.REPORT.name, documentType = report.type, prompt = prompt)
}
