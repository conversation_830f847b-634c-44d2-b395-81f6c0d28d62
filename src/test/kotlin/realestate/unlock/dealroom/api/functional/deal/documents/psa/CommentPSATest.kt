package realestate.unlock.dealroom.api.functional.deal.documents.psa

import io.swagger.models.Method
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.document.DocumentInteractionRepository
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.creator.PSACreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.documents.psa.InteractionRequestFactory
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class CommentPSATest : BaseFunctionalTest() {

    private lateinit var documentRepository: DocumentRepository
    private lateinit var documentInteractionRepository: DocumentInteractionRepository

    @BeforeEach
    fun setUp() {
        documentRepository = Context.injector.getInstance(DocumentRepository::class.java)
        documentInteractionRepository = Context.injector.getInstance(DocumentInteractionRepository::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/documents/psa",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `can comment a psa`() {
        val deal = DealCreator.createDealByRest()
        PSACreator.createByRest(deal.id)

        val givenUrl = "$localUrl/deal/${deal.id}/documents/psa"

        val request = InteractionRequestFactory.comment()

        val response = Unirest.post(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(request))
            .asEmpty()

        assertEquals(201, response.status)
        val psa = documentRepository.findByDealIdAndDocumentType(deal.id, DocumentType.PSA)!!
        val interactions = documentInteractionRepository.findByRoundIdAndDocumentId(documentId = psa.id, roundId = psa.currentRoundId!!)
        val commentInteraction = interactions.first { it.type == Interaction.Type.COMMENT }
        assertEquals(request.comment, commentInteraction.comment)
    }
}
