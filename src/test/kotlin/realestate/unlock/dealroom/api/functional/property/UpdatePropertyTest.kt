package realestate.unlock.dealroom.api.functional.property

import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.property.UnitsMixDataRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.property.put.request.MultifamilyDataRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.property.put.request.UpdatePropertyRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal

class UpdatePropertyTest : BaseFunctionalTest() {

    private val propertyToUpdateUrl = "$localUrl/properties"

    private lateinit var propertyRepository: PropertyRepository

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        propertyRepository = Context.injector.getInstance(PropertyRepository::class.java)
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/property",
        method = Method.PUT.name,
        permissions = listOf()
    )

    @Test
    fun `medical property should correctly update all it's fields`() {
        // given
        val propertyId = DealCreator.createDealByRest().propertyId
        val propertyToUpdate = propertyRepository.findById(propertyId)

        val body = UpdatePropertyRequest(
            yearBuilt = 2015,
            squareFootage = BigDecimal(2000),
            mainPhotoId = anyString(),
            interiorPhotoId = anyString(),
            keywayId = "newKeywayId",
            name = anyString()
        )

        // when
        val response = Unirest.put(propertyToUpdateUrl.plus("/${propertyToUpdate.id}"))
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(body))
            .asString()

        // then
        assertThat(response.status, equalTo(202))

        val updatedProperty = JsonMapper.decode(response.body, Property::class.java)

        assertThat(updatedProperty.id, equalTo(propertyId))
        assertThat(updatedProperty.address.street, equalTo(propertyToUpdate.address.street))
        assertThat(updatedProperty.address.apartment, equalTo(propertyToUpdate.address.apartment))
        assertThat(updatedProperty.address.city, equalTo(propertyToUpdate.address.city))
        assertThat(updatedProperty.address.state, equalTo(propertyToUpdate.address.state))
        assertThat(updatedProperty.address.zip, equalTo(propertyToUpdate.address.zip))
        assertThat(updatedProperty.yearBuilt, equalTo(body.yearBuilt))
        assertThat(updatedProperty.mainPhotoId, equalTo(body.mainPhotoId))
        assertThat(updatedProperty.interiorPhotoId, equalTo(body.interiorPhotoId))
        assertThat(updatedProperty.squareFootage, equalTo(body.squareFootage))
        assertThat(updatedProperty.keywayId, equalTo(body.keywayId))
        assertThat(updatedProperty.name, equalTo(body.name))
    }

    @Test
    fun `multifamily property should correctly update all it's fields`() {
        // given
        val propertyId = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY }
        ).propertyId
        val propertyToUpdate = propertyRepository.findById(propertyId)

        val body = UpdatePropertyRequest(
            yearBuilt = 2015,
            squareFootage = BigDecimal(2000),
            askingPrice = BigDecimal(30001),
            mainPhotoId = anyString(),
            interiorPhotoId = anyString(),
            keywayId = "newKeywayId",
            multifamilyData = MultifamilyDataRequest(
                brokerFirm = anyString(),
                units = 10,
                averageSquareFootage = BigDecimal.TEN,
                occupancy = BigDecimal.ONE,
                parkingSpots = 8,
                parkingRatio = BigDecimal.ONE,
                owner = anyString(),
                propertyManager = anyString(),
                unitsMixDataRequests = listOf(
                    UnitsMixDataRequest(
                        bedrooms = 1,
                        quantity = 3,
                        squareFootage = BigDecimal.TEN,
                        studio = false,
                        rent = BigDecimal.valueOf(321),
                        renoRent = BigDecimal.valueOf(551)
                    )
                )
            ),
            name = anyString()
        )

        // when
        val response = Unirest.put(propertyToUpdateUrl.plus("/${propertyToUpdate.id}"))
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(body))
            .asString()

        // then
        assertThat(response.status, equalTo(202))

        val updatedProperty = JsonMapper.decode(response.body, Property::class.java)

        assertThat(updatedProperty.id, equalTo(propertyId))
        assertThat(updatedProperty.address.street, equalTo(propertyToUpdate.address.street))
        assertThat(updatedProperty.address.apartment, equalTo(propertyToUpdate.address.apartment))
        assertThat(updatedProperty.address.city, equalTo(propertyToUpdate.address.city))
        assertThat(updatedProperty.address.state, equalTo(propertyToUpdate.address.state))
        assertThat(updatedProperty.address.zip, equalTo(propertyToUpdate.address.zip))
        assertThat(updatedProperty.yearBuilt, equalTo(body.yearBuilt))
        assertThat(updatedProperty.mainPhotoId, equalTo(body.mainPhotoId))
        assertThat(updatedProperty.interiorPhotoId, equalTo(body.interiorPhotoId))
        assertThat(updatedProperty.squareFootage, equalTo(body.squareFootage))
        assertThat(updatedProperty.askingPrice, equalTo(body.askingPrice))
        assertThat(updatedProperty.keywayId, equalTo(body.keywayId))
        assertThat(updatedProperty.name, equalTo(body.name))

        assertThat(updatedProperty.multifamilyData?.units, equalTo(body.multifamilyData?.units))
        assertThat(updatedProperty.multifamilyData?.parkingSpots, equalTo(body.multifamilyData?.parkingSpots))
        assertThat(updatedProperty.multifamilyData?.propertyManager, equalTo(body.multifamilyData?.propertyManager))
        assertThat(updatedProperty.multifamilyData?.parkingRatio, equalTo(body.multifamilyData?.parkingRatio))
        assertThat(updatedProperty.multifamilyData?.occupancy, equalTo(body.multifamilyData?.occupancy))
        assertThat(updatedProperty.multifamilyData?.brokerFirm, equalTo(body.multifamilyData?.brokerFirm))
        assertThat(updatedProperty.multifamilyData?.averageSquareFootage, equalTo(body.multifamilyData?.averageSquareFootage))
        assertThat(updatedProperty.multifamilyData?.owner, equalTo(body.multifamilyData?.owner))
    }
}
