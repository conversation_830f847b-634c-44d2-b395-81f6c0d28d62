package realestate.unlock.dealroom.api.functional.deal.file

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.CreateLinkedFileInput
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileData
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.DirectoryFile
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.DirectoryFolder
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.functional.deal.report.ReportRequestBuilder
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DealFilesAndFoldersConfig
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealDirectoryLinkedFilesTest : BaseDealDirectoryTest() {

    private lateinit var dealFilesAndFoldersConfig: DealFilesAndFoldersConfig

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        dealFilesAndFoldersConfig = Context.injector.getInstance(DealFilesAndFoldersConfig::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/directory",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `should return files associated with task`() {
        // Given
        val fileName = "t1.json"
        val deal = createDealDirectory()
        val files = getDirectory(deal.id, "")
        val linkedTask = deal.findOneTask()

        val linkedFile = createLinkedFile(
            dealId = deal.id,
            input = CreateLinkedFileInput(
                fileId = (files.first { it.name == fileName } as DirectoryFile).uid,
                entityId = linkedTask.id.toString(),
                entityType = LinkedFileEntityType.TASK,
                filePath = null
            )
        )

        // When
        val result = getDirectory(deal.id, "")

        // Then
        val t1 = result.first { it.name == fileName } as DirectoryFile

        assertEquals(t1.uid, linkedFile.fileId)
        assertEquals(t1.linkedData?.id, linkedFile.id)
        assertEquals(t1.linkedData?.entityId, linkedTask.id.toString())
        assertEquals(t1.linkedData?.entityName, linkedTask.title)
    }

    @Test
    fun `should return files associated with report`() {
        // Given
        val fileName = "r1.json"
        val deal = createDealDirectory()
        val files = getDirectory(deal.id, "")
        val report = HttpClient.post(
            url = "$localUrl/deal/${deal.id}/reports",
            body = JsonMapper.encode(ReportRequestBuilder().apply { reportName = "ALTO REPORTE" }.build()),
            expectedStatus = HttpStatus.CREATED,
            responseHandler = { JsonMapper.decode(it, ReportResponse::class.java) }
        )

        val linkedFile = createLinkedFile(
            dealId = deal.id,
            input = CreateLinkedFileInput(
                fileId = (files.first { it.name == fileName } as DirectoryFile).uid,
                entityId = report.id.toString(),
                entityType = LinkedFileEntityType.REPORT,
                filePath = null
            )
        )

        // When
        val result = getDirectory(deal.id, "")

        // Then
        val t1 = result.first { it.name == fileName } as DirectoryFile

        assertEquals(t1.uid, linkedFile.fileId)
        assertEquals(t1.linkedData?.id, linkedFile.id)
        assertEquals(t1.linkedData?.entityId, report.id.toString())
        assertEquals(t1.linkedData?.entityName, "Seismic Report")
    }

    @Test
    fun `should return folder count of associated files`() {
        val deal = createDealDirectory()
        val filesTask = getDirectory(deal.id, "Task")
        val filesSubTask = getDirectory(deal.id, "Task/SubTask")
        val linkedTask = deal.findOneTask()
        filesTask.filterIsInstance<DirectoryFile>().forEach {
            createLinkedFile(
                dealId = deal.id,
                input = CreateLinkedFileInput(
                    fileId = it.uid,
                    entityId = linkedTask.id.toString(),
                    entityType = LinkedFileEntityType.TASK,
                    filePath = null
                )
            )
        }
        filesSubTask.filterIsInstance<DirectoryFile>().forEach {
            createLinkedFile(
                dealId = deal.id,
                input = CreateLinkedFileInput(
                    fileId = it.uid,
                    entityId = linkedTask.id.toString(),
                    entityType = LinkedFileEntityType.TASK,
                    filePath = null
                )
            )
        }
        createFile(deal.id, "Task", "t3.json")
        createFile(deal.id, "Task", "t4.json")

        // When
        val result = getDirectory(deal.id, "").filterIsInstance<DirectoryFolder>()

        val taskFolder = result.first { it.name == "Task" }

        assertEquals(6, taskFolder.childCount)
        assertEquals(4, taskFolder.childLinkedFilesCount)
    }

    private fun createLinkedFile(
        dealId: Long,
        input: CreateLinkedFileInput
    ) = HttpClient.post(
        url = "$localUrl/deal/$dealId/linked-files",
        body = JsonMapper.encode(input),
        expectedStatus = HttpStatus.CREATED,
        responseHandler = { JsonMapper.decode(it, LinkedFileData::class.java) }
    )

    private fun createDealDirectory(): CompleteDeal = DealCreator.createDealByRest().also {
        createFolder(it.id, "", "Task")
        createFile(it.id, "Task", "t1.json")
        createFile(it.id, "Task", "t2.json")
        createFolder(it.id, "Task", "SubTask")
        createFile(it.id, "Task/SubTask", "t1.json")
        createFile(it.id, "Task/SubTask", "t2.json")
        createFolder(it.id, "", "Reports")
        createFile(it.id, "Reports", "r1.json")
        createFile(it.id, "Reports", "r2.json")
        createFile(it.id, "", "t1.json")
        createFile(it.id, "", "t2.json")
        createFile(it.id, "", "r1.json")
        createFile(it.id, "", "r2.json")
    }
}
