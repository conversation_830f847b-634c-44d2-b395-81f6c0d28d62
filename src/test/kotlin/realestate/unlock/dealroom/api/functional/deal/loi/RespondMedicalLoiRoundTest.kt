package realestate.unlock.dealroom.api.functional.deal.loi

import io.mockk.every
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.sign.BeginSignFlowOutput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalRound
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.LetterOfIntentFileRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.RespondLetterOfIntentRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.MemberSignBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class RespondMedicalLoiRoundTest : BaseFunctionalTest() {

    private val sellerToken = anyString()

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
        AuthMock.createMemberWithUser(memberType = "seller", token = sellerToken, email = "<EMAIL>", uid = sellerToken)
        Context.injector.getInstance(SignGateway::class.java).let { signGateway ->
            every { signGateway.beginFlow(any()) } returns BeginSignFlowOutput("signingId")
        }
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/loi/round/123/respond",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - seller non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/123/respond",
            memberType = "seller",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - seller non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/123/respond",
            memberType = "seller",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any buyer should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/123/respond",
            memberType = "buyer",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `Can reject an LOI with status pending without sellerSigner information`() {
        // Given a deal with an LOI created
        val deal = DealCreator.createDealByRest()
        val loiCreationResponse = givenLoiCreation(deal.id)

        val rejectLoiRequest = RespondLetterOfIntentRequest(
            accepted = false,
            comments = "good offer",
            files = listOf(LetterOfIntentFileRequest(uid = anyString(), name = "file-name")),
            sellerSignerRequest = null
        )

        // When we reject the LOI
        val rejectResult = Unirest.post("$localUrl/deal/${deal.id}/loi/round/${loiCreationResponse.id}/respond")
            .headers(AuthMock.getAuthHeader(sellerToken))
            .body(JsonMapper.encode(rejectLoiRequest))
            .asJson()

        // Then
        assertThat(rejectResult.status, equalTo(200))
        val rejectionResponse = JsonMapper.decode(rejectResult.body.toPrettyString(), LoiMedicalRound::class.java)
        assertThat(rejectionResponse.status, equalTo(LetterOfIntentRoundStatus.REJECTED))
        assertThat(rejectionResponse.offerResponseComments, equalTo(rejectLoiRequest.comments))
    }

    @Test
    fun `Can accept an LOI with status pending`() {
        // Given a deal with an LOI created
        val deal = DealCreator.createDealByRest()
        val loiCreationResponse = givenLoiCreation(deal.id)

        val acceptLoiRequest = RespondLetterOfIntentRequest(
            accepted = true,
            comments = "good offer",
            files = listOf(),
            sellerSignerRequest = MemberSignBuilder().getSellerSignRequest()
        )

        // When we accept the LOI
        val acceptResult = Unirest.post("$localUrl/deal/${deal.id}/loi/round/${loiCreationResponse.id}/respond")
            .headers(AuthMock.getAuthHeader(sellerToken))
            .body(JsonMapper.encode(acceptLoiRequest))
            .asJson()

        // then
        assertThat(acceptResult.status, equalTo(200))
        val accResponse = JsonMapper.decode(acceptResult.body.toPrettyString(), LoiMedicalRound::class.java)
        assertThat(accResponse.status, equalTo(LetterOfIntentRoundStatus.ACCEPTED))
        assertThat(accResponse.offerResponseComments, equalTo(acceptLoiRequest.comments))
    }

    @Test
    fun `Cannot accept an LOI which is already accepted`() {
        // Given a deal with an LOI created and accepted
        val deal = DealCreator.createDealByRest()
        val loiCreationResponse = givenLoiCreation(deal.id)

        val acceptLoiRequest = RespondLetterOfIntentRequest(
            accepted = true,
            comments = "good offer",
            files = listOf(),
            sellerSignerRequest = MemberSignBuilder().getSellerSignRequest()
        )

        Unirest.post("$localUrl/deal/${deal.id}/loi/round/${loiCreationResponse.id}/respond")
            .headers(AuthMock.getAuthHeader(sellerToken))
            .body(JsonMapper.encode(acceptLoiRequest))
            .asJson()

        // When we try to accept the same LOI again
        val acceptResult = Unirest.post("$localUrl/deal/${deal.id}/loi/round/${loiCreationResponse.id}/respond")
            .headers(AuthMock.getAuthHeader(sellerToken))
            .body(JsonMapper.encode(acceptLoiRequest))
            .asJson()

        // then
        assertThat(acceptResult.status, equalTo(400))
    }

    @Test
    fun `Cannot accept an loi without seller signer information`() {
        val deal = DealCreator.createDealByRest()
        val loiCreationResponse = givenLoiCreation(deal.id)

        val acceptLoiRequest = RespondLetterOfIntentRequest(
            accepted = true,
            comments = "good offer",
            files = listOf(),
            sellerSignerRequest = null
        )

        // When we accept the LOI
        val acceptResult = Unirest.post("$localUrl/deal/${deal.id}/loi/round/${loiCreationResponse.id}/respond")
            .headers(AuthMock.getAuthHeader(sellerToken))
            .body(JsonMapper.encode(acceptLoiRequest))
            .asJson()

        // then
        assertThat(acceptResult.status, equalTo(400))
        val expectedError = JsonMapper.decode(acceptResult.body.toString(), BadRequestException::class.java)
        assertThat(expectedError.message, equalTo("Cannot accept an LOI without seller signer information"))
    }

    private fun givenLoiCreation(dealId: Long): LoiMedicalRound {
        val createLoiUrl = "$localUrl/deal/$dealId/loi/round"
        val loiCreationRequest = givenLoiCreationRequest()

        val loiCreationResult = Unirest.post(createLoiUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(loiCreationRequest))
            .asJson()

        return JsonMapper.decode(loiCreationResult.body.toPrettyString(), LoiMedicalRound::class.java)
    }

    private fun givenLoiCreationRequest() = PostMedicalLoiRequestBuilder().build()
}
