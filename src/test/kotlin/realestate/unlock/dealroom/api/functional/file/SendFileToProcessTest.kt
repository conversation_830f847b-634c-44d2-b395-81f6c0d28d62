package realestate.unlock.dealroom.api.functional.file

import io.swagger.models.Method
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.gpt.SendFileToProcessBody
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class SendFileToProcessTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/file/456/process",
        method = Method.POST.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `it can't send a file to process without fileType`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenFileId = anyString()

        HttpClient.post(
            url = "$localUrl/deal/${givenDeal.id}/file/$givenFileId/process",
            body = JsonMapper.encode(Unit),
            responseHandler = { it },
            expectedStatus = 400
        )
    }

    @Test
    fun `it can send a file to process with fileType`() {
        val givenDeal = DealCreator.createDealByRest()
        val givenFileId = anyString()

        HttpClient.post(
            url = "$localUrl/deal/${givenDeal.id}/file/$givenFileId/process",
            body = JsonMapper.encode(SendFileToProcessBody(fileType = TokenFileType.TASK)),
            responseHandler = { it },
            expectedStatus = 201
        )
    }
}
