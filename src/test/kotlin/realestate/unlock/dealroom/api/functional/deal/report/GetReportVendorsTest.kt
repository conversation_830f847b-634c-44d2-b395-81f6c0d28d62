package realestate.unlock.dealroom.api.functional.deal.report

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportVendor
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetReportVendorsTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/report-vendors?reportType=SEISMIC_REPORT",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/report-vendors?reportType=SEISMIC_REPORT",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `It retrieves the vendors by report type`() {
        // given
        val givenReportType = "SEISMIC_REPORT"

        val givenUrl = "$localUrl/report-vendors?reportType=$givenReportType"

        // when
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<ReportVendor>>() {}
        )

        assertThat(response, hasSize(3))
        assertTrue(response.any { it.key == "CRETELLIGENT" })
        assertTrue(response.any { it.key == "EBI" })
        assertTrue(response.any { it.key == "OTHER" })
    }
}
