package realestate.unlock.dealroom.api.functional.task.file

import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.not
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.UploadFileToStagingResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.creator.FileCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub

class UploadTaskFileTest : BaseFunctionalTest() {

    private lateinit var taskFileGatewayStub: FileGatewayStub

    @BeforeEach
    fun setUp() {
        taskFileGatewayStub = Context.injector.getInstance(FileGateway::class.java) as FileGatewayStub
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/123/file",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/task/$givenTaskId/file",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenTaskId = givenDeal.findOneTask().id

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/task/$givenTaskId/file",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.UPDATE_OWN_DEALS)

        )
        val dealResponse = DealCreator.createDealByRest(seller = seller)
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")

        // When
        val result = Unirest.post(givenUploadFileUrl(givenTaskToUpdate.id))
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, not(equalTo(403)))
    }

    @Test
    fun `can upload a file`() {
        // Given
        val file = FileCreator.create()
        val dealResponse = DealCreator.createDealByRest()
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")
        val givenKFileId = givenSuccessfullyGatewayResponse()

        // When
        val result = Unirest.post(givenUploadFileUrl(givenTaskToUpdate.id))
            .headers(AuthMock.getAuthHeader())
            .field("file", file)
            .asJson()

        // Then
        assertThat(result.status, equalTo(201))
        val response = JsonMapper.decode(result.body.toPrettyString(), UploadFileToStagingResponse::class.java)
        assertThat(response.uid, equalTo(givenKFileId))
    }

    @Test
    fun `cannot upload more than a file`() {
        // Given
        val file = FileCreator.create()
        val dealResponse = DealCreator.createDealByRest()
        val givenTaskToUpdate = dealResponse.findTaskByTemplateKey("financial_statements")

        // When
        val result = Unirest.post(givenUploadFileUrl(givenTaskToUpdate.id))
            .headers(AuthMock.getAuthHeader())
            .field("file1", file)
            .field("file2", file)
            .asJson()

        // Then
        assertThat(result.status, equalTo(400))
    }

    private fun givenSuccessfullyGatewayResponse() = anyString().apply {
        taskFileGatewayStub.nextUploadFileUid(this)
    }

    private fun givenUploadFileUrl(taskId: Long) = "$localUrl/task/$taskId/file"
}
