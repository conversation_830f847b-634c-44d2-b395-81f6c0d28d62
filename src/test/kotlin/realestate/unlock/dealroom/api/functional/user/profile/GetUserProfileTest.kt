package realestate.unlock.dealroom.api.functional.user.profile

import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.entrypoint.rest.contract.user.profile.get.GetProfileResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetUserProfileTest : BaseFunctionalTest() {

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/user/profile",
        method = Method.GET.name,
        permissions = listOf()
    )

    @Test
    fun `should get user profile successfully`() {
        // Given
        val user = AuthMock.createMemberWithUser()
        val givenUrl = "$localUrl/user/profile"
        val expectedDealTeam = MemberDealTeam.BUYER

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            GetProfileResponse::class.java
        )

        assertThat(response.firstName, equalTo(user.firstName))
        assertThat(response.lastName, equalTo(user.lastName))
        assertThat(response.email, equalTo(user.email))
        assertThat(response.phoneNumber, equalTo(user.phoneNumber))
        assertThat(response.dealTeam, equalTo(expectedDealTeam))
        assertThat(response.memberType, equalTo(user.type.key))
    }

    @Test
    fun `should fail if not exist Authenticated user`() {
        // Given
        val givenUrl = "$localUrl/user/profile"

        // When
        val result = Unirest.get(givenUrl)
            .asJson()

        // Then
        assertThat(result.status, equalTo(HttpStatus.UNAUTHORIZED))
    }
}
