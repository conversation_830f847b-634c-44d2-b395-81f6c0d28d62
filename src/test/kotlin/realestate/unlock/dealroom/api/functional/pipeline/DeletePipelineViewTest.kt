package realestate.unlock.dealroom.api.functional.pipeline

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.pipeline.PipelineView
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.pipeline.PipelineViewInput
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class DeletePipelineViewTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/pipeline-views/123",
        method = Method.DELETE.name,
        permissions = listOf()
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.DELETE.name,
            url = "$localUrl/pipeline-views/123",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `it can delete a pipeline view`() {
        // Given
        val created = createPipelineView(
            PipelineViewInput(
                name = "Pipeline View 1",
                propertyType = PropertyType.MULTIFAMILY,
                data = mapOf("some" to "data")
            )
        )
        checkPipelineView(created.id, HttpStatus.OK)

        // When
        val response = HttpClient.delete(
            url = "$localUrl/pipeline-views/${created.id}",
            expectedStatus = HttpStatus.NO_CONTENT,
            responseHandler = { it }
        )

        // Then
        checkPipelineView(created.id, HttpStatus.NOT_FOUND)
    }

    private fun createPipelineView(input: PipelineViewInput) = HttpClient.post(
        url = "$localUrl/pipeline-views",
        body = JsonMapper.encode(input),
        expectedStatus = HttpStatus.CREATED,
        responseHandler = { JsonMapper.decode(it, PipelineView::class.java) }
    )

    private fun checkPipelineView(id: Long, expectedStatus: Int) = HttpClient.get(
        url = "$localUrl/pipeline-views/$id",
        expectedStatus = expectedStatus,
        responseHandler = { it }
    )
}
