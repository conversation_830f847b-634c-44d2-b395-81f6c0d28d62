package realestate.unlock.dealroom.api.functional.task

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.schema.DealSchemaData
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.time.LocalDate

class UpdateDealImpactOnTaskTest : BaseFunctionalTest() {

    private val sqlClient = Context.injector.getInstance(SqlClient::class.java)
    private val taskRepository = Context.injector.getInstance(TaskRepository::class.java)

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/333",
        method = Method.PATCH.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Task should be correctly updated and deal date also`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenTask = dealResponse.findOneTask()
        sqlClient.update(
            query = "UPDATE task SET due_date_external_data_definition = 'deal.loiExecutedDate' WHERE id = ${givenTask.id}",
            params = emptyList()
        )
        val dueDate = LocalDate.now()
        // When
        HttpClient.patch(
            url = "$localUrl/deal/${dealResponse.id}",
            body = """{"loi_executed_date": "$dueDate"}""",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )
        assertEquals(getTask(givenTask.id).dueDate, dueDate)
    }

    @Test
    fun `can update tour date and call for offers dueDate and impact on task`() {
        val dealResponse = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY }
        )

        val tourDateTask = dealResponse.findTaskByTemplateKey("multifamily_property_tour_checklist")
        val callForOfferTask = dealResponse.findTaskByTemplateKey("multifamily_loi_call_for_offers")
        val dueDate = LocalDate.now().plusDays(10)

        HttpClient.put(
            url = "$localUrl/deal/${dealResponse.id}/schema-data",
            body = JsonMapper.encode(
                DealSchemaData(
                    mapOf(
                        "callForOffers" to dueDate,
                        "tourDate" to dueDate
                    )
                )
            ),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        assertEquals(getTask(tourDateTask.id).dueDate, dueDate)
        assertEquals(getTask(callForOfferTask.id).dueDate, dueDate)

        HttpClient.put(
            url = "$localUrl/deal/${dealResponse.id}/schema-data",
            body = JsonMapper.encode(
                mapOf(
                    "data" to mapOf<String, Any?>(
                        "callForOffers" to null,
                        "tourDate" to null
                    )
                )
            ),
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        assertEquals(getTask(tourDateTask.id).dueDate, null)
        assertEquals(getTask(callForOfferTask.id).dueDate, null)
    }

    private fun getTask(taskId: Long): Task = taskRepository.findById(taskId)
}
