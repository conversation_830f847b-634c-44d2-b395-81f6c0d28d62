package realestate.unlock.dealroom.api.functional.deal.loi

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.core.IsEqual
import org.hamcrest.core.IsNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalRound
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalTemplate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetLoiTemplateTest : BaseFunctionalTest() {
    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/loi/round/templates",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/templates",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/templates",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/templates",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `can get default valued template if there is not a previous loi offer`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val property = getDealProperty(deal.propertyId)

        val result = HttpClient.get(
            url = "$localUrl/deal/${deal.id}/loi/round/templates",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, LoiMedicalTemplate::class.java) }
        )
        // Then
        assertDefaultLoiTemplate(result, deal, property)
    }

    @Test
    fun `can get the pre-filled template with existing loi data`() {
        // Given
        val deal = DealCreator.createDealByRest()
        val property = getDealProperty(deal.propertyId)
        val loi = givenLoiFor("$localUrl/deal/${deal.id}/loi/round")

        // When
        val result = HttpClient.get(
            url = "$localUrl/deal/${deal.id}/loi/round/templates",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, LoiMedicalTemplate::class.java) }
        )

        // Then
        assertLoiTemplate(result, loi, property)
    }

    @Test
    fun `cannot be retrieved by seller`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val givenSeller = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken
        )

        val deal = DealCreator.createDealByRest(seller = givenSeller)
        val loiTemplateUrl = "$localUrl/deal/${deal.id}/loi/round/templates"

        // When
        val result = Unirest.get(loiTemplateUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asEmpty()

        // Then
        assertThat(result.status, equalTo(403))
    }

    @Test
    fun `can be retrieved by buyer`() {
        // Given
        val givenToken = "not-admin-with-deal-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val givenBuyer = AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "buyer",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken
        )

        val deal = DealCreator.createDealByRest(buyer = givenBuyer)
        val loiTemplateUrl = "$localUrl/deal/${deal.id}/loi/round/templates"

        // When
        val result = Unirest.get(loiTemplateUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asEmpty()

        // Then
        assertThat(result.status, equalTo(200))
    }

    private fun givenLoiFor(
        createLoiUrl: String
    ): LoiMedicalRound {
        val response = Unirest.post(createLoiUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(givenLoiCreationRequest()))
            .asJson()

        return response.let { JsonMapper.decode(it.body.toPrettyString(), LoiMedicalRound::class.java) }
    }

    private fun givenLoiCreationRequest() = PostMedicalLoiRequestBuilder().build()

    private fun assertLoiTemplate(
        response: LoiMedicalTemplate,
        loi: LoiMedicalRound,
        property: Property
    ) {
        assertThat(response.tenantName, IsEqual(loi.tenantName))
        assertThat(response.propertySquareFootage, IsEqual(loi.propertySquareFootage))
        assertThat(response.propertyAddress, IsEqual(property.address.fullAddress()))
        assertThat(response.propertyFullAddress.street, IsEqual(property.address.street))
        assertThat(response.propertyFullAddress.apartment, IsEqual(property.address.apartment))
        assertThat(response.propertyFullAddress.city, IsEqual(property.address.city))
        assertThat(response.propertyFullAddress.state, IsEqual(property.address.state))
        assertThat(response.propertyFullAddress.zip, IsEqual(property.address.zip))
        assertThat(response.vertical, IsEqual(loi.vertical))
        assertThat(response.broker, IsEqual(loi.broker))
        assertThat(response.brokerageCompanyName, IsEqual(loi.brokerageCompanyName))
        assertThat(response.offerPrice, IsEqual(loi.offerPrice))
        assertThat(response.offerLeaseRent, IsEqual(loi.offerLeaseRent))
        assertThat(response.offerLeaseType, IsEqual(loi.offerLeaseType))
        assertThat(response.offerLeaseCondition, IsEqual(loi.offerLeaseCondition))
        assertThat(response.offerLeaseRentIncrease, IsEqual(loi.offerLeaseRentIncrease))
        assertThat(response.offerLeaseIncreaseEveryYear, IsEqual(loi.offerLeaseIncreaseEveryYear))
        assertThat(response.offerLeaseLength, IsEqual(loi.offerLeaseLength))
        assertThat(response.offerLeaseExpirationYear, IsEqual(loi.offerLeaseExpirationYear))
        assertThat(response.offerLeaseNumberOfOptions, IsEqual(loi.offerLeaseNumberOfOptions))
        assertThat(response.offerLeaseOptionLengths, IsEqual(loi.offerLeaseOptionLengths))
        assertThat(response.offerClosingPeriod, IsEqual(loi.offerClosingPeriod))
        assertThat(response.offerClosingPeriodExtension, IsEqual(loi.offerClosingPeriodExtension))
        assertThat(response.offerClosingExtensionDeposit, IsEqual(loi.offerClosingExtensionDeposit))
        assertThat(response.offerContractTermination, IsEqual(loi.offerContractTermination))
        assertThat(response.offerEarnestMoneyDeposit, IsEqual(loi.offerEarnestMoneyDeposit))
        assertThat(response.offerDueDiligenceNumber, IsEqual(loi.offerDueDiligenceNumber))
        assertThat(response.offerRentStepType, IsEqual(loi.offerRentStepType))
        assertThat(response.offerRentCpi, IsEqual(loi.offerRentCpi))
        assertThat(response.offerCustomSections!!.sections.first().title, IsEqual(loi.offerCustomSections!!.sections.first().title))
        assertThat(response.offerCustomSections!!.sections.first().content, IsEqual(loi.offerCustomSections!!.sections.first().content))
        assertThat(response.offerClosingCost, IsEqual(loi.offerClosingCost))
    }

    private fun assertDefaultLoiTemplate(response: LoiMedicalTemplate, deal: CompleteDeal, property: Property) {
        assertThat(response.tenantName, IsEqual(deal.tenantName))
        assertThat(response.propertySquareFootage, IsEqual(property.squareFootage))
        assertThat(response.propertyAddress, IsEqual(property.address.fullAddress()))
        assertThat(response.propertyFullAddress.city, IsEqual(property.address.city))
        assertThat(response.propertyFullAddress.state, IsEqual(property.address.state))
        assertThat(response.propertyFullAddress.street, IsEqual(property.address.street))
        assertThat(response.propertyFullAddress.zip, IsEqual(property.address.zip))
        assertThat(response.vertical, IsEqual(deal.vertical))
        assertThat(response.broker, IsNull())
        assertThat(response.brokerageCompanyName, IsNull())
        assertThat(response.offerPrice, IsEqual(deal.offerPrice ?: property.askingPrice))
        assertThat(response.offerLeaseRent, IsEqual(deal.lease.rent))
        assertThat(response.offerLeaseType, IsEqual(deal.lease.type))
        assertThat(response.offerLeaseCondition, IsEqual(deal.type?.getLeaseCondition()))
        assertThat(response.offerLeaseRentIncrease, IsEqual(deal.lease.rentIncrease))
        assertThat(response.offerLeaseIncreaseEveryYear, IsEqual(deal.lease.increaseEveryYear))
        assertThat(response.offerLeaseLength, IsEqual(deal.lease.length))
        assertThat(response.offerLeaseExpirationYear, IsEqual(deal.lease.expirationYear))
        assertThat(response.offerLeaseNumberOfOptions, IsEqual(deal.lease.numberOfOptions))
        assertThat(response.offerLeaseOptionLengths, IsEqual(deal.lease.optionLengths))
        assertThat(response.offerClosingPeriod, IsEqual(deal.lease.closingPeriod))
        assertThat(response.offerClosingPeriodExtension, IsEqual(deal.lease.closingPeriodExtension))
        assertThat(response.offerClosingExtensionDeposit, IsEqual(deal.extensionDeposit))
        assertThat(response.offerContractTermination, IsNull())
        assertThat(response.offerEarnestMoneyDeposit, IsEqual(deal.earnestMoneyDeposit))
        assertThat(response.offerDueDiligenceNumber, IsEqual(deal.lease.dueDiligenceNumber))
        assertThat(response.offerRentStepType, IsEqual(deal.lease.rentStepType))
        assertThat(response.offerRentCpi, IsEqual(deal.lease.rentCpi))
        assertThat(response.vertical, IsEqual(deal.vertical))
        assertThat(response.offerCustomSections, IsNull())
        assertThat(response.offerClosingCost, IsNull())
    }

    private fun getDealProperty(propertyId: Long) = Unirest.get("$localUrl/properties/$propertyId")
        .headers(AuthMock.getAuthHeader())
        .asJson()
        .let { JsonMapper.decode(it.body.toPrettyString(), object : TypeReference<Property>() {}) }
}
