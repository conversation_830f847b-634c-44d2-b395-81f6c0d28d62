package realestate.unlock.dealroom.api.functional.keypilot

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.notNullValue
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.keypilot.DocumentQuestion
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDocumentSuggestedQuestionsTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/keypilot/suggested-questions?document=REPORT&type=APPRAISAL",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `it retrieves the suggested questions successfully`() {
        val response = HttpClient.get(
            url = "$localUrl/keypilot/suggested-questions?document=REPORT&type=APPRAISAL",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, object : TypeReference<List<DocumentQuestion>>() {}) }
        )

        assertThat(response, notNullValue())
        assertTrue(response.any { it.question == "How did the appraiser arrive at the estimated value?" })
    }
}
