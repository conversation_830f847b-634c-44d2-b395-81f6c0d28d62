package realestate.unlock.dealroom.api.functional.deal.file

import com.fasterxml.jackson.core.type.TypeReference
import io.mockk.mockk
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.deal.file.DealDirectoryFilter
import realestate.unlock.dealroom.api.core.entity.deal.file.DeleteDirectoriesInput
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.*
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import java.net.URLEncoder

abstract class BaseDealDirectoryTest : BaseFunctionalTest() {

    protected lateinit var fileGateway: FileGateway

    protected fun createFile(dealId: Long, path: String, name: String) = fileGateway.uploadFile(
        FileToUpload(
            content = mockk(),
            contentType = "application/pdf",
            path = FileUtils.buildDealRepositoryPath(dealId, path),
            filename = name
        )
    ).also {
        assert(getDirectory(dealId, path).any { it.name == name && it.type == DirectoryItemType.file })
    }

    protected fun deleteDirectories(dealId: Long, input: DeleteDirectoriesInput) = HttpClient.delete(
        url = "$localUrl/deal/$dealId/directory",
        body = JsonMapper.encode(input),
        expectedStatus = HttpStatus.NO_CONTENT,
        responseHandler = { it }
    )

    protected fun createFolder(dealId: Long, path: String, name: String) = HttpClient.post(
        url = "$localUrl/deal/$dealId/directory",
        body = JsonMapper.encode(
            CreateDirectoryItem(
                path = path,
                name = name,
                type = DirectoryItemType.folder
            )
        ),
        expectedStatus = HttpStatus.CREATED,
        responseHandler = { it }
    ).also {
        assert(getDirectory(dealId, path).any { it.name == name && it.type == DirectoryItemType.folder })
    }

    protected fun getDirectory(dealId: Long, path: String, filter: DealDirectoryFilter? = null): List<DirectoryItem> = HttpClient.get(
        url = (
            "$localUrl/deal/$dealId/directory" +
                "?path=${path.let { URLEncoder.encode(it, "UTF-8") }}" +
                "&filter=${filter?.let { URLEncoder.encode(JsonMapper.encode(it), "UTF-8") } ?: ""}"
            ),
        expectedStatus = HttpStatus.OK,
        responseHandler = {
            JsonMapper.decode(it, object : TypeReference<List<Map<String, Any>>>() {}).map { mapped ->
                when (mapped["type"]) {
                    DirectoryItemType.file -> JsonMapper.decode(JsonMapper.encode(mapped), DirectoryFile::class.java)
                    DirectoryItemType.folder -> JsonMapper.decode(JsonMapper.encode(mapped), DirectoryFolder::class.java)
                    else -> throw Exception("BAD TYPE")
                }
            }
        }
    )
}
