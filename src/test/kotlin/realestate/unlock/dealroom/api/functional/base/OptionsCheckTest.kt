package realestate.unlock.dealroom.api.functional.base

import kong.unirest.Unirest
import org.eclipse.jetty.http.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest

class OptionsCheckTest : BaseFunctionalTest() {

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig.dontTestPermissions()

    @Test
    fun `Options check test`() {
        // Given
        val givenUrl = "$localUrl/health"

        // When
        val result = Unirest.options(givenUrl).asString()

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK_200))
    }

    @Test
    fun `Options user check test`() {
        // Given
        val givenUrl = "$localUrl/user"

        // When
        val result = Unirest.options(givenUrl).asString()

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK_200))
    }

    @Test
    fun `Options user deals check test`() {
        // Given
        val givenUrl = "$localUrl/user/deals"

        // When
        val result = Unirest.options(givenUrl).asString()

        // Then
        assertThat(result.status, equalTo(HttpStatus.OK_200))
    }
}
