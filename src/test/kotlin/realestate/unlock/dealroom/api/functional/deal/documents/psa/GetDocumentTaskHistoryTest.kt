package realestate.unlock.dealroom.api.functional.deal.documents.psa

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.task.TaskStatusTransitions
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.task.TaskTransition
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.InteractionHistoryResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.InteractionHistoryType
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.InteractionRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.patch.request.ChangeTaskStatusRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.creator.PSACreator
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.documents.psa.InteractionRequestFactory
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDocumentTaskHistoryTest : BaseFunctionalTest() {

    private lateinit var taskTransition: TaskTransition
    private lateinit var member: Member

    @BeforeEach
    fun setUp() {
        member = AuthMock.createMemberWithUser()
        taskTransition = Context.injector.getInstance(TaskTransition::class.java)
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/documents/psa/task/history",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/task/history",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents/psa/task/history",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val givenDeal = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${givenDeal.id}/documents/psa/task/history"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, Matchers.not(Matchers.equalTo(403)))
    }

    @Test
    fun `It only retrieves the allowed interactions`() {
        val deal = DealCreator.createDealByRest()
        PSACreator.createByRest(deal.id)

        commentPsa(deal.id)
        legalReview(deal.id)
        businessReview(deal.id)
        sharePsa(deal.id)
        rejectPsaTask(deal)
        sharePsa(deal.id)
        acceptPsaTask(deal)

        // When
        val response = Unirest.get("$localUrl/deal/${deal.id}/documents/psa/task/history")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertEquals(200, response.status)

        val interactions = JsonMapper.decode(
            response.body.toPrettyString(),
            object : TypeReference<List<InteractionHistoryResponse>>() {}
        )

        assertThat(interactions, hasSize(4)) // it was shared twice, requested changes and approved once
        assertThat(interactions.filter { it.type == InteractionHistoryType.SHARE }, hasSize(2))
        assertTrue(interactions.any { it.type == InteractionHistoryType.REQUEST_CHANGES })
        assertTrue(interactions.any { it.type == InteractionHistoryType.APPROVE })
    }

    private fun commentPsa(dealId: Long) = interactWithPsa(dealId, InteractionRequestFactory.comment())
    private fun legalReview(dealId: Long) = interactWithPsa(dealId, InteractionRequestFactory.legalReview())
    private fun businessReview(dealId: Long) = interactWithPsa(dealId, InteractionRequestFactory.businessReview())
    private fun sharePsa(dealId: Long) = interactWithPsa(dealId, InteractionRequestFactory.share())

    private fun rejectPsaTask(deal: CompleteDeal) {
        val request = ChangeTaskStatusRequest(
            transition = TaskStatusTransitions.REQUEST_CHANGES,
            reason = null,
            dueDate = null,
            attachedForm = mapOf("comment" to "i dont like it")
        )
        interactWithPsaTask(deal = deal, changeTaskStatusRequest = request)
    }

    private fun acceptPsaTask(deal: CompleteDeal) {
        val request = ChangeTaskStatusRequest(
            transition = TaskStatusTransitions.ACCEPT,
            reason = null,
            dueDate = null,
            attachedForm = mapOf(
                "first_name" to "First name",
                "last_name" to "Last name",
                "email" to "<EMAIL>",
                "comment" to "awesome!!"
            )
        )
        interactWithPsaTask(deal = deal, changeTaskStatusRequest = request)
    }

    private fun interactWithPsa(dealId: Long, interactionRequest: InteractionRequest) = Unirest.post("$localUrl/deal/$dealId/documents/psa")
        .headers(AuthMock.getAuthHeader())
        .body(JsonMapper.encode(interactionRequest))
        .asEmpty()

    private fun interactWithPsaTask(deal: CompleteDeal, changeTaskStatusRequest: ChangeTaskStatusRequest) {
        val taskId = deal.categories.first { it.categoryKey == "purchase_and_sale_contract" }.tasksWithFilesAndHistory.first().task.id
        Unirest.put("$localUrl/task/$taskId/status")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(changeTaskStatusRequest))
            .asEmpty()
    }
}
