package realestate.unlock.dealroom.api.functional.member

import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetMembersTest : BaseFunctionalTest() {

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/member",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_MEMBERS)
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/member",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should get members successfully`() {
        // Given
        val givenUrl = "$localUrl/member"

        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            List::class.java
        )

        assertThat(response.size, equalTo(1))
    }

    @Test
    fun `should get filtered members by type successfully`() {
        // Given
        val givenUrl = "$localUrl/member"

        val givenQueryParam = "?memberType=seller"

        AuthMock.createMemberWithUser(
            firstName = "Test",
            lastName = "Name Seller",
            memberType = "seller",
            email = "<EMAIL>",
            uid = "seller-uid"
        )

        AuthMock.createMemberWithUser(
            firstName = "Test",
            lastName = "Name Buyer",
            memberType = "buyer",
            email = "<EMAIL>",
            uid = "buyer-uid"
        )

        // When
        val result = Unirest.get(givenUrl.plus(givenQueryParam))
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            List::class.java
        )

        assertThat(response.size, equalTo(1))

        val totalMembers = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson().let { totalResult ->
                JsonMapper.decode(
                    totalResult.body.toPrettyString(),
                    List::class.java
                ).size
            }

        assertThat(totalMembers, equalTo(2))
    }

    @Test
    fun `should restrict access if user is not admin`() {
        // Given
        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val givenUrl = "$localUrl/member"

        // When
        val result = Unirest.get(givenUrl)
            .header(HttpHeaders.AUTHORIZATION, "Bearer $givenToken")
            .asJson()

        // Then
        assertThat(result.status, equalTo(403))
    }
}
