package realestate.unlock.dealroom.api.functional.deal

import com.fasterxml.jackson.core.type.TypeReference
import com.google.i18n.phonenumbers.PhoneNumberUtil
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.post.PostDealSupportMemberRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class CreateLeasebackDealSupportMemberTest : BaseFunctionalTest() {

    private lateinit var givenPhone: String

    @BeforeEach
    fun setUp() {
        val phoneUtil = PhoneNumberUtil.getInstance()
        givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/point-of-contact",
        method = Method.POST.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without UPDATE_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/point-of-contact",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with UPDATE_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/point-of-contact",
            memberType = "buyer",
            permissions = listOf(Permission.UPDATE_OWN_DEALS, Permission.UPDATE_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal/${givenDeal.id}/point-of-contact",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `Creating a new point of contact should add it as part of the seller team`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenFirstName = "Johnny"
        val givenLastName = "Depp"
        val givenCompany = "Pepito inc"
        val givenEmail = "<EMAIL>"

        // when
        val createPointOfContactUrl = "$localUrl/deal/${givenDeal.id}/point-of-contact"
        val body = PostDealSupportMemberRequest(
            firstName = givenFirstName,
            lastName = givenLastName,
            companyName = givenCompany,
            phoneNumber = givenPhone,
            email = givenEmail
        )

        val createPointOfContactResponse = Unirest.post(createPointOfContactUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(body))
            .asString()

        val dealTeam = getDealSellerMembers(givenDeal.id)

        // then
        assertThat(createPointOfContactResponse.status, equalTo(HttpStatus.CREATED))

        assertPointOfContact(dealTeam, body)
    }

    @Test
    fun `Sellers that are not from the deal should not be able to create point of contacts`() {
        // Given
        val anotherSellerToken = "another-seller-token"
        AuthMock.createMemberWithUser(memberType = "seller", token = anotherSellerToken, email = "<EMAIL>", uid = anyString())
        val givenDeal = DealCreator.createDealByRest()

        // when
        val createPointOfContactUrl = "$localUrl/deal/${givenDeal.id}/point-of-contact"
        val body = PostDealSupportMemberRequest(firstName = "Pepito", lastName = "lastName", companyName = "pepito inc", phoneNumber = givenPhone, email = "<EMAIL>")

        val createPointOfContactResponse = Unirest.post(createPointOfContactUrl)
            .headers(AuthMock.getAuthHeader(anotherSellerToken))
            .body(JsonMapper.encode(body))
            .asString()

        // then
        assertThat(createPointOfContactResponse.status, equalTo(HttpStatus.FORBIDDEN))
    }

    @Test
    fun `Creating a new point of contact without phone number should work as expected`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()
        val givenFirstName = "Johnny"
        val givenLastName = "Depp"
        val givenCompany = "Pepito inc"
        val givenEmail = "<EMAIL>"

        // when
        val createPointOfContactUrl = "$localUrl/deal/${givenDeal.id}/point-of-contact"
        val body = PostDealSupportMemberRequest(
            firstName = givenFirstName,
            lastName = givenLastName,
            companyName = givenCompany,
            email = givenEmail
        )

        val createPointOfContactResponse = Unirest.post(createPointOfContactUrl)
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(body))
            .asString()

        val dealTeam = getDealSellerMembers(givenDeal.id)

        // then
        assertThat(createPointOfContactResponse.status, equalTo(HttpStatus.CREATED))

        assertPointOfContact(dealTeam, body)
    }

    private fun assertPointOfContact(
        dealTeam: List<MemberDto>,
        body: PostDealSupportMemberRequest
    ) {
        val pointOfContact = dealTeam.first { it.type == MemberTypeEnum.SELLER_SUPPORT }
        assertThat(pointOfContact, notNullValue())
        assertThat(pointOfContact.firstName, equalTo(body.firstName))
        assertThat(pointOfContact.lastName, equalTo(body.lastName))
        assertThat(pointOfContact.companyName, equalTo(body.companyName))
        assertThat(pointOfContact.phoneNumber, equalTo(body.phoneNumber))
        assertThat(pointOfContact.email, equalTo(body.email))
    }

    private fun getDealSellerMembers(dealId: Long): List<MemberDto> {
        val getDealTeamResponse = Unirest.get("$localUrl/deal/$dealId/members?team=SELLER")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        return JsonMapper.decode(
            getDealTeamResponse.body.toPrettyString(),
            object : TypeReference<List<MemberDto>>() {}
        )
    }
}
