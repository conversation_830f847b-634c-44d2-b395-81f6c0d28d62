package realestate.unlock.dealroom.api.functional.deal.loi

import io.mockk.every
import io.swagger.models.Method
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.hamcrest.core.IsEqual
import org.hamcrest.core.IsNot
import org.hamcrest.core.IsNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.loi.LoiSignStatus
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.sign.BeginSignFlowOutput
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalRound
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiSignResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.RespondLetterOfIntentRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.SellerSignerRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.MemberSignBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetMedicalLoiRoundSignTest : BaseFunctionalTest() {

    private val buyerToken = "buyer-token"
    private val sellerToken = "seller-token"

    private lateinit var admin: Member
    private lateinit var buyer: Member
    private lateinit var seller: Member

    @BeforeEach
    fun setUp() {
        // Create admin so `DealCreator.createDealByRest` works
        admin = AuthMock.createMemberWithUser()
        buyer = AuthMock.createMemberWithUser(memberType = "buyer", token = buyerToken, email = "<EMAIL>", uid = anyString())
        seller = AuthMock.createMemberWithUser(memberType = "seller", token = sellerToken, email = "<EMAIL>", uid = anyString())
        Context.injector.getInstance(SignGateway::class.java).let { signGateway ->
            every { signGateway.beginFlow(any()) } returns BeginSignFlowOutput("signingId")
        }
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/loi/round/123/sign",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/123/sign",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/loi/round/123/sign",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - seller member should return != 403`() {
        // Given
        val token = anyString()
        val seller = AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = "seller",
            token = token,
            permissions = listOf(Permission.READ_OWN_DEALS)

        )
        val postDealResponse = DealCreator.createDealByRest(seller = seller)
        val givenUrl = "$localUrl/deal/${postDealResponse.id}/loi/round/123/sign"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        assertThat(result.status, Matchers.not(Matchers.equalTo(403)))
    }

    @Test
    fun `get loi sign`() {
        // Given
        val deal = DealCreator.createDealByRest(buyer = buyer, seller = seller)
        val loi = createLoiByRest(deal)
        val sellerSignerRequest = MemberSignBuilder().getSellerSignRequest()
        val acceptedLoi = respondLoi(loi, true, sellerSignerRequest)
        beginLoiSign(acceptedLoi)

        // When
        val response = Unirest.get("$localUrl/deal/${acceptedLoi.dealId}/loi/round/${acceptedLoi.id}/sign")
            .headers(AuthMock.getAuthHeader(buyerToken))
            .asJson()

        // Then
        assertThat(response.status, IsEqual(HttpStatus.OK))
        val result = JsonMapper.decode(response.body.toPrettyString(), LoiSignResponse::class.java)
        assertThat(result.loiId, IsEqual(acceptedLoi.id))
        assertThat(result.status, IsEqual(LoiSignStatus.SELLER_SIGN_PENDING.name))
        assertThat(result.buyerSign.id, IsEqual(buyer.id.toString()))
        assertThat(result.buyerSign.name, IsEqual(buyer.fullName))
        assertThat(result.buyerSign.completedAt, IsNot(IsNull()))
        assertThat(result.sellerSign.id, IsNot(IsNull()))
        assertThat(result.sellerSign.name, IsEqual("${sellerSignerRequest.firstName} ${sellerSignerRequest.lastName}"))
        assertThat(result.sellerSign.completedAt, IsNull())
    }

    private fun createLoiByRest(deal: CompleteDeal): LoiMedicalRound {
        val loiCreationRequest = PostMedicalLoiRequestBuilder().build()
        return Unirest.post("$localUrl/deal/${deal.id}/loi/round")
            .headers(AuthMock.getAuthHeader(buyerToken))
            .body(JsonMapper.encode(loiCreationRequest))
            .asJson()
            .let { JsonMapper.decode(it.body.toPrettyString(), LoiMedicalRound::class.java) }
    }

    private fun respondLoi(loi: LoiMedicalRound, accepted: Boolean, sellerSignerRequest: SellerSignerRequest = MemberSignBuilder().getSellerSignRequest()): LoiMedicalRound {
        val acceptLoiRequest = RespondLetterOfIntentRequest(
            accepted = accepted,
            comments = "some comment",
            files = listOf(),
            sellerSignerRequest = sellerSignerRequest
        )
        return Unirest.post("$localUrl/deal/${loi.dealId}/loi/round/${loi.id}/respond")
            .headers(AuthMock.getAuthHeader(sellerToken))
            .body(JsonMapper.encode(acceptLoiRequest))
            .asJson()
            .let { JsonMapper.decode(it.body.toPrettyString(), LoiMedicalRound::class.java) }
    }

    private fun beginLoiSign(acceptedLoi: LoiMedicalRound) =
        Unirest.post("$localUrl/deal/${acceptedLoi.dealId}/loi/round/${acceptedLoi.id}/begin-sign")
            .headers(AuthMock.getAuthHeader(buyerToken))
            .asEmpty()
}
