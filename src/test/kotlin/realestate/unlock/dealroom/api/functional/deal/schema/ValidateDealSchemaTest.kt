package realestate.unlock.dealroom.api.functional.deal.schema

import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedInput
import realestate.unlock.dealroom.api.core.entity.search.SortOrder
import realestate.unlock.dealroom.api.core.repository.deal.schema.DealSchemaRepository
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig.Companion.dontTestPermissions
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest

class ValidateDealSchemaTest : BaseFunctionalTest() {

    private lateinit var dealSchemaRepository: DealSchemaRepository

    override fun getPermissionsConfig(): BaseFunctionalPermissionsConfig = dontTestPermissions()

    @BeforeEach
    fun setUp() {
        dealSchemaRepository = Context.injector.getInstance(DealSchemaRepository::class.java)
    }

    @Test
    fun `validate view vs schema`() {
        val dealSchemas = dealSchemaRepository.findAll(PaginatedInput(100000, 100000, 0, "created_at", SortOrder.DESC, mapOf()))

        dealSchemas.data.forEach { schema ->
            schema.viewSchema.sections.filter {
                (it["type"] as String) == "CUSTOM"
            }.forEach { section ->
                val fields = section["fields"] as List<String>
                fields.forEach { fields ->
                    assert(schema.jsonSchema.properties.containsKey(fields)) { "Missing property $fields" }
                }
            }
        }
    }
}
