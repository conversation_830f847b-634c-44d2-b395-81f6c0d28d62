package realestate.unlock.dealroom.api.functional.deal.report

import com.fasterxml.jackson.core.type.TypeReference
import io.mockk.mockk
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.hasSize
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.kfile.FileToUpload
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.usecase.utils.FileUtils
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.report.ReportResponse
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealReportsTest : BaseFunctionalTest() {

    private lateinit var fileGateway: FileGateway

    @BeforeEach
    fun setUp() {
        fileGateway = Context.injector.getInstance(FileGateway::class.java)
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/reports",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/reports",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/reports",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/reports",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `can retrieve the list of reports`() {
        // given
        val givenDeal = DealCreator.createDealByRest()
        val firstReport = ReportRequestBuilder().apply { this.type = "HAZARDOUS_MATERIALS_RECORDS" }.build()
        val secondReportFileInput = FileInput(uid = createFile(givenDeal.id, "", "file.pdf").uid, name = anyString())
        val secondReport = ReportRequestBuilder()
            .apply { this.type = "MOLD_SURVEY" }
            .apply { this.documents = listOf(secondReportFileInput) }
            .build()
        createDealReport(givenDeal.id, firstReport)
        createDealReport(givenDeal.id, secondReport)

        // when
        val result = Unirest.get("$localUrl/deal/${givenDeal.id}/reports")
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<ReportResponse>>() {}
        )

        assertThat(response, hasSize(2))
        assertThat(response[0].type, equalTo("HAZARDOUS_MATERIALS_RECORDS"))
        assertThat(response[1].type, equalTo("MOLD_SURVEY"))
        assertThat(response[1].documents, hasSize(1))
        assertThat(response[1].documents[0].uid, equalTo(secondReportFileInput.uid))
        assertThat(response[1].documents[0].name, equalTo(secondReportFileInput.name))
    }

    private fun createDealReport(dealId: Long, input: ReportRequest) =
        Unirest.post("$localUrl/deal/$dealId/reports")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(input))
            .asJson()

    private fun createFile(dealId: Long, path: String, name: String) = fileGateway.uploadFile(
        FileToUpload(
            content = mockk(),
            contentType = "application/pdf",
            path = FileUtils.buildDealRepositoryPath(dealId, path),
            filename = name
        )
    )
}
