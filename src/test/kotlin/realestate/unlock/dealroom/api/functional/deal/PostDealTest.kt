package realestate.unlock.dealroom.api.functional.deal

import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.keyway.kommons.http.HttpStatus
import io.swagger.models.Method
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.deal.category.CompleteDealCategory
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.property.PropertyCreationByAddressInput
import realestate.unlock.dealroom.api.core.entity.property.PropertyCreationByIdInput
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.entity.property.PropertyAssetsPropertyBuilder
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.member.post.PostMemberRequestBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FeatureFlagsStub

class PostDealTest : BaseFunctionalTest() {

    private val createDealInputBuilder = CreateDealInputBuilder().apply { propertyKeywayId = null }
    private val featureFlags = Context.injector.getInstance(FeatureFlags::class.java) as FeatureFlagsStub

    @BeforeEach
    fun setUp() {
        featureFlags.configureFeature(Feature.ORG_CAN_CREATE_NNN, true)
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        AuthMock.createMemberWithUser(
            phoneNumber = givenPhone
        )
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal",
        method = Method.POST.name,
        permissions = listOf(Permission.CREATE_DEALS)
    )

    @Test
    fun `Permissions - any seller should return 403`() {
        testPermissions(
            method = Method.POST.name,
            url = "$localUrl/deal",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `should create a medical deal successfully`() {
        // Given
        val givenSeller = givenSeller()
        val givenBuyer = givenBuyer()
        val givenFirstPassId = "first-pass-id"

        val givenUrl = "$localUrl/deal"
        val givenRequestCreationBody = createDealInputBuilder
            .apply { sellerMemberId = givenSeller.id }
            .apply { buyerMemberId = givenBuyer.id }
            .apply { guaranteeType = GuaranteeType.PERSONAL }
            .apply { propertyType = PropertyType.MEDICAL }
            .apply { firstPassId = givenFirstPassId }
            .buildSaleLeaseback()

        // When
        val response = HttpClient.post(
            url = givenUrl,
            body = JsonMapper.encode(givenRequestCreationBody),
            expectedStatus = HttpStatus.CREATED.code,
            responseHandler = { JsonMapper.decode(it, CompleteDeal::class.java) }
        )

        // Then
        val propertyToCreate = givenRequestCreationBody.property as PropertyCreationByAddressInput

        assertThat(response.categories, not(empty()))
        assertThat(response.members, not(empty()))
        assertThat(response.stage, equalTo(Stage.EVALUATION))
        assertThat(response.guaranteeType, equalTo(GuaranteeType.PERSONAL))
        assertThat(response.property.type, equalTo(PropertyType.MEDICAL))
        assertThat(response.property.askingPrice, equalTo(propertyToCreate.askingPrice))
        assertThat(response.property.squareFootage, equalTo(propertyToCreate.squareFootage))
        assertThat(response.property.yearBuilt, equalTo(propertyToCreate.yearBuilt))
        assertThat(response.firstPassId, equalTo(givenFirstPassId))

        response.categories.forEach { category ->
            assertThat(category.tasksWithFilesAndHistory, not(empty()))
        }
        verifyCategoriesWereCreatedSuccessfully(medicalTaskByCategory, response.categories)
    }

    @Test
    fun `should create a medical deal with name successfully`() {
        // Given
        val givenSeller = givenSeller()
        val givenBuyer = givenBuyer()
        val givenPropertyName = "this is the property name"

        val givenUrl = "$localUrl/deal"
        val givenRequestCreationBody = createDealInputBuilder
            .apply { propertyName = givenPropertyName }
            .apply { sellerMemberId = givenSeller.id }
            .apply { buyerMemberId = givenBuyer.id }
            .apply { propertyType = PropertyType.MEDICAL }
            .buildSaleLeaseback()

        val createdDeal = HttpClient.post(
            url = givenUrl,
            body = JsonMapper.encode(givenRequestCreationBody),
            expectedStatus = HttpStatus.CREATED.code,
            responseHandler = { JsonMapper.decode(it, CompleteDeal::class.java) }
        )

        assertThat(createdDeal.property.name, equalTo(givenPropertyName))
    }

    @Test
    fun `should create a multifamily deal successfully`() {
        // Given
        val givenSeller = givenSeller()
        val givenBuyer = givenBuyer()

        val givenUrl = "$localUrl/deal"
        val givenRequestCreationBody = createDealInputBuilder
            .apply { sellerMemberId = givenSeller.id }
            .apply { buyerMemberId = givenBuyer.id }
            .apply { guaranteeType = GuaranteeType.PERSONAL }
            .apply { propertyType = PropertyType.MULTIFAMILY }
            .buildAcquisition()

        // When
        val response = HttpClient.post(
            url = givenUrl,
            body = JsonMapper.encode(givenRequestCreationBody),
            expectedStatus = HttpStatus.CREATED.code,
            responseHandler = { JsonMapper.decode(it, CompleteDeal::class.java) }
        )

        // Then
        val propertyToCreate = givenRequestCreationBody.property as PropertyCreationByAddressInput

        assertThat(response.categories, not(empty()))
        assertThat(response.members, not(empty()))
        assertThat(response.stage, equalTo(Stage.EVALUATION))
        assertThat(response.guaranteeType, equalTo(GuaranteeType.PERSONAL))
        assertThat(response.property.type, equalTo(PropertyType.MULTIFAMILY))
        assertThat(response.property.askingPrice, equalTo(propertyToCreate.askingPrice))
        assertThat(response.property.squareFootage, equalTo(propertyToCreate.squareFootage))
        assertThat(response.property.yearBuilt, equalTo(propertyToCreate.yearBuilt))
        assertThat(response.property.multifamilyData?.propertyManager, equalTo(propertyToCreate.multifamilyData?.propertyManager))
        assertThat(response.property.multifamilyData?.owner, equalTo(propertyToCreate.multifamilyData?.owner))
        assertThat(response.property.multifamilyData?.brokerFirm, equalTo(propertyToCreate.multifamilyData?.brokerFirm))
        assertThat(response.property.multifamilyData?.units, equalTo(propertyToCreate.multifamilyData?.units))
        assertThat(response.property.multifamilyData?.occupancy, equalTo(propertyToCreate.multifamilyData?.occupancy))
        assertThat(response.property.multifamilyData?.parkingRatio, equalTo(propertyToCreate.multifamilyData?.parkingRatio))
        assertThat(response.property.multifamilyData?.parkingSpots, equalTo(propertyToCreate.multifamilyData?.parkingSpots))
        assertThat(response.property.multifamilyData?.averageSquareFootage, equalTo(propertyToCreate.multifamilyData?.averageSquareFootage))
        assertThat(response.property.multifamilyData?.unitsMix, equalTo(propertyToCreate.multifamilyData?.unitsMix))

        response.categories.forEach { category ->
            assertThat(category.tasksWithFilesAndHistory, not(empty()))
        }
        verifyCategoriesWereCreatedSuccessfully(multifamilyTaskByCategory, response.categories)
    }

    @Test
    fun `should create a multifamily deal with an existing property`() {
        // Given
        val givenSeller = givenSeller()
        val givenBuyer = givenBuyer()

        val givenUrl = "$localUrl/deal"
        val givenRequestCreationBody = createDealInputBuilder
            .apply { sellerMemberId = givenSeller.id }
            .apply { buyerMemberId = givenBuyer.id }
            .apply { guaranteeType = GuaranteeType.PERSONAL }
            .apply { propertyType = PropertyType.MULTIFAMILY }
            .buildAcquisition()
            .copy(property = PropertyCreationByIdInput(keywayId = "USDX-123"))

        // When
        val response = HttpClient.post(
            url = givenUrl,
            body = JsonMapper.encode(givenRequestCreationBody),
            expectedStatus = HttpStatus.CREATED.code,
            responseHandler = { JsonMapper.decode(it, CompleteDeal::class.java) }
        )

        // Then
        val stubbedPropertyAssetsResponse = PropertyAssetsPropertyBuilder()

        assertThat(response.categories, not(empty()))
        assertThat(response.members, not(empty()))
        assertThat(response.stage, equalTo(Stage.EVALUATION))
        assertThat(response.guaranteeType, equalTo(GuaranteeType.PERSONAL))
        assertThat(response.property.type, equalTo(PropertyType.MULTIFAMILY))
        assertThat(response.property.address.street, equalTo(stubbedPropertyAssetsResponse.address))
        assertThat(response.property.address.city, equalTo(stubbedPropertyAssetsResponse.city))
        assertThat(response.property.address.state, equalTo(stubbedPropertyAssetsResponse.state))
        assertThat(response.property.address.zip, equalTo(stubbedPropertyAssetsResponse.zipCode.toString()))
        assertThat(response.property.address.coordinates!!.latitude, equalTo(stubbedPropertyAssetsResponse.location.latitude))
        assertThat(response.property.address.coordinates!!.longitude, equalTo(stubbedPropertyAssetsResponse.location.longitude))
        assertThat(response.property.squareFootage, equalTo(stubbedPropertyAssetsResponse.squareFootage))

        response.categories.forEach { category ->
            assertThat(category.tasksWithFilesAndHistory, not(empty()))
        }
        verifyCategoriesWereCreatedSuccessfully(multifamilyTaskByCategory, response.categories)
    }

    @Test
    fun `should create deal with only LOI date and property successfully`() {
        // Given

        val givenSeller = givenSeller()
        val givenBuyer = givenBuyer()
        val givenSellerBroker = givenSellerBroker()

        val givenUrl = "$localUrl/deal"
        val givenRequestCreationBody = createDealInputBuilder
            .apply { sellerMemberId = givenSeller.id }
            .apply { buyerMemberId = givenBuyer.id }
            .apply { sellerBrokerMemberId = givenSellerBroker.id }
            .buildSaleLeaseback()

        // When
        val response = HttpClient.post(
            url = givenUrl,
            body = JsonMapper.encode(givenRequestCreationBody),
            expectedStatus = HttpStatus.CREATED.code,
            responseHandler = { JsonMapper.decode(it, CompleteDeal::class.java) }
        )

        // Then

        assertThat(response.categories, not(empty()))
        assertThat(response.members, not(empty()))

        response.categories.forEach { category ->
            assertThat(category.tasksWithFilesAndHistory, not(empty()))
        }
    }

    @Test
    fun `should create a deal without LOI execution date`() {
        // Given
        val givenSeller = givenSeller()
        val givenBuyer = givenBuyer()

        val givenUrl = "$localUrl/deal"
        val givenRequestCreationBody = createDealInputBuilder
            .apply { sellerMemberId = givenSeller.id }
            .apply { buyerMemberId = givenBuyer.id }
            .buildSaleLeaseback()

        // When
        val response = HttpClient.post(
            url = givenUrl,
            body = JsonMapper.encode(givenRequestCreationBody),
            expectedStatus = HttpStatus.CREATED.code,
            responseHandler = { JsonMapper.decode(it, CompleteDeal::class.java) }
        )

        // Then
        assertThat(response.loiExecutedDate, nullValue())
    }

    @Test
    fun `should restrict access if user is not admin`() {
        // Given
        val givenToken = "not-admin-token"
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)

        AuthMock.createMemberWithUser(
            address = "Test Address",
            memberType = "seller",
            firstName = "Test",
            lastName = "Name",
            companyName = "Test Company",
            phoneNumber = givenPhone,
            email = "<EMAIL>",
            uid = "not-admin",
            token = givenToken

        )

        val givenUrl = "$localUrl/deal"

        // When
        HttpClient.post(
            url = givenUrl,
            body = JsonMapper.encode(CreateDealInputBuilder().buildSaleLeaseback()),
            expectedStatus = HttpStatus.FORBIDDEN.code,
            responseHandler = { it },
            token = givenToken
        )
    }

    @Test
    fun `can create a deal without seller successfully`() {
        // Given
        val givenBuyer = givenBuyer()

        val givenUrl = "$localUrl/deal"
        val givenRequestCreationBody = createDealInputBuilder
            .apply { sellerMemberId = null }
            .apply { buyerMemberId = givenBuyer.id }
            .apply { guaranteeType = GuaranteeType.PERSONAL }
            .apply { propertyType = PropertyType.MEDICAL }
            .buildSaleLeaseback()

        // When
        val response = HttpClient.post(
            url = givenUrl,
            body = JsonMapper.encode(givenRequestCreationBody),
            expectedStatus = HttpStatus.CREATED.code,
            responseHandler = { JsonMapper.decode(it, CompleteDeal::class.java) }
        )

        // Then
        val propertyToCreate = givenRequestCreationBody.property as PropertyCreationByAddressInput
        assertThat(response.categories, not(empty()))
        assertThat(response.members, not(empty()))
        assertTrue(response.members.none { it.isTeam(MemberDealTeam.SELLER) })
        assertThat(response.stage, equalTo(Stage.EVALUATION))
        assertThat(response.guaranteeType, equalTo(GuaranteeType.PERSONAL))
        assertThat(response.property.type, equalTo(PropertyType.MEDICAL))
        assertThat(response.property.askingPrice, equalTo(propertyToCreate.askingPrice))
        assertThat(response.property.squareFootage, equalTo(propertyToCreate.squareFootage))
        assertThat(response.property.yearBuilt, equalTo(propertyToCreate.yearBuilt))

        response.categories.forEach { category ->
            assertThat(category.tasksWithFilesAndHistory, not(empty()))
        }
        verifyCategoriesWereCreatedSuccessfully(medicalTaskByCategory, response.categories)
    }

    private fun givenSeller(): MemberDto = givenMemberCreation("seller", "<EMAIL>")
    private fun givenBuyer(): MemberDto = givenMemberCreation("buyer", "<EMAIL>")
    private fun givenSellerBroker(): MemberDto = givenMemberCreation("seller_broker", "<EMAIL>")

    private fun givenMemberCreation(memberType: String, email: String): MemberDto {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        val givenMemberRequestCreationBody = PostMemberRequestBuilder()
            .apply { this.memberType = memberType }
            .apply { this.phoneNumber = givenPhone }
            .apply { this.email = email }
            .build()

        val givenMemberUrl = "$localUrl/member"
        return HttpClient.post(
            url = givenMemberUrl,
            body = JsonMapper.encode(givenMemberRequestCreationBody),
            responseHandler = { JsonMapper.decode(it, MemberDto::class.java) }
        )
    }

    private fun verifyCategoriesWereCreatedSuccessfully(expectedTasksByCategory: Map<String, Set<String>>, categories: List<CompleteDealCategory>) {
        assertEquals(expectedTasksByCategory.size, categories.size)
        expectedTasksByCategory.forEach { (categoryKey, taskTemplates) -> assertCategory(categoryKey, categories, taskTemplates) }
    }

    private fun assertCategory(key: String, categories: List<CompleteDealCategory>, expectedTaskTemplateKeys: Set<String>) {
        val category = categories.firstOrNull { it.categoryKey == key }
        checkNotNull(category) { "category not found with key '$key'" }
        assertThat(category.tasksWithFilesAndHistory, hasSize(expectedTaskTemplateKeys.size))
        expectedTaskTemplateKeys.forEach { taskTemplateKey ->
            assertTrue(category.tasksWithFilesAndHistory.any { it.task.templateKey == taskTemplateKey })
        }
    }

    companion object {

        val medicalTaskByCategory = mapOf(
            "point_of_contact" to setOf("new_point_of_contact"),
            "payment_and_invoicing" to setOf("payment_and_invoicing_details_tutorial"),
            "insurance_checklist" to setOf("proof_of_flood_insurance", "proof_of_property_insurance"),
            "closing_documents" to setOf("share_invoices_checklist", "wet_notarized_documents_tutorial", "wet_documents_tutorial"),
            "title" to setOf("title"),
            "property_taxes" to setOf("real_estate_tax_bills"),
            "lease_documents" to setOf("tenant_estoppel", "tenant_correspondence", "sublease_documents", "snda", "additional_lease_documents", "previous_tenant_estoppels_and_sndas"),
            "lease" to setOf("lease_document"),
            "financial" to setOf("records_and_correspondence_regarding_pending_or_threatened_litigation", "financial_statements", "tax_return"),
            "building_documents" to setOf("personal_property_inventory", "utility_bills", "certificates_of_occupancy", "as_builts", "floor_plans", "zoning_report"),
            "survey" to setOf("survey"),
            "payment_and_invoicing" to setOf("payment_and_invoicing_details_tutorial"),
            "purchase_and_sale_contract" to setOf("purchase_and_sale_contract_document"),
            "insurance" to setOf("loss_run_report", "insurance", "liability_insurance", "umbrella_insurance", "property_insurance"),
            "permits_contracts_and_warranties" to setOf("parking_revenue_expenses_and_management", "service_contracts", "remaining_warranties_guarantees", "building_permits"),
            "physical_and_environmental" to setOf(
                "new_physical_inspection",
                "environmental",
                "phase_ii_environmental_assessment",
                "physical",
                "any_existing_seismic_reports",
                "phase_i_report",
                "phase_ii_report",
                "asbestos_survey",
                "mold_survey",
                "hazardous_materials_records",
                "soil_and_geologic_report",
                "other_reports"
            ),
            "nnn_evaluation" to setOf(
                "nnn_upload_om",
                "nnn_tenant_data_collection",
                "nnn_catlyst_for_saling"
            ),
            "nnn_business_and_finances" to setOf(
                "nnn_client_financial_documentation",
                "nnn_keyway_financial_documentation"
            ),
            "nnn_pipeline" to setOf(
                "nnn_pipeline_meeting",
                "nnn_investment_committee_meeting"
            ),
            "nnn_icm1" to setOf(
                "nnn_icm1"
            ),
            "nnn_psa_elaboration" to setOf(
                "nnn_psa_information",
                "nnn_psa_legal_team"
            ),
            "nnn_icm2" to setOf(
                "nnn_icm2"
            ),
            "nnn_legal" to setOf(
                "nnn_legal"
            ),
            "nnn_wiring" to setOf(
                "nnn_wiring"
            ),
            "nnn_verification" to setOf(
                "nnn_settlement_statement",
                "nnn_occupancy_certificate",
                "nnn_insurance_proof",
                "nnn_closing_binder"
            ),
            "nnn_welcome_letter" to setOf(
                "nnn_welcome_letter"
            ),
            "nnn_notifications" to setOf(
                "nnn_social_media"
            )
        )
        val multifamilyTaskByCategory = mapOf(
            "multifamily_preliminary_analysis" to setOf("multifamily_preliminary_micro_location_analysis", "multifamily_preliminary_rent_comp_analysis"),
            "multifamily_main_analysis" to setOf(
                "multifamily_property_data_request",
                "multifamily_underwriting_model",
                "multifamily_submarket_tour_checklist",
                "multifamily_property_tour_checklist",
                "multifamily_acquisitions_team_reviews_underwriting"
            ),
            "multifamily_loi" to setOf("multifamily_start_loi", "multifamily_execute_loi", "multifamily_loi_call_for_offers"),
            "multifamily_psa" to setOf("multifamily_start_psa", "multifamily_execute_psa"),
            "multifamily_equity_negotiation" to setOf("multifamily_distribute_equity_memo"),
            "multifamily_internal" to setOf("multifamily_investment_committee_memo_i", "multifamily_investment_committee_memo_ii"),
            "multifamily_earnest_money" to setOf("multifamily_money_received_by_title"),
            "multifamily_dd_documents" to setOf(
                "multifamily_begin_deal_structuring_checklist",
                "multifamily_prepare_due_diligence_checklist",
                "multifamily_send_out_calendar_reminders",
                "multifamily_schedule_kickoff_call",
                "multifamily_schedule_weekly_meetings",
                "multifamily_execute_commitment_letter",
                "multifamily_negotiate_loan_terms",
                "multifamily_submit_loan_app",
                "multifamily_seller_survey",
                "multifamily_updated_survey",
                "multifamily_prior_three_years_financial_statements",
                "multifamily_units_and_square_footage",
                "multifamily_mortgage_loan_documents",
                "multifamily_history_capital_expenditures",
                "multifamily_certificates_of_occupancy",
                "multifamily_rent_roll_lease_charges",
                "multifamily_delinquency_bad_debt_aged_receivables_report",
                "multifamily_security_deposits",
                "multifamily_availability_report",
                "multifamily_parking_map",
                "multifamily_site_map",
                "multifamily_loss_runs_and_current_insurance_policy",
                "multifamily_personal_property_list",
                "multifamily_employees_salaries_list",
                "multifamily_property_tax_appeal_records",
                "multifamily_pending_litigation",
                "multifamily_rubs_documentation",
                "multifamily_water_and_sewer_bills",
                "multifamily_electricity_bills",
                "multifamily_gas_bills",
                "multifamily_lease_audit",
                "multifamily_copy_of_blank_lease",
                "multifamily_unit_walks",
                "multifamily_environmental_reports",
                "multifamily_irrigation",
                "multifamily_pool",
                "multifamily_sewer",
                "multifamily_plumbing_supply_shut_offs",
                "multifamily_fire_systems",
                "multifamily_foundation_and_structure",
                "multifamily_electrical_systems",
                "multifamily_hvac",
                "multifamily_roof",
                "multifamily_boilers_hot_water",
                "multifamily_chiller_system",
                "multifamily_elevator",
                "multifamily_building_safety",
                "multifamily_evacuation_procedures",
                "multifamily_emergency_procedures",
                "multifamily_compliance_status",
                "multifamily_coordinate_it_inspection",
                "multifamily_order_new_equipment",
                "multifamily_check_building_permits",
                "multifamily_verify_brand",
                "multifamily_property_insurance_quote",
                "multifamily_property_tax_forecast",
                "multifamily_no_violations_letter",
                "multifamily_org_structure_chart",
                "multifamily_subscription_agreement",
                "multifamily_operating_agreement",
                "multifamily_entity_creation",
                "multifamily_underlying_documents",
                "multifamily_title_objection_letter",
                "multifamily_due_diligence_ends",
                "multifamily_orders_completed"
            ),
            "multifamily_loan_documents" to setOf(
                "multifamily_promissory_note",
                "multifamily_deed_of_trust",
                "multifamily_guaranty",
                "multifamily_ucc_financing_statement",
                "multifamily_transfer_tax_form",
                "multifamily_loan_commitment",
                "multifamily_loan_agreement",
                "multifamily_bill_of_sale",
                "multifamily_exhibit_a_to_bill_of_sale",
                "multifamily_assignment_of_leases",
                "multifamily_exhibit_a_to_assignment_of_leases",
                "multifamily_tenant_notices",
                "multifamily_non_foreign_certification",
                "multifamily_closingResolutions",
                "multifamily_non_compete_agreement",
                "multifamily_terminations",
                "multifamily_closing_instructions",
                "multifamily_title_closing_documents",
                "multifamily_additional_lender_documents"
            ),
            "multifamily_insurance" to setOf(
                "multifamily_comprehensive_general_liability",
                "multifamily_flood_insurance",
                "multifamily_property_damage_insurance"
            ),
            "multifamily_closing_documents" to setOf(
                "multifamily_settlement_statement",
                "multifamily_rent_proration_amounts",
                "multifamily_utility_deposit_amounts",
                "multifamily_insurance_certificates",
                "multifamily_third_party_invoices",
                "multifamily_fee_agreement_amounts",
                "multifamily_payment_of_brokers",
                "multifamily_state_transfer_taxes"
            ),
            "multifamily_closing" to setOf("multifamily_contract_closing_date"),
            "multifamily_equity_closing" to setOf(
                "multifamily_equity_fully_committed",
                "multifamily_equity_structure",
                "multifamily_waterfall_structure",
                "multifamily_equity_fully_called"
            ),
            "multifamily_extended_closing" to setOf("multifamily_closing_extension"),
            "multifamily_asset_management" to setOf(
                "multifamily_tax_true_ups",
                "multifamily_utility_true_ups",
                "multifamily_renovation_plan",
                "multifamily_capex_budget",
                "multifamily_capex_bids",
                "multifamily_days_pm_plan",
                "multifamily_transition_plan"
            ),
            "multifamily_branding" to setOf(
                "multifamily_website_branding",
                "multifamily_social_media_posts"
            ),
            "purchase_and_sale_contract" to setOf("purchase_and_sale_contract_document")

        )
    }
}
