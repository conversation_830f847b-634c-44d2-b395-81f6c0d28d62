package realestate.unlock.dealroom.api.functional.task

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.post.request.EnableTaskRequest
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class EnableTaskTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/123/enabled",
        method = Method.PUT.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `it disables a task correctly`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenTaskToDisable = dealResponse.findOneTask()

        // When
        enableTask(givenTaskToDisable.id, enabled = false)

        // then
        val task = getTask(givenTaskToDisable.id)
        assertThat(task.id, equalTo(givenTaskToDisable.id))
        assertFalse(task.enabled)
    }

    @Test
    fun `it enables a disabled task correctly`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenTaskToDisable = dealResponse.findOneTask()
        disableTask(givenTaskToDisable.id)
        enableTask(givenTaskToDisable.id, enabled = true)

        // When
        val task = getTask(givenTaskToDisable.id)

        assertThat(task.id, equalTo(givenTaskToDisable.id))
        assertTrue(task.enabled)
    }

    private fun enableTask(taskId: Long, enabled: Boolean) = HttpClient.put(
        url = "$localUrl/task/$taskId/enabled",
        expectedStatus = HttpStatus.ACCEPTED,
        responseHandler = { it },
        body = JsonMapper.encode(EnableTaskRequest(enabled = enabled))
    )

    private fun getTask(taskId: Long) = HttpClient.get(
        url = "$localUrl/task/$taskId",
        expectedStatus = HttpStatus.OK,
        responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
    )

    private fun disableTask(taskId: Long) {
        enableTask(taskId = taskId, enabled = false)
        val task = getTask(taskId)
        assertFalse(task.enabled)
    }
}
