package realestate.unlock.dealroom.api.functional.deal.update

import com.keyway.adapters.dtos.response.property.additional.multifamily.mortgages.MultifamilyMortgagesResponse
import com.keyway.adapters.dtos.response.property.additional.multifamily.sales.MultifamilySalesResponse
import io.mockk.every
import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.not
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.SourceType
import realestate.unlock.dealroom.api.core.entity.property.PropertyCreationByAddressInput
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.gateway.property.PropertyAssetsGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.GeoPointResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyAssetsResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.MultifamilyAdditionalDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.MultifamilyRealEstateDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.units.MultifamilyUnitsResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.images.PropertyImagesResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.PropertyAssetsGatewayStub
import java.math.BigDecimal

class UpdateDealPropertyTest : BaseFunctionalTest() {

    private lateinit var propertyAssetsGateway: PropertyAssetsGateway

    @BeforeEach
    fun setUp() {
        propertyAssetsGateway = Context.injector.getInstance(PropertyAssetsGateway::class.java) as PropertyAssetsGatewayStub
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/property",
        method = Method.PUT.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `it updates the property as expected`() {
        val givenDeal = DealCreator.createDealByRest()
        val input = givenInput()
        val givenPropertyAssetsResponse = givenPropertyAssetsResponse()
        every {
            propertyAssetsGateway.findOrCreateByAddress(
                PropertyAssetsGateway.Input(
                    propertyType = input.type.name,
                    normalizedAddress = PropertyAssetsGateway.NormalizedAddress(
                        streetAddress = input.street,
                        city = input.city,
                        state = input.state,
                        zipCode = input.zip
                    )
                )
            )
        } returns givenPropertyAssetsResponse

        val response = HttpClient.put(
            url = "$localUrl/deal/${givenDeal.id}/property",
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) },
            body = JsonMapper.encode(input)
        )
        val newProperty = getProperty(response.propertyId)

        assertThat(response.id, equalTo(givenDeal.id))
        assertThat(response.propertyId, not(equalTo(givenDeal.propertyId)))
        assertThat(newProperty.name, equalTo(input.name))
        assertThat(newProperty.address.street, equalTo(input.street))
        assertThat(newProperty.address.city, equalTo(input.city))
        assertThat(newProperty.address.apartment, equalTo(input.apartment))
        assertThat(newProperty.address.zip, equalTo(input.zip))
    }

    private fun getProperty(id: Long) =
        Context.injector.getInstance(PropertyRepository::class.java).let { propertyRepository ->
            propertyRepository.findById(id)
        }

    private fun givenInput() = PropertyCreationByAddressInput(
        name = "amazing property",
        street = "amazing street",
        apartment = "ap",
        city = "Springfield",
        state = "Florida",
        zip = "1231",
        type = PropertyType.MEDICAL,
        keywayId = null,
        yearBuilt = 1980,
        squareFootage = BigDecimal.valueOf(1231),
        askingPrice = BigDecimal.valueOf(12315),
        latitude = BigDecimal.valueOf(3125),
        longitude = BigDecimal.valueOf(5123),
        multifamilyData = null
    )

    private fun givenPropertyAssetsResponse() = PropertyAssetsResponse(
        id = "USWV-000045",
        address = "930 Benge Dr",
        fullAddress = "930 Benge Dr, Arlington, TX 76013, USA",
        city = "Arlington",
        county = "Tarrant County",
        zipCode = 76013,
        state = "Texas",
        tractCode = 48439122402,
        location = GeoPointResponse(
            latitude = BigDecimal.valueOf(32.7235632),
            longitude = BigDecimal.valueOf(-97.1201021)
        ),
        propertyType = realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyType.MEDICAL_OFFICE,
        squareFootage = BigDecimal.valueOf(130288),
        sourceType = SourceType.OFF_MARKET,
        additionalData = MultifamilyAdditionalDataResponse(
            realEstateData = MultifamilyRealEstateDataResponse(
                source = "yardi",
                name = "Maverick Place",
                occupancyPercentage = BigDecimal.valueOf(0.99),
                constructionYear = 2009,
                productType = "Garden",
                propertyClass = "B",
                units = MultifamilyUnitsResponse(
                    records = listOf()
                ),
                communityAmenities = listOf(),
                apartmentsAmenities = listOf(),
                amenities = listOf(),
                images = listOf()
            ),
            sales = MultifamilySalesResponse(records = listOf()),
            mortgages = MultifamilyMortgagesResponse(records = listOf())
        ),
        images = PropertyImagesResponse(
            urls = listOf("fake-img-url")
        ),
        constructionYear = 1980
    )
}
