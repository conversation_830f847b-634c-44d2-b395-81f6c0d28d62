package realestate.unlock.dealroom.api.functional.deal.documents

import com.fasterxml.jackson.core.type.TypeReference
import io.swagger.models.Method
import kong.unirest.Unirest
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.equalTo
import org.hamcrest.Matchers.nullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.summary.LoiDocumentStatus
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.DocumentResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.DocumentType
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalRound
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

class GetDealMainDocumentsTest : BaseFunctionalTest() {

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/deal/123/documents",
        method = Method.GET.name,
        permissions = listOf(Permission.READ_OWN_DEALS)
    )

    @Test
    fun `Permissions - buyer non member without READ_ALL_DEALS should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS),
            allowed = false
        )
    }

    @Test
    fun `Permissions - buyer non member with READ_ALL_DEALS should return != 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents",
            memberType = "buyer",
            permissions = listOf(Permission.READ_OWN_DEALS, Permission.READ_ALL_DEALS),
            allowed = true
        )
    }

    @Test
    fun `Permissions - any seller should return 403`() {
        // Given
        val givenDeal = DealCreator.createDealByRest()

        testPermissions(
            method = Method.GET.name,
            url = "$localUrl/deal/${givenDeal.id}/documents",
            memberType = "seller",
            permissions = Permission.values().toList(),
            allowed = false
        )
    }

    @Test
    fun `Given a deal with loi in negotiation it should retrieve the expected loi document`() {
        val deal = DealCreator.createDealByRest()
        val loi = givenLoiFor(deal.id)

        val givenUrl = "$localUrl/deal/${deal.id}/documents"

        // When
        val result = Unirest.get(givenUrl)
            .headers(AuthMock.getAuthHeader())
            .asJson()

        // Then
        assertThat(result.status, equalTo(200))

        val response = JsonMapper.decode(
            result.body.toPrettyString(),
            object : TypeReference<List<DocumentResponse>>() {}
        )

        assertThat(response.size, equalTo(3))

        val loiDocument = response.first { it.type == DocumentType.LOI }
        assertThat(loiDocument.status, equalTo(LoiDocumentStatus.IN_NEGOTIATION.toString()))
        assertThat(loiDocument.createdAt, equalTo(loi.date))

        val psaDocument = response.first { it.type == DocumentType.PSA }
        assertThat(psaDocument.status, equalTo(DocumentStatus.NOT_STARTED.name))
        assertThat(psaDocument.createdAt, nullValue())

        val psaLease = response.first { it.type == DocumentType.LEASE }
        assertThat(psaLease.status, equalTo(DocumentStatus.NOT_STARTED.name))
        assertThat(psaLease.createdAt, nullValue())
    }

    private fun givenLoiFor(
        dealId: Long
    ): LoiMedicalRound {
        return Unirest.post("$localUrl/deal/$dealId/loi/round")
            .headers(AuthMock.getAuthHeader())
            .body(JsonMapper.encode(PostMedicalLoiRequestBuilder().build()))
            .asJson().let { JsonMapper.decode(it.body.toPrettyString(), LoiMedicalRound::class.java) }
    }
}
