package realestate.unlock.dealroom.api.functional.task

import io.swagger.models.Method
import kong.unirest.HttpStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.deal.DealStatus
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealData
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.field.DealDataFieldParser
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.schema.DealSchemaData
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.get.GetTaskResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.patch.request.UpdateTaskRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.repository.database.toJsonPGObject
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalPermissionsConfig
import realestate.unlock.dealroom.api.utils.base.BaseFunctionalTest
import realestate.unlock.dealroom.api.utils.base.HttpClient
import realestate.unlock.dealroom.api.utils.creator.DealCreator
import realestate.unlock.dealroom.api.utils.entity.deal.findOneTask
import realestate.unlock.dealroom.api.utils.entity.deal.findTaskByTemplateKey
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.extensions.*
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.math.BigDecimal
import java.time.LocalDate

class UpdateTaskImpactOnDealTest : BaseFunctionalTest() {

    private val sqlClient = Context.injector.getInstance(SqlClient::class.java)
    private val getDealData = Context.injector.getInstance(GetDealData::class.java)

    @BeforeEach
    fun setUp() {
        AuthMock.createMemberWithUser()
    }

    override fun getPermissionsConfig() = BaseFunctionalPermissionsConfig(
        url = "$localUrl/task/333",
        method = Method.PATCH.name,
        permissions = listOf(Permission.UPDATE_OWN_DEALS)
    )

    @Test
    fun `Task should be correctly updated and deal date also`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()

        val givenTask = dealResponse.findOneTask()
        sqlClient.update(
            query = "UPDATE task SET due_date_external_data_definition = 'deal.evaluationDueDate' WHERE id = ${givenTask.id}",
            params = emptyList()
        )
        val updateRequest = UpdateTaskRequest(
            dueDate = LocalDate.now()
        )
        // When
        val taskResponse = HttpClient.patch(
            url = "$localUrl/task/${givenTask.id}",
            body = JsonMapper.encode(updateRequest),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
        // Then
        val dealDto = HttpClient.get(
            url = "$localUrl/deal/${dealResponse.id}",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        assertEquals(updateRequest.dueDate, taskResponse.dueDate)
        assertEquals(updateRequest.dueDate, dealDto.evaluationDueDate)
    }

    @Test
    fun `Task should be correctly updated and deal data also`() {
        // Given
        val dealResponse = DealCreator.createDealByRest()
        val formData = mapOf(
            "deal.evaluationDueDate" to LocalDate.now(),
            "deal.offerPrice" to BigDecimal.valueOf(123L),
            "deal.schemaData.data.noi" to 123,
            "deal.status" to DealStatus.ON_HOLD,
            "property.name" to anyString()
        )
        val givenTask = dealResponse.findOneTask()
        sqlClient.update(
            query = "UPDATE task SET form_external_data_definition = ? WHERE id = ${givenTask.id}",
            params = listOf(formData.keys.toJsonPGObject())
        )
        val updateRequest = UpdateTaskRequest(
            attachedForm = formData
        )
        // When
        val taskResponse = HttpClient.patch(
            url = "$localUrl/task/${givenTask.id}",
            body = JsonMapper.encode(updateRequest),
            expectedStatus = HttpStatus.ACCEPTED,
            responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
        )
        // Then
        val dealDto = HttpClient.get(
            url = "$localUrl/deal/${dealResponse.id}",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealDto::class.java) }
        )

        val schemaData = HttpClient.get(
            url = "$localUrl/deal/${dealResponse.id}/schema-data",
            expectedStatus = HttpStatus.OK,
            responseHandler = { JsonMapper.decode(it, DealSchemaData::class.java) }
        )

        assertEquals(formData["deal.evaluationDueDate"], dealDto.evaluationDueDate)
        assertEquals(formData["deal.offerPrice"], dealDto.offerPrice)
        assertEquals(formData["deal.status"], dealDto.status)
        assertEquals(formData["deal.schemaData.data.noi"], schemaData.data["noi"])
    }

    @Test
    fun `can update tour date and call for offers dueDate and impact on deal`() {
        val dealResponse = DealCreator.createDealByRest(
            dealCreationRequestBuilder = CreateDealInputBuilder().apply { propertyType = PropertyType.MULTIFAMILY }
        )

        val tourDateTask = dealResponse.findTaskByTemplateKey("multifamily_property_tour_checklist")
        val callForOfferTask = dealResponse.findTaskByTemplateKey("multifamily_loi_call_for_offers")
        val updateTaskRequest = UpdateTaskRequest(
            dueDate = LocalDate.now().plusDays(10)
        )
        listOf(tourDateTask, callForOfferTask).forEach { task ->
            HttpClient.patch(
                url = "$localUrl/task/${task.id}",
                body = JsonMapper.encode(updateTaskRequest),
                expectedStatus = HttpStatus.ACCEPTED,
                responseHandler = { JsonMapper.decode(it, GetTaskResponse::class.java) }
            )
        }
        val dealData = getDealData.get(dealId = dealResponse.id)

        val tourDate = DealDataFieldParser.getValue(
            fieldId = tourDateTask.externalDataDefinition!!.dueDate!!,
            dealData = dealData
        )
        val callForOffer = DealDataFieldParser.getValue(
            fieldId = callForOfferTask.externalDataDefinition!!.dueDate!!,
            dealData = dealData
        )

        assertEquals(tourDate.toString(), updateTaskRequest.dueDate.toString())
        assertEquals(callForOffer.toString(), updateTaskRequest.dueDate.toString())
    }
}
