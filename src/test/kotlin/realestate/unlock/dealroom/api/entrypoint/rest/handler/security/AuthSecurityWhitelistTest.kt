package realestate.unlock.dealroom.api.entrypoint.rest.handler.security

import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.infrastructure.configuration.model.OpenApiConfig

class AuthSecurityWhitelistTest {

    private lateinit var authSecurityWhitelist: AuthSecurityWhitelist

    @BeforeEach
    fun setUp() {
        authSecurityWhitelist = AuthSecurityWhitelist(OpenApiConfig(true))
    }

    @Test
    fun `returns false when endpoint is not in the white list`() {
        assertFalse(authSecurityWhitelist.isInTheWhiteList("/other/endpoint/00", "GET"))
        assertFalse(authSecurityWhitelist.isInTheWhiteList("/prospect/8989/template", "GET"))
    }

    @Test
    fun `returns false when endpoint is in the white list but method does not match`() {
        assertFalse(authSecurityWhitelist.isInTheWhiteList("/prospect/8989/deal", "POST"))
    }

    @Test
    fun `returns true when endpoint is in the white list`() {
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/health", "GET"))
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/user/password/reset", "GET"))
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/api/docs", "GET"))
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/swagger", "GET"))
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/webjars/swagger-ui/3.25.2/swagger-ui.css", "GET"))
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/webjars/swagger-ui/3.25.2/swagger-ui-bundle.js", "GET"))
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/webjars/swagger-ui/3.25.2/swagger-ui-standalone-preset.js", "GET"))
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/webjars/swagger-ui/3.25.2/favicon-16x16.png", "GET"))
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/webjars/swagger-ui/3.25.2/favicon-32x32.png", "GET"))
    }

    @Test
    fun `returns true when endpoint and method is in the white list`() {
        assertTrue(authSecurityWhitelist.isInTheWhiteList("/activate-account?token=anyToken", "GET"))
    }
}
