package realestate.unlock.dealroom.api.utils.stub

import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.MemberToSave
import realestate.unlock.dealroom.api.repository.database.member.MemberDatabaseRepository
import javax.inject.Inject

class CreateMemberTransactionManagementStub @Inject constructor(
    private val memberDatabaseRepository: MemberDatabaseRepository
) {

    fun execute(memberToSave: MemberToSave, exception: Exception? = null): Member =
        memberDatabaseRepository.save(memberToSave)
            .also { exception?.let { throw exception } }
}
