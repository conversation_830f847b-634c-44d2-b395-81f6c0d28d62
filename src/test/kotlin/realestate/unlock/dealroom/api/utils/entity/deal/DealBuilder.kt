package realestate.unlock.dealroom.api.utils.entity.deal

import realestate.unlock.dealroom.api.core.entity.deal.*
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

@Suppress("MemberVisibilityCanBePrivate")
class DealBuilder {

    var id: Long = anyId()
    var propertyId: Long = anyId()
    var stage: Stage = Stage.EVALUATION
    var loiExecutedDate: LocalDate? = LocalDate.now()
    var diligenceExpirationDate: LocalDate? = LocalDate.now()
    var initialClosingDate: LocalDate? = null
    var outsideClosingDate: LocalDate? = null
    var contractExecutedDate: LocalDate? = null
    var updatedAt: OffsetDateTime = OffsetDateTime.now()
    var createdAt: OffsetDateTime = OffsetDateTime.now()
    var lease = DealLeaseBuilder().build()
    var guaranteeType: GuaranteeType? = null
    var dealType: DealType = DealType.SALE_LEASEBACK
    var vertical: String? = null
    var status: DealStatus = DealStatus.ACTIVE
    var evaluationDueDate: LocalDate? = LocalDate.now()
    var underwritingDueDate: LocalDate? = LocalDate.now()
    var schemaId: Long = anyId()
    var schemaData: Map<String, Any> = emptyMap()
    var tags: Set<String> = emptySet()
    var leaderId: Long = anyId()
    var offerPrice: BigDecimal? = null
    var earnestMoneyDeposit: BigDecimal? = null
    var extensionDeposit: BigDecimal? = null
    var calendarId: String? = null
    var hasFindings: Boolean = false
    var organizationId: String = "Keyway"
    var buyerCompanyName: String? = "buyer company name"
    var brokerCompanyName: String? = "broker company name"
    var sellerCompanyName: String? = "seller company name"
    var firstPassId: String? = null

    fun withId(id: Long) = apply { this.id = id }
    fun withPropertyId(propertyId: Long) = apply { this.propertyId = propertyId }
    fun withStage(stage: Stage) = apply { this.stage = stage }
    fun withStatus(status: DealStatus) = apply { this.status = status }
    fun archived() = apply { this.status = DealStatus.DELETED }

    fun build() = Deal(
        id = id,
        propertyId = propertyId,
        stage = stage,
        loiExecutedDate = loiExecutedDate,
        diligenceExpirationDate = diligenceExpirationDate,
        initialClosingDate = initialClosingDate,
        outsideClosingDate = outsideClosingDate,
        contractExecutedDate = contractExecutedDate,
        createdAt = createdAt,
        updatedAt = updatedAt,
        lease = lease,
        guaranteeType = guaranteeType,
        type = dealType,
        vertical = vertical,
        status = status,
        evaluationDueDate = evaluationDueDate,
        underwritingDueDate = underwritingDueDate,
        schemaData = SchemaData(
            schemaId = schemaId,
            data = schemaData
        ),
        omFileId = null,
        sourceType = null,
        tenantName = null,
        tags = tags,
        leaderId = leaderId,
        offerPrice = offerPrice,
        earnestMoneyDeposit = earnestMoneyDeposit,
        extensionDeposit = extensionDeposit,
        calendarId = calendarId,
        hasFindings = hasFindings,
        organizationId = organizationId,
        buyerCompanyName = buyerCompanyName,
        brokerCompanyName = brokerCompanyName,
        sellerCompanyName = sellerCompanyName,
        firstPassId = firstPassId
    )
}
