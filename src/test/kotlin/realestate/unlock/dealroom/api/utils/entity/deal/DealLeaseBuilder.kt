package realestate.unlock.dealroom.api.utils.entity.deal

import realestate.unlock.dealroom.api.core.entity.deal.DealLease
import realestate.unlock.dealroom.api.core.entity.deal.RentStepType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.Month

class DealLeaseBuilder {
    var rent: BigDecimal? = BigDecimal.TEN
    var type: String? = "NNN"
    var rentIncrease: Float? = 1.0f
    var increaseEveryYear: Float? = 1.0f
    var length: Long? = 1L
    var expirationYear: Long? = 1L
    var numberOfOptions: Long? = 1L
    var optionLengths: Long? = 1L
    var rentCpi: Float? = 1.0f
    var rentStepType: RentStepType? = RentStepType.CPI
    var dueDiligenceNumber: Long? = 1
    var closingPeriod: Long? = 1
    var closingPeriodExtension: Long? = 1
    var date: LocalDate? = LocalDate.of(2023, Month.AUGUST, 12)

    fun build() =
        DealLease(
            rent = rent,
            type = type,
            rentIncrease = rentIncrease,
            increaseEveryYear = increaseEveryYear,
            length = length,
            expirationYear = expirationYear,
            numberOfOptions = numberOfOptions,
            optionLengths = optionLengths,
            rentCpi = rentCpi,
            rentStepType = rentStepType,
            dueDiligenceNumber = dueDiligenceNumber,
            closingPeriod = closingPeriod,
            closingPeriodExtension = closingPeriodExtension,
            date = date
        )
}
