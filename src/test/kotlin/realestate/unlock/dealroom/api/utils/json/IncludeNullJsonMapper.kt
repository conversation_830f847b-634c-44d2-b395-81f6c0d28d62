package realestate.unlock.dealroom.api.utils.json

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import realestate.unlock.dealroom.api.infrastructure.utils.extension.manageDecodeException
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.Mapper
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.applyCamelCaseConfig
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.applyDefaultDealRoomConfig
object IncludeNullJsonMapper : Mapper {
    private lateinit var objectMapper: ObjectMapper

    override fun getMapper(): ObjectMapper {
        if (!this::objectMapper.isInitialized) {
            objectMapper = ObjectMapper().applyDefaultDealRoomConfig().setSerializationInclusion(
                JsonInclude.Include.ALWAYS
            )
        }
        return objectMapper
    }

    override fun encode(obj: Any): String =
        getMapper().writeValueAsString(obj)

    override fun <T> decode(str: String, clazz: Class<T>): T =
        getMapper().readValue(str, clazz)

    override fun <T> decode(str: String, typeReference: TypeReference<T>): T =
        getMapper().readValue(str, typeReference)

    override fun <T> mapTo(map: Map<String, Any?>, clazz: Class<out T>): T = runCatching {
        decode(encode(map), clazz)
    }.onFailure {
        it.manageDecodeException(clazz)
    }.getOrThrow()

    override fun <K, V> convertToMap(any: Any): Map<K, V> =
        decode(encode(any), object : TypeReference<Map<K, V>>() {})
}

object IncludeNullJsonMapperCC : Mapper {
    private lateinit var objectMapper: ObjectMapper

    override fun getMapper(): ObjectMapper {
        if (!this::objectMapper.isInitialized) {
            objectMapper = ObjectMapper().applyCamelCaseConfig().setSerializationInclusion(
                JsonInclude.Include.ALWAYS
            )
        }
        return objectMapper
    }

    override fun encode(obj: Any): String =
        getMapper().writeValueAsString(obj)

    override fun <T> decode(str: String, clazz: Class<T>): T =
        getMapper().readValue(str, clazz)

    override fun <T> decode(str: String, typeReference: TypeReference<T>): T =
        getMapper().readValue(str, typeReference)

    override fun <T> mapTo(map: Map<String, Any?>, clazz: Class<out T>): T = runCatching {
        decode(encode(map), clazz)
    }.onFailure {
        it.manageDecodeException(clazz)
    }.getOrThrow()

    override fun <K, V> convertToMap(any: Any): Map<K, V> =
        decode(encode(any), object : TypeReference<Map<K, V>>() {})
}
