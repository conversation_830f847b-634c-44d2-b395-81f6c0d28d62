package realestate.unlock.dealroom.api.utils.stub

import realestate.unlock.dealroom.api.infrastructure.gateway.property.PropertyAssetsGateway
import realestate.unlock.dealroom.api.utils.entity.property.PropertyAssetsPropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString

class PropertyAssetsGatewayStub : PropertyAssetsGateway {

    var keywayId: String = anyString()
    override fun findOrCreateByAddress(input: PropertyAssetsGateway.Input) = PropertyAssetsPropertyBuilder()
        .withId(keywayId)
        .build()
    override fun findById(keywayId: String) = PropertyAssetsPropertyBuilder().withId(keywayId).build()
}
