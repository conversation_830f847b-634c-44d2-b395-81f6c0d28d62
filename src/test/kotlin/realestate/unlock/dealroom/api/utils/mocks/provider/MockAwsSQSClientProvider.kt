package realestate.unlock.dealroom.api.utils.mocks.provider

import com.keyway.kommons.aws.config.AwsConfig
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import java.net.URI
import javax.inject.Inject
import javax.inject.Provider

class MockAwsSQSClientProvider @Inject constructor(
    private val awsConfig: AwsConfig
) : Provider<SqsAsyncClient> {

    override fun get(): SqsAsyncClient {
        val builder =
            SqsAsyncClient.builder()
                .region(Region.of(awsConfig.region))

        awsConfig.endpointOverride?.let {
            builder.endpointOverride(URI.create(awsConfig.endpointOverride))
        }

        builder.credentialsProvider(
            StaticCredentialsProvider.create(
                AwsBasicCredentials.create(awsConfig.accessKey, awsConfig.secretKey)
            )
        )

        return builder.build()
    }
}
