package realestate.unlock.dealroom.api.utils.entity.document

import realestate.unlock.dealroom.api.core.entity.document.DocumentSigner
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

class DocumentSignerBuilder {
    var documentId: Long = 1
    var envelopeId: String = anyString()
    var docusignDocumentId: String = anyString()
    var signerRecipientId: String = anyString()
    var signerEmail: String? = anyString()
    var signerFirstName: String? = anyString()
    var signerLastName: String? = anyString()
    var signerTeamType: MemberDealTeam = MemberDealTeam.SELLER
    var signCompletedAt: OffsetDateTime? = null
    var createdAt: OffsetDateTime = OffsetDateTime.now()

    fun setSignCompleted(setCompleted: Boolean) =
        this.apply { signCompletedAt = if (setCompleted) OffsetDateTime.now() else null }
    fun setBuyerMemberTeam() = this.apply { signerTeamType = MemberDealTeam.BUYER }

    fun build() = DocumentSigner(
        documentId = documentId,
        signerTeamType = signerTeamType,
        signerEmail = signerEmail,
        signerFirstName = signerFirstName,
        signerLastName = signerLastName,
        envelopeId = envelopeId,
        docusignDocumentId = docusignDocumentId,
        createdAt = createdAt,
        signCompletedAt = signCompletedAt,
        signerRecipientId = signerRecipientId
    )
}
