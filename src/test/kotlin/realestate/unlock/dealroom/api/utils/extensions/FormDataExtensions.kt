package realestate.unlock.dealroom.api.utils.extensions

fun fileInStringJsonFormat(fileUID: String = anyString(), fileName: String = anyString()) = """{"file":[{"uid":"$fileUID","name":"$fileName"}]}""".trimIndent()

fun filesInStringJsonFormat(fileNames: List<String> = listOf(anyString())) =
    """{"file":${fileNames.map {"""{"uid":"${anyString()}","name":"$it"}"""}}}""".trimIndent()

fun fileInStringJsonFormatValue(fileUID: String = anyString(), fileName: String = anyString()) = """[{"uid":"$fileUID","name":"$fileName"}]""".trimIndent()

fun filesInStringJsonFormatValue(fileUIDs: List<String>) = "["
    .plus(
        fileUIDs.joinToString(",") {
            "{\"uid\":\"$it\",\"name\":\"name_of_$it\"}"
        }
    )
    .plus("]")
