package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.RentStepType
import realestate.unlock.dealroom.api.core.entity.loi.ClosingCost
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.repository.loi.SaveLetterOfIntentFile
import realestate.unlock.dealroom.api.core.repository.loi.SaveMedicalLetterOfIntentRound
import realestate.unlock.dealroom.api.core.repository.loi.SaveOfferCustomSection
import realestate.unlock.dealroom.api.core.repository.loi.SaveOfferCustomSections
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate

@Suppress("MemberVisibilityCanBePrivate")
class SaveMedicalLetterOfIntentRoundBuilder {
    var dealId: Long = 4L
    var status: LetterOfIntentRoundStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION
    var memberId: Long = 3L
    var dealTeam: String = "buyer"
    var tenantName = anyString()
    var propertySquareFootage: BigDecimal = BigDecimal.TEN
    var vertical: String = "Medical"
    var broker: String = "broker"
    var brokerCompanyName: String = "broker company name"
    var files = listOf<SaveLetterOfIntentFile>()
    var salesPrice: BigDecimal = BigDecimal.TEN
    var leaseRent: BigDecimal = BigDecimal.TEN
    var leaseType = "NNN"
    var leaseCondition = "condition"
    var leaseRentIncrease = 1.0f
    var leaseIncreaseEveryYear = 1.5f
    var leaseLength = 10L
    var leaseExpirationYear = 2030L
    var leaseNumberOfOptions = 4L
    var leaseOptionLengths = 2L
    var closingPeriod = 45L
    var closingPeriodExtension = 60L
    var closingExtensionDeposit: BigDecimal = BigDecimal.ONE
    var contractTermination: LocalDate = LocalDate.now()
    var earnestMoneyDeposit: BigDecimal = BigDecimal.ONE
    var dueDiligenceNumber: Long = 10
    var comments = anyString()
    var offerRentCpi: Float? = 1f
    var offerRentStepType = RentStepType.CPI
    var offerCustomSections: SaveOfferCustomSections? = SaveOfferCustomSections(
        sections = listOf(
            SaveOfferCustomSection(
                title = "section title",
                content = "section content"
            )
        )
    )
    var guaranteeType: GuaranteeType = GuaranteeType.CORPORATE
    var offerClosingCost: ClosingCost = ClosingCost.DEFAULT

    fun build() = SaveMedicalLetterOfIntentRound(
        dealId = dealId,
        status = status,
        tenantName = tenantName,
        propertySquareFootage = propertySquareFootage,
        vertical = vertical,
        brokerName = broker,
        brokerCompanyName = brokerCompanyName,
        offerFiles = files,
        offerPrice = salesPrice,
        offerLeaseRent = leaseRent,
        offerLeaseType = leaseType,
        offerLeaseCondition = leaseCondition,
        offerLeaseRentIncrease = leaseRentIncrease,
        offerLeaseIncreaseEveryYear = leaseIncreaseEveryYear,
        offerLeaseLength = leaseLength,
        offerLeaseExpirationYear = leaseExpirationYear,
        offerLeaseNumberOfOptions = leaseNumberOfOptions,
        offerLeaseOptionLengths = leaseOptionLengths,
        offerClosingPeriod = closingPeriod,
        offerClosingPeriodExtension = closingPeriodExtension,
        offerClosingExtensionDeposit = closingExtensionDeposit,
        offerContractTermination = contractTermination,
        offerEarnestMoneyDeposit = earnestMoneyDeposit,
        offerDueDiligenceNumber = dueDiligenceNumber,
        offerComments = comments,
        memberId = memberId,
        dealTeam = dealTeam,
        offerRentCpi = offerRentCpi,
        offerRentStepType = offerRentStepType,
        offerCustomSections = offerCustomSections,
        offerClosingCost = offerClosingCost,
        guaranteeType = guaranteeType
    )
}
