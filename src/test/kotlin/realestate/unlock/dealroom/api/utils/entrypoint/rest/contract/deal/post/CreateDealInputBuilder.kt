package realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post

import realestate.unlock.dealroom.api.core.entity.deal.DealType
import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.RentStepType
import realestate.unlock.dealroom.api.core.entity.deal.inputs.ContractExecutionInput
import realestate.unlock.dealroom.api.core.entity.deal.inputs.DealDataInput
import realestate.unlock.dealroom.api.core.entity.deal.inputs.LeaseDataInput
import realestate.unlock.dealroom.api.core.entity.deal.inputs.MembersInput
import realestate.unlock.dealroom.api.core.entity.property.*
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.create.CreateAcquisitionDealInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.create.CreateSaleLeasebackDealInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.create.DealCreationType
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal

class CreateDealInputBuilder {
    var sellerMemberId: Long? = 1
    var buyerMemberId: Long = 1
    var propertyKeywayId: String? = anyString()
    var propertyStreet: String = "Test Street"
    var propertyApartment: String? = "2 C"
    var propertyName: String? = null
    var propertyCity: String = "Miami"
    var propertyState: String = "Florida"
    var propertyZip: String = "667788"
    var propertyYearBuilt: Int? = 2005
    var propertySquareFootage: BigDecimal? = BigDecimal.TEN
    var propertyAskingPrice: BigDecimal? = BigDecimal.ONE
    var earnestMoneyDeposit: BigDecimal? = BigDecimal.TEN
    var extensionDeposit: BigDecimal? = BigDecimal.TEN
    var propertyLatitude: BigDecimal? = BigDecimal.ONE
    var propertyLongitude: BigDecimal? = BigDecimal.ONE
    var leaseExpirationYear: Long? = 2032
    var leaseLength: Long? = 10L
    var leaseNumberOfOptions: Long? = 2L
    var leaseOptionLengths: Long? = 1L
    var leaseRent: BigDecimal? = BigDecimal.TEN
    var leaseRentCpi: Float? = 2.2f
    var leaseRentIncrease: Float? = 10.4f
    var leaseIncreaseEveryYear: Float? = 10.4f
    var leaseRentStepType: RentStepType? = RentStepType.MIXED
    var leaseType: String? = "NNN"
    var offerPrice: BigDecimal? = null
    var sellerBrokerMemberId: Long? = null
    var tenantName: String? = "some tenant"
    var vertical: String? = "Medical"
    var guaranteeType: GuaranteeType? = GuaranteeType.CORPORATE
    var dueDiligenceNumber: Long? = 1
    var closingPeriod: Long? = 1
    var closingPeriodExtension: Long? = 1
    var estimateId: String? = null
    var propertyType: PropertyType = PropertyType.MEDICAL
    var tags: Set<String> = emptySet()
    var brokerFirm: String? = anyString()
    var units: Long? = 10
    var averageSquareFootage: BigDecimal? = BigDecimal.TEN
    var occupancy: BigDecimal? = BigDecimal.TEN
    var parkingSpots: Int? = 10
    var parkingRatio: BigDecimal? = BigDecimal.ONE
    var owner: String? = anyString()
    var propertyManager: String? = anyString()
    var unitsMixDataRequests: List<UnitsMix> = listOf(UnitsMix(2, 10, BigDecimal.TEN, false, BigDecimal.valueOf(321), BigDecimal.valueOf(612)))
    var firstPassId: String? = null

    fun buildSaleLeaseback() = CreateSaleLeasebackDealInput(
        type = DealCreationType.saleLeaseback,
        property = propertyInput(),
        dealData = DealDataInput(
            estimateId = this.estimateId,
            guaranteeType = this.guaranteeType,
            vertical = this.vertical,
            type = DealType.SALE_LEASEBACK,
            offerPrice = this.offerPrice,
            tags = this.tags,
            earnestMoneyDeposit = this.earnestMoneyDeposit,
            extensionDeposit = this.extensionDeposit,
            firstPassId = firstPassId
        ),
        leaseData = LeaseDataInput(
            expirationYear = this.leaseExpirationYear,
            length = this.leaseLength,
            numberOfOptions = this.leaseNumberOfOptions,
            optionLengths = this.leaseOptionLengths,
            rent = this.leaseRent,
            rentCpi = this.leaseRentCpi,
            rentIncrease = this.leaseRentIncrease,
            increaseEveryYear = this.leaseIncreaseEveryYear,
            rentStepType = this.leaseRentStepType,
            type = this.leaseType
        ),
        members = MembersInput(
            dealMembers = setOfNotNull(sellerMemberId, sellerBrokerMemberId, buyerMemberId),
            leaderId = this.buyerMemberId,
            tenantName = this.tenantName
        ),
        contractExecution = ContractExecutionInput(
            dueDiligenceNumber = this.dueDiligenceNumber,
            closingPeriod = this.closingPeriod,
            closingPeriodExtension = this.closingPeriodExtension
        )
    )

    fun buildAcquisition() = CreateAcquisitionDealInput(
        type = DealCreationType.acquisition,
        property = propertyInput(),
        dealData = DealDataInput(
            estimateId = this.estimateId,
            guaranteeType = this.guaranteeType,
            vertical = this.vertical,
            type = DealType.ACQUISITION,
            tags = this.tags,
            offerPrice = this.offerPrice,
            earnestMoneyDeposit = this.earnestMoneyDeposit,
            extensionDeposit = this.extensionDeposit,
            firstPassId = firstPassId
        ),
        members = MembersInput(
            dealMembers = setOfNotNull(sellerMemberId, sellerBrokerMemberId, buyerMemberId),
            leaderId = this.buyerMemberId,
            tenantName = this.tenantName
        ),
        contractExecution = ContractExecutionInput(
            dueDiligenceNumber = this.dueDiligenceNumber,
            closingPeriod = this.closingPeriod,
            closingPeriodExtension = this.closingPeriodExtension
        )
    )

    private fun propertyInput() =
        PropertyCreationByAddressInput(
            street = this.propertyStreet,
            apartment = this.propertyApartment,
            city = this.propertyCity,
            state = this.propertyState,
            zip = this.propertyZip,
            keywayId = this.propertyKeywayId,
            yearBuilt = this.propertyYearBuilt,
            squareFootage = this.propertySquareFootage,
            askingPrice = this.propertyAskingPrice,
            latitude = this.propertyLatitude,
            longitude = this.propertyLongitude,
            name = this.propertyName,
            type = this.propertyType,
            multifamilyData = if (this.propertyType == PropertyType.MEDICAL) {
                null
            } else {
                MultifamilyDataInput(
                    brokerFirm,
                    units,
                    averageSquareFootage,
                    occupancy,
                    parkingSpots,
                    parkingRatio,
                    owner,
                    propertyManager,
                    unitsMixDataRequests
                )
            }
        )
}
