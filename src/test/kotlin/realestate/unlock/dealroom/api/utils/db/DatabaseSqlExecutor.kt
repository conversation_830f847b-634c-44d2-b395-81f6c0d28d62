package realestate.unlock.dealroom.api.utils.db

import realestate.unlock.dealroom.api.infrastructure.client.database.extension.execute
import javax.sql.DataSource

object DatabaseSqlExecutor {

    fun executeFromFile(location: String, dataSource: DataSource, params: Map<String, String> = mapOf()) {
        var query = javaClass.classLoader.getResource(location).readText().trimIndent()
        params.forEach { entry -> query = query.replace("%${entry.key}%", entry.value) }
        dataSource.execute(query)
    }
}
