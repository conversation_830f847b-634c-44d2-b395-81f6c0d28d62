package realestate.unlock.dealroom.api.utils.entity.deal

import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.task.TaskWithFilesAndHistory

fun CompleteDeal.getRandomTask(exceptTaskId: Long? = null): Task =
    this.getAllTasks(exceptTaskId).first()

fun CompleteDeal.getAllTasks(exceptTaskId: Long? = null): List<Task> {
    val list = mutableListOf<Task>()
    this.categories.forEach {
        list.addAll(it.tasksWithFilesAndHistory.map(TaskWithFilesAndHistory::task))
    }
    return exceptTaskId?.let { list.filter { it.id != exceptTaskId } } ?: list
}

fun CompleteDeal.findOneTask(templateKey: String? = null) =
    when {
        templateKey != null -> this.findTaskByTemplateKey(templateKey)
        else -> this.getRandomTask()
    }

fun CompleteDeal.findTaskByTemplateKey(templateKey: String) =
    this.getAllTasks().first { it.templateKey == templateKey }
