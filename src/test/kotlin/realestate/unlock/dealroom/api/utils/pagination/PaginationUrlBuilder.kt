package realestate.unlock.dealroom.api.utils.pagination

import realestate.unlock.dealroom.api.core.entity.search.SortOrder
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.net.URLEncoder

class PaginationUrlBuilder {

    var filter: Map<String, Any> = emptyMap()
    var orderBy: String = ""
    var order: SortOrder = SortOrder.DESC
    var size: Int = 1000
    var offset: Int = 0
    var total: Int = 0

    fun withOffset(offset: Int) = apply { this.offset = offset }
    fun withSize(size: Int) = apply { this.size = size }
    fun withOrder(order: SortOrder) = apply { this.order = order }
    fun withOrderBy(orderBy: String) = apply { this.orderBy = orderBy }
    fun withFilter(filter: Map<String, Any>) = apply { this.filter = filter }
    fun withTotal(total: Int) = apply { this.total = total }

    fun getUrl(url: String): String =
        "$url?" +
            "size=$size&" +
            "offset=$offset&" +
            "filter=${URLEncoder.encode(JsonMapper.encode(filter), "UTF-8")}&" +
            "orderBy=$orderBy" +
            "&order=${order.name}" +
            "&total=$total"
}
