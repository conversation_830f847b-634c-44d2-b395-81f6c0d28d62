package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.loi.Closing
import java.math.BigDecimal

class ClosingBuilder {

    var period: Long = 4
    var periodExtension: Long = 2
    var extensionDeposit: BigDecimal = BigDecimal.TEN

    fun build() = Closing(
        period = period,
        periodExtension = periodExtension,
        extensionDeposit = extensionDeposit
    )
}
