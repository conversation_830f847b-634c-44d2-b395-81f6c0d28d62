package realestate.unlock.dealroom.api.utils.entity.document

import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.Clock
import java.time.OffsetDateTime

class DocumentBuilder {

    var id: Long = anyId()
    var dealId: Long = anyId()
    var status: DocumentStatus = DocumentStatus.NOT_STARTED
    var currentRoundId: Long? = null
    var executed: OffsetDateTime? = null
    var draftSubmitted: OffsetDateTime? = null
    var expectedBy: OffsetDateTime? = null
    var type: DocumentType = DocumentType.PSA

    fun build() = Document(
        id = id,
        dealId = dealId,
        status = status,
        currentRoundId = currentRoundId,
        executedAt = executed,
        draftSubmittedAt = draftSubmitted,
        expectedBy = expectedBy,
        createdAt = OffsetDateTime.now(Clock.systemUTC()),
        updatedAt = OffsetDateTime.now(Clock.systemUTC()),
        type = type
    )
}
