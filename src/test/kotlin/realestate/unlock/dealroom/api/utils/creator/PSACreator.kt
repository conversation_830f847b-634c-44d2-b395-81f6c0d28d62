package realestate.unlock.dealroom.api.utils.creator

import org.eclipse.jetty.http.HttpHeader
import org.junit.jupiter.api.Assertions.assertEquals
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.InteractionRequest
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.documents.psa.InteractionRequestFactory
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.nio.charset.Charset
import java.time.Duration

object PSACreator {

    private val client = HttpClient.newBuilder()
        .version(HttpClient.Version.HTTP_2)
        .build()

    fun createByRest(
        dealId: Long,
        localUrl: String = "http://localhost:8080",
        interactionRequest: InteractionRequest = InteractionRequestFactory.draft(),
        token: String = AuthMock.authToken
    ) {
        val givenUrl = "$localUrl/deal/$dealId/documents/psa"

        val result = client.send(
            HttpRequest.newBuilder()
                .uri(URI(givenUrl))
                .POST(HttpRequest.BodyPublishers.ofString(JsonMapper.encode(interactionRequest), Charset.defaultCharset()))
                .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
                .timeout(Duration.ofSeconds(10))
                .build(),
            HttpResponse.BodyHandlers.discarding()
        ).statusCode()

        assertEquals(201, result)
    }
}
