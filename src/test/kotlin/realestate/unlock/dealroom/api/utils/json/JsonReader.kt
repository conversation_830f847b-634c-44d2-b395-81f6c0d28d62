package realestate.unlock.dealroom.api.utils.json

object JsonReader {

    fun fromFile(path: String, params: Map<String, String> = mapOf()): String =
        javaClass.classLoader.getResourceAsStream(path).reader().readText()
            .let { rawJson ->
                params.entries.fold(rawJson) { acc, entry ->
                    acc.replace("%${entry.key}%", entry.value)
                }
            }
}
