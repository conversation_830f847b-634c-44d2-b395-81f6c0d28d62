package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.loi.MemberSign
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.SellerSignerRequest
import java.time.OffsetDateTime
import java.util.*

class MemberSignBuilder {
    private var id: String = UUID.randomUUID().toString()
    var completedAt: OffsetDateTime? = null
    private var email: String = "<EMAIL>"
    private var firstName: String = "churros"
    private var lastName: String = "el topo"
    private var memberType: MemberTypeEnum = MemberTypeEnum.SELLER

    fun forBuyer(id: Long = 1) = apply {
        this.id = id.toString()
        this.memberType = MemberTypeEnum.BUYER
        this.email = "<EMAIL>"
    }

    fun build() = MemberSign(
        id = this.id,
        completedAt = this.completedAt,
        memberType = this.memberType,
        email = this.email,
        firstName = this.firstName,
        lastName = this.lastName
    )

    fun getSellerSignRequest() = SellerSignerRequest(
        email = this.email,
        firstName = this.firstName,
        lastName = this.lastName
    )
}
