package realestate.unlock.dealroom.api.utils.mocks.provider

import com.keyway.kommons.aws.config.AwsConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sns.AwsSNSConfig
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sns.SnsAsyncClient
import software.amazon.awssdk.services.sns.model.CreateTopicRequest
import java.net.URI
import javax.inject.Inject
import javax.inject.Provider

class MockAwsSNSClientProvider @Inject constructor(
    private val awsSNSConfig: AwsSNSConfig,
    private val awsCredentials: AwsConfig
) : Provider<SnsAsyncClient> {

    private val awsProtocol = "http://"
    private val localStackPath = "localstack:4566"
    private val awsRegion = Region.US_EAST_1

    override fun get(): SnsAsyncClient {
        val builder = SnsAsyncClient.builder()
            .region(Region.of(awsCredentials.region))

        if (awsCredentials.endpointOverride != null) {
            builder.endpointOverride(URI.create(awsCredentials.endpointOverride))
        }

        builder.credentialsProvider(
            StaticCredentialsProvider.create(
                AwsBasicCredentials.create(
                    awsCredentials.accessKey,
                    awsCredentials.secretKey
                )
            )
        )

        return builder.build().also { snsClient ->
            snsClient.createTopic(
                CreateTopicRequest.builder().name(
                    awsSNSConfig.taskChangedTopicArn.split(":").last()
                ).build()
            )
        }
    }
}
