package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.loi.ClosingCost
import realestate.unlock.dealroom.api.core.entity.loi.Loan
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiRoundType
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.*
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate

@Suppress("MemberVisibilityCanBePrivate")
class PostMultifamilyLoiRequestBuilder {
    var type = LoiRoundType.MULTIFAMILY
    var broker: String = "broker"
    var brokerCompanyName: String = "broker company name"
    var offerFiles: List<LetterOfIntentFileRequest> = listOf(
        LetterOfIntentFileRequest(uid = anyString(), name = anyString())
    )
    var legalEntity: String = "legal"
    var loan: Loan = Loan.FREE_AND_CLEAR
    var offerPrice: BigDecimal = BigDecimal.TEN
    var offerDeposit: BigDecimal = BigDecimal.TEN
    var offerClosingExtensionDeposit: BigDecimal = BigDecimal.TEN
    var dueDiligencePeriod: Long = 10L
    var dueDiligenceExtensionPeriod: Long = 10L
    var closingPeriod: Long = 10L
    var closingExtensionPeriod: Long = 10L
    var closingCost: ClosingCost = ClosingCost.DEFAULT
    var offerCustomSections: OfferCustomSectionsRequest = OfferCustomSectionsRequest(
        sections = listOf(
            OfferCustomSectionRequest(
                title = "some title",
                content = "some content"
            )
        )
    )
    var dueDate: LocalDate? = null
    val emailOptions: EmailOptionsRequest = EmailOptionsRequest(
        sendToBroker = true,
        sendToSeller = true
    )
    var executedAt = LocalDate.now().minusDays(3)
    var propertySquareFootage: Int = 10000

    fun build() = PostMultifamilyLoiRoundRequest(
        type = type,
        offerFiles = offerFiles,
        offerClosingExtensionDeposit = offerClosingExtensionDeposit,
        offerCustomSections = offerCustomSections,
        emailOptions = emailOptions,
        dueDate = dueDate,
        brokerName = broker,
        brokerCompanyName = brokerCompanyName,
        legalEntity = legalEntity,
        loan = loan,
        offerPrice = offerPrice,
        offerDeposit = offerDeposit,
        dueDiligencePeriod = dueDiligencePeriod,
        dueDiligenceExtensionPeriod = dueDiligenceExtensionPeriod,
        closingPeriod = closingPeriod,
        closingExtensionPeriod = closingExtensionPeriod,
        closingCost = closingCost,
        propertySquareFootage = propertySquareFootage
    )

    fun buildExecuted() = ExecutedMultifamilyLoiInput(
        type = type,
        files = offerFiles,
        offerClosingExtensionDeposit = offerClosingExtensionDeposit,
        executedAt = executedAt,
        brokerName = broker,
        brokerCompanyName = brokerCompanyName,
        legalEntity = legalEntity,
        loan = loan,
        offerPrice = offerPrice,
        offerDeposit = offerDeposit,
        dueDiligencePeriod = dueDiligencePeriod,
        dueDiligenceExtensionPeriod = dueDiligenceExtensionPeriod,
        closingPeriod = closingPeriod,
        closingExtensionPeriod = closingExtensionPeriod,
        closingCost = closingCost,
        propertySquareFootage = propertySquareFootage
    )
}
