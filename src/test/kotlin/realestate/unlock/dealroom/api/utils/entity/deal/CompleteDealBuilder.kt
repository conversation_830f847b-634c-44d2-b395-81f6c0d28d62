package realestate.unlock.dealroom.api.utils.entity.deal

import realestate.unlock.dealroom.api.core.entity.deal.*
import realestate.unlock.dealroom.api.core.entity.deal.category.CompleteDealCategory
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

class CompleteDealBuilder {

    var id: Long = 1
    var propertyId: Long = 1
    var stage = Stage.EVALUATION
    var loiExecutedDate: LocalDate? = LocalDate.now()
    var contractExecutedDate: LocalDate? = null
    var diligenceExpirationDate: LocalDate? = null
    var initialClosingDate: LocalDate? = null
    var outsideClosingDate: LocalDate? = null
    var updatedAt: OffsetDateTime = OffsetDateTime.now()
    var createdAt: OffsetDateTime = OffsetDateTime.now()
    var property: Property = PropertyBuilder().withId(propertyId).build()
    var members: Set<Member> = setOf(
        MemberObjectMother.buyer(),
        MemberObjectMother.seller()
    )
    var categories: List<CompleteDealCategory> = listOf()
    var dealType: DealType? = DealType.SALE_LEASEBACK
    var tenantName: String? = "some tenant"
    var vertical: String? = "Medical"
    var guaranteeType: GuaranteeType? = GuaranteeType.CORPORATE
    var lease: DealLease = DealLeaseBuilder().build()
    var status: DealStatus = DealStatus.ACTIVE
    var schemaId: Long = 1L
    var tags: Set<String> = emptySet()
    var offerPrice: BigDecimal? = null
    var schemaData: SchemaData = SchemaData(anyId(), emptyMap())
    var extensionDeposit: BigDecimal? = null
    var earnestMoneyDeposit: BigDecimal? = null
    var hasFindings: Boolean = false
    var buyerCompanyName: String? = "buyer company name"
    var brokerCompanyName: String? = "broker company name"
    var sellerCompanyName: String? = "seller company name"
    var firstPassId: String? = null

    fun build() = CompleteDeal(
        id = id,
        propertyId = propertyId,
        status = status,
        stage = stage,
        loiExecutedDate = loiExecutedDate,
        contractExecutedDate = contractExecutedDate,
        diligenceExpirationDate = diligenceExpirationDate,
        initialClosingDate = initialClosingDate,
        outsideClosingDate = outsideClosingDate,
        offerPrice = offerPrice,
        updatedAt = updatedAt,
        createdAt = createdAt,
        property = property,
        members = members,
        categories = categories,
        type = dealType,
        tenantName = tenantName,
        vertical = vertical,
        guaranteeType = guaranteeType,
        lease = lease,
        schemaData = schemaData,
        omFileId = null,
        sourceType = null,
        tags = tags,
        leaderId = members.first { it.team == MemberDealTeam.BUYER }.id,
        earnestMoneyDeposit = earnestMoneyDeposit,
        extensionDeposit = extensionDeposit,
        hasFindings = hasFindings,
        buyerCompanyName = buyerCompanyName,
        brokerCompanyName = brokerCompanyName,
        sellerCompanyName = sellerCompanyName,
        firstPassId = firstPassId
    )
}
