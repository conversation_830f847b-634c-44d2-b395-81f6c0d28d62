package realestate.unlock.dealroom.api.utils.rest

import com.fasterxml.jackson.core.type.TypeReference
import kong.unirest.Unirest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.DocumentResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.DocumentType
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.mocks.AuthMock

object DocumentFinder {

    fun get(
        documentType: DocumentType,
        dealId: Long,
        localUrl: String = "http://localhost:8080",
        token: String = AuthMock.authToken
    ): DocumentResponse {
        val documentsUrl = "$localUrl/deal/$dealId/documents"

        val documents = Unirest.get(documentsUrl)
            .headers(AuthMock.getAuthHeader(token))
            .asJson().let {
                JsonMapper.decode(
                    it.body.toPrettyString(),
                    object : TypeReference<List<DocumentResponse>>() {}
                )
            }
        return documents.first { it.type == documentType }
    }
}
