package realestate.unlock.dealroom.api.utils.db

import org.flywaydb.core.Flyway
import realestate.unlock.dealroom.api.infrastructure.client.database.AppDataSource
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DatabaseConfig

object DatabaseMigration {

    fun migrate(config: DatabaseConfig) {
        Flyway.configure()
            .dataSource(AppDataSource.getInstance(config))
            .locations("/db/migration")
            .load()
            .migrate()
    }
}
