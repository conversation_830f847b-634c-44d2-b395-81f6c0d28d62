package realestate.unlock.dealroom.api.utils.entity.document

import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

class InteractionBuilder {
    var id: Long = anyId()
    var documentId: Long = anyId()
    var type: Interaction.Type = Interaction.Type.NEW_DRAFT
    var comment: String = anyString()
    var date: OffsetDateTime = OffsetDateTime.now()
    var memberId: Long = anyId()
    var files: List<File> = listOf()

    fun build() = Interaction(
        id = id,
        roundId = documentId,
        type = type,
        comment = comment,
        date = date,
        memberId = memberId,
        files = files
    )
}
