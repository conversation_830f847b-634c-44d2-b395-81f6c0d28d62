package realestate.unlock.dealroom.api.utils.entity.document

import realestate.unlock.dealroom.api.core.entity.document.DraftDocuments
import realestate.unlock.dealroom.api.core.entity.document.ExecutedDocument
import realestate.unlock.dealroom.api.core.entity.document.FinalDocument
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString

object DocumentRoundObjectMother {

    fun finalDocument(
        id: Long = anyId(),
        documentId: Long = anyId(),
        fileId: Long = anyId(),
        comment: String = anyString(),
        memberId: Long = anyId()
    ) = FinalDocument(
        id = id,
        documentId = documentId,
        fileId = fileId,
        comment = comment,
        memberId = memberId
    )

    fun draftDocuments(
        id: Long = anyId(),
        documentId: Long = anyId(),
        cleanVersionFileId: Long? = anyId(),
        redLineVersionFileId: Long? = anyId(),
        comment: String? = anyString(),
        memberId: Long = anyId()
    ) = DraftDocuments(
        id = id,
        documentId = documentId,
        cleanVersionFileId = cleanVersionFileId,
        redLineVersionFileId = redLineVersionFileId,
        comment = comment,
        memberId = memberId
    )

    fun executedDocument(
        id: Long = anyId(),
        documentId: Long = anyId(),
        fileId: Long = anyId()
    ) = ExecutedDocument(
        id = id,
        documentId = documentId,
        fileId = fileId
    )
}
