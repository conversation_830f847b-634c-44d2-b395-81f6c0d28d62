package realestate.unlock.dealroom.api.utils.entity.property

import realestate.unlock.dealroom.api.core.entity.property.*
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.OffsetDateTime

class PropertyBuilder {

    var id: Long = 1L
    var name: String? = anyString()
    var address: Address = Address(
        street = anyString(),
        apartment = null,
        city = "New York",
        state = "NY",
        zip = "101010",
        coordinates = null
    )
    var keywayId: String = anyString()

    var yearBuilt: Int? = 1984
    var squareFootage: BigDecimal? = BigDecimal.ONE
    var askingPrice: BigDecimal? = null
    var mainPhotoId: String? = null
    val interiorPhotoId: String? = null
    var type: PropertyType = PropertyType.MEDICAL
    var brokerFirm: String? = null
    var units: Long? = null
    var averageSquareFootage: BigDecimal? = null
    var occupancy: BigDecimal? = null
    var parkingSpots: Int? = null
    var parkingRatio: BigDecimal? = null
    var owner: String? = null
    var propertyManager: String? = null
    var unitsMix: List<UnitsMix> = listOf()

    fun withId(id: Long) = apply { this.id = id }

    fun build() = Property(
        id = id,
        name = name,
        address = address,
        keywayId = keywayId,
        yearBuilt = yearBuilt,
        squareFootage = squareFootage,
        askingPrice = askingPrice,
        mainPhotoId = mainPhotoId,
        interiorPhotoId = interiorPhotoId,
        interiorPhoto = null,
        mainPhoto = null,
        createdAt = OffsetDateTime.now(),
        updatedAt = OffsetDateTime.now(),
        type = type,
        multifamilyData = if (type == PropertyType.MEDICAL) {
            null
        } else {
            MultifamilyData(
                brokerFirm, units, averageSquareFootage, occupancy, parkingSpots, parkingRatio, owner, propertyManager, unitsMix
            )
        }
    )
}
