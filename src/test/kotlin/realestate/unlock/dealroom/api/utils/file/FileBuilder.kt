package realestate.unlock.dealroom.api.utils.file

import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

class FileBuilder {

    var id: Long = anyId()
    var name: String = anyString()
    var kFileId: String = anyString()
    var memberId: Long = anyId()
    var createdAt: OffsetDateTime = OffsetDateTime.now()

    fun build() = File(
        id = id,
        name = name,
        kFileId = kFileId,
        memberId = memberId,
        createdAt = createdAt
    )
}
