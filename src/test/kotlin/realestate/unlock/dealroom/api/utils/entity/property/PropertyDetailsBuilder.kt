package realestate.unlock.dealroom.api.utils.entity.property

import realestate.unlock.dealroom.api.core.entity.property.Coordinates
import realestate.unlock.dealroom.api.core.gateway.property.PropertyDetails
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate

class PropertyDetailsBuilder {

    var keywayId: String = anyString()
    var name: String? = anyString()
    var address: String = anyString()
    var city: String = anyString()
    var state: String = anyString()
    var price: BigDecimal = BigDecimal.TEN
    var squareFootage: BigDecimal = BigDecimal.ONE
    var leaseType: String = "NNN"
    var rentBumpsDescription: String = anyString()
    var constructionYear: Int = 1990
    var leaseLength: Int = 2
    var latitude: BigDecimal = BigDecimal.ONE
    var longitude: BigDecimal = BigDecimal.ONE
    var tenant: String = anyString()
    var vertical: String = "Medical"
    var rent: BigDecimal = BigDecimal.TEN
    var rentBumpsFrequencyInYears: BigDecimal = BigDecimal.TEN
    var optionLength: BigDecimal = BigDecimal.ONE
    var numberOfOptions: BigDecimal = BigDecimal.ONE
    var diligencePeriod: BigDecimal? = BigDecimal.TEN
    var closingPeriod: BigDecimal? = BigDecimal.TEN
    var closingPeriodExtension: BigDecimal? = BigDecimal.TEN
    var earnestMoneyDeposit: BigDecimal? = BigDecimal.TEN
    var closingExtensionDeposit: BigDecimal? = BigDecimal.TEN
    var contractExecutionDate: LocalDate? = LocalDate.parse("2022-02-02")
    var guaranteeDetails: String? = "Guarantee details"
    var leaseCondition: String? = "Lease condition"
    var creditType: String? = "Credit type"
    var rentStepType: String? = "Rent step type"
    var cpiMultiplier: BigDecimal? = BigDecimal.TEN
    var rentBumpsAmount: BigDecimal? = BigDecimal.valueOf(11)
    var askingPrice: BigDecimal? = BigDecimal(51231)
    var units: Int? = null

    fun build() =
        PropertyDetails(
            keywayId = keywayId,
            name = name,
            address = address,
            city = city,
            state = state,
            squareFootage = squareFootage,
            constructionYear = constructionYear,
            coordinates = Coordinates(latitude, longitude),
            zipCode = anyString(),
            tenant = tenant,
            vertical = vertical,
            askingPrice = askingPrice,
            units = units
        )
}
