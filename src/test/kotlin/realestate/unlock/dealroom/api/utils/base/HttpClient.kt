package realestate.unlock.dealroom.api.utils.base

import org.eclipse.jetty.http.HttpHeader
import org.junit.jupiter.api.Assertions
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.nio.charset.Charset
import java.time.Duration

object HttpClient {
    private val client = HttpClient.newBuilder()
        .version(HttpClient.Version.HTTP_2)
        .build()

    private fun <T> executeRequest(
        httpRequest: HttpRequest,
        expectedStatus: Int? = null,
        responseHandler: (String) -> T
    ): T {
        val response = executeRequest(httpRequest)

        if (expectedStatus != null) {
            Assertions.assertEquals(expectedStatus, response.statusCode(), "Body: " + response.body())
        }

        return responseHandler(response.body())
    }

    fun executeRequest(httpRequest: HttpRequest) =
        client.send(httpRequest, HttpResponse.BodyHandlers.ofString())

    fun <T> get(
        url: String,
        token: String = AuthMock.authToken,
        expectedStatus: Int? = null,
        responseHandler: (String) -> T
    ): T {
        val request = HttpRequest.newBuilder()
            .uri(URI(url))
            .GET()
            .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
            .timeout(Duration.ofSeconds(10))
            .build()

        return executeRequest(
            httpRequest = request,
            expectedStatus = expectedStatus,
            responseHandler = responseHandler
        )
    }

    fun <T> put(
        url: String,
        body: String,
        token: String = AuthMock.authToken,
        expectedStatus: Int? = null,
        responseHandler: (String) -> T
    ): T {
        val request = HttpRequest.newBuilder()
            .uri(URI(url))
            .PUT(HttpRequest.BodyPublishers.ofString(body, Charset.defaultCharset()))
            .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
            .timeout(Duration.ofSeconds(10))
            .build()

        return executeRequest(
            httpRequest = request,
            expectedStatus = expectedStatus,
            responseHandler = responseHandler
        )
    }

    fun <T> post(
        url: String,
        body: String,
        token: String = AuthMock.authToken,
        expectedStatus: Int? = null,
        responseHandler: (String) -> T
    ): T {
        val request = HttpRequest.newBuilder()
            .uri(URI(url))
            .POST(HttpRequest.BodyPublishers.ofString(body, Charset.defaultCharset()))
            .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
            .timeout(Duration.ofSeconds(10))
            .build()

        return executeRequest(
            httpRequest = request,
            expectedStatus = expectedStatus,
            responseHandler = responseHandler
        )
    }

    fun <T> patch(
        url: String,
        body: String,
        token: String = AuthMock.authToken,
        expectedStatus: Int? = null,
        responseHandler: (String) -> T
    ): T {
        val request =
            HttpRequest.newBuilder()
                .uri(URI(url))
                .method("PATCH", HttpRequest.BodyPublishers.ofString(body, Charset.defaultCharset()))
                .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
                .timeout(Duration.ofSeconds(10))
                .build()

        return executeRequest(
            httpRequest = request,
            expectedStatus = expectedStatus,
            responseHandler = responseHandler
        )
    }

    fun <T> delete(
        url: String,
        token: String = AuthMock.authToken,
        body: String? = null,
        expectedStatus: Int? = null,
        responseHandler: (String) -> T
    ): T {
        val bodyPublisher = if (body == null) {
            HttpRequest.BodyPublishers.noBody()
        } else {
            HttpRequest.BodyPublishers.ofString(body, Charset.defaultCharset())
        }
        val request = HttpRequest.newBuilder()
            .uri(URI(url))
            .method("DELETE", bodyPublisher)
            .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
            .timeout(Duration.ofSeconds(10))
            .build()

        return executeRequest(
            httpRequest = request,
            expectedStatus = expectedStatus,
            responseHandler = responseHandler
        )
    }
}
