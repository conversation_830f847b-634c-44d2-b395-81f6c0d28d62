package realestate.unlock.dealroom.api.utils.entity

import realestate.unlock.dealroom.api.core.entity.category.Category
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString

class CategoryBuilder {

    var id = anyId()
    var key = anyString()
    var name = anyString()
    var description = anyString()
    var typeKey = anyString()
    var stage = Stage.DILIGENCE

    fun build() = Category(
        id = id,
        key = key,
        name = name,
        description = description,
        typeKey = typeKey,
        stage = stage
    )
}
