package realestate.unlock.dealroom.api.utils.base

import com.google.inject.Guice
import com.google.inject.util.Modules
import com.sendgrid.Response
import com.sendgrid.SendGrid
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.unmockkAll
import kong.unirest.Unirest
import org.eclipse.jetty.http.HttpHeader
import org.hamcrest.CoreMatchers
import org.hamcrest.MatcherAssert
import org.hamcrest.Matchers
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.infrastructure.Application
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.TransactionalDataSource
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DatabaseConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.SystemConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.parser.ConfigParser
import realestate.unlock.dealroom.api.infrastructure.module.AppModule
import realestate.unlock.dealroom.api.infrastructure.module.RepositoryModule
import realestate.unlock.dealroom.api.utils.db.DatabaseMigration
import realestate.unlock.dealroom.api.utils.db.DatabaseSqlExecutor
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.module.TestingModule
import realestate.unlock.dealroom.api.utils.stub.FeatureFlagsStub
import realestate.unlock.dealroom.api.utils.stub.FileGatewayStub
import java.net.URI
import java.net.http.HttpRequest
import java.time.Duration

abstract class BaseFunctionalTest {

    private val systemConfig: SystemConfig = Context.injector.getInstance(SystemConfig::class.java)
    val localUrl: String = "http://localhost:${systemConfig.httpPort}"

    companion object {
        private var isInitialized = false

        @JvmStatic
        @BeforeAll
        fun init() {
            if (isInitialized.not()) {
                ConfigParser.read(isTest = true)
                Context.injector = Guice.createInjector(
                    Modules
                        .override(AppModule(), RepositoryModule())
                        .with(TestingModule())
                )
                    .also { injector ->
                        injector.getInstance(DatabaseConfig::class.java)
                            .let(DatabaseMigration::migrate)
                    }
                Application.main(arrayOf())
                isInitialized = true
            }
        }
    }

    protected fun testPermissions(method: String, url: String, memberType: String, permissions: List<Permission>, allowed: Boolean, forbiddenMessage: String? = null) {
        // When
        val token = anyString()
        AuthMock.createMemberWithUser(
            uid = token,
            email = "$<EMAIL>",
            memberType = memberType,
            token = token,
            permissions = permissions
        )
        val result = Unirest.request(method, url)
            .headers(AuthMock.getAuthHeader(token))
            .asJson()

        // Then
        if (allowed) {
            MatcherAssert.assertThat(result.status, Matchers.not(Matchers.equalTo(403)))
        } else {
            MatcherAssert.assertThat(result.status, Matchers.equalTo(403))
            if (forbiddenMessage != null) {
                MatcherAssert.assertThat(result.body.toPrettyString(), CoreMatchers.containsString(forbiddenMessage))
            }
        }
    }

    protected abstract fun getPermissionsConfig(): BaseFunctionalPermissionsConfig

    @Test
    fun `Permissions - it need permissions`() {
        val permissionsConfig = getPermissionsConfig()
        if (!permissionsConfig.testPermissions) return
        if (permissionsConfig.permissions.isEmpty()) {
            return // We ignore this test for endpoints that don't need permissions
        }

        // Given
        val token = "permission-test-token"
        AuthMock.createMemberWithUser(
            uid = "permission-test-uid",
            token = token,
            email = "<EMAIL>",
            permissions = emptyList()
        )

        // When
        val result = HttpClient.executeRequest(
            HttpRequest.newBuilder()
                .uri(URI(permissionsConfig.url))
                .method(permissionsConfig.method, HttpRequest.BodyPublishers.noBody())
                .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
                .timeout(Duration.ofSeconds(10))
                .build()
        )

        // Then
        val permissionsList = permissionsConfig.permissions.flatMap { it.keys() }
        val message = "The url ${permissionsConfig.method}:${permissionsConfig.url} should ask for this permissions: $permissionsList"
        MatcherAssert.assertThat(message, result.statusCode(), CoreMatchers.equalTo(403))
        MatcherAssert.assertThat(message, result.body().toString(), CoreMatchers.containsString("Permissions needed: $permissionsList"))
    }

    @Test
    fun `Permissions - it doesn't need permissions`() {
        val permissionsConfig = getPermissionsConfig()
        if (!permissionsConfig.testPermissions) return
        if (permissionsConfig.permissions.isNotEmpty()) {
            return // We ignore this test for endpoints that need permissions
        }

        // Given
        val token = "permission-test-token"
        AuthMock.createMemberWithUser(
            uid = "permission-test-uid",
            token = token,
            email = "<EMAIL>",
            permissions = emptyList()
        )

        // When
        val result = HttpClient.executeRequest(
            HttpRequest.newBuilder()
                .uri(URI(permissionsConfig.url))
                .method(permissionsConfig.method, HttpRequest.BodyPublishers.noBody())
                .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
                .timeout(Duration.ofSeconds(10))
                .build()
        )

        // Then
        val message = "The url ${permissionsConfig.method}:${permissionsConfig.url} should not ask for this permissions"
        MatcherAssert.assertThat(message, result.statusCode(), CoreMatchers.not(CoreMatchers.equalTo(403)))
    }

    @BeforeEach
    fun beforeEach() {
        Context.injector.getInstance(SendGrid::class.java).let { sendgrid ->
            val response: Response = mockk()
            every { response.statusCode } returns 202
            every { sendgrid.api(any()) } returns response
        }
        TransactionManager.initialize(Context.injector.getInstance(TransactionalDataSource::class.java))
    }

    @AfterEach
    fun cleanApplication() {
        deleteTestingData()
        clearStubs()
        clearAllMocks()
        unmockkAll()
    }

    fun configureFeatureFlag(feature: Feature, on: Boolean) {
        val featureFlags = Context.injector.getInstance(FeatureFlags::class.java) as FeatureFlagsStub
        featureFlags.configureFeature(feature = feature, on = on)
    }

    private fun clearStubs() {
        val featureFlags = Context.injector.getInstance(FeatureFlags::class.java) as FeatureFlagsStub
        featureFlags.clear()

        val fileGateway = Context.injector.getInstance(FileGateway::class.java) as FileGatewayStub
        fileGateway.clear()
    }

    private fun deleteTestingData() {
        DatabaseSqlExecutor.executeFromFile(
            location = "sql/delete_testing_data.sql",
            dataSource = Context.injector.getInstance(TransactionalDataSource::class.java)
        )
    }
}
