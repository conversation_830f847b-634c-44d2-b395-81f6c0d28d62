package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.loi.*
import realestate.unlock.dealroom.api.core.entity.loi.file.LetterOfIntentFile
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal

@Suppress("MemberVisibilityCanBePrivate")
class MultifamilyOfferBuilder {

    var files: List<LetterOfIntentFile> = listOf(LetterOfIntentFile(id = 45L, memberId = 23L, name = anyString(), kFileId = anyString()))
    var salesPrice: BigDecimal = BigDecimal.ONE
    var deposit: BigDecimal = BigDecimal.ONE
    var closingExtensionDeposit: BigDecimal = BigDecimal.ONE
    var createdBy = MemberObjectMother.buyer()
    var customSections: OfferCustomSections? = OfferCustomSections(
        sections = listOf(
            OfferCustomSection(
                title = "section title",
                content = "section content"
            )
        )
    )
    var closingCost: ClosingCost = ClosingCost.ORIGINAL

    fun build() = MultifamilyOffer(
        files = files,
        createdBy = createdBy,
        customSections = customSections,
        price = salesPrice,
        deposit = deposit,
        closingExtensionDeposit = closingExtensionDeposit
    )
}
