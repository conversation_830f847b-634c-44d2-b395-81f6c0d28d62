package realestate.unlock.dealroom.api.utils.entity.member

import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.utils.extensions.anyId
import java.time.OffsetDateTime

class MemberBuilder {

    var updatedAt: OffsetDateTime = OffsetDateTime.now()
    var createdAt: OffsetDateTime = OffsetDateTime.now()
    var email: String = "<EMAIL>"
    var phoneNumber: String = "1234567"
    var address: String? = null
    var companyName: String = "MyCompany"
    var firstName: String = "Peter"
    var lastName: String = "Foo"
    var id: Long = anyId()
    var typeKey: String = "seller"
    var authId: String? = null
    var walkThroughDone: Boolean = false
    var organizationId: String = "Keyway"

    fun withId(id: Long): MemberBuilder {
        return apply { this.id = id }
    }

    fun withTypeKey(typeKey: String): MemberBuilder {
        return apply { this.typeKey = typeKey }
    }

    fun withSellerType(): MemberBuilder {
        return apply { this.typeKey = "seller" }
    }

    fun withSellerBrokerType(): MemberBuilder {
        return apply { this.typeKey = "seller_broker" }
    }

    fun withSellerCounselType(): MemberBuilder {
        return apply { this.typeKey = "seller_counsel" }
    }

    fun withBuyerType(): MemberBuilder {
        return apply { this.typeKey = "buyer" }
    }

    fun withBuyerCounselType(): MemberBuilder {
        return apply { this.typeKey = "buyer_counsel" }
    }

    fun build(): Member {
        val id = this.id
        val typeKey = this.typeKey
        return Member(
            id = id,
            typeKey = typeKey,
            firstName = firstName,
            lastName = lastName,
            companyName = companyName,
            address = address,
            phoneNumber = phoneNumber,
            email = email,
            createdAt = createdAt,
            updatedAt = updatedAt,
            enabled = true,
            authId = authId,
            walkThroughDone = walkThroughDone,
            organizationId = organizationId
        )
    }
}
