package realestate.unlock.dealroom.api.utils.entity.report

import realestate.unlock.dealroom.api.core.entity.deal.reports.Report
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportStatus
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate

class ReportBuilder {

    var id: Long = anyId()
    var dealId: Long = anyId()
    var type: String = "ASBESTOS_SURVEY"
    var reportName: String? = null
    var vendorKey: String = "EBI"
    var vendorName: String = anyString()
    var status: ReportStatus = ReportStatus.ORDERED
    var expectedDate: LocalDate? = null
    var costEstimate: BigDecimal? = null
    var cost: BigDecimal? = null
    var tags: List<Long> = listOf()
    var findings: String? = null
    var documents: List<File> = listOf()

    fun build() = Report(
        id = id,
        dealId = dealId,
        type = type,
        reportName = reportName,
        vendorKey = vendorKey,
        vendorName = vendorName,
        status = status,
        expectedDate = expectedDate,
        costEstimate = costEstimate,
        cost = cost,
        tags = tags,
        findings = findings,
        documents = documents
    )
}
