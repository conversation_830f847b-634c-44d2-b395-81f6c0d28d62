package realestate.unlock.dealroom.api.utils.creator

import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.io.File
import java.io.FileWriter
import kotlin.io.path.absolutePathString
import kotlin.io.path.createTempDirectory

object FileCreator {

    fun create(fileName: String = "test-file.txt", fileText: String = anyString()): File {
        return createTempDirectory("test-").let { tempDir ->
            "${tempDir.absolutePathString()}/$fileName"
                .also { FileWriter(it).apply { write(fileText) } }
                .let { File(it) }
        }
    }
}
