package realestate.unlock.dealroom.api.utils.entity.task.file

import realestate.unlock.dealroom.api.core.entity.task.file.TaskFile
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

object TaskFileObjectMother {

    fun taskFile(id: Long = 55, taskId: Long = 99, memberId: Long = 90): TaskFile {
        return TaskFile(
            id = id,
            name = anyString(),
            path = anyString(),
            kFileId = anyString(),
            taskId = taskId,
            memberId = memberId,
            createdAt = OffsetDateTime.now()
        )
    }
}
