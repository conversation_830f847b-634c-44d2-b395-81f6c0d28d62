package realestate.unlock.dealroom.api.utils.stub

import realestate.unlock.dealroom.api.core.gateway.gpt.ChatGptGateway
import realestate.unlock.dealroom.api.core.gateway.gpt.ChatGptGateway.AskQuestionInput
import realestate.unlock.dealroom.api.utils.extensions.anyString

class ChatGptGatewayStub : ChatGptGateway {

    override fun uploadFileToProcess(url: String, authToken: String): String = anyString()

    override fun askQuestion(input: AskQuestionInput): String = anyString()
}
