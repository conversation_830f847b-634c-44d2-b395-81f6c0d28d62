package realestate.unlock.dealroom.api.utils.entity.user

import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother

object CompleteUserObjectMother {

    fun admin(id: Long = 1) =
        CompleteUserBuilder().withId(id).withMember(MemberObjectMother.buyer()).withR<PERSON><PERSON>ey("admin").build()

    fun buyer(id: Long = 1) =
        CompleteUserBuilder().withId(id).withMember(MemberObjectMother.buyer()).withRoleKey("deal_user").build()

    fun seller(id: Long = 1) =
        CompleteUserBuilder().withId(id).withMember(MemberObjectMother.seller()).withRoleKey("deal_user").build()
}
