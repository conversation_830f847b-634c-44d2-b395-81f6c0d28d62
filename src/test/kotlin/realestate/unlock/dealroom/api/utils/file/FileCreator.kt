package realestate.unlock.dealroom.api.utils.file

import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.io.File
import java.io.FileWriter
import kotlin.io.path.absolutePathString
import kotlin.io.path.createTempDirectory

object FileCreator {

    fun file(fileName: String = "test-file.txt", fileContent: String = anyString()): File =
        createTempDirectory("test-").let { tempDir ->
            "${tempDir.absolutePathString()}/$fileName"
                .also { FileWriter(it).apply { write(fileContent) } }
                .let { File(it) }
        }
}
