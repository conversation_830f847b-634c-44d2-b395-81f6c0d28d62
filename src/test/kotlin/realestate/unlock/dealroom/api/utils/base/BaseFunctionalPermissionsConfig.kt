package realestate.unlock.dealroom.api.utils.base

import realestate.unlock.dealroom.api.core.entity.user.auth.Permission

data class BaseFunctionalPermissionsConfig(
    val url: String,
    val method: String,
    val permissions: List<Permission>,
    val testPermissions: Boolean = true
) {
    companion object {
        fun dontTestPermissions() = BaseFunctionalPermissionsConfig("", "", listOf(), false)
    }
}
