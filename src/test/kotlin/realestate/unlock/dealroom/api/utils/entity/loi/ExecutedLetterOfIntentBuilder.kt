package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.loi.ExecutedLetterOfIntent
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentSigned
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.LocalDate

class ExecutedLetterOfIntentBuilder {

    var dealId: Long = 1L
    var loiId: Long = 1L
    var letterOfIntentSigned: LetterOfIntentSigned = LetterOfIntentSigned(
        name = anyString(),
        kFileId = anyString()
    )
    var executedAt: LocalDate = LocalDate.now()

    fun build() = ExecutedLetterOfIntent(
        dealId = dealId,
        loiId = loiId,
        letterOfIntentSigned = letterOfIntentSigned,
        executedAt = executedAt
    )
}
