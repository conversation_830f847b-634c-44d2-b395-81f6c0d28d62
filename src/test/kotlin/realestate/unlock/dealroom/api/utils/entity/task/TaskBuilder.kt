package realestate.unlock.dealroom.api.utils.entity.task

import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import java.time.LocalDate
import java.time.OffsetDateTime

class TaskBuilder {

    var id: Long = 1L
    var dealCategoryId: Long = 9L
    var assignedBuyer: Member = MemberObjectMother.buyer()
    var assignedTeam: MemberDealTeam = MemberDealTeam.SELLER
    var typeKey: String = "form"
    var statusKey: TaskStatus = TaskStatus.TO_DO
    var templateKey: String? = "personal_property_inventory"
    var attachedFormData: Map<String, Any>? = null
    var title: String = "Title"
    var description: String = "Description"
    var dueDate: LocalDate? = null
    var formSchema: Map<String, Any>? = null
    var rejectionReason: String? = null
    var data: Map<String, Any>? = null
    var required: Boolean = false
    var prioritizeAfterClosing: Boolean = false
    var priority: Priority = Priority(PriorityValue.LOW, 1)
    var updatedAt: OffsetDateTime = OffsetDateTime.now()
    var createdAt: OffsetDateTime = OffsetDateTime.now()
    var visibility: Task.Visibility = Task.Visibility.ALL
    var enabled: Boolean = true
    var taskExternalDataDefinition: TaskExternalDataDefinition = TaskExternalDataDefinition(null, emptySet())
    var custom: Boolean = false

    fun withId(id: Long) = this.apply { this.id = id }
    fun withAssignedBuyer(assignedBuyer: Member) = this.apply { this.assignedBuyer = assignedBuyer }
    fun withAssignedTeam(assignedTeam: MemberDealTeam) = this.apply { this.assignedTeam = assignedTeam }
    fun withDealCategoryId(dealCategoryId: Long) = this.apply { this.dealCategoryId = dealCategoryId }
    fun withTypeKey(typeKey: String) = this.apply { this.typeKey = typeKey }
    fun withStatus(status: TaskStatus) = this.apply { this.statusKey = status }
    fun withTemplateKey(templateKey: String) = this.apply { this.templateKey = templateKey }
    fun withAttachedFormData(attachedFormData: Map<String, Any>?) = this.apply { this.attachedFormData = attachedFormData }
    fun withFormSchema(formSchema: Map<String, Any>?) = this.apply { this.formSchema = formSchema }
    fun withPriority(priority: Priority) = this.apply { this.priority = priority }
    fun withRequired(required: Boolean) = this.apply { this.required = required }
    fun withPrioritizeAfterClosing(prioritizeAfterClosing: Boolean) = this.apply { this.prioritizeAfterClosing = prioritizeAfterClosing }
    fun withRejectionReason(rejectionReason: String?) = this.apply { this.rejectionReason = rejectionReason }
    fun withDueDate(dueDate: LocalDate) = this.apply { this.dueDate = dueDate }
    fun withData(data: Map<String, Any>?) = this.apply { this.data = data }
    fun withVisibility(visibility: Task.Visibility) = this.apply { this.visibility = visibility }
    fun enabled(enabled: Boolean) = this.apply { this.enabled = enabled }
    fun updatedAt(time: OffsetDateTime) = this.apply { this.updatedAt = time }

    fun build(): Task {
        return Task(
            id = id,
            dealCategoryId = dealCategoryId,
            assignedBuyer = assignedBuyer,
            assignedTeam = assignedTeam,
            typeKey = typeKey,
            statusKey = statusKey,
            templateKey = templateKey,
            attachedFormData = attachedFormData,
            title = title,
            description = description,
            dueDate = dueDate,
            formSchema = formSchema,
            updatedAt = updatedAt,
            createdAt = createdAt,
            rejectionReason = rejectionReason,
            data = data,
            priority = priority,
            required = required,
            prioritizeAfterClosing = prioritizeAfterClosing,
            visibility = visibility,
            enabled = enabled,
            externalDataDefinition = taskExternalDataDefinition
        )
    }
}
