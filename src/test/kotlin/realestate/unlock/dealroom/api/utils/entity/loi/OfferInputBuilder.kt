package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.RentStepType
import realestate.unlock.dealroom.api.core.entity.file.FileInput
import realestate.unlock.dealroom.api.core.entity.loi.*
import realestate.unlock.dealroom.api.core.entity.loi.input.*
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate

@Suppress("MemberVisibilityCanBePrivate")
class OfferInputBuilder {

    var dealId: Long = 4L
    var memberId: Long = 3L
    var dealTeam: String = "buyer"
    var tenantName = anyString()
    var propertySquareFootage: BigDecimal = BigDecimal.TEN
    var vertical: String = "Medical"
    var broker: String = "broker"
    var brokerCompanyName: String = "broker company name"
    var files = listOf<FileInput>()
    var salesPrice: BigDecimal = BigDecimal.TEN
    var leaseRent: BigDecimal = BigDecimal.TEN
    var leaseType = "NNN"
    var leaseCondition = "condition"
    var leaseRentIncrease = 1.0f
    var leaseIncreaseEveryYear = 1.5f
    var leaseLength = 10L
    var leaseExpirationYear = 2030L
    var leaseNumberOfOptions = 4L
    var leaseOptionLengths = 2L
    var closingPeriod = 45L
    var closingPeriodExtension = 60L
    var closingExtensionDeposit: BigDecimal = BigDecimal.ONE
    var contractTermination: LocalDate = LocalDate.now()
    var earnestMoneyDeposit: BigDecimal = BigDecimal(123)
    var dueDiligenceNumber: Long = 10
    var comments = anyString()
    var offerRentCpi: Float? = 1f
    var offerRentStepType = RentStepType.CPI
    var offerCustomSections: OfferCustomSectionsInput? = OfferCustomSectionsInput(
        sections = listOf(
            OfferCustomSectionInput(
                title = "section title",
                content = "section content"
            )
        )
    )
    var emailOptions: LoiEmailOptionsInput? = LoiEmailOptionsInput(
        sendToBroker = true,
        sendToSeller = true
    )
    var guaranteeType: GuaranteeType = GuaranteeType.CORPORATE
    var offerClosingCost: ClosingCost = ClosingCost.DEFAULT
    var dueDate: LocalDate? = null

    fun build() = MedicalRoundInput(
        dealId = dealId,
        tenantName = tenantName,
        propertySquareFootage = propertySquareFootage,
        vertical = vertical,
        broker = LoiBrokerInput(company = brokerCompanyName, name = broker),
        files = files,
        salesPrice = salesPrice,
        lease = LoiLeaseInput(
            rent = leaseRent,
            type = leaseType,
            condition = leaseCondition,
            rentIncrease = leaseRentIncrease,
            increaseEveryYear = leaseIncreaseEveryYear,
            length = leaseLength,
            expirationYear = leaseExpirationYear,
            numberOfOptions = leaseNumberOfOptions,
            optionLengths = leaseOptionLengths,

            rentCpi = offerRentCpi,
            rentStepType = offerRentStepType,
            contractTermination = contractTermination,
            earnestMoneyDeposit = earnestMoneyDeposit,
            guaranteeType = guaranteeType
        ),
        contractExecution = LoiClosingInput(
            closingPeriod = closingPeriod,
            closingExtensionPeriod = closingPeriodExtension,
            closingExtensionDeposit = closingExtensionDeposit,
            dueDiligenceNumber = dueDiligenceNumber,
            closingCost = offerClosingCost,
            dueDiligenceExtensionPeriod = 10L
        ),
        memberId = memberId,
        dealTeam = dealTeam,
        offerCustomSections = offerCustomSections,
        emailOptions = emailOptions,
        dueDate = dueDate
    )
}
