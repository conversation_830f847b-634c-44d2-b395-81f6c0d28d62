package realestate.unlock.dealroom.api.utils.mocks

import com.keyway.security.domain.ticket.TicketSdk
import com.keyway.security.domain.token.TokenSdk
import com.keyway.security.domain.token.UserToken
import com.keyway.security.domain.token.UserTokenClaims
import com.keyway.security.domain.user.User
import com.keyway.security.domain.user.UserSdk
import io.mockk.every
import io.mockk.mockk
import org.apache.http.HttpHeaders
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.MemberCreationInput
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.member.CreateMember
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.utils.extensions.anyString

object AuthMock {

    const val authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWV9.dyt0CoTl4WoVjAHI9Q_CwSKhl6d_9rhM3NrXuJttkao"

    fun getAuthHeader(token: String = authToken) = mapOf(HttpHeaders.AUTHORIZATION to getAuthBearerToken(token))

    fun getAuthBearerToken(token: String = authToken) = "Bearer $token"

    fun mockMemberUserSdk(member: Member) =
        Context.injector.getInstance(UserSdk::class.java).let { userSdk ->
            val user = User(
                id = member.authId!!,
                email = member.email,
                name = member.fullName,
                emailVerified = true,
                blocked = false
            )
            every { userSdk.findById(any()) } returns user
        }

    fun createMemberWithUser(
        firstName: String = "Test Name",
        lastName: String = "With Auth",
        email: String = "<EMAIL>",
        uid: String = anyString(),
        claims: Map<String, Any> = mapOf(),
        isEmailVerified: Boolean = true,
        address: String = "Test Address",
        memberType: String = "buyer",
        companyName: String = "Test Company",
        phoneNumber: String? = null,
        needUser: Boolean = true,
        token: String = authToken,
        disable: Boolean = false,
        permissions: List<Permission> = Permission.values().toList(),
        organizationId: String = "org-id"
    ): Member =

        MemberCreationInput(
            memberType = memberType,
            firstName = firstName,
            lastName = lastName,
            companyName = companyName,
            address = address,
            phoneNumber = phoneNumber,
            email = email,
            needUser = needUser,
            organizationId = organizationId,
            authId = uid
        ).also { input ->
            Context.injector.getInstance(UserSdk::class.java).let { userSdk ->
                val userMock = mockk<User>()
                every { userMock.id } returns uid
                every { userMock.email } returns input.email
                every { userMock.name } returns "$firstName $lastName"
                every { userMock.emailVerified } returns isEmailVerified
                every { userMock.blocked } returns disable
                every { userSdk.findById(uid) } returns userMock
                every { userSdk.addRole(any(), any()) } returns mockk()
            }
            Context.injector.getInstance(TicketSdk::class.java).let { ticketSdk ->
                every { ticketSdk.resetPassword(any()) } returns "ticket"
            }
        }
            .let(Context.injector.getInstance(CreateMember::class.java)::create)
            .also {
                Context.injector.getInstance(TokenSdk::class.java).let { tokenSdk ->
                    every { tokenSdk.validate(eq(token)) } returns UserToken(
                        id = uid,
                        claims = UserTokenClaims(
                            iss = "iss",
                            aud = listOf(),
                            exp = 0,
                            permissions = permissions.flatMap { it.keys() },
                            organizationId = anyString()
                        )
                    )
                }
            }
}
