package realestate.unlock.dealroom.api.utils.entity.user

import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.infrastructure.gateway.auth0.DealRoomRole
import realestate.unlock.dealroom.api.utils.entity.member.MemberBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

class CompleteUserBuilder {

    private var id: Long = anyId()
    private var googleUid: String = anyString()
    private var email: String = "<EMAIL>"
    private var roleKey: String = DealRoomRole.DEAL_USER.key
    private var member: Member = MemberBuilder().build()
    private val updatedAt: OffsetDateTime = OffsetDateTime.now()
    private val createdAt: OffsetDateTime = OffsetDateTime.now()
    private val walkThroughDone: Boolean = false

    fun withId(id: Long) = this.apply { this.id = id }
    fun with<PERSON><PERSON><PERSON>(member: Member) = this.apply { this.member = member }
    fun withRoleKey(roleKey: String) = this.apply { this.roleKey = roleKey }

    fun build() = CompleteUser(
        id = id,
        authId = googleUid,
        email = email,
        roleKey = roleKey,
        memberId = member.id,
        member = member,
        updatedAt = updatedAt,
        createdAt = createdAt,
        walkThroughDone = walkThroughDone
    )
}
