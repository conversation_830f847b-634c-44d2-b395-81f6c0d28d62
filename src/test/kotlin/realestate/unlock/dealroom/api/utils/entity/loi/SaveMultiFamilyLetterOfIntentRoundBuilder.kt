package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.loi.ClosingCost
import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentRoundStatus
import realestate.unlock.dealroom.api.core.entity.loi.Loan
import realestate.unlock.dealroom.api.core.repository.loi.*
import java.math.BigDecimal
import java.time.LocalDate

@Suppress("MemberVisibilityCanBePrivate")
class SaveMultiFamilyLetterOfIntentRoundBuilder {
    var dealId: Long = 4L
    var status: LetterOfIntentRoundStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION
    var memberId: Long = 3L
    var broker: String = "broker"
    var brokerCompanyName: String = "broker company name"
    var files = listOf<SaveLetterOfIntentFile>()
    var salesPrice: BigDecimal = BigDecimal.TEN
    var closingPeriod = 45L
    var closingPeriodExtension = 60L
    var closingExtensionDeposit: BigDecimal = BigDecimal.ONE
    var contractTermination: LocalDate = LocalDate.now()
    var dueDiligenceNumber: Long = 10
    var offerCustomSections: SaveOfferCustomSections? = SaveOfferCustomSections(
        sections = listOf(
            SaveOfferCustomSection(
                title = "section title",
                content = "section content"
            )
        )
    )
    var loan: Loan = Loan.FREE_AND_CLEAR
    var dueDate: LocalDate? = null
    var legalEntity: String = "Legal"
    var dueDiligenceExtensionPeriod: Long = 1L
    var offerDeposit: BigDecimal = BigDecimal.TEN
    var closingCost: ClosingCost = ClosingCost.DEFAULT
    var propertySquareFootage: Int = 10000

    fun build() = SaveMultifamilyLetterOfIntentRound(
        dealId = dealId,
        status = status,
        brokerName = broker,
        brokerCompanyName = brokerCompanyName,
        offerClosingExtensionDeposit = closingExtensionDeposit,
        offerCustomSections = offerCustomSections,
        legalEntity = legalEntity,
        loan = loan.name,
        offerCreatedByMemberId = memberId,
        offerDeposit = offerDeposit,
        offerPrice = salesPrice,
        dueDate = dueDate,
        dueDiligenceExtensionPeriod = dueDiligenceExtensionPeriod,
        dueDiligencePeriod = dueDiligenceNumber,
        closingPeriod = closingPeriod,
        closingExtensionPeriod = closingPeriodExtension,
        offerFiles = files,
        closingCost = closingCost,
        propertySquareFootage = propertySquareFootage
    )
}
