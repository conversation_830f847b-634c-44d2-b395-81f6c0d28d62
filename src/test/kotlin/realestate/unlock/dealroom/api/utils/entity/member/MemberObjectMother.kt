package realestate.unlock.dealroom.api.utils.entity.member

import realestate.unlock.dealroom.api.core.entity.member.Member

object MemberObjectMother {
    fun seller(id: Long = 1L, authId: String? = null): Member = MemberBuilder()
        .withId(id)
        .withSellerType()
        .apply { firstName = "Seller" }
        .apply { email = "<EMAIL>" }
        .apply { this.authId = authId }
        .build()
    fun buyer(id: Long = 2L, authId: String? = null): Member = MemberBuilder()
        .withId(id)
        .withBuyerType()
        .apply { firstName = "Buyer" }
        .apply { email = "<EMAIL>" }
        .apply { this.authId = authId }
        .build()
    fun sellerCounsel(id: Long = 3L, authId: String? = null): Member = MemberBuilder()
        .withId(id)
        .withSellerCounselType()
        .apply { firstName = "Seller Counsel" }
        .apply { email = "<EMAIL>" }
        .apply { this.authId = authId }
        .build()
    fun buyerCounsel(id: Long = 4L, authId: String? = null): Member = MemberBuilder()
        .withId(id)
        .withBuyerCounselType()
        .apply { firstName = "Buyer Counsel" }
        .apply { email = "<EMAIL>" }
        .apply { this.authId = authId }
        .build()
    fun sellerBroker(id: Long = 5L, authId: String? = null): Member = MemberBuilder()
        .withId(id)
        .withSellerBrokerType()
        .apply { firstName = "Seller Broker" }
        .apply { email = "<EMAIL>" }
        .apply { this.authId = authId }
        .build()
}
