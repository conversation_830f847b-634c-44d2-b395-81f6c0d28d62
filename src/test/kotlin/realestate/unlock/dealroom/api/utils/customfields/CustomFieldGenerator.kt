package realestate.unlock.dealroom.api.utils.customfields

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.io.File

class CustomFieldGenerator {

    /**
     * file format
     *  propertyName, type, title, readOnly
     */
    operator fun invoke(fileName: String): String {
        val customFields = processLines(readFile(fileName))
        if (customFields.groupingBy { it.first }.eachCount().any { (_, occurrence) -> occurrence > 1 }) {
            throw RuntimeException("There are fields repeated")
        }

        return JsonMapper.encode(customFields.toMap())
    }

    private fun processLines(lines: List<String>) = lines.filter { !it.startsWith("#") && it.isNotEmpty() && it.isNotBlank() }
        .map { customFieldFrom(it) }

    private fun customFieldFrom(line: String): Pair<String, CustomField> {
        val values = line.split(",").map { it.trim() }
        check(values.size >= 3) { "line `$line` could not be read" }
        return Pair(values.first(), CustomField(values[1], values[2], values.getOrNull(3)?.let { it.toBoolean() }))
    }

    private fun readFile(fileName: String) = File(fileName).useLines { it.toList() }

    @JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy::class)
    data class CustomField(val type: String, val title: String, val readOnly: Boolean?)
}
