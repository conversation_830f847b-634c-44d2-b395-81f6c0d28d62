package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.loi.*
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate

class LoiMedicalRoundBuilder {

    var id: Long = 543L
    var date: LocalDate = LocalDate.now()
    var dealId: Long = 444L
    var tenantName: String = anyString()
    var status: LetterOfIntentRoundStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION
    var offer: MedicalLoiOffer = MedicalOfferBuilder().build()
    var offerResponse: OfferResponse? = null
    var propertySquareFootage: BigDecimal = BigDecimal.TEN
    var vertical: String = "Medical"
    var broker: Broker = BrokerBuilder().build()
    var guaranteeType: GuaranteeType = GuaranteeType.CORPORATE
    var dueDate: LocalDate? = null

    fun withStatus(status: LetterOfIntentRoundStatus) = apply { this.status = status }

    fun build() = MedicalLoiRound(
        id = id,
        date = date,
        dealId = dealId,
        tenantName = tenantName,
        propertySquareFootage = propertySquareFootage,
        broker = broker,
        vertical = vertical,
        status = status,
        offer = offer,
        offerResponse = offerResponse,
        guaranteeType = guaranteeType,
        dueDate = dueDate
    )
}
