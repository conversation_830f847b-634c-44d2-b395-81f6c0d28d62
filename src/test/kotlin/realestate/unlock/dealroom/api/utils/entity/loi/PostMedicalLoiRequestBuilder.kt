package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.deal.GuaranteeType
import realestate.unlock.dealroom.api.core.entity.deal.RentStepType
import realestate.unlock.dealroom.api.core.entity.loi.ClosingCost
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiRoundType
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.*
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate

@Suppress("MemberVisibilityCanBePrivate")
class PostMedicalLoiRequestBuilder {
    var type = LoiRoundType.MEDICAL
    var tenantName = anyString()
    var propertySquareFootage = BigDecimal.TEN
    var vertical: String = "Medical"
    var broker: String = "broker"
    var brokerageCompanyName: String = "broker company name"
    var offerFiles: List<LetterOfIntentFileRequest> = listOf(
        LetterOfIntentFileRequest(uid = tenantName, name = tenantName)
    )
    var offerPrice: BigDecimal = BigDecimal.TEN
    var offerLeaseRent: BigDecimal = BigDecimal.ONE
    var offerLeaseType: String = "NNN"
    var offerLeaseCondition: String = "condition"
    var offerLeaseRentIncrease: Float = 2f
    var offerLeaseIncreaseEveryYear: Float = 3f
    var offerLeaseLength: Long = 10
    var offerLeaseExpirationYear: Long = 2030
    var offerLeaseNumberOfOptions: Long = 2
    var offerLeaseOptionLengths: Long = 5
    var offerClosingPeriod: Long = 45
    var offerClosingPeriodExtension: Long = 5
    var offerClosingExtensionDeposit: BigDecimal = BigDecimal.ONE
    var offerContractTermination: LocalDate = LocalDate.now()
    var offerEarnestMoneyDeposit: BigDecimal = BigDecimal.ONE
    var offerDueDiligenceNumber: Long = 10
    var offerComments: String = anyString()
    var offerRentCpi: Float? = 1f
    var offerRentStepType = RentStepType.CPI
    var offerCustomSections: OfferCustomSectionsRequest? = OfferCustomSectionsRequest(
        sections = listOf(
            OfferCustomSectionRequest(
                title = "some title",
                content = "some content"
            )
        )
    )
    var emailOptions: EmailOptionsRequest? = EmailOptionsRequest(
        sendToBroker = true,
        sendToSeller = true
    )
    var guaranteeType: GuaranteeType = GuaranteeType.CORPORATE
    var offerClosingCost: ClosingCost = ClosingCost.DEFAULT
    var dueDate: LocalDate? = null
    var executedAt = LocalDate.now().minusDays(3)

    fun build() = PostMedicalLoiRoundRequest(
        type = type,
        tenantName = tenantName,
        propertySquareFootage = propertySquareFootage,
        vertical = vertical,
        broker = broker,
        brokerageCompanyName = brokerageCompanyName,
        offerFiles = offerFiles,
        offerPrice = offerPrice,
        offerLeaseRent = offerLeaseRent,
        offerLeaseType = offerLeaseType,
        offerLeaseCondition = offerLeaseCondition,
        offerLeaseRentIncrease = offerLeaseRentIncrease,
        offerLeaseIncreaseEveryYear = offerLeaseIncreaseEveryYear,
        offerLeaseLength = offerLeaseLength,
        offerLeaseExpirationYear = offerLeaseExpirationYear,
        offerLeaseNumberOfOptions = offerLeaseNumberOfOptions,
        offerLeaseOptionLengths = offerLeaseOptionLengths,
        offerClosingPeriod = offerClosingPeriod,
        offerClosingPeriodExtension = offerClosingPeriodExtension,
        offerClosingExtensionDeposit = offerClosingExtensionDeposit,
        offerContractTermination = offerContractTermination,
        offerEarnestMoneyDeposit = offerEarnestMoneyDeposit,
        offerDueDiligenceNumber = offerDueDiligenceNumber,
        offerComments = offerComments,
        offerRentCpi = offerRentCpi,
        offerRentStepType = offerRentStepType,
        offerCustomSections = offerCustomSections,
        offerClosingCost = offerClosingCost,
        emailOptions = emailOptions,
        guaranteeType = guaranteeType,
        dueDate = dueDate
    )

    fun buildExecuted() = ExecutedMedicalLoiInput(
        type = type,
        tenantName = tenantName,
        propertySquareFootage = propertySquareFootage,
        vertical = vertical,
        broker = broker,
        brokerageCompanyName = brokerageCompanyName,
        files = offerFiles,
        offerPrice = offerPrice,
        offerLeaseRent = offerLeaseRent,
        offerLeaseType = offerLeaseType,
        offerLeaseCondition = offerLeaseCondition,
        offerLeaseRentIncrease = offerLeaseRentIncrease,
        offerLeaseIncreaseEveryYear = offerLeaseIncreaseEveryYear,
        offerLeaseLength = offerLeaseLength,
        offerLeaseExpirationYear = offerLeaseExpirationYear,
        offerLeaseNumberOfOptions = offerLeaseNumberOfOptions,
        offerLeaseOptionLengths = offerLeaseOptionLengths,
        offerClosingPeriod = offerClosingPeriod,
        offerClosingPeriodExtension = offerClosingPeriodExtension,
        offerClosingExtensionDeposit = offerClosingExtensionDeposit,
        offerEarnestMoneyDeposit = offerEarnestMoneyDeposit,
        offerDueDiligenceNumber = offerDueDiligenceNumber,
        offerRentCpi = offerRentCpi,
        offerRentStepType = offerRentStepType,
        offerClosingCost = offerClosingCost,
        guaranteeType = guaranteeType,
        executedAt = executedAt
    )
}
