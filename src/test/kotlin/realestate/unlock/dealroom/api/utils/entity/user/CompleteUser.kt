package realestate.unlock.dealroom.api.utils.entity.user

import realestate.unlock.dealroom.api.core.entity.member.Member
import java.time.OffsetDateTime

data class CompleteUser(
    val id: Long,
    val authId: String,
    val email: String,
    val roleKey: String,
    val memberId: Long,
    val member: Member,
    val updatedAt: OffsetDateTime,
    val createdAt: OffsetDateTime,
    val walkThroughDone: Boolean
)
