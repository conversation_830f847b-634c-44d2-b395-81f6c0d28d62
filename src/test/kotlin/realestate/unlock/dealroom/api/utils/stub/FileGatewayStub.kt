package realestate.unlock.dealroom.api.utils.stub

import realestate.unlock.dealroom.api.core.entity.kfile.*
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.kfileservice.StagingFileToConfirm
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.NotFoundException
import realestate.unlock.dealroom.api.infrastructure.utils.extension.fixPath
import realestate.unlock.dealroom.api.infrastructure.utils.file.SanitizedFilename
import realestate.unlock.dealroom.api.utils.creator.FileCreator
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.Instant
import java.util.*

class FileGatewayStub : FileGateway {

    private var nextUid: String? = null
    private var throwsOnNextGet: Boolean = false
    private var confirmedFiles: MutableSet<String> = mutableSetOf()

    companion object {
        private var kFileByPath: MutableMap<String, KPathFile> = mutableMapOf()
        private var kFileById: MutableMap<String, KPathFile> = mutableMapOf()
    }

    private fun KPathFile.save(): KPathFile {
        kFileById[this.uid] = this
        kFileByPath[this.path] = this
        return this
    }

    private fun KPathFile.remove() {
        kFileById.remove(this.uid)
        kFileByPath.remove(this.path)
    }

    override fun getFileWithUrl(kFileId: String, downloadName: SanitizedFilename?): KFileWithUrl {
        if (throwsOnNextGet) {
            throwsOnNextGet = false
            throw NotFoundException()
        }

        return kFileById.getOrDefault(
            kFileId,
            KPathFile(
                uid = kFileId,
                name = anyString(),
                sizeInBytes = 123L,
                lastModified = Instant.now(),
                path = anyString()
            )
        ).let { KFileWithUrl(it.uid, "http://localhost:8081/${downloadName?.value ?: it.path}", name = it.name) }
    }

    override fun confirmStagingFile(files: List<StagingFileToConfirm>) {
        if (files.any { file -> confirmedFiles.contains(file.kFileId) }) {
            throw BadRequestException(message = "The target Staging file could not be found")
        }
        confirmedFiles.addAll(files.map(StagingFileToConfirm::kFileId))
    }

    override fun getStageUploadFileUrl(fileName: String): KFileWithUrl {
        val kFileId = nextUid ?: UUID.randomUUID().toString()
        nextUid = null
        return KFileWithUrl(kFileId = kFileId, url = anyString(), name = fileName)
    }

    override fun uploadStagedFile(stagingFile: FileToStaging): KFile {
        val kFileId = nextUid ?: UUID.randomUUID().toString()
        nextUid = null
        return KFile(kFileId, stagingFile.filename)
    }

    override fun uploadFile(fileToUpload: FileToUpload): KFile {
        val kFileId = nextUid ?: UUID.randomUUID().toString()
        nextUid = null
        return KPathFile(
            uid = kFileId,
            path = "${fileToUpload.path}/${fileToUpload.filename}".fixPath(),
            name = fileToUpload.filename,
            sizeInBytes = anyId(),
            lastModified = Instant.now()
        ).save().toKFile()
    }

    override fun deleteFiles(paths: Set<String>) {
        paths.forEach { path ->
            kFileByPath.remove(path)
        }
    }

    override fun moveFiles(input: FileGateway.MoveFilesInput) {
        input.files.forEach { file ->
            val kFile = kFileByPath[file.sourcePath] ?: throw Exception("invalid source path")
            kFile.remove()
            kFile.copy(
                name = file.destinationPath.substringAfterLast("/"),
                path = file.destinationPath
            ).save()
        }
    }

    override fun getFile(kFileId: String, fileName: String): KFileWithFile {
        return KFileWithFile(
            kFileId,
            FileCreator.create()
        )
    }

    override fun getFiles(path: String): List<KPathFile> = kFileByPath
        .filter { it.key.startsWith(path) }
        .values.toList()

    override fun getFileUploadUrl(path: String): String = "www.keyway.com/$path"
    override fun getFileDownloadUrl(path: String): String = "www.keyway.com/$path"

    fun nextUploadFileUid(uid: String) {
        nextUid = uid
    }

    fun throwsNotFoundOnNextGet(throws: Boolean) {
        throwsOnNextGet = throws
    }

    fun clear() {
        nextUid = null
        throwsOnNextGet = false
        confirmedFiles = mutableSetOf()
        kFileByPath = mutableMapOf()
        kFileById = mutableMapOf()
    }
}
