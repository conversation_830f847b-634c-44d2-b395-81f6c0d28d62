package realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.member.post

import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.post.PostMemberRequest

class PostMemberRequestBuilder {
    var memberType: String = MemberTypeEnum.SELLER.name
    var firstName: String = "Test"
    var lastName: String = "Name"
    var companyName: String = "Test Company"
    var address: String? = "Test Address"
    var phoneNumber: String? = null
    var email: String = "<EMAIL>"
    var needUser: Boolean = false
    var organizationId: String = "org-id"

    fun build() = PostMemberRequest(
        memberType = memberType,
        firstName = firstName,
        lastName = lastName,
        companyName = companyName,
        address = address,
        phoneNumber = phoneNumber,
        email = email,
        needUser = needUser,
        organizationId = organizationId
    )
}
