package realestate.unlock.dealroom.api.utils.stub

import realestate.unlock.dealroom.api.core.gateway.email.EmailData
import realestate.unlock.dealroom.api.core.gateway.email.EmailGateway
import realestate.unlock.dealroom.api.core.gateway.email.TemplatedEmailData

class EmailGatewayStub : EmailGateway {
    override fun sendTemplated(templatedEmailData: TemplatedEmailData) {}

    override fun send(emailData: EmailData) {}
}
