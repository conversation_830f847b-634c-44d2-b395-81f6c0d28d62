package realestate.unlock.dealroom.api.utils.creator

import com.google.i18n.phonenumbers.PhoneNumberUtil
import org.eclipse.jetty.http.HttpHeader
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.UpdateDates
import realestate.unlock.dealroom.api.entrypoint.rest.contract.mappers.Mapper
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.post.PostMemberRequest
import realestate.unlock.dealroom.api.infrastructure.Context
import realestate.unlock.dealroom.api.infrastructure.gateway.property.PropertyAssetsGateway
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.post.CreateDealInputBuilder
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.member.post.PostMemberRequestBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import realestate.unlock.dealroom.api.utils.stub.FeatureFlagsStub
import realestate.unlock.dealroom.api.utils.stub.PropertyAssetsGatewayStub
import java.net.URI
import java.net.http.HttpRequest
import java.net.http.HttpRequest.BodyPublishers
import java.net.http.HttpResponse
import java.nio.charset.Charset
import java.time.Duration
import java.net.http.HttpClient as OldHttpClient

object DealCreator {

    private val client = OldHttpClient.newBuilder()
        .version(OldHttpClient.Version.HTTP_2)
        .build()

    private val propertyAssetsGatewayStub = Context.injector.getInstance(PropertyAssetsGateway::class.java) as PropertyAssetsGatewayStub
    private val featureFlagsStub = Context.injector.getInstance(FeatureFlags::class.java) as FeatureFlagsStub

    fun createDealByRest(
        seller: Member? = null,
        buyer: Member? = null,
        buyerEmail: String = "<EMAIL>",
        sellerEmail: String = "<EMAIL>",
        localUrl: String = "http://localhost:8080",
        keywayId: String? = anyString(),
        dealCreationRequestBuilder: CreateDealInputBuilder = CreateDealInputBuilder(),
        updateDates: UpdateDates? = null,
        token: String = AuthMock.authToken
    ): CompleteDeal {
        propertyAssetsGatewayStub.keywayId = keywayId!!
        val givenSellerMemberRequestCreationBody = givenPostMemberRequest(email = sellerEmail)
        val givenMemberUrl = "$localUrl/member"
        featureFlagsStub.configureFeature(Feature.ORG_CAN_CREATE_NNN, true)

        val givenSeller = seller?.let(Mapper.toMemberDto)
            ?: postMember(givenSellerMemberRequestCreationBody, givenMemberUrl, token)
        val givenBuyer = buyer?.let(Mapper.toMemberDto)
            ?: postMember(
                givenSellerMemberRequestCreationBody.copy(
                    memberType = "buyer",
                    email = buyerEmail
                ),
                givenMemberUrl,
                token
            )

        val givenDealCreationUrl = "$localUrl/deal"
        val givenDealRequestCreationBody = dealCreationRequestBuilder
            .apply { sellerMemberId = givenSeller.id }
            .apply { buyerMemberId = givenBuyer.id }
            .apply { propertyKeywayId = keywayId }
            .buildSaleLeaseback()

        val completeDeal = client.send(
            HttpRequest.newBuilder()
                .uri(URI(givenDealCreationUrl))
                .POST(BodyPublishers.ofString(JsonMapper.encode(givenDealRequestCreationBody), Charset.defaultCharset()))
                .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
                .timeout(Duration.ofSeconds(10))
                .build(),
            HttpResponse.BodyHandlers.ofString()
        ).let {
            JsonMapper.decode(
                it.body(),
                CompleteDeal::class.java
            )
        }

        if (updateDates == null) {
            return completeDeal
        }

        client.send(
            HttpRequest.newBuilder()
                .uri(URI("""$localUrl/deal/${completeDeal.id}/dates"""))
                .PUT(BodyPublishers.ofString(JsonMapper.encode(updateDates), Charset.defaultCharset()))
                .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
                .timeout(Duration.ofSeconds(10))
                .build(),
            HttpResponse.BodyHandlers.ofString()
        )

        return completeDeal.copy(
            diligenceExpirationDate = updateDates.diligenceExpirationDate,
            loiExecutedDate = updateDates.loiExecutedDate,
            initialClosingDate = updateDates.initialClosingDate,
            outsideClosingDate = updateDates.outsideClosingDate,
            contractExecutedDate = updateDates.contractExecutedDate
        )
    }

    private fun postMember(postMemberRequest: PostMemberRequest, url: String, token: String): MemberDto =
        client.send(
            HttpRequest.newBuilder()
                .uri(URI(url))
                .POST(BodyPublishers.ofString(JsonMapper.encode(postMemberRequest), Charset.defaultCharset()))
                .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken(token))
                .timeout(Duration.ofSeconds(10))
                .build(),
            HttpResponse.BodyHandlers.ofString()
        ).let { JsonMapper.decode(it.body(), MemberDto::class.java) }

    private fun givenPostMemberRequest(
        address: String = "Test Address",
        memberType: String = "seller",
        firstName: String = "Test",
        lastName: String = "Name",
        companyName: String = "Test Company",
        email: String = "<EMAIL>"
    ): PostMemberRequest {
        val phoneUtil = PhoneNumberUtil.getInstance()
        val givenPhone = phoneUtil.format(phoneUtil.getExampleNumber("US"), PhoneNumberUtil.PhoneNumberFormat.E164)
        return PostMemberRequestBuilder().apply {
            this.address = address
            this.memberType = memberType
            this.firstName = firstName
            this.lastName = lastName
            this.companyName = companyName
            this.phoneNumber = givenPhone
            this.email = email
        }.build()
    }
}
