package realestate.unlock.dealroom.api.utils.module

import com.google.api.services.calendar.Calendar
import com.google.inject.AbstractModule
import com.google.inject.name.Names
import com.keyway.kommons.aws.config.AwsConfig
import com.keyway.security.domain.authentication.AuthenticationSdk
import com.keyway.security.domain.ticket.TicketSdk
import com.keyway.security.domain.token.TokenSdk
import com.keyway.security.domain.user.UserImpersonationSdk
import com.keyway.security.domain.user.UserSdk
import com.sendgrid.SendGrid
import io.mockk.mockk
import io.mockk.spyk
import io.split.client.SplitClient
import kotlinx.coroutines.CoroutineScope
import realestate.unlock.dealroom.api.core.event.deal.DealEventPublisher
import realestate.unlock.dealroom.api.core.event.task.TaskChangedPublisher
import realestate.unlock.dealroom.api.core.gateway.calendar.CalendarGateway
import realestate.unlock.dealroom.api.core.gateway.email.EmailGateway
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags
import realestate.unlock.dealroom.api.core.gateway.gpt.ChatGptGateway
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.property.PropertyDetailsGateway
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.client.SQSChatGptFileSentClient
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.client.SQSReadExcelFileClient
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sns.AwsSNSConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.parser.ConfigParser
import realestate.unlock.dealroom.api.infrastructure.gateway.property.PropertyAssetsGateway
import realestate.unlock.dealroom.api.utils.mocks.provider.MockAwsSNSClientProvider
import realestate.unlock.dealroom.api.utils.mocks.provider.MockAwsSQSClientProvider
import realestate.unlock.dealroom.api.utils.mocks.provider.MockCoroutineScopeProvider
import realestate.unlock.dealroom.api.utils.stub.*
import software.amazon.awssdk.services.sns.SnsAsyncClient
import software.amazon.awssdk.services.sqs.SqsAsyncClient

class TestingModule : AbstractModule() {

    override fun configure() {
        // Auth provider Mocks
        bind(UserSdk::class.java).toInstance(mockk { UserSdk::class.java })
        bind(TokenSdk::class.java).toInstance(mockk { TokenSdk::class.java })
        bind(TicketSdk::class.java).toInstance(mockk { TicketSdk::class.java })
        bind(UserImpersonationSdk::class.java).toInstance(mockk { UserImpersonationSdk::class.java })
        bind(AuthenticationSdk::class.java).toInstance(mockk { AuthenticationSdk::class.java })

        // Aws Configurations
        val config = ConfigParser.read()
        bind(AwsConfig::class.java).toInstance(config.awsCredentials)
        bind(AwsSNSConfig::class.java).toInstance(config.sns)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsTaskChanged")).toInstance(config.sqsTaskChanged)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsLoiSigned")).toInstance(config.sqsLoiSigned)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsCronJobs")).toInstance(config.sqsCronJobs)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsDealEvents")).toInstance(config.sqsDealEvents)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsReadExcelFile")).toInstance(config.sqsReadExcelFile)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsChatGptFileSent")).toInstance(config.sqsChatGptFileSent)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsChatGptFileReady")).toInstance(config.sqsChatGptFileReady)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsExcelFileResult")).toInstance(config.sqsExcelFileResult)

        // Aws SNS Localstack Mock
        bind(SnsAsyncClient::class.java).toProvider(MockAwsSNSClientProvider::class.java).asEagerSingleton()

        // Aws sqs Localstack Mock
        bind(SqsAsyncClient::class.java).toProvider(MockAwsSQSClientProvider::class.java).asEagerSingleton()

        // Stubs
        bind(CreateMemberTransactionManagementStub::class.java).asEagerSingleton()
        bind(FileGateway::class.java).toInstance(spyk(FileGatewayStub()))
        bind(PropertyAssetsGateway::class.java).toInstance(spyk(PropertyAssetsGatewayStub()))
        bind(SignGateway::class.java).toInstance(mockk())
        bind(PropertyDetailsGateway::class.java).toInstance(mockk())
        bind(TaskChangedPublisher::class.java).toInstance(mockk(relaxed = true))
        bind(DealEventPublisher::class.java).toInstance(mockk(relaxed = true))
        bind(ChatGptGateway::class.java).toInstance(ChatGptGatewayStub())
        bind(SQSChatGptFileSentClient::class.java).toInstance(mockk(relaxed = true))
        bind(SQSReadExcelFileClient::class.java).toInstance(mockk(relaxed = true))
        bind(EmailGateway::class.java).toInstance(spyk(EmailGatewayStub()))

        // Google Calendar
        bind(CalendarGateway::class.java).toInstance(CalendarGatewayStub())
        bind(Calendar::class.java).toInstance(mockk(relaxed = true))

        // Kotlin's coroutines test
        bind(CoroutineScope::class.java).toProvider(MockCoroutineScopeProvider::class.java).asEagerSingleton()

        // Sendgrid
        bind(SendGrid::class.java).toInstance(mockk { SendGrid::class.java })

        // Split Io
        bind(SplitClient::class.java).toInstance(mockk(relaxed = true))
        bind(FeatureFlags::class.java).toInstance(FeatureFlagsStub())
    }
}
