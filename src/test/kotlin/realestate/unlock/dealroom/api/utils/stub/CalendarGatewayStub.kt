package realestate.unlock.dealroom.api.utils.stub

import realestate.unlock.dealroom.api.core.gateway.calendar.CalendarGateway
import realestate.unlock.dealroom.api.core.gateway.calendar.CreateDealCalendarInput
import realestate.unlock.dealroom.api.core.gateway.calendar.DealCalendar
import realestate.unlock.dealroom.api.core.gateway.calendar.DealCalendarEvent
import realestate.unlock.dealroom.api.utils.extensions.anyString

class CalendarGatewayStub : CalendarGateway {
    override fun createCalendar(input: CreateDealCalendarInput): String = anyString()

    override fun updateCalendar(dealCalendar: DealCalendar) = Unit

    override fun deleteCalendar(calendarId: String) = Unit

    override fun createEvent(dealCalendarEvent: DealCalendarEvent) = Unit

    override fun updateEvent(dealCalendarEvent: DealCalendarEvent) = Unit

    override fun deleteEvent(calendarId: String, eventId: String) = Unit

    override fun findEvent(calendarId: String, eventId: String): DealCalendarEvent? = null
}
