package realestate.unlock.dealroom.api.utils.security

import realestate.unlock.dealroom.api.core.entity.user.auth.AuthorizationToken
import realestate.unlock.dealroom.api.core.entity.user.auth.Claims
import realestate.unlock.dealroom.api.utils.extensions.anyString

fun anyUserToken() = AuthorizationToken(
    anyString(),
    Claims(
        iss = "iss",
        aud = listOf("aud"),
        exp = 0,
        permissions = listOf(),
        organizationId = anyString()
    )
)
