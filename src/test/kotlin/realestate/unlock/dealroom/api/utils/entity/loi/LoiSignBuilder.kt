package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.loi.LetterOfIntentSign
import realestate.unlock.dealroom.api.core.entity.loi.MemberSign
import realestate.unlock.dealroom.api.utils.extensions.anyString

class LoiSignBuilder {

    var loiId: Long = 3L
    var signingId: String = anyString()
    var buyerSign: MemberSign = MemberSignBuilder().forBuyer().build()
    var sellerSign: MemberSign = MemberSignBuilder().build()

    fun build() = LetterOfIntentSign(
        loiId = loiId,
        signingId = signingId,
        documentId = loiId,
        buyerSign = buyerSign,
        sellerSign = sellerSign
    )
}
