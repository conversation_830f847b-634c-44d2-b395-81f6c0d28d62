package realestate.unlock.dealroom.api.utils.entity.deal

import realestate.unlock.dealroom.api.core.entity.deal.category.DealCategory
import realestate.unlock.dealroom.api.utils.extensions.anyId
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.OffsetDateTime

class DealCategoryBuilder {

    var id: Long = anyId()
    var dealId: Long = anyId()
    var categoryKey: String = anyString()
    val createdAt: OffsetDateTime = OffsetDateTime.now()

    fun build() = DealCategory(
        id = id,
        dealId = dealId,
        categoryKey = categoryKey,
        createdAt = createdAt
    )
}
