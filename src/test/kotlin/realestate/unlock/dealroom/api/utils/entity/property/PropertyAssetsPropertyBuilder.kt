package realestate.unlock.dealroom.api.utils.entity.property

import realestate.unlock.dealroom.api.core.entity.deal.SourceType
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.GeoPointResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyAssetsResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyType
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.PropertyAdditionalDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.images.PropertyImagesResponse
import java.math.BigDecimal

class PropertyAssetsPropertyBuilder {
    var id: String = "kwid"
    var address: String = "address"
    var fullAddress: String = "full address"
    var city: String = "city"
    var county: String = "county"
    var zipCode: Long = 123
    var state: String = "state"
    var tractCode: Long? = 444
    var location: GeoPointResponse = GeoPointResponse(latitude = BigDecimal(12), longitude = BigDecimal(21))
    var propertyType: PropertyType = PropertyType.MULTIFAMILY
    var squareFootage: BigDecimal? = BigDecimal(333)
    var sourceType: SourceType? = SourceType.ON_MARKET
    var images: PropertyImagesResponse? = null
    var additionalData: PropertyAdditionalDataResponse? = null
    var constructionYear: Int? = 1980

    fun withId(id: String) = apply { this.id = id }
    fun withAdditionalData(additionalData: PropertyAdditionalDataResponse) = apply { this.additionalData = additionalData }

    fun build() = PropertyAssetsResponse(
        id = id,
        address = address,
        fullAddress = fullAddress,
        city = city,
        county = county,
        zipCode = zipCode,
        state = state,
        tractCode = tractCode,
        location = location,
        propertyType = propertyType,
        squareFootage = squareFootage,
        sourceType = sourceType,
        additionalData = additionalData,
        images = images,
        constructionYear = constructionYear
    )
}
