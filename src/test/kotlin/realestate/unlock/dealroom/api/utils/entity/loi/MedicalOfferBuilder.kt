package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.loi.*
import realestate.unlock.dealroom.api.core.entity.loi.file.LetterOfIntentFile
import realestate.unlock.dealroom.api.utils.entity.member.MemberObjectMother
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.math.BigDecimal
import java.time.LocalDate

@Suppress("MemberVisibilityCanBePrivate")
class MedicalOfferBuilder {

    var files: List<LetterOfIntentFile> = listOf(LetterOfIntentFile(id = 45L, memberId = 23L, name = anyString(), kFileId = anyString()))
    var salesPrice: BigDecimal = BigDecimal.ONE
    var lease: Lease = LeaseBuilder().build()
    var closing: Closing = ClosingBuilder().build()
    var contractTermination: LocalDate = LocalDate.now()
    var earnestMoneyDeposit: BigDecimal = BigDecimal.TEN
    var comments: String? = null
    var createdBy = MemberObjectMother.buyer()
    var dueDiligence: DueDiligence = DueDiligenceBuilder().build()
    var customSections: OfferCustomSections? = OfferCustomSections(
        sections = listOf(
            OfferCustomSection(
                title = "section title",
                content = "section content"
            )
        )
    )
    var closingCost: ClosingCost = ClosingCost.ORIGINAL

    fun build() = MedicalLoiOffer(
        files = files,
        salesPrice = salesPrice,
        lease = lease,
        closing = closing,
        contractTermination = contractTermination,
        earnestMoneyDeposit = earnestMoneyDeposit,
        comments = comments,
        createdBy = createdBy,
        dueDiligence = dueDiligence,
        customSections = customSections,
        closingCost = closingCost
    )
}
