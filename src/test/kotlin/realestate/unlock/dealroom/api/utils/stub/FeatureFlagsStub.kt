package realestate.unlock.dealroom.api.utils.stub

import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags

class FeatureFlagsStub(
    private val flags: MutableMap<Feature, Boolean> = mutableMapOf()
) : FeatureFlags {

    override fun isOn(feature: Feature, user: String, context: Map<String, Any>): Boolean {
        return flags[feature] ?: false
    }

    fun configureFeature(feature: Feature, on: Boolean) {
        flags[feature] = on
    }

    fun clear() {
        flags.clear()
    }
}
