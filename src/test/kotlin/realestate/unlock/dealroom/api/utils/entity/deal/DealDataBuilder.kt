package realestate.unlock.dealroom.api.utils.entity.deal

import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.deal.DealData
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.utils.entity.property.PropertyBuilder

class DealDataBuilder {

    var deal: Deal = DealBuilder().build()
    var property: Property = PropertyBuilder().build()

    fun build() =
        DealData(
            deal = deal,
            property = property
        )
}
