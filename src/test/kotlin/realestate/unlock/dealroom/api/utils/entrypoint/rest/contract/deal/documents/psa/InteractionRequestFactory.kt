package realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.deal.documents.psa

import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.*
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.FileRequest
import realestate.unlock.dealroom.api.utils.entrypoint.rest.contract.file.FileRequestBuilder
import realestate.unlock.dealroom.api.utils.extensions.anyString
import java.time.LocalDate

object InteractionRequestFactory {

    val documentType: DocumentType = DocumentType.PSA

    fun draft(
        cleanVersion: FileRequest? = FileRequestBuilder().build(),
        redLineVersion: FileRequest? = FileRequestBuilder().build(),
        comment: String? = anyString(),
        expectedDate: LocalDate? = null
    ) = NewDraftVersionInteractionRequest(
        cleanVersion = cleanVersion,
        redLineVersion = redLineVersion,
        comment = comment,
        type = InteractionRequestType.newDraft,
        expectedBy = expectedDate
    )

    fun comment(comment: String = anyString()) = CommentInteractionRequest(comment = comment, type = InteractionRequestType.comment)

    fun share(comment: String = anyString()) = ShareDocumentRequest(comment = comment, type = InteractionRequestType.share)

    fun legalReview(comment: String = anyString()) = LegalReviewDocumentRequest(comment = comment, type = InteractionRequestType.legalReview)

    fun businessReview(comment: String = anyString()) = BusinessReviewDocumentRequest(comment = comment, type = InteractionRequestType.businessReview)

    fun onHold(comment: String = anyString()) = OnHoldDocumentRequest(comment = comment, type = InteractionRequestType.onHold)

    fun resume(comment: String = anyString()) = ResumeDocumentRequest(comment = comment, type = InteractionRequestType.resume)

    fun changeExpectedDate(expectedDate: LocalDate) = ChangeExpectedDateRequest(type = InteractionRequestType.changeExpectedDate, expectedBy = expectedDate, comment = null)
}
