package realestate.unlock.dealroom.api.utils.creator

import org.eclipse.jetty.http.HttpHeader
import realestate.unlock.dealroom.api.core.entity.loi.MultifamilyLoiRound
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.LoiMedicalRound
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.loi.post.request.LetterOfIntentFileRequest
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.utils.entity.loi.PostMedicalLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.entity.loi.PostMultifamilyLoiRequestBuilder
import realestate.unlock.dealroom.api.utils.mocks.AuthMock
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.nio.charset.Charset
import java.time.Duration

object LoiCreator {

    private val client = HttpClient.newBuilder()
        .version(HttpClient.Version.HTTP_2)
        .build()

    fun createMedicalLoiByRest(
        dealId: Long,
        offerFiles: List<LetterOfIntentFileRequest>,
        localUrl: String = "http://localhost:8080"
    ): LoiMedicalRound {
        val givenLoiCreationUrl = "$localUrl/deal/$dealId/loi/round"
        val givenLoiRequestCreationBody = PostMedicalLoiRequestBuilder()
            .apply { this.offerFiles = offerFiles }
            .build()

        return client.send(
            HttpRequest.newBuilder()
                .uri(URI(givenLoiCreationUrl))
                .POST(HttpRequest.BodyPublishers.ofString(JsonMapper.encode(givenLoiRequestCreationBody), Charset.defaultCharset()))
                .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken())
                .timeout(Duration.ofSeconds(10))
                .build(),
            HttpResponse.BodyHandlers.ofString()
        ).let { JsonMapper.decode(it.body(), LoiMedicalRound::class.java) }
    }

    fun createMultiFamilyLoiByRest(
        dealId: Long,
        offerFiles: List<LetterOfIntentFileRequest>,
        localUrl: String = "http://localhost:8080"
    ): MultifamilyLoiRound {
        val givenLoiCreationUrl = "$localUrl/deal/$dealId/loi/round"
        val givenLoiRequestCreationBody = PostMultifamilyLoiRequestBuilder()
            .apply { this.offerFiles = offerFiles }
            .build()

        return client.send(
            HttpRequest.newBuilder()
                .uri(URI(givenLoiCreationUrl))
                .POST(HttpRequest.BodyPublishers.ofString(JsonMapper.encode(givenLoiRequestCreationBody), Charset.defaultCharset()))
                .header(HttpHeader.AUTHORIZATION.name, AuthMock.getAuthBearerToken())
                .timeout(Duration.ofSeconds(10))
                .build(),
            HttpResponse.BodyHandlers.ofString()
        ).let { JsonMapper.decode(it.body(), MultifamilyLoiRound::class.java) }
    }
}
