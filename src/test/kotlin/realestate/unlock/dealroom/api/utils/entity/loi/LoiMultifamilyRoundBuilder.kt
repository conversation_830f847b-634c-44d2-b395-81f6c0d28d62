package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.loi.*
import java.time.LocalDate

class LoiMultifamilyRoundBuilder {

    var id: Long = 543L
    var date: LocalDate = LocalDate.now()
    var dealId: Long = 444L
    var status: LetterOfIntentRoundStatus = LetterOfIntentRoundStatus.IN_NEGOTIATION
    var offer: MultifamilyOffer = MultifamilyOfferBuilder().build()
    var offerResponse: OfferResponse? = null
    var broker: Broker = BrokerBuilder().build()
    var legalEntity: String = "legal"
    var propertySquareFootage: Int = 10000
    var loan: Loan = Loan.FREE_AND_CLEAR
    var dueDiligenceExtensionPeriod: Long = 1L
    var dueDiligencePeriod: Long = 1L
    var dueDate: LocalDate? = null
    var closingPeriod: Long = 1L
    var closingPeriodExtensionPeriod: Long = 1L

    fun withStatus(status: LetterOfIntentRoundStatus) = apply { this.status = status }

    fun build() = MultifamilyLoiRound(
        id = id,
        date = date,
        dealId = dealId,
        broker = broker,
        status = status,
        offer = offer,
        offerResponse = offerResponse,
        legalEntity = legalEntity,
        loan = loan,
        dueDiligenceExtensionPeriod = dueDiligenceExtensionPeriod,
        dueDiligencePeriod = dueDiligencePeriod,
        dueDate = dueDate,
        closingPeriod = closingPeriod,
        closingExtensionPeriod = closingPeriodExtensionPeriod,
        propertySquareFootage = propertySquareFootage
    )
}
