package realestate.unlock.dealroom.api.utils.entity.loi

import realestate.unlock.dealroom.api.core.entity.deal.RentStepType
import realestate.unlock.dealroom.api.core.entity.loi.Lease
import java.math.BigDecimal

class LeaseBuilder {

    var rent: BigDecimal = BigDecimal.ONE
    var type: String = "NNN"
    var rentIncrease: Float = 4f
    var increaseEveryYear: Float = 1f
    var length: Long = 4
    var expirationYear: Long = 2024
    var numberOfOptions: Long = 2
    var optionLengths: Long = 1
    var condition: String = "condition"
    var rentCpi: Float? = 1f
    var rentStepType: RentStepType = RentStepType.CPI

    fun build() = Lease(
        rent = rent,
        type = type,
        rentIncrease = rentIncrease,
        increaseEveryYear = increaseEveryYear,
        length = length,
        expirationYear = expirationYear,
        numberOfOptions = numberOfOptions,
        optionLengths = optionLengths,
        condition = condition,
        rentCpi = rentCpi,
        rentStepType = rentStepType
    )
}
