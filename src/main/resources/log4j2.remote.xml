<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Appenders>
        <Console name="STDOUT" target="SYSTEM_OUT">
            <JSONLayout compact="true" eventEol="true" properties="true" stacktraceAsString="true" />
        </Console>
    </Appenders>
    <Loggers>
        <Logger name="realestate.unlock" level="info" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Logger name="com.keyway" level="info" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Logger name="io.javalin" level="info" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Root level="warn">
            <AppenderRef ref="STDOUT"/>
        </Root>
    </Loggers>
</Configuration>