create table if not exists "file_type_prompt"
(
    id              serial not null,
    report_type     varchar not null,
    prompt          varchar not null,

    constraint file_type_prompt_pk_id primary key (id),
    constraint file_type_prompt_uq_report_type unique (report_type)
);

insert into file_type_prompt (report_type, prompt)
values
    ('DEFAULT', 'Please use the following document excerpts to answer the questions below if possible, answering as you are a real estate agent. If you are unable to answer the question from the sources, use your knowledge only if you are asked to do so. If you are unable to answer the question from the sources or your knowledge, write ''\''''Unable to answer''\'''' and move on to the next question.'),
    ('ZONING', 'Act as an agent of commercial real estate expert in diligence report analysis. Use your knowledge about commercial real estate, multifamily, due dilgence and zoning documents. Write a summary with current findings and facts in bullet point format, and indicate what page you got each piece of information from. Do not include anything related to information that was not found in the document. Tell me about informed things that might affect the commercial real estate multifamily investment and that could be risky.');

insert into gpt_report_questions(report_type, question, priority, ask_automatically)
values ('ZONING', 'Write a summary about the following items:
- Property summary Property Data
- Regulatory Response Summary and Violations
- Conformance and Nonconforming Characteristics
- Damage and Reconstruction
- Parking status', 10, true)