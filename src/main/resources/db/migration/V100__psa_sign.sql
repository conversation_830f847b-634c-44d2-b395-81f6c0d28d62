CREATE TABLE IF NOT EXISTS public.psa_sign
(
    psa_id bigint NOT NULL,
    envelope_id character varying,
    document_id character varying,
    signer_recipient_id character varying,
    signer_email character varying,
    signer_first_name character varying,
    signer_last_name character varying,
    signer_team_type character varying NOT NULL,
    sign_completed_at timestamp with time zone,
    created_at timestamp with time zone NOT NULL
);

ALTER TABLE file ALTER COLUMN member_id DROP NOT NULL;
ALTER TABLE psa_round_interaction ALTER COLUMN member_id DROP NOT NULL;