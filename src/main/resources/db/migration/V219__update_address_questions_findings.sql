update finding_questions
set query = 'Given the following deal information:
{
    "property_address": "244 South Rainbow Boulevard, Las Vegas, Nevada 89145"
}

Respond indicating whether there is an error in the report information and, if so, whether the property address match. Your response should follow this format:

{
  "property_address": "[the deal information property address]",
  "report_property_address": "[replace with the property address in the report document]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "property_address": "102 28th Street, Fair Lawn, NJ, 07410",
  "report_property_address": "102 28th Street, Fair Lawn, NJ, 07410",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the property addresses don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}',
    prompt = 'Act as an agent of commercial real estate expert in diligence report analysis. Use your knowledge about commercial real estate, multifamily, nnn, due diligence and reports documents.'
where key like 'REPORT_%_ADDRESS';