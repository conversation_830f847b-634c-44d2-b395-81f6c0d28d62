UPDATE deal_schema
    SET view_schema = '{"sections": [{"name": "PROPERTY", "type": "DEFAULT"}, {"name": "INTERNAL_DATA", "type": "CUSTOM", "title": "Internal Data", "fields": ["askingSalePrice", "askingNoi", "askingCapRate", "offerNoi", "offerCapRate", "creditType"]},{"name": "DEAL_DETAIL", "type": "DEFAULT"},{"name": "LEASE", "type": "DEFAULT"},{"name": "NOTES", "type": "CUSTOM", "title": "Notes", "fields": ["propertyNotes", "updateNotes", "pipelineNotes"]}]}',
        json_schema = '{"type": "object", "properties":{ "updateNotes": {"type": "string", "title": "Update Notes"}, "pipelineNotes": {"type": "string", "title": "Pipeline Notes"}, "propertyNotes": {"type": "string", "title": "Property Notes"}, "askingSalePrice": {"type": "number", "title": "Asking Sale Price"},"offerNoi": {"type": "number", "title": "Offer NOI"}, "askingCapRate": {"type": "number", "title": "Asking Cap Rate"}, "askingNoi": {"type": "number", "title": "Asking NOI"}, "offerCapRate": {"type": "number", "title": "Offer Cap Rate"}, "creditType": {"type": "number", "title": "Credit Type"}}}',
        field_definitions = '{"askingNoi": {"kind": "CURRENCY"},"offerNoi": {"kind": "CURRENCY"},"updateNotes": {"kind": "NOTE"},"pipelineNotes": {"kind": "NOTE"},"propertyNotes": {"kind": "NOTE"}, "askingSalePrice": {"kind": "CURRENCY"}}'
    WHERE id = 1;

UPDATE deal_schema
    SET view_schema = '{"sections": [{"name": "PROPERTY", "type": "DEFAULT"}, {"name": "INTERNAL_DATA", "type": "CUSTOM", "title": "Internal Data", "fields": ["askingSalePrice", "submarket", "tourDate", "callForOffers", "units"]},{"name": "DEAL_DETAIL", "type": "DEFAULT"}, {"name": "NOTES", "type": "CUSTOM", "title": "Notes", "fields": ["propertyNotes", "updateNotes", "pipelineNotes"]}]}',
        json_schema = '{"type": "object", "properties": {"units": {"type": "integer", "title": "Units"}, "tourDate": {"type": "date", "title": "Tour Date"}, "submarket": {"type": "string", "title": "Submarket"}, "updateNotes": {"type": "string", "title": "Update Notes"}, "callForOffers": {"type": "string", "title": "Call for offers"}, "pipelineNotes": {"type": "string", "title": "Pipeline Notes"}, "propertyNotes": {"type": "string", "title": "Property Notes"}, "askingSalePrice": {"type": "number", "title": "Asking Sale Price"}}}',
        field_definitions = '{"updateNotes": {"kind": "NOTE"},"pipelineNotes": {"kind": "NOTE"},"propertyNotes": {"kind": "NOTE"},"askingSalePrice": {"kind": "CURRENCY"}}'
    WHERE id = 2;