INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
    (
	    'multifamily_main_analysis',
	    'form',
	    'buyer',
	    'Property Data Request',
	    '',
	    'multifamily_property_data_request',
	    '{"type":"object","properties":{"rentRoll":{"type":"number","title":"Rent Roll"},"propertyFinancials":{"type":"number","title":"Property Financials"}}}',
	    null,
	    'LOW',
	    3,
	    false,
	    false,
	    'BUYER_TEAM'
	),
	(
    	'multifamily_main_analysis',
    	'form_and_file',
    	'buyer',
        'Underwriting Model',
    	'',
        'multifamily_underwriting_model',
    	'{"type":"object","properties":{"checklist":{"type":"object","properties":{"createUnderwritingModel":{"type":"boolean","title":"Create Underwriting Model"},"assumptionsAndReturnsTab":{"type":"boolean","title":"Assumptions & Returns Tab"},"growthAssumptionTab":{"type":"boolean","title":"Growth Assumption Tab"},"otherIncomeAndOpExTab":{"type":"boolean","title":"Other Income & OpEx Tab"},"renoPremiumsTab":{"type":"boolean","title":"Reno Premiums Tab"},"projectBudgetTab":{"type":"boolean","title":"Project Budget Tab"},"drawScheduleTab":{"type":"boolean","title":"Draw Schedule Tab"},"rentalComps":{"type":"boolean","title":"Rental Comps"},"saleCompsTab":{"type":"boolean","title":"Sale Comps Tab"}}},"mainAnalysis":{"type":"string","title":"Please upload Main Analysis","format":"data-url"}}}',
    	null,
    	'LOW',
    	4,
    	false,
    	false,
    	'BUYER_TEAM'
    ),
    (
	    'multifamily_main_analysis',
	    'upload_file',
	    'buyer',
	    'Submarket Tour Checklist',
	    '',
	    'multifamily_submarket_tour_checklist',
	    '{"type":"object","required":["submarketTourChecklist"],"properties":{"submarketTourChecklist":{"type":"string","title":"Please upload Submarket Tour Checklist","format":"data-url"}}}',
	    null,
	    'LOW',
	    5,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_main_analysis',
	    'upload_file',
	    'buyer',
	    'Property Tour Checklist',
	    '',
	    'multifamily_property_tour_checklist',
	    '{"type":"object","required":["propertyTourChecklist"],"properties":{"propertyTourChecklist":{"type":"string","title":"Please upload Property Tour Checklist","format":"data-url"}}}',
	    null,
	    'LOW',
	    6,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_main_analysis',
	    'form',
	    'buyer',
	    'Acquisitions Team Reviews Underwriting w/ Asset Management',
	    '',
	    'multifamily_acquisitions_team_reviews_underwriting',
	    '{"type":"object","required":["acquisitionsTeamReviewsUnderwriting"],"properties":{"acquisitionsTeamReviewsUnderwriting":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    7,
	    false,
	    false,
	    'BUYER_TEAM'
	)	;

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('multifamily', 'multifamily_property_data_request'),
    ('multifamily', 'multifamily_underwriting_model'),
    ('multifamily', 'multifamily_submarket_tour_checklist'),
    ('multifamily', 'multifamily_property_tour_checklist'),
    ('multifamily', 'multifamily_acquisitions_team_reviews_underwriting');