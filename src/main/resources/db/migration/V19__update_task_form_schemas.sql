
update public.task_template set form_schema = '{"type":"object","properties":{"parkingRevenue":{"type":"boolean","title":"Is there any parking revenue?"},"parkingExpenses":{"type":"boolean","title":"Is there any parking expenses?"},"parkingAgreement":{"type":"boolean","title":"Is there any parking agreement?"},"file":{"type":"string","format":"data-url","title":"Please upload any related parking agreements, revenue, or expenses"}}}'
where key = 'parking_revenue_expenses_and_management';

update public.task_template set form_schema = '{"type":"object","properties":{"serviceContracts":{"type":"boolean","title":"Are there any service contracts at the landlord level?"}},"dependencies":{"serviceContracts":{"oneOf":[{"properties":{"serviceContracts":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload any service contracts"}}}]}}}'
where key = 'service_contracts';

update public.task_template set form_schema = '{"type":"object","properties":{"personalProperty":{"type":"boolean","title":"Is there any personal property that would transfer ownership upon close?"}},"dependencies":{"personalProperty":{"oneOf":[{"properties":{"personalProperty":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload an inventory of the personal property"}}}]}}}'
where key = 'personal_property_inventory';

update public.task_template set form_schema = '{"type":"object","properties":{"rentRoll":{"type":"boolean","title":"Is there a rent roll?"}},"dependencies":{"rentRoll":{"oneOf":[{"properties":{"rentRoll":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload the tenant rent roll"}}}]}}}'
where key = 'current_property_rent_roll';

update public.task_template set form_schema = '{"type":"object","required":["visitOn","name","phone","email"],"properties":{"visitOn":{"type":"string","title":"Unlock scheduled a site visit for","format":"date"},"name":{"type":"string","title":"Name"},"phone":{"type":"number","title":"Phone","format":"phone"},"email":{"type":"string","title":"Email","format":"email"}}}'
where key = 'new_physical_inspection';

update public.task_template set form_schema = '{"type":"object","required":["visitOn","name","phone","email"],"properties":{"visitOn":{"type":"string","title":"Unlock scheduled a site visit for","format":"date"},"name":{"type":"string","title":"Name"},"phone":{"type":"number","title":"Phone","format":"phone"},"email":{"type":"string","title":"Email","format":"email"}}}'
where key = 'environmental';

update public.task_template set form_schema = '{"type":"object","required":["visitOn","name","phone","email"],"properties":{"visitOn":{"type":"string","title":"Unlock scheduled a site visit for","format":"date"},"name":{"type":"string","title":"Name"},"phone":{"type":"number","title":"Phone","format":"phone"},"email":{"type":"string","title":"Email","format":"email"}}}'
where key = 'phase_ii_environmental_assessment';

update public.task_template set form_schema = '{"type":"object","required":["lotBlock","parcelNumber","constructedOn"],"properties":{"lotBlock":{"type":"string","title":"Lot & Block"},"parcelNumber":{"type":"number","title":"Tax Parcel Number"},"constructedOn":{"type":"string","title":"Date of Construction","format":"date"}}}'
where key = 'zoning';

update public.task_template set form_schema = '{"type":"object","properties":{"warrantiesGuarantees":{"type":"boolean","title":"Are there any current warranties or guarantees?"}},"dependencies":{"warrantiesGuarantees":{"oneOf":[{"properties":{"warrantiesGuarantees":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload any warranties or guarantees"}}}]}}}'
where key = 'remaining_warranties_guarantees';

update public.task_template set form_schema = '{"type":"object","required":["constructionType","enclosedParking","openParking","stories","builtOn"],"properties":{"constructionType":{"type":"string","title":"What is the construction type?"},"enclosedParking":{"type":"number","title":"How many square feet of enclosed parking is there?"},"openParking":{"type":"number","title":"How many square feet of open parking area is there?"},"stories":{"type":"number","title":"How many stories is the asset?"},"builtOn":{"type":"string","title":"What year was it built? ","format":"date"},"wiringUpdatedOn":{"type":"string","title":"What year was the wiring updated?","format":"date"},"heatingUpdatedOn":{"type":"string","title":"What year was the heating updated?","format":"date"},"wiringType":{"type":"string","title":"What is the wiring type?"},"emergencyLighting":{"type":"boolean","title":"Is there emergency lighting?"},"smokeDetector":{"type":"string","title":"Is the smoke detector battery or hardwired?","enum":["","Battery","Hardwired"]},"alarm":{"type":"string","title":"Is the alarm CS or local?","enum":["","CS","Local"]},"sprinklerCoverage":{"type":"number","title":"What sprinkler coverage is there? (in percentage) "},"security":{"type":"boolean","title":"Is there security?"}}}'
where key = 'insurance';
