alter table deal add column status varchar;

update deal set status = deal_status.current_status
from (
        select deal_id,
               case
                    when (lois.loi_id is null) then 'NEW'
                    when (lois.seller_signer_completed is not null and lois.buyer_signer_completed is not null) then 'CONTRACT_NEGOTIATION'
                    else 'LOI_SUBMITTED'
               end	as current_status
        from (
             select d.id as deal_id, l.id as loi_id, ls.loi_id as loi_sign_id, ls.seller_signer_completed, ls.buyer_signer_completed
             from deal d
                left join loi l on d.id = l.deal_id
                left join loi_sign ls on l.id = ls.loi_id
            ) lois
    ) deal_status
where deal.id = deal_status.deal_id;

alter table deal alter column status set not null;

