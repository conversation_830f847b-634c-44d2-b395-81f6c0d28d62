create table if not exists "loi_sign"
(
    loi_id                             bigint not null,
    signing_id                         varchar not null,
    created_at                         timestamp with time zone not null,
    seller_signer_id                   bigint not null,
    seller_signer_completed            timestamp with time zone,
    buyer_signer_id                    bigint not null,
    buyer_signer_completed             timestamp with time zone,
    constraint loi_sign_fk_loi foreign key (loi_id) references "loi"(id),
    constraint loi_sign_signing_id_uk_key unique (signing_id)
);