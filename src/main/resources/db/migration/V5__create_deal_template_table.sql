
------ DEAL_TEMPLATE -------
create table if not exists "deal_template"
(
    id                      serial  not null,
    task_category           varchar not null,
    task_type               varchar not null,
    member_type_assignment  varchar not null,
    task_title              varchar not null,
    task_description        varchar not null,

    constraint deal_template_pk_id primary key (id),
    constraint deal_template_fk_category foreign key (task_category) references "category"("key"),
    constraint deal_template_fk_task_type foreign key (task_type) references "task_type"("key"),
    constraint deal_template_fk_member_type foreign key (member_type_assignment) references "member_type"("key")
);