
update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'records_and_correspondence_regarding_pending_or_threatened_litigation';

update public.task_template set form_schema = '{"type":"object","properties":{"liens":{"type":"boolean","title":"Are there any outstanding liens?"}},"dependencies":{"liens":{"oneOf":[{"properties":{"liens":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload any documents relating to liens"}}}]}}}'
where key = 'third_parties_with_respect_to_liens';

update public.task_template set form_schema = '{"type":"object","properties":{"parking_revenue":{"type":"boolean","title":"Is there any parking revenue?"},"parking_expenses":{"type":"boolean","title":"Is there any parking expenses?"},"parking_agreement":{"type":"boolean","title":"Is there any parking agreement?"},"file":{"type":"string","format":"data-url","title":"Please upload any related parking agreements, revenue, or expenses"}}}'
where key = 'parking_revenue_expenses_and_management';

update public.task_template set form_schema = '{"type":"object","properties":{"service_contracts":{"type":"boolean","title":"Are there any service contracts at the landlord level?"}},"dependencies":{"service_contracts":{"oneOf":[{"properties":{"service_contracts":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload any service contracts"}}}]}}}'
where key = 'service_contracts';

update public.task_template set form_schema = '{"type":"object","properties":{"personal_property":{"type":"boolean","title":"Is there any personal property that would transfer ownership upon close?"}},"dependencies":{"personal_property":{"oneOf":[{"properties":{"personal_property":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload an inventory of the personal property"}}}]}}}'
where key = 'personal_property_inventory';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload an inventory of the personal property"}}}'
where key = 'additional_lease_documents';

update public.task_template set form_schema = '{"type":"object","properties":{"sublease":{"type":"boolean","title":"Is there a sublease?"}},"dependencies":{"sublease":{"oneOf":[{"properties":{"sublease":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload any sublease documents"}}}]}}}'
where key = 'sublease_documents';

update public.task_template set form_schema = '{"title":"Tenant Correspondence","description":"Correspondence from the tenant related to missed payments, maintenance concerns, or anything else","type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any tenant correspondence"}}}'
where key = 'tenant_correspondence';

update public.task_template set form_schema = '{"type":"object","properties":{"estoppel":{"type":"boolean","title":"Is there a form estoppel attached to the lease?"}},"dependencies":{"estoppel":{"oneOf":[{"properties":{"estoppel":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload the form estoppel"}}}]}}}'
where key = 'tenant_estoppel';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'financial_statements';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'operating_budgets';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'real_estate_tax_bills';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'utility_bills';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'tax_return';

update public.task_template set form_schema = '{"type":"object","properties":{"rent_roll":{"type":"boolean","title":"Is there a rent roll?"}},"dependencies":{"rent_roll":{"oneOf":[{"properties":{"rent_roll":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload the tenant rent roll"}}}]}}}'
where key = 'current_property_rent_roll';

update public.task_template set form_schema = '{"type":"object","required":["visit_on"],"properties":{"visit_on":{"type":"string","title":"Unlock scheduled a site visit for","format":"date"},"contact":{"title":"The contact information for the site visitor is","type":"object","required":["name","phone","email"],"properties":{"name":{"type":"string","title":"Name"},"phone":{"type":"number","title":"Phone","format":"phone"},"email":{"type":"string","title":"Email","format":"email"}}}}}'
where key = 'new_physical_inspection';

update public.task_template set form_schema = '{"type":"object","required":["visit_on"],"properties":{"visit_on":{"type":"string","title":"Unlock scheduled a site visit for","format":"date"},"contact":{"title":"The contact information for the site visitor is","type":"object","required":["name","phone","email"],"properties":{"name":{"type":"string","title":"Name"},"phone":{"type":"number","title":"Phone","format":"phone"},"email":{"type":"string","title":"Email","format":"email"}}}}}'
where key = 'environmental';

update public.task_template set form_schema = '{"type":"object","required":["visit_on"],"properties":{"visit_on":{"type":"string","title":"Unlock scheduled a site visit for","format":"date"},"contact":{"title":"The contact information for the site visitor is","type":"object","required":["name","phone","email"],"properties":{"name":{"type":"string","title":"Name"},"phone":{"type":"number","title":"Phone","format":"phone"},"email":{"type":"string","title":"Email","format":"email"}}}}}'
where key = 'phase_ii_environmental_assessment';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'physical';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'seller_environmental_information';

update public.task_template set form_schema = '{"type":"object","required":["lot_block","parcel_number","constructed_on"],"properties":{"lot_block":{"type":"string","title":"Lot & Block"},"parcel_number":{"type":"number","title":"Tax Parcel Number"},"constructed_on":{"type":"string","title":"Date of Construction","format":"date"}}}'
where key = 'zoning';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'certificates_of_occupancy';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'as_builts';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'any_existing_seismic_reports';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'building_area_calculations';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'floor_plans';

update public.task_template set form_schema = '{"type":"object","properties":{"warranties_guarantees":{"type":"boolean","title":"Are there any current warranties or guarantees?"}},"dependencies":{"warranties_guarantees":{"oneOf":[{"properties":{"warranties_guarantees":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload any warranties or guarantees"}}}]}}}'
where key = 'remaining_warranties_guarantees';

update public.task_template set form_schema = '{"type":"object","properties":{"permits":{"type":"boolean","title":"Are there any permits?"}},"dependencies":{"permits":{"oneOf":[{"properties":{"permits":{"enum":[true]},"file":{"type":"string","format":"data-url","title":"Please upload any permits"}}}]}}}'
where key = 'building_permits';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'loss_run_report';

update public.task_template set form_schema = '{"type":"object","properties":{"file":{"type":"string","format":"data-url","title":"Please upload any related document"}}}'
where key = 'evidence_of_current_insurance';

update public.task_template set form_schema = '{"type":"object","required":["construction_type","enclosed_parking","open_parking","stories","built_on"],"properties":{"construction_type":{"type":"string","title":"What is the construction type?"},"enclosed_parking":{"type":"number","title":"How many square feet of enclosed parking is there?"},"open_parking":{"type":"number","title":"How many square feet of open parking area is there?"},"stories":{"type":"number","title":"How many stories is the asset?"},"built_on":{"type":"string","title":"What year was it built? ","format":"date"},"wiring_updated_on":{"type":"string","title":"What year was the wiring updated?","format":"date"},"heating_updated_on":{"type":"string","title":"What year was the heating updated?","format":"date"},"wiring_type":{"type":"string","title":"What is the wiring type?"},"emergency_lighting":{"type":"boolean","title":"Is there emergency lighting?"},"smoke_detector":{"type":"string","title":"Is the smoke detector battery or hardwired?","enum":["","Battery","Hardwired"]},"alarm":{"type":"string","title":"Is the alarm CS or local?","enum":["","CS","Local"]},"sprinkler_coverage":{"type":"number","title":"What sprinkler coverage is there? (in percentage) "},"security":{"type":"boolean","title":"Is there security?"}}}'
where key = 'insurance';
