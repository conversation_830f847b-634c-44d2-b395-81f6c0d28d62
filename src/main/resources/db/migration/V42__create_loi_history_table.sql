create table if not exists "loi_history"
(
    id                                  serial not null,
    deal_id                             bigint not null,
    loi_id                              bigint not null,
    type                                varchar not null,
    description                         varchar,
    created_at                          timestamp with time zone not null,
    file_name                           varchar,
    file_id                             varchar,
    member_name                         varchar,
    member_team                         varchar,

    constraint loi_history_pk_id primary key (id),
    constraint loi_history_fk_loi foreign key (loi_id) references "loi"(id),
    constraint loi_history_fk_deal foreign key (deal_id) references "deal"(id)
);