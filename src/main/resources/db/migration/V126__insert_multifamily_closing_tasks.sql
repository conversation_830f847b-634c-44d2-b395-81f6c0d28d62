INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Promissory Note',
	    '',
	    'multifamily_promissory_note',
	    '{"type":"object","required":["promissoryNote"],"properties":{"promissoryNote":{"type":"string","title":"Please upload Promissory Note","format":"data-url"}}}',
	    null,
	    'LOW',
	    80,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Deed of Trust, Security Agreement and Fixture Filing/Mortgage',
	    '',
	    'multifamily_deed_of_trust',
	    '{"type":"object","properties":{"deedOfTrust":{"type":"string","title":"Please upload Deed of Trust","format":"data-url"},"securityAgreement":{"type":"string","title":"Please upload Security Agreement","format":"data-url"},"mortgage":{"type":"string","title":"Please upload Mortgage","format":"data-url"}}}',
	    null,
	    'LOW',
	    81,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Guaranty',
	    '',
	    'multifamily_guaranty',
	    '{"type":"object","required":["guaranty"],"properties":{"guaranty":{"type":"string","title":"Please upload Guaranty","format":"data-url"}}}',
	    null,
	    'LOW',
	    82,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'UCC Financing Statement',
	    '',
	    'multifamily_ucc_financing_statement',
	    '{"type":"object","required":["uccFinancingStatement"],"properties":{"uccFinancingStatement":{"type":"string","title":"Please upload UCC Financing Statement","format":"data-url"}}}',
	    null,
	    'LOW',
	    83,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Transfer Tax Form',
	    '',
	    'multifamily_transfer_tax_form',
	    '{"type":"object","required":["transferTaxForm"],"properties":{"transferTaxForm":{"type":"string","title":"Please upload Transfer Tax Form","format":"data-url"}}}',
	    null,
	    'LOW',
	    84,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Loan Commitment',
	    '',
	    'multifamily_loan_commitment',
	    '{"type":"object","required":["loanCommitment"],"properties":{"loanCommitment":{"type":"string","title":"Please upload Loan Commitment","format":"data-url"}}}',
	    null,
	    'LOW',
	    85,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Loan Agreement',
	    '',
	    'multifamily_loan_agreement',
	    '{"type":"object","required":["loanAgreement"],"properties":{"loanAgreement":{"type":"string","title":"Please upload Loan Agreement","format":"data-url"}}}',
	    null,
	    'LOW',
	    86,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Bill of Sale',
	    '',
	    'multifamily_bill_of_sale',
	    '{"type":"object","required":["billOfSale"],"properties":{"billOfSale":{"type":"string","title":"Please upload Bill of Sale","format":"data-url"}}}',
	    null,
	    'LOW',
	    87,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Exhibit A to Bill of Sale',
	    '',
	    'multifamily_exhibit_a_to_bill_of_sale',
	    '{"type":"object","required":["billOfSale"],"properties":{"billOfSale":{"type":"string","title":"Please upload Exhibit A to Bill of Sale","format":"data-url"}}}',
	    null,
	    'LOW',
	    88,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Assignment of Leases',
	    '',
	    'multifamily_assignment_of_leases',
	    '{"type":"object","required":["assignmentOfLeases"],"properties":{"assignmentOfLeases":{"type":"string","title":"Please upload Assignment of Leases","format":"data-url"}}}',
	    null,
	    'LOW',
	    89,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Exhibit A to Assignment of Leases',
	    '',
	    'multifamily_exhibit_a_to_assignment_of_leases',
	    '{"type":"object","required":["assignmentOfLeases"],"properties":{"assignmentOfLeases":{"type":"string","title":"Please upload Exhibit A to Assignment of Leases","format":"data-url"}}}',
	    null,
	    'LOW',
	    90,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Tenant Notices',
	    '',
	    'multifamily_tenant_notices',
	    '{"type":"object","required":["tenantNotices"],"properties":{"tenantNotices":{"type":"string","title":"Please upload Tenant Notices","format":"data-url"}}}',
	    null,
	    'LOW',
	    91,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Non-Foreign Certification',
	    '',
	    'multifamily_non_foreign_certification',
	    '{"type":"object","required":["nonForeignCertification"],"properties":{"nonForeignCertification":{"type":"string","title":"Please upload Non-Foreign Certification","format":"data-url"}}}',
	    null,
	    'LOW',
	    92,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Closing Resolutions',
	    '',
	    'multifamily_closingResolutions',
	    '{"type":"object","required":["closingResolutions"],"properties":{"closingResolutions":{"type":"string","title":"Please upload Closing Resolutions","format":"data-url"}}}',
	    null,
	    'LOW',
	    93,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Non-Compete Agreement',
	    '',
	    'multifamily_non_compete_agreement',
	    '{"type":"object","required":["nonCompeteAgreement"],"properties":{"nonCompeteAgreement":{"type":"string","title":"Please upload Non-Compete Agreement","format":"data-url"}}}',
	    null,
	    'LOW',
	    94,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Lien Releases/UCC Terminations',
	    '',
	    'multifamily_terminations',
	    '{"type":"object","required":["terminations"],"properties":{"terminations":{"type":"string","title":"Please upload Lien Releases/UCC Terminations","format":"data-url"}}}',
	    null,
	    'LOW',
	    95,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Closing Instructions',
	    '',
	    'multifamily_closing_instructions',
	    '{"type":"object","required":["closingInstructions"],"properties":{"closingInstructions":{"type":"string","title":"Please upload Closing Instructions","format":"data-url"}}}',
	    null,
	    'LOW',
	    96,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Title Closing Documents',
	    '',
	    'multifamily_title_closing_documents',
	    '{"type":"object","required":["titleClosingDocuments"],"properties":{"titleClosingDocuments":{"type":"string","title":"Please upload Title Closing Documents","format":"data-url"}}}',
	    null,
	    'LOW',
	    97,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loan_documents',
	    'upload_file',
	    'buyer',
	    'Additional Lender Documents',
	    '',
	    'multifamily_additional_lender_documents',
	    '{"type":"object","required":["additionalLenderDocuments"],"properties":{"additionalLenderDocuments":{"type":"string","title":"Please upload Additional Lender Documents","format":"data-url"}}}',
	    null,
	    'LOW',
	    98,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_insurance',
	    'upload_file',
	    'buyer',
	    'Comprehensive General Liability',
	    '',
	    'multifamily_comprehensive_general_liability',
	    '{"type":"object","required":["comprehensiveGeneralLiability"],"properties":{"comprehensiveGeneralLiability":{"type":"string","title":"Please upload Comprehensive General Liability","format":"data-url"}}}',
	    null,
	    'LOW',
	    99,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_insurance',
	    'upload_file',
	    'buyer',
	    'Flood Insurance',
	    '',
	    'multifamily_flood_insurance',
	    '{"type":"object","required":["floodInsurance"],"properties":{"floodInsurance":{"type":"string","title":"Please upload Flood Insurance","format":"data-url"}}}',
	    null,
	    'LOW',
	    99,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_insurance',
	    'upload_file',
	    'buyer',
	    'Property Damage Insurance',
	    '',
	    'multifamily_property_damage_insurance',
	    '{"type":"object","required":["propertyDamageInsurance"],"properties":{"propertyDamageInsurance":{"type":"string","title":"Please upload Property Damage Insurance","format":"data-url"}}}',
	    null,
	    'LOW',
	    100,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_equity_closing',
	    'form',
	    'buyer',
	    'Equity Fully Committed',
	    '',
	    'multifamily_equity_fully_committed',
	    '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    101,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_equity_closing',
	    'upload_file',
	    'buyer',
	    'Equity Structure',
	    '',
	    'multifamily_equity_structure',
	    '{"type":"object","required":["equityStructure"],"properties":{"equityStructure":{"type":"string","title":"Please upload Equity Structure","format":"data-url"}}}',
	    null,
	    'LOW',
	    102,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_equity_closing',
	    'upload_file',
	    'buyer',
	    'Waterfall Structure',
	    '',
	    'multifamily_waterfall_structure',
	    '{"type":"object","required":["waterfallStructure"],"properties":{"waterfallStructure":{"type":"string","title":"Please upload Waterfall Structure","format":"data-url"}}}',
	    null,
	    'LOW',
	    103,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_equity_closing',
	    'form',
	    'buyer',
	    'Equity Fully Called',
	    '',
	    'multifamily_equity_fully_called',
	    '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    104,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_closing_documents',
	    'upload_file',
	    'buyer',
	    'Settlement Statement',
	    '',
	    'multifamily_settlement_statement',
	    '{"type":"object","required":["settlementStatement"],"properties":{"settlementStatement":{"type":"string","title":"Please upload Settlement Statement","format":"data-url"}}}',
	    null,
	    'LOW',
	    105,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_closing_documents',
	    'upload_file',
	    'buyer',
	    'Finalize Rent Proration Amounts',
	    '',
	    'multifamily_rent_proration_amounts',
	    '{"type":"object","required":["rentProrationAmounts"],"properties":{"rentProrationAmounts":{"type":"string","title":"Please upload Rent Proration Amounts","format":"data-url"}}}',
	    null,
	    'LOW',
	    106,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_closing_documents',
	    'upload_file',
	    'buyer',
	    'Finalize Utility Deposit Amounts',
	    '',
	    'multifamily_utility_deposit_amounts',
	    '{"type":"object","required":["utilityDepositAmounts"],"properties":{"utilityDepositAmounts":{"type":"string","title":"Please upload Utility Deposit Amounts","format":"data-url"}}}',
	    null,
	    'LOW',
	    107,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_closing_documents',
	    'upload_file',
	    'buyer',
	    'Insurance Certificates',
	    '',
	    'multifamily_insurance_certificates',
	    '{"type":"object","required":["insuranceCertificates"],"properties":{"insuranceCertificates":{"type":"string","title":"Please upload Insurance Certificates","format":"data-url"}}}',
	    null,
	    'LOW',
	    108,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_closing_documents',
	    'upload_file',
	    'buyer',
	    'Include Third Party Invoices for Settlement Statement',
	    '',
	    'multifamily_third_party_invoices',
	    '{"type":"object","required":["thirdPartyInvoices"],"properties":{"thirdPartyInvoices":{"type":"string","title":"Please upload Third Party Invoices for Settlement Statement","format":"data-url"}}}',
	    null,
	    'LOW',
	    109,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_closing_documents',
	    'upload_file',
	    'buyer',
	    'Finalize Any Fee Agreement Amounts',
	    '',
	    'multifamily_fee_agreement_amounts',
	    '{"type":"object","required":["feeAgreementAmounts"],"properties":{"feeAgreementAmounts":{"type":"string","title":"Please upload Fee Agreement Amounts","format":"data-url"}}}',
	    null,
	    'LOW',
	    110,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_closing_documents',
	    'upload_file',
	    'buyer',
	    'Finalize and include Payment of Brokers and Agents',
	    '',
	    'multifamily_payment_of_brokers',
	    '{"type":"object","required":["paymentOfBrokers"],"properties":{"paymentOfBrokers":{"type":"string","title":"Please upload Payment of Brokers and Agents","format":"data-url"}}}',
	    null,
	    'LOW',
	    111,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_closing_documents',
	    'upload_file',
	    'buyer',
	    'State Transfer Taxes',
	    '',
	    'multifamily_state_transfer_taxes',
	    '{"type":"object","required":["stateTransferTaxes"],"properties":{"stateTransferTaxes":{"type":"string","title":"Please upload State Transfer Taxes","format":"data-url"}}}',
	    null,
	    'LOW',
	    111,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_closing',
	    'form',
	    'buyer',
	    'Contract Closing Date',
	    '',
	    'multifamily_contract_closing_date',
	    '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    112,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_extended_closing',
	    'form',
	    'buyer',
	    'Closing Extension',
	    '',
	    'multifamily_closing_extension',
	    '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    113,
	    false,
	    false,
	    'BUYER_TEAM'
	);

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('multifamily', 'multifamily_promissory_note'),
    ('multifamily', 'multifamily_deed_of_trust'),
    ('multifamily', 'multifamily_guaranty'),
    ('multifamily', 'multifamily_ucc_financing_statement'),
    ('multifamily', 'multifamily_transfer_tax_form'),
    ('multifamily', 'multifamily_loan_commitment'),
    ('multifamily', 'multifamily_loan_agreement'),
    ('multifamily', 'multifamily_bill_of_sale'),
    ('multifamily', 'multifamily_exhibit_a_to_bill_of_sale'),
    ('multifamily', 'multifamily_assignment_of_leases'),
    ('multifamily', 'multifamily_exhibit_a_to_assignment_of_leases'),
    ('multifamily', 'multifamily_tenant_notices'),
    ('multifamily', 'multifamily_non_foreign_certification'),
    ('multifamily', 'multifamily_closingResolutions'),
    ('multifamily', 'multifamily_non_compete_agreement'),
    ('multifamily', 'multifamily_terminations'),
    ('multifamily', 'multifamily_closing_instructions'),
    ('multifamily', 'multifamily_title_closing_documents'),
    ('multifamily', 'multifamily_additional_lender_documents'),
    ('multifamily', 'multifamily_comprehensive_general_liability'),
    ('multifamily', 'multifamily_flood_insurance'),
    ('multifamily', 'multifamily_property_damage_insurance'),
    ('multifamily', 'multifamily_equity_fully_committed'),
    ('multifamily', 'multifamily_equity_structure'),
    ('multifamily', 'multifamily_waterfall_structure'),
    ('multifamily', 'multifamily_equity_fully_called'),
    ('multifamily', 'multifamily_settlement_statement'),
    ('multifamily', 'multifamily_rent_proration_amounts'),
    ('multifamily', 'multifamily_utility_deposit_amounts'),
    ('multifamily', 'multifamily_insurance_certificates'),
    ('multifamily', 'multifamily_third_party_invoices'),
    ('multifamily', 'multifamily_fee_agreement_amounts'),
    ('multifamily', 'multifamily_payment_of_brokers'),
    ('multifamily', 'multifamily_state_transfer_taxes'),
    ('multifamily', 'multifamily_contract_closing_date'),
    ('multifamily', 'multifamily_closing_extension');
