ALTER TABLE "member" ADD COLUMN auth_id character varying;
ALTER TABLE "member" ADD COLUMN walk_through_done boolean;


UPDATE member me
SET auth_id = us.auth_id,
walk_through_done = us.walk_through_done,
enabled = us.enabled
FROM (SELECT member_id, auth_id, walk_through_done, enabled FROM "user") us
WHERE me.id = us.member_id;

UPDATE member
SET walk_through_done = false
WHERE walk_through_done IS NULL;

ALTER TABLE "member" ALTER COLUMN walk_through_done SET NOT NULL;