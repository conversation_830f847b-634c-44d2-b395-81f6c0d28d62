UPDATE deal_schema
    SET json_schema = '{"type": "object", "properties":{ "updateNotes": {"type": "string", "title": "Update Notes"}, "pipelineNotes": {"type": "string", "title": "Pipeline Notes"}, "propertyNotes": {"type": "string", "title": "Property Notes"}, "askingSalePrice": {"type": "number", "title": "Asking Sale Price"},"offerNoi": {"type": "number", "title": "Offer NOI"}, "askingCapRate": {"type": "number", "title": "Asking Cap Rate", "readOnly": true}, "askingNoi": {"type": "number", "title": "Asking NOI"}, "offerCapRate": {"type": "number", "title": "Offer Cap Rate"}, "creditType": {"type": "number", "title": "Credit Type"}}}'
    WHERE id = 1;
