INSERT INTO loi
(
deal_id,
tenant_name,
status,
date,
offer_response_date,
vertical,
guarantee_type,
property_square_footage,
offer_custom_sections,
offer_comments,
offer_response_comments,
offer_response_accepted,
offer_sales_price,
offer_lease_rent,
offer_lease_type,
offer_lease_rent_increase,
offer_lease_increase_every_year,
offer_lease_length,
offer_lease_expiration_year,
offer_lease_number_of_options,
offer_lease_option_lengths,
offer_lease_condition,
offer_rent_cpi,
offer_rent_step_type,
offer_contract_termination,
offer_earnest_money_deposit,
offer_closing_extension_deposit,
offer_closing_period,
offer_closing_period_extension,
offer_due_diligence_number,
offer_created_by_member_id,
offer_response_by_member_id,
broker_name,
broker_company_name,
created_at
)
SELECT
d.id,
COALESCE(d.tenant_name,'N/A') as tenant_name,
'ACCEPTED' as status,
COALESCE(d.loi_executed_date, CURRENT_DATE) as date,
COALESCE(d.loi_executed_date, CURRENT_DATE) as offer_response_date,
COALESCE(d.vertical, 'N/A') as vertical,
COALESCE(d.guarantee_type, 'NA') as guarantee_type,
COALESCE(p.square_footage, 0) as square_footage,
'{"sections": []}' as custom_sections,
null as offer_comments,
null as offer_response_comments,
true as response_accepted,
COALESCE(p.purchase_price, 0) as purchase_price,
COALESCE(d.lease_rent, 0) as lease_rent,
COALESCE(d.lease_type, 'NNN') as lease_type,
d.lease_rent_increase,
COALESCE(d.lease_increase_every_year, 0) as lease_increase_every_year,
COALESCE(d.lease_length, 0) as lease_length,
COALESCE(d.lease_expiration_year, DATE_PART('year', now())) as lease_expiration_year,
COALESCE(d.lease_number_of_options, 0) as lease_number_of_options,
COALESCE(d.lease_option_lengths, 0) as lease_option_lengths,
'assume the existing' as offer_lease_condition,
d.lease_rent_cpi,
COALESCE(d.lease_rent_step_type, 'FIXED') as lease_rent_step_type,
(current_date  + INTERVAL '7 day')::date as offer_contract_termination,
COALESCE(p.earnest_money_deposit, 0) as earnest_money_deposit,
COALESCE(p.extension_deposit, 0) as extension_deposit,
COALESCE(DATE_PART('day', initial_closing_date::timestamp - diligence_expiration_date::timestamp), 30) as offer_closing_period,
COALESCE(DATE_PART('day', outside_closing_date::timestamp - initial_closing_date::timestamp), 15) as offer_closing_period_extension,
COALESCE(DATE_PART('day', d.diligence_expiration_date::timestamp - d.contract_executed_date::timestamp), 45) as offer_due_diligence_number,
(select dm.member_id from deal_member dm join member m on dm.member_id=m.id where m.type_key='buyer' and dm.deal_id=d.id) as buyer,
(select dm.member_id from deal_member dm join member m on dm.member_id=m.id where m.type_key='seller' and dm.deal_id=d.id) as seller,
COALESCE((select m.full_name from deal_member dm join member m on dm.member_id=m.id where m.type_key='seller_broker' and dm.deal_id=d.id), 'N/A') as broker_full_name,
COALESCE((select m.company_name from deal_member dm join member m on dm.member_id=m.id where m.type_key='seller_broker' and dm.deal_id=d.id), 'N/A') as broker_company_name,
now() as created_at
FROM deal d
join property p
on d.property_id = p.id
where
status = 'CONTRACT_NEGOTIATION'
and not exists(select l.id from loi l where l.deal_id=d.id);
