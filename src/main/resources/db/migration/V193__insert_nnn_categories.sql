insert into category
    ("key", name, type_key, stage, description)
values
    ('nnn_evaluation', 'Evaluation', 'tasks_board', 'evaluation', ''),
    ('nnn_business_and_finances', 'Business and Finances', 'tasks_board', 'underwriting', ''),
    ('nnn_pipeline', 'Pipeline', 'tasks_board', 'underwriting', ''),
    ('nnn_icm1', 'ICM1', 'tasks_board', 'underwriting', ''),
    ('nnn_psa_elaboration', 'PSA Elaboration', 'tasks_board', 'negotiation', ''),
    ('nnn_icm2', 'ICM2', 'tasks_board', 'diligence', ''),
    ('nnn_legal', 'Legal', 'tasks_board', 'closing', ''),
    ('nnn_wiring', 'Wiring', 'tasks_board', 'closing', ''),
    ('nnn_verification', 'Verification', 'tasks_board', 'closing', ''),
    ('nnn_welcome_letter', 'Welcome Letter', 'tasks_board', 'closing', ''),
    ('nnn_notifications', 'Notifications', 'tasks_board', 'post_closing', '');
