create table if not exists "psa_round_interaction_file" (
    id                 bigint not null,
    interaction_id     bigint not null,
    file_id            bigint not null,

    constraint psa_round_interaction_file_pk_id primary key (id),
    constraint psa_round_interaction_file_fk_interaction foreign key (interaction_id) references "psa_round_interaction"(id),
    constraint psa_round_interaction_file_fk_file foreign key (file_id) references "file"(id)
)