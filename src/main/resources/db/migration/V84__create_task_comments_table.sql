create table if not exists "task_comment"
(
    id      serial  not null,
    task_id     bigint not null,
    member_id     bigint not null,
    comment    varchar not null,
    created_at timestamp with time zone not null,

    constraint task_comment_pk_id primary key (id),
    constraint task_comment_fk_task foreign key (task_id) references "task"(id),
    constraint task_comment_fk_member foreign key (member_id) references "member"(id)
);
