
------ TASK_HISTORY -------
create table if not exists "task_history"
(
    id          serial  not null,
    task_id     bigint  not null,
    user_id     bigint  not null,
    snapshot    json    not null,
    created_at  timestamp with time zone not null,

    constraint task_history_pk_id primary key (id),
    constraint task_history_fk_task foreign key (task_id) references "task"(id),
    constraint task_history_fk_user foreign key (user_id) references "user"(id)
);