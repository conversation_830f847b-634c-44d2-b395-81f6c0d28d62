create table "purchase_and_sale_contract"
(
    id                     bigint not null,
    deal_id                bigint not null,
    status                 varchar not null,
    current_document_id    bigint,
    executed_at            timestamp with time zone,
    draft_submitted_at     timestamp with time zone,
    expected_by            timestamp with time zone,
    created_at             timestamp with time zone not null,
    updated_at             timestamp with time zone not null,

    constraint purchase_and_sale_contract_pk_id primary key (id),
    constraint purchase_and_sale_contract_fk_deal foreign key (deal_id) references "deal"(id)
);

CREATE SEQUENCE "seq_purchase_and_sale_contract";

create table "psa_document"
(
    id                       bigint not null,
    psa_id                   bigint not null,
    member_id                bigint,
    comment                  varchar,
    file_id                  bigint,
    clean_version_file_id    bigint,
    red_line_version_file_id bigint,
    document_type            varchar,

    constraint psa_document_pk_id primary key (id),
    constraint psa_document_fk_psa foreign key (psa_id) references "purchase_and_sale_contract"(id)
);

CREATE SEQUENCE "seq_psa_document";

create table "psa_document_interaction"
(
    id                    bigint not null,
    document_id           bigint not null,
    member_id             bigint,
    comment               varchar,
    date                  timestamp with time zone not null,
    type                  varchar,

    constraint psa_document_interaction_pk_id primary key (id),
    constraint psa_document_interaction_fk_psa_document foreign key (document_id) references "psa_document"(id)
);

CREATE SEQUENCE "seq_psa_document_interaction";

create table "file"
(
    id                   bigint not null,
    member_id            bigint not null,
    name                 varchar not null,
    k_file_id            varchar not null,
    created_at            timestamp with time zone not null,

    constraint file_pk_id primary key (id)
);

CREATE SEQUENCE "seq_file";