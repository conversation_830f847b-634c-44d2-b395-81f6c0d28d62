create table if not exists "category_phase"
(
    id      serial  not null,
    key     varchar not null,
    name    varchar not null,

    constraint phase_pk_id primary key (id),
    constraint phase_uk_key unique (key)
);

insert into category_phase (key, name)
values ('due_diligence', 'Diligence tasks'),
        ('closing', 'Closing tasks');

alter table category add column phase varchar;
alter table category add constraint category_fk_phase_key foreign key ("phase") references category_phase("key");

update category set "phase" = 'due_diligence' where "key" = 'purchase_and_sale_contract';
update category set "phase" = 'due_diligence' where "key" = 'lease';
update category set "phase" = 'due_diligence' where "key" = 'title';
update category set "phase" = 'due_diligence' where "key" = 'survey';
update category set "phase" = 'due_diligence' where "key" = 'legal';
update category set "phase" = 'due_diligence' where "key" = 'financial';
update category set "phase" = 'due_diligence' where "key" = 'physical_and_environmental';
update category set "phase" = 'due_diligence' where "key" = 'zoning';
update category set "phase" = 'due_diligence' where "key" = 'building_documents';
update category set "phase" = 'due_diligence' where "key" = 'insurance';

alter table category alter column phase set not null;
