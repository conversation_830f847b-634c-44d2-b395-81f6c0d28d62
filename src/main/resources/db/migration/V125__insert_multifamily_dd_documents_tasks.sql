INSERT INTO task_template(
        task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
    (
	    'multifamily_dd_documents',
	    'form',
	    'buyer',
	    'Begin Deal Structuring Checklist',
	    '',
	    'multifamily_begin_deal_structuring_checklist',
	    '{"type":"object","required":["dealStructuringChecklist"],"properties":{"dealStructuringChecklist":{"type":"string","title":"Please upload Deal Structuring Checklist","format":"data-url"}}}',
	    null,
	    'LOW',
	    15,
	    true,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Prepare Due Diligence Checklist',
	    '',
	    'multifamily_prepare_due_diligence_checklist',
	    '{"type":"object","required":["prepareDueDiligenceChecklist"],"properties":{"prepareDueDiligenceChecklist":{"type":"string","title":"Please upload Deal Structuring Checklist","format":"data-url"}}}',
	    null,
	    'LOW',
	    16,
	    true,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form',
	    'buyer',
	    'Review Critical Date Chart and Send Out Calendar Reminders',
	    '',
	    'multifamily_send_out_calendar_reminders',
	    '{"type":"object","required":["sendOutCalendarReminders"],"properties":{"sendOutCalendarReminders":{"enum":["","Yes","No"],"type":"string","title":"Calendar reminders sent?"}}}',
	    null,
	    'LOW',
	    17,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form',
	    'buyer',
	    'Schedule Kickoff Call with all Parties Involved in Transaction',
	    '',
	    'multifamily_schedule_kickoff_call',
	    '{"type":"object","required":["scheduleKickoffCall"],"properties":{"scheduleKickoffCall":{"enum":["","Yes","No"],"type":"string","title":"Is the kickoff meeting already scheduled?"}}}',
	    null,
	    'LOW',
	    18,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form',
	    'buyer',
	    'Schedule Weekly Meetings Until Closing',
	    '',
	    'multifamily_schedule_weekly_meetings',
	    '{"type":"object","required":["scheduleWeeklyMeetings"],"properties":{"scheduleWeeklyMeetings":{"enum":["","Yes","No"],"type":"string","title":"Is the weekly meeting already scheduled?"}}}',
	    null,
	    'LOW',
	    19,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Execute Commitment Letter',
	    '',
	    'multifamily_execute_commitment_letter',
	    '{"type":"object","required":["signOff"],"properties":{"executeCommitmentLetter":{"type":"string","title":"Please Upload Execute Commitment Letter","format":"data-url"},"signOff":{"enum":["","Yes","No"],"type":"string","title":"Sign Off? "}}}',
	    null,
	    'LOW',
	    20,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form',
	    'buyer',
	    'Negotiate Loan Terms',
	    '',
	    'multifamily_negotiate_loan_terms',
	    '{"type":"object","required":["negotiateLoanTerms"],"properties":{"negotiateLoanTerms":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    21,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Submit Loan App',
	    '',
	    'multifamily_submit_loan_app',
	    '{"type":"object","required":["loanApp"],"properties":{"loanApp":{"enum":["","Yes","No"],"type":"string","title":"Loan App?"}},"dependencies":{"loanAppFile":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload the Loan App","format":"data-url"},"loanApp":{"enum":["Yes"]}}}]}}}',
	    null,
	    'LOW',
	    22,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Seller`s Survey',
	    '',
	    'multifamily_seller_survey',
	    '{"type":"object","required":["survey"],"properties":{"survey":{"type":"string","title":"Please upload the Survey","format":"data-url"},"newSurvey":{"enum":["","Yes","No"],"type":"string","title":"Have you already requested the new survey?"}}}',
	    null,
	    'LOW',
	    23,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form',
	    'buyer',
	    'Updated/New Survey',
	    '',
	    'multifamily_updated_survey',
	    '{"type":"object","required":["newSurvey"],"properties":{"newSurvey":{"enum":["","Yes","No"],"type":"string","title":"Is the new Survey already reviewed?"}}}',
	    null,
	    'LOW',
	    24,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Prior Three Years Financial Statements',
	    '',
	    'multifamily_prior_three_years_financial_statements',
	    '{"type":"object","required":["financialStatements"],"properties":{"financialStatements":{"type":"string","title":"Please upload Prior Three Years Financial Statements","format":"data-url"}}}',
	    null,
	    'LOW',
	    25,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Unit Mix w/ # of Units and Square Footage',
	    '',
	    'multifamily_units_and_square_footage',
	    '{"type":"object","required":["unitsAndSquareFootage"],"properties":{"unitsAndSquareFootage":{"type":"string","title":"Please upload Unit Mix w/ # of Units and Square Footage","format":"data-url"}}}',
	    null,
	    'LOW',
	    26,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Mortgage Loan Documents',
	    '',
	    'multifamily_mortgage_loan_documents',
	    '{"type":"object","required":["mortgageLoanDocuments"],"properties":{"mortgageLoanDocuments":{"enum":["","Yes","No"],"type":"string","title":"Loan assumption?"}},"dependencies":{"documents":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload the Mortgage Loan Documents","format":"data-url"},"mortgageLoanDocuments":{"enum":["Yes"]}}}]}}}',
	    null,
	    'LOW',
	    27,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'History of Capital Expenditures (Five Years Prior)',
	    '',
	    'multifamily_history_capital_expenditures',
	    '{"type":"object","required":["historyCapitalExpenditures"],"properties":{"historyCapitalExpenditures":{"type":"string","title":"Please upload History of Capital Expenditures","format":"data-url"}}}',
	    null,
	    'LOW',
	    28,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Certificates of Occupancy',
	    '',
	    'multifamily_certificates_of_occupancy',
	    '{"type":"object","required":["certificatesOccupancy"],"properties":{"certificatesOccupancy":{"type":"string","title":"Please upload Certificates of Occupancy","format":"data-url"}}}',
	    null,
	    'LOW',
	    29,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Rent Roll w/ Lease Charges',
	    '',
	    'multifamily_rent_roll_lease_charges',
	    '{"type":"object","required":["rentRollLeaseCharges"],"properties":{"rentRollLeaseCharges":{"type":"string","title":"Please upload Rent Roll w/ Lease Charges","format":"data-url"}}}',
	    null,
	    'LOW',
	    30,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Delinquency/Bad Debt/Aged Receivables Report',
	    '',
	    'multifamily_delinquency_bad_debt_aged_receivables_report',
	    '{"type":"object","required":["delinquencyBadDebtAgedReceivablesReport"],"properties":{"delinquencyBadDebtAgedReceivablesReport":{"type":"string","title":"Please upload Delinquency/Bad Debt/Aged Receivables Report","format":"data-url"}}}',
	    null,
	    'LOW',
	    32,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Security Deposits',
	    '',
	    'multifamily_security_deposits',
	    '{"type":"object","required":["securityDeposits"],"properties":{"securityDeposits":{"type":"string","title":"Please upload Security Deposits","format":"data-url"}}}',
	    null,
	    'LOW',
	    33,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Availability Report',
	    '',
	    'multifamily_availability_report',
	    '{"type":"object","required":["availabilityReport"],"properties":{"availabilityReport":{"type":"string","title":"Please upload Availability Report","format":"data-url"}}}',
	    null,
	    'LOW',
	    34,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Parking Map',
	    '',
	    'multifamily_parking_map',
	    '{"type":"object","required":["parkingMap"],"properties":{"parkingMap":{"type":"string","title":"Please upload Parking Map","format":"data-url"}}}',
	    null,
	    'LOW',
	    35,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Site Map',
	    '',
	    'multifamily_site_map',
	    '{"type":"object","required":["siteMap"],"properties":{"siteMap":{"type":"string","title":"Please upload Site Map","format":"data-url"}}}',
	    null,
	    'LOW',
	    36,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Loss Runs and Current Insurance Policy',
	    '',
	    'multifamily_loss_runs_and_current_insurance_policy',
	    '{"type":"object","required":["lossRunsAndCurrentInsurancePolicy"],"properties":{"lossRunsAndCurrentInsurancePolicy":{"type":"string","title":"Please upload Loss Runs and Current Insurance Policy","format":"data-url"}}}',
	    null,
	    'LOW',
	    37,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Personal Property List',
	    '',
	    'multifamily_personal_property_list',
	    '{"type":"object","required":["personalPropertyList"],"properties":{"personalPropertyList":{"type":"string","title":"Please upload Personal Property List","format":"data-url"}}}',
	    null,
	    'LOW',
	    38,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Employees & Salaries List',
	    '',
	    'multifamily_employees_salaries_list',
	    '{"type":"object","required":["employeesSalariesList"],"properties":{"employeesSalariesList":{"type":"string","title":"Please upload Employees & Salaries List","format":"data-url"}}}',
	    null,
	    'LOW',
	    39,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Property Tax Appeal Records',
	    '',
	    'multifamily_property_tax_appeal_records',
	    '{"type":"object","required":["propertyTaxAppealRecords"],"properties":{"propertyTaxAppealRecords":{"enum":["","Yes","No"],"type":"string","title":"Property Tax Appeal Records?"}},"dependencies":{"documents":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload Property Tax Appeal Records","format":"data-url"},"propertyTaxAppealRecords":{"enum":["Yes"]}}}]}}}',
	    null,
	    'LOW',
	    40,
	    true,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Pending Litigation',
	    '',
	    'multifamily_pending_litigation',
	    '{"type":"object","required":["pendingLitigation"],"properties":{"pendingLitigation":{"type":"string","title":"Please upload Pending Litigation","format":"data-url"}}}',
	    null,
	    'LOW',
	    41,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'RUBS Documentation for the past 24 months',
	    '',
	    'multifamily_rubs_documentation',
	    '{"type":"object","required":["rubsDocumentation"],"properties":{"rubsDocumentation":{"type":"string","title":"Please upload RUBS Documentation for the past 24 months","format":"data-url"}}}',
	    null,
	    'LOW',
	    42,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Water and Sewer Bills for the Past 24 months',
	    '',
	    'multifamily_water_and_sewer_bills',
	    '{"type":"object","required":["waterAndSewerBills"],"properties":{"waterAndSewerBills":{"type":"string","title":"Please upload Water and Sewer Bills for the Past 24 months","format":"data-url"}}}',
	    null,
	    'LOW',
	    43,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Electricity Bills for the Past 24 months',
	    '',
	    'multifamily_electricity_bills',
	    '{"type":"object","required":["electricityBills"],"properties":{"electricityBills":{"type":"string","title":"Please upload Electricity Bills for the Past 24 months","format":"data-url"}}}',
	    null,
	    'LOW',
	    44,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Gas Bills for the Past 24 months',
	    '',
	    'multifamily_gas_bills',
	    '{"type":"object","required":["gasBills"],"properties":{"gasBills":{"type":"string","title":"Please upload Gas Bills for the Past 24 months","format":"data-url"}}}',
	    null,
	    'LOW',
	    45,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Lease Audit',
	    '',
	    'multifamily_lease_audit',
	    '{"type":"object","required":["leaseAudit"],"properties":{"leaseAudit":{"type":"string","title":"Please upload Lease Audit","format":"data-url"}}}',
	    null,
	    'LOW',
	    46,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Copy of Blank Lease',
	    '',
	    'multifamily_copy_of_blank_lease',
	    '{"type":"object","required":["copyOfBlankLease"],"properties":{"copyOfBlankLease":{"type":"string","title":"Please upload Copy of Blank Lease","format":"data-url"}}}',
	    null,
	    'LOW',
	    47,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Unit Walks',
	    '',
	    'multifamily_unit_walks',
	    '{"type":"object","required":["unitWalks"],"properties":{"unitWalks":{"type":"string","title":"Please upload Unit Walks","format":"data-url"}}}',
	    null,
	    'LOW',
	    48,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Environmental Reports',
	    '',
	    'multifamily_environmental_reports',
	    '{"type":"object","required":["environmentalReports"],"properties":{"environmentalReports":{"type":"string","title":"Please upload Environmental Reports","format":"data-url"}}}',
	    null,
	    'LOW',
	    49,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Irrigation',
	    '',
	    'multifamily_irrigation',
	    '{"type":"object","required":["irrigation"],"properties":{"irrigation":{"type":"string","title":"Please upload Irrigation","format":"data-url"}}}',
	    null,
	    'LOW',
	    50,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Pool',
	    '',
	    'multifamily_pool',
	    '{"type":"object","required":["pool"],"properties":{"pool":{"type":"string","title":"Please upload Pool","format":"data-url"}}}',
	    null,
	    'LOW',
	    51,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Sewer',
	    '',
	    'multifamily_sewer',
	    '{"type":"object","required":["sewer"],"properties":{"sewer":{"type":"string","title":"Please upload Sewer","format":"data-url"}}}',
	    null,
	    'LOW',
	    52,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Plumbing/Water Supply Shut Offs',
	    '',
	    'multifamily_plumbing_supply_shut_offs',
	    '{"type":"object","required":["plumbingSupplyShutOffs"],"properties":{"plumbingSupplyShutOffs":{"type":"string","title":"Please upload Plumbing/Water Supply Shut Offs","format":"data-url"}}}',
	    null,
	    'LOW',
	    53,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Fire Systems',
	    '',
	    'multifamily_fire_systems',
	    '{"type":"object","required":["fireSystems"],"properties":{"fireSystems":{"type":"string","title":"Please upload Fire Systems","format":"data-url"}}}',
	    null,
	    'LOW',
	    54,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Foundation and Structure',
	    '',
	    'multifamily_foundation_and_structure',
	    '{"type":"object","required":["foundationAndStructure"],"properties":{"foundationAndStructure":{"type":"string","title":"Please upload Foundation and Structure","format":"data-url"}}}',
	    null,
	    'LOW',
	    55,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Electrical Systems',
	    '',
	    'multifamily_electrical_systems',
	    '{"type":"object","required":["electricalSystems"],"properties":{"electricalSystems":{"type":"string","title":"Please upload Electrical Systems","format":"data-url"}}}',
	    null,
	    'LOW',
	    56,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'HVAC',
	    '',
	    'multifamily_hvac',
	    '{"type":"object","required":["hvac"],"properties":{"hvac":{"type":"string","title":"Please upload HVAC","format":"data-url"}}}',
	    null,
	    'LOW',
	    57,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Roof',
	    '',
	    'multifamily_roof',
	    '{"type":"object","required":["roof"],"properties":{"roof":{"type":"string","title":"Please upload Roof","format":"data-url"}}}',
	    null,
	    'LOW',
	    58,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Boilers/Hot Water',
	    '',
	    'multifamily_boilers_hot_water',
	    '{"type":"object","required":["boilersHotWater"],"properties":{"boilersHotWater":{"type":"string","title":"Please upload Boilers/Hot Water","format":"data-url"}}}',
	    null,
	    'LOW',
	    59,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Chiller System',
	    '',
	    'multifamily_chiller_system',
	    '{"type":"object","required":["chillerSystem"],"properties":{"chillerSystem":{"type":"string","title":"Please upload Chiller System","format":"data-url"}}}',
	    null,
	    'LOW',
	    60,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Elevator',
	    '',
	    'multifamily_elevator',
	    '{"type":"object","required":["elevator"],"properties":{"elevator":{"type":"string","title":"Please upload Elevator","format":"data-url"}}}',
	    null,
	    'LOW',
	    61,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Building Safety',
	    '',
	    'multifamily_building_safety',
	    '{"type":"object","required":["buildingSafety"],"properties":{"buildingSafety":{"type":"string","title":"Please upload Building Safety","format":"data-url"}}}',
	    null,
	    'LOW',
	    62,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Evacuation Procedures',
	    '',
	    'multifamily_evacuation_procedures',
	    '{"type":"object","required":["evacuationProcedures"],"properties":{"evacuationProcedures":{"type":"string","title":"Please upload Evacuation Procedures","format":"data-url"}}}',
	    null,
	    'LOW',
	    63,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Emergency Procedures',
	    '',
	    'multifamily_emergency_procedures',
	    '{"type":"object","required":["emergencyProcedures"],"properties":{"emergencyProcedures":{"type":"string","title":"Please upload Emergency Procedures","format":"data-url"}}}',
	    null,
	    'LOW',
	    64,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Compliance Status',
	    '',
	    'multifamily_compliance_status',
	    '{"type":"object","required":["complianceStatus"],"properties":{"complianceStatus":{"type":"string","title":"Please upload Compliance Status","format":"data-url"}}}',
	    null,
	    'LOW',
	    65,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Coordinate IT inspection of all computers/equipment',
	    '',
	    'multifamily_coordinate_it_inspection',
	    '{"type":"object","required":["coordinateItInspection"],"properties":{"coordinateItInspection":{"enum":["","Yes","No"],"type":"string","title":"Have you already requested IT inspection?"}},"dependencies":{"documents":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload IT inspection of all computers/equipment","format":"data-url"},"coordinateItInspection":{"enum":["Yes"]}}}]}}}',
	    null,
	    'LOW',
	    66,
	    true,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Order New Equipment as Needed',
	    '',
	    'multifamily_order_new_equipment',
	    '{"type":"object","required":["orderNewEquipment"],"properties":{"orderNewEquipment":{"type":"string","title":"Please upload Order New Equipment as Needed","format":"data-url"}}}',
	    null,
	    'LOW',
	    67,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Check building permits for ongoing construction',
	    '',
	    'multifamily_check_building_permits',
	    '{"type":"object","properties":{"executeCommitmentLetter":{"type":"string","title":"Please Upload building permits","format":"data-url"},"reviewed":{"enum":["","Yes","No"],"type":"string","title":"Is it already reviewed?"}}}',
	    null,
	    'LOW',
	    68,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Verify Brand/Type of all Locks',
	    '',
	    'multifamily_verify_brand',
	    '{"type":"object","required":["verifyBrand"],"properties":{"verifyBrand":{"type":"string","title":"Please upload Brand/Type of all Locks","format":"data-url"}}}',
	    null,
	    'LOW',
	    69,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Confirm Property Insurance Quote',
	    '',
	    'multifamily_property_insurance_quote',
	    '{"type":"object","required":["propertyInsuranceQuote"],"properties":{"propertyInsuranceQuote":{"enum":["","Yes","No"],"type":"string","title":"Have you already requested Property Insurance Quote?"},"reviewed":{"enum":["","Yes","No"],"type":"string","title":"Is it already reviewed?"}}}',
	    null,
	    'LOW',
	    70,
	    true,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form_and_file',
	    'buyer',
	    'Confirm Real Estate Property Tax Forecast',
	    '',
	    'multifamily_property_tax_forecast',
	    '{"type":"object","required":["propertyTaxForecast"],"properties":{"propertyTaxForecast":{"enum":["","Yes","No"],"type":"string","title":"Have you already requested Real Estate Property Tax Forecast?"},"reviewed":{"enum":["","Yes","No"],"type":"string","title":"Is it already reviewed?"}}}',
	    null,
	    'LOW',
	    71,
	    true,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Obtain a "No Violations Letter" from the Planning and Zoning Department',
	    '',
	    'multifamily_no_violations_letter',
	    '{"type":"object","required":["noViolationsLetter"],"properties":{"noViolationsLetter":{"type":"string","title":"Please upload No Violations Letter","format":"data-url"}}}',
	    null,
	    'LOW',
	    72,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Org Structure Chart',
	    '',
	    'multifamily_org_structure_chart',
	    '{"type":"object","required":["orgStructureChart"],"properties":{"orgStructureChart":{"type":"string","title":"Please upload Org Structure Chart","format":"data-url"}}}',
	    null,
	    'LOW',
	    73,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Subscription Agreement',
	    '',
	    'multifamily_subscription_agreement',
	    '{"type":"object","required":["subscriptionAgreement"],"properties":{"subscriptionAgreement":{"type":"string","title":"Please upload Subscription Agreement","format":"data-url"}}}',
	    null,
	    'LOW',
	    74,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Operating Agreement',
	    '',
	    'multifamily_operating_agreement',
	    '{"type":"object","required":["operatingAgreement"],"properties":{"operatingAgreement":{"type":"string","title":"Please upload Operating Agreement","format":"data-url"}}}',
	    null,
	    'LOW',
	    75,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Entity Creation',
	    '',
	    'multifamily_entity_creation',
	    '{"type":"object","required":["entityCreation"],"properties":{"entityCreation":{"type":"string","title":"Please upload Entity Creation","format":"data-url"}}}',
	    null,
	    'LOW',
	    76,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Finalize Title Commitment and Underlying Documents',
	    '',
	    'multifamily_underlying_documents',
	    '{"type":"object","required":["underlyingDocuments"],"properties":{"underlyingDocuments":{"type":"string","title":"Please upload Finalize Title Commitment and Underlying Documents","format":"data-url"}}}',
	    null,
	    'LOW',
	    77,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Title Objection Letter',
	    '',
	    'multifamily_title_objection_letter',
	    '{"type":"object","required":["titleObjectionLetter"],"properties":{"titleObjectionLetter":{"type":"string","title":"Please upload Title Objection Letter","format":"data-url"}}}',
	    null,
	    'LOW',
	    78,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'upload_file',
	    'buyer',
	    'Present Investment Committee Memo II',
	    '',
	    'multifamily_investment_committee_memo_ii',
	    '{"type":"object","required":["memo"],"properties":{"memo":{"type":"string","title":"Please upload Investment Committee Memo II","format":"data-url"}}}',
	    null,
	    'LOW',
	    79,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_dd_documents',
	    'form',
	    'buyer',
	    'Due Diligence Ends/Earnest Money Non-Refundable',
	    '',
	    'multifamily_due_diligence_ends',
	    '{"type":"object","required":["dueDiligenceEnds"],"properties":{"dueDiligenceEnds":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    80,
	    false,
	    false,
	    'BUYER_TEAM'
	)
	;



INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('multifamily', 'multifamily_begin_deal_structuring_checklist'),
    ('multifamily', 'multifamily_prepare_due_diligence_checklist'),
    ('multifamily', 'multifamily_send_out_calendar_reminders'),
    ('multifamily', 'multifamily_schedule_kickoff_call'),
    ('multifamily', 'multifamily_schedule_weekly_meetings'),
    ('multifamily', 'multifamily_execute_commitment_letter'),
    ('multifamily', 'multifamily_negotiate_loan_terms'),
    ('multifamily', 'multifamily_submit_loan_app'),
    ('multifamily', 'multifamily_seller_survey'),
    ('multifamily', 'multifamily_updated_survey'),
    ('multifamily', 'multifamily_prior_three_years_financial_statements'),
    ('multifamily', 'multifamily_units_and_square_footage'),
    ('multifamily', 'multifamily_mortgage_loan_documents'),
    ('multifamily', 'multifamily_history_capital_expenditures'),
    ('multifamily', 'multifamily_certificates_of_occupancy'),
    ('multifamily', 'multifamily_rent_roll_lease_charges'),
    ('multifamily', 'multifamily_delinquency_bad_debt_aged_receivables_report'),
    ('multifamily', 'multifamily_security_deposits'),
    ('multifamily', 'multifamily_availability_report'),
    ('multifamily', 'multifamily_parking_map'),
    ('multifamily', 'multifamily_site_map'),
    ('multifamily', 'multifamily_loss_runs_and_current_insurance_policy'),
    ('multifamily', 'multifamily_personal_property_list'),
    ('multifamily', 'multifamily_employees_salaries_list'),
    ('multifamily', 'multifamily_property_tax_appeal_records'),
    ('multifamily', 'multifamily_pending_litigation'),
    ('multifamily', 'multifamily_rubs_documentation'),
    ('multifamily', 'multifamily_water_and_sewer_bills'),
    ('multifamily', 'multifamily_electricity_bills'),
    ('multifamily', 'multifamily_gas_bills'),
    ('multifamily', 'multifamily_lease_audit'),
    ('multifamily', 'multifamily_copy_of_blank_lease'),
    ('multifamily', 'multifamily_unit_walks'),
    ('multifamily', 'multifamily_environmental_reports'),
    ('multifamily', 'multifamily_irrigation'),
    ('multifamily', 'multifamily_pool'),
    ('multifamily', 'multifamily_sewer'),
    ('multifamily', 'multifamily_plumbing_supply_shut_offs'),
    ('multifamily', 'multifamily_fire_systems'),
    ('multifamily', 'multifamily_foundation_and_structure'),
    ('multifamily', 'multifamily_electrical_systems'),
    ('multifamily', 'multifamily_hvac'),
    ('multifamily', 'multifamily_roof'),
    ('multifamily', 'multifamily_boilers_hot_water'),
    ('multifamily', 'multifamily_chiller_system'),
    ('multifamily', 'multifamily_elevator'),
    ('multifamily', 'multifamily_building_safety'),
    ('multifamily', 'multifamily_evacuation_procedures'),
    ('multifamily', 'multifamily_emergency_procedures'),
    ('multifamily', 'multifamily_compliance_status'),
    ('multifamily', 'multifamily_coordinate_it_inspection'),
    ('multifamily', 'multifamily_order_new_equipment'),
    ('multifamily', 'multifamily_check_building_permits'),
    ('multifamily', 'multifamily_verify_brand'),
    ('multifamily', 'multifamily_property_insurance_quote'),
    ('multifamily', 'multifamily_property_tax_forecast'),
    ('multifamily', 'multifamily_no_violations_letter'),
    ('multifamily', 'multifamily_org_structure_chart'),
    ('multifamily', 'multifamily_subscription_agreement'),
    ('multifamily', 'multifamily_operating_agreement'),
    ('multifamily', 'multifamily_entity_creation'),
    ('multifamily', 'multifamily_underlying_documents'),
    ('multifamily', 'multifamily_title_objection_letter'),
    ('multifamily', 'multifamily_investment_committee_memo_ii'),
    ('multifamily', 'multifamily_due_diligence_ends')
    ;