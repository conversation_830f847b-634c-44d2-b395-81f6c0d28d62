
CREATE TABLE IF NOT EXISTS "deal_schema"
(
    id                          SERIAL NOT NULL,
    property_type               VARCHAR NOT NULL,
    view_schema               JSONB NOT NULL,
    json_schema                 JSONB NOT NULL,
    created_at                  TIMESTAMP WITH TIME ZONE NOT NULL,

    CONSTRAINT deal_schema_pk_id primary key (id)
);

INSERT INTO deal_schema (property_type, view_schema, json_schema, created_at)
VALUES ('MEDICAL',
        '{"sections":[{"title":"Asking","fields":["askingSalePrice","askingNoi","askingCapRate"]},{"title":"Offer","fields":["offerSalePrice","offerNoi","offerCapRate"]}]}',
        '{"type":"object","properties":{"askingSalePrice":{"type":"number","title":"Asking Sale Price"},"askingNoi":{"type":"number","title":"Asking NOI"},"askingCapRate":{"type":"number","title":"Asking Cap Rate"},"offerSalePrice":{"type":"number","title":"Offer Sale Price"},"offerNoi":{"type":"number","title":"Offer NOI"},"offerCapRate":{"type":"number","title":"Offer Cap Rate"}}}',
        now());

INSERT INTO deal_schema (property_type, view_schema, json_schema, created_at)
VALUES ('MULTIFAMILY',
        '{"sections":[{"title":"Asking","fields":["askingSalePrice","askingNoi","askingCapRate"]}]}',
        '{"type":"object","properties":{"askingSalePrice":{"type":"number","title":"Asking Sale Price"},"askingNoi":{"type":"number","title":"Asking NOI"},"askingCapRate":{"type":"number","title":"Asking Cap Rate"}}}',
        now());