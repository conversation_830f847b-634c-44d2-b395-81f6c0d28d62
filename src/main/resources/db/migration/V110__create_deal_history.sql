create table "deal_history"
(
    id                     bigint not null,
    deal_id                bigint not null,
    old_status             varchar not null,
    new_status             varchar not null,
    comment                varchar,
    member_id              bigint,
    created_at             timestamp with time zone not null,

    constraint deal_history_pk_id primary key (id),
    constraint deal_history_fk_deal foreign key (deal_id) references "deal"(id)
);

CREATE SEQUENCE "seq_deal_history";