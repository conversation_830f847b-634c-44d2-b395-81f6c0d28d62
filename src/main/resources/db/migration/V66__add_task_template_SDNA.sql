insert into task_template
(task_category,
task_type,
member_type_assignment,
task_title,
task_description,
key,
form_schema,
data,
sorting,
priority,
prioritize_after_closing,
required
)
values
('purchase_and_sale_contract',
'form_and_file',
'seller',
'SNDA',
'The notarized SNDA related to the lease',
'snda',
'{"type":"object","required":["snda"],"properties":{"snda":{"enum":["","Yes","No"],"type":"string","title":"Do you have an SNDA?"}},"dependencies":{"snda":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload any SNDA","format":"data-url"},"snda":{"enum":["Yes"]}}}]}}}',
null,
10,
'MEDIUM',
false,
false
);

insert into deal_task_template (deal_template_key, task_template_key) values ('default', 'snda');

update task_template set task_description='Documents regarding current or potential litigation' where key='records_and_correspondence_regarding_pending_or_threatened_litigation';
update task_template set task_description='Provide any warranties/guarantees for property items such as roof, facade, foundation, parking lot, etc.' where key='remaining_warranties_guarantees';