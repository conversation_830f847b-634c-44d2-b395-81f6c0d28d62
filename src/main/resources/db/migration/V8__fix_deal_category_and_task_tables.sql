
------ DEAL_CATEGORY -------
alter table deal_category add column category_key varchar not null;
alter table deal_category drop column category_id;
alter table deal_category add constraint deal_category_fk_category foreign key (category_key) references category(key);

------ TASK -------
alter table task add column template_key varchar not null;
alter table task add column type_key varchar not null;
alter table task add column status_key varchar not null;
alter table task add column assigned_member_id bigint not null;
alter table task add column title varchar not null;
alter table task add column description varchar not null;

alter table task drop column type_id;
alter table task drop column status_id;
alter table task drop column assigned_user_id;

alter table task add constraint task_fk_task_type foreign key (type_key) references task_type("key");
alter table task add constraint task_fk_task_status foreign key (status_key) references task_status("key");
alter table task add constraint task_fk_task_template foreign key (template_key) references task_template("key");
alter table task add constraint task_fk_member foreign key (assigned_member_id) references member("id");
