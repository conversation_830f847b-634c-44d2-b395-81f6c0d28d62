insert into finding_questions(key, property_type, entity, entity_type, query, prompt)
values
    ('REPORT_ALTA_SURVEY_RISKS', 'ALL', 'REPORT', 'ALTA_SURVEY', 'this will be updated', 'this will be updated'),
    ('REPORT_ALTA_SURVEY_ADDRESS', 'ALL', 'REPORT', 'ALTA_SURVEY', 'this will be updated', 'this will be updated'),
    ('REPORT_APPRAISAL_RISKS', 'ALL', 'REPORT', 'APPRAISAL', 'this will be updated', 'this will be updated'),
    ('REPORT_APPRAISAL_ADDRESS', 'ALL', 'REPORT', 'APPRAISAL', 'this will be updated', 'this will be updated'),
    ('REPORT_ASBESTOS_SURVEY_RISKS', 'ALL', 'REPORT', 'ASBESTOS_SURVEY', 'this will be updated', 'this will be updated'),
    ('REPORT_ASBESTOS_SURVEY_ADDRESS', 'ALL', 'REPORT', 'ASBESTOS_SURVEY', 'this will be updated', 'this will be updated'),
    ('REPORT_HAZARDOUS_MATERIALS_RECORDS_RISKS', 'ALL', 'REPORT', 'HAZARDOUS_MATERIALS_RECORDS', 'this will be updated', 'this will be updated'),
    ('REPORT_HAZARDOUS_MATERIALS_RECORDS_ADDRESS', 'ALL', 'REPORT', 'HAZARDOUS_MATERIALS_RECORDS', 'this will be updated', 'this will be updated'),
    ('REPORT_INSURANCE_QUOTE_RISKS', 'ALL', 'REPORT', 'INSURANCE_QUOTE', 'this will be updated', 'this will be updated'),
    ('REPORT_INSURANCE_QUOTE_ADDRESS', 'ALL', 'REPORT', 'INSURANCE_QUOTE', 'this will be updated', 'this will be updated'),
    ('REPORT_MOLD_SURVEY_RISKS', 'ALL', 'REPORT', 'MOLD_SURVEY', 'this will be updated', 'this will be updated'),
    ('REPORT_MOLD_SURVEY_ADDRESS', 'ALL', 'REPORT', 'MOLD_SURVEY', 'this will be updated', 'this will be updated'),
    ('REPORT_NOI_AUDIT_RISKS', 'ALL', 'REPORT', 'NOI_AUDIT', 'this will be updated', 'this will be updated'),
    ('REPORT_NOI_AUDIT_ADDRESS', 'ALL', 'REPORT', 'NOI_AUDIT', 'this will be updated', 'this will be updated'),
    ('REPORT_PCR_RISKS', 'ALL', 'REPORT', 'PCR', 'this will be updated', 'this will be updated'),
    ('REPORT_PCR_ADDRESS', 'ALL', 'REPORT', 'PCR', 'this will be updated', 'this will be updated'),
    ('REPORT_PHASE_I_RISKS', 'ALL', 'REPORT', 'PHASE_I', 'this will be updated', 'this will be updated'),
    ('REPORT_PHASE_I_ADDRESS', 'ALL', 'REPORT', 'PHASE_I', 'this will be updated', 'this will be updated'),
    ('REPORT_PHASE_II_RISKS', 'ALL', 'REPORT', 'PHASE_II', 'this will be updated', 'this will be updated'),
    ('REPORT_PHASE_II_ADDRESS', 'ALL', 'REPORT', 'PHASE_II', 'this will be updated', 'this will be updated'),
    ('REPORT_PHYSICAL_RISKS', 'ALL', 'REPORT', 'PHYSICAL', 'this will be updated', 'this will be updated'),
    ('REPORT_PHYSICAL_ADDRESS', 'ALL', 'REPORT', 'PHYSICAL', 'this will be updated', 'this will be updated'),
    ('REPORT_PROPERTY_CONDITION_ASSESSMENT_RISKS', 'ALL', 'REPORT', 'PROPERTY_CONDITION_ASSESSMENT', 'this will be updated', 'this will be updated'),
    ('REPORT_PROPERTY_CONDITION_ASSESSMENT_ADDRESS', 'ALL', 'REPORT', 'PROPERTY_CONDITION_ASSESSMENT', 'this will be updated', 'this will be updated'),
    ('REPORT_PROPERTY_ONSITE_DUE_DILIGENCE_RISKS', 'ALL', 'REPORT', 'PROPERTY_ONSITE_DUE_DILIGENCE', 'this will be updated', 'this will be updated'),
    ('REPORT_PROPERTY_ONSITE_DUE_DILIGENCE_ADDRESS', 'ALL', 'REPORT', 'PROPERTY_ONSITE_DUE_DILIGENCE', 'this will be updated', 'this will be updated'),
    ('REPORT_REAL_ESTATE_PROPERTY_TAX_FORECAST_RISKS', 'ALL', 'REPORT', 'REAL_ESTATE_PROPERTY_TAX_FORECAST', 'this will be updated', 'this will be updated'),
    ('REPORT_REAL_ESTATE_PROPERTY_TAX_FORECAST_ADDRESS', 'ALL', 'REPORT', 'REAL_ESTATE_PROPERTY_TAX_FORECAST', 'this will be updated', 'this will be updated'),
    ('REPORT_SEISMIC_REPORT_RISKS', 'ALL', 'REPORT', 'SEISMIC_REPORT', 'this will be updated', 'this will be updated'),
    ('REPORT_SEISMIC_REPORT_ADDRESS', 'ALL', 'REPORT', 'SEISMIC_REPORT', 'this will be updated', 'this will be updated'),
    ('REPORT_SOIL_AND_GEOLOGIC_REPORT_RISKS', 'ALL', 'REPORT', 'SOIL_AND_GEOLOGIC_REPORT', 'this will be updated', 'this will be updated'),
    ('REPORT_SOIL_AND_GEOLOGIC_REPORT_ADDRESS', 'ALL', 'REPORT', 'SOIL_AND_GEOLOGIC_REPORT', 'this will be updated', 'this will be updated'),
    ('REPORT_SURVEY_RISKS', 'ALL', 'REPORT', 'SURVEY', 'this will be updated', 'this will be updated'),
    ('REPORT_SURVEY_ADDRESS', 'ALL', 'REPORT', 'SURVEY', 'this will be updated', 'this will be updated'),
    ('REPORT_TERMITE_REPORT_RISKS', 'ALL', 'REPORT', 'TERMITE_REPORT', 'this will be updated', 'this will be updated'),
    ('REPORT_TERMITE_REPORT_ADDRESS', 'ALL', 'REPORT', 'TERMITE_REPORT', 'this will be updated', 'this will be updated'),
    ('REPORT_TITLE_RISKS', 'ALL', 'REPORT', 'TITLE', 'this will be updated', 'this will be updated'),
    ('REPORT_TITLE_ADDRESS', 'ALL', 'REPORT', 'TITLE', 'this will be updated', 'this will be updated'),
    ('REPORT_ZONING_RISKS', 'ALL', 'REPORT', 'ZONING', 'this will be updated', 'this will be updated'),
    ('REPORT_ZONING_ADDRESS', 'ALL', 'REPORT', 'ZONING', 'this will be updated', 'this will be updated');

update finding_questions
set query = 'Read the file uploaded and tell me what kind of risk can you find in the report that could endanger the investment.
Please respond with the following format:
{
  error_found: [respond with true if you find any potential risk inside the document given or false if not],
  explanation: [respond with a description of the potential risks or respond unable to answer if you don''t find any risk]
}',
    prompt = 'Act as an agent of commercial real estate expert in diligence report analysis. Use your knowledge about commercial real estate, multifamily, nnn, due dilgence and reports documents.'
where key like 'REPORT_%_RISKS';

update finding_questions
set query = 'Given the following deal information:
{
    "property_address": "244 South Rainbow Boulevard, Las Vegas, Nevada 89145"
}

Respond indicating whether there is an error in the report information and, if so, whether the property address match. Your response should follow this format:

{
  “property_address”: "[the deal information property address]",
  “report_property_address: "[replace with the property address in the report document]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "property_address": "102 28th Street, Fair Lawn, NJ, 07410",
  "report_property_address": "102 28th Street, Fair Lawn, NJ, 07410",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the property addresses don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}',
    prompt = 'Act as an agent of commercial real estate expert in diligence report analysis. Use your knowledge about commercial real estate, multifamily, nnn, due dilgence and reports documents.'
where key like 'REPORT_%_ADDRESS';
