create table if not exists "loi_draft"
(
    id                              serial not null,
    deal_id                         bigint not null,
    tenant_name                     varchar,
    offer_sales_price               numeric,
    offer_lease_rent                numeric,
    offer_lease_type                varchar,
    offer_lease_rent_increase       numeric,
    offer_lease_increase_every_year numeric,
    offer_lease_length              bigint,
    offer_lease_expiration_year     bigint,
    offer_lease_number_of_options   bigint,
    offer_lease_option_lengths      bigint,
    offer_closing_period            bigint,
    offer_closing_period_extension  bigint,
    offer_closing_extension_deposit numeric,
    offer_contract_termination      date,
    offer_earnest_money_deposit     numeric,
    offer_comments                  varchar,
    property_square_footage         numeric,
    vertical                        varchar,
    broker_name                     varchar,
    broker_company_name             varchar,
    offer_due_diligence_number      bigint,
    offer_lease_condition           varchar,
    offer_rent_cpi                  numeric,
    offer_rent_step_type            varchar,
    offer_custom_sections           jsonb,
    guarantee_type                  varchar,
    offer_closing_cost              varchar,

    constraint loi_draft_pk_id primary key (id),
    constraint loi_draft_fk_deal foreign key (deal_id) references "deal"(id),
    constraint loi_draft_unique_deal unique(deal_id)
);