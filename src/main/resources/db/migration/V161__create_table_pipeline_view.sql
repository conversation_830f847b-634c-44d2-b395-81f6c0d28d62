
CREATE TABLE IF NOT EXISTS "pipeline_view" (
    id                  SERIAL NOT NULL,
    name                VA<PERSON>HAR NOT NULL,
    property_type       VARCHAR NOT NULL,
    data                JSONB NOT NULL,
    updated_at          TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at          TIMESTAMP WITH TIME ZONE NOT NULL,

    CONSTRAINT pipeline_view_pk_id primary key (id),
    CONSTRAINT pipeline_view_uk_name UNIQUE (name)
);
