INSERT INTO task_template(
        task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	    )
	VALUES
    (
	    'multifamily_psa',
	    'form',
	    'buyer',
	    'Start Purchase and Sale Agreement',
	    '',
	    'multifamily_start_psa',
	    '{"type":"object","required":["startPsa"],"properties":{"startPsa":{"enum":["","Yes","No"],"type":"string","title":"The first PSA was sent?"}}}',
	    null,
	    'LOW',
	    10,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_equity_negotiation',
	    'upload_file',
	    'buyer',
	    'Distribute Equity Memo',
	    '',
	    'multifamily_distribute_equity_memo',
	    '{"type":"object","required":["distributeEquityMemo"],"properties":{"distributeEquityMemo":{"type":"string","title":"Please upload Equity Memo","format":"data-url"}}}',
	    null,
	    'LOW',
	    11,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_internal',
	    'upload_file',
	    'buyer',
	    'Present Investment Committee Memo I',
	    '',
	    'multifamily_investment_committee_memo_i',
	    '{"type":"object","required":["investmentCommitteeMemo"],"properties":{"investmentCommitteeMemo":{"type":"string","title":"Please upload Investment Committee Memo I","format":"data-url"}}}',
	    null,
	    'LOW',
	    12,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_psa',
	    'form',
	    'buyer',
	    'Execute Purchase and Sale Agreement',
	    '',
	    'multifamily_execute_psa',
	    '{"type":"object","required":["executePsa"],"properties":{"executePsa":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    13,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_earnest_money',
	    'form',
	    'buyer',
	    'Earnest Money Received by Title',
	    '',
	    'multifamily_money_received_by_title',
	    '{"type":"object","required":["moneyReceived_by_title"],"properties":{"executePsa":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    14,
	    false,
	    false,
	    'BUYER_TEAM'
	)	;

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('multifamily', 'multifamily_start_psa'),
    ('multifamily', 'multifamily_distribute_equity_memo'),
    ('multifamily', 'multifamily_investment_committee_memo_i'),
    ('multifamily', 'multifamily_execute_psa'),
    ('multifamily', 'multifamily_money_received_by_title');