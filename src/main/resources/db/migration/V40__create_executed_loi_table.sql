create table if not exists "executed_loi"
(
    deal_id                            bigint not null,
    loi_id                             bigint not null,
    signed_file_name                   varchar not null,
    signed_file_id                     varchar not null,
    executed_at                        date not null,
    constraint executed_loi_fk_loi foreign key (loi_id) references "loi"(id),
    constraint executed_loi_fk_deal foreign key (deal_id) references "deal"(id),
    constraint executed_loi_loi_id_uk_key unique (loi_id),
    constraint executed_loi_deal_id_uk_key unique (deal_id)
);