update finding_questions
set query = 'Please provide a JSON
Given the following deal information:
{
  "name": "$_property.name_",
   "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
   "offer_price": $_deal.offerPrice_,
   "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
   "closing_extension_deposit": $_deal.extensionDeposit_,
   "year_built": $_property.yearBuilt_,
   "type": "$_property.type_",
   "square_footage": $_property.squareFootage_,
   "asking_price": $_property.askingPrice_,
   "units": $_property.multifamilyData.units_,
   "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
   "occupancy": $_property.multifamilyData.occupancy_,
   "parking_spots": $_property.multifamilyData.parkingSpots_,
   "owner": "$_property.multifamilyData.owner_",
   "property_manager": "$_property.multifamilyData.propertyManager_",
   "tenant_name": "$_deal.tenantName_",
   "guarantee_type": "$_deal.guaranteeType_",
   "vertical": "$_deal.vertical_",
   "lease_rent": $_deal.lease.rent_,
   "lease_type": "$_deal.lease.type_",
   "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
   "lease_length": $_deal.lease.length_,
   "lease_expiration_year": $_deal.lease.expirationYear_,
   "lease_option_lengths": $_deal.lease.optionLengths_,
   "lease_number_of_options": $_deal.lease.numberOfOptions_,
   "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
   "closing_period": $_deal.lease.closingPeriod_,
   "closing_extension_period": $_deal.lease.closingPeriodExtension_
}

Respond indicating whether there is an error in the document information and, if so, whether the addresses match. Your response should follow this format:
{
  "property_address": "[the deal information property_address]",
  "loi_document_address_value": "[replace with the address mentioned on the given LOI document, you can find it on the heading of the LOI document referenced by ''the Property'' on Re: line]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "property_address": "[the deal information property_address]",
  "loi_document_address_value": "[replace with the address mentioned on the given LOI document, you can find it on the heading of the LOI document referenced by ''the Property'' on Re: line]",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}
You use the example above as a template for your response. Make sure to set ''error_found'' to true if the property addresses don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}'
where key = 'VALIDATE_ADDRESS';

update finding_questions
set query = 'Please provide a JSON
Given the following deal information:
{
  "name": "$_property.name_",
   "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
   "offer_price": $_deal.offerPrice_,
   "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
   "closing_extension_deposit": $_deal.extensionDeposit_,
   "year_built": $_property.yearBuilt_,
   "type": "$_property.type_",
   "square_footage": $_property.squareFootage_,
   "asking_price": $_property.askingPrice_,
   "units": $_property.multifamilyData.units_,
   "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
   "occupancy": $_property.multifamilyData.occupancy_,
   "parking_spots": $_property.multifamilyData.parkingSpots_,
   "owner": "$_property.multifamilyData.owner_",
   "property_manager": "$_property.multifamilyData.propertyManager_",
   "tenant_name": "$_deal.tenantName_",
   "guarantee_type": "$_deal.guaranteeType_",
   "vertical": "$_deal.vertical_",
   "lease_rent": $_deal.lease.rent_,
   "lease_type": "$_deal.lease.type_",
   "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
   "lease_length": $_deal.lease.length_,
   "lease_expiration_year": $_deal.lease.expirationYear_,
   "lease_option_lengths": $_deal.lease.optionLengths_,
   "lease_number_of_options": $_deal.lease.numberOfOptions_,
   "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
   "closing_period": $_deal.lease.closingPeriod_,
   "closing_extension_period": $_deal.lease.closingPeriodExtension_
}

Respond indicating whether there is an error in the document information and, if so, whether the square footage match. Your response should follow this format:
{
  "square_footage": "[the deal information square_footage]",
  "loi_square_footage": "[The square footage (SQFT) of the building in the LOI document]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "square_footage": "[the deal information square_footage]",
  "loi_square_footage": "[The square footage (SQFT) of the building in the LOI document]",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the square footage don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}'
where key = 'VALIDATE_SQUARE_FOOTAGE';