INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
    (
	    'nnn_evaluation',
	    'upload_file',
	    'buyer',
	    'Upload the OM',
	    '',
	    'nnn_upload_om',
	    '{"type":"object","required":["om"],"properties":{"om":{"type":"string","title":"Please Upload the OM","format":"data-url"}}}',
	    null,
	    'NA',
	    16,
	    false,
	    false,
	    'BUYER_TEAM'
	),
	(
    	'nnn_evaluation',
    	'form',
    	'buyer',
        'Tenant Data Collection and Address Verification',
    	'',
        'nnn_tenant_data_collection',
    	'{"type":"object","properties":{"text":{"type":"string","title":"Tenant Data","format":"textarea"}}}',
    	null,
    	'NA',
    	17,
    	false,
    	false,
        'BUYER_TEAM'
    ),
    (
        'nnn_evaluation',
        'form',
        'buyer',
        'Catlyst For Saling',
        '',
        'nnn_catlyst_for_saling',
        '{"type":"object","properties":{"text":{"type":"string","title":"Catlyst For Saling","format":"textarea"}}}',
        null,
        'NA',
        18,
        false,
        false,
        'BUYER_TEAM'
    );

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('default', 'nnn_upload_om'),
    ('default', 'nnn_tenant_data_collection'),
    ('default', 'nnn_catlyst_for_saling');