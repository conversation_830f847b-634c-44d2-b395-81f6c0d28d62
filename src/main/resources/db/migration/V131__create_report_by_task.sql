create table if not exists "report_by_task"
(
    report_type             varchar not null,
    task_type               varchar not null,
    property_type           varchar not null,

    constraint report_by_task_pk primary key (report_type,task_type,property_type)
);

insert into "report_by_task"(report_type, task_type, property_type)
values
    ('SURVEY', 'survey', 'MEDICAL'),
    ('ZONING', 'zoning', 'MEDICAL'),
    ('TITLE', 'title', 'MEDICAL'),
    ('PHASE_I', 'phase_i_report', 'MEDICAL'),
    ('PHASE_II', 'phase_ii_report', 'MEDICAL'),
    ('SEISMIC_REPORT', 'any_existing_seismic_reports', 'MEDICAL'),
    ('SOIL_AND_GEOLOGIC_REPORT', 'soil_and_geologic_report', 'MEDICAL'),
    ('MOLD_SURVEY', 'mold_survey', 'MEDICAL'),
    ('HAZARDOUS_MATERIALS_RECORDS', 'hazardous_materials_records', 'MEDICAL'),
    ('ASBESTOS_SURVEY', 'asbestos_survey', 'MEDICAL');

