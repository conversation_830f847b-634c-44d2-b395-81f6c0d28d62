
ALTER TABLE deal_schema ADD COLUMN field_definitions JSONB;

UPDATE deal_schema
    SET view_schema = '{"sections": [{"name": "property", "type": "DEFAULT"}, {"name": "transaction", "type": "DEFAULT"}, {"name": "lease", "type": "DEFAULT"}, {"name": "follow_up", "type": "CUSTOM", "title": "Deal Follow-up", "fields": ["askingSalePrice", "askingNoi", "om", "sourceType"]}, {"name": "notes", "type": "CUSTOM", "title": "Notes", "fields": ["propertyNotes", "updateNotes", "pipelineNotes"]}]}',
        json_schema = '{"type": "object", "properties": {"om": {"type": "string", "title": "OM"}, "askingNoi": {"type": "number", "title": "Asking NOI"}, "sourceType": {"type": "enum", "title": "Source Type"}, "updateNotes": {"type": "string", "title": "Update Notes"}, "pipelineNotes": {"type": "string", "title": "Pipeline Notes"}, "propertyNotes": {"type": "string", "title": "Property Notes"}, "askingSalePrice": {"type": "number", "title": "Asking Sale Price"}}}',
        field_definitions = '{"askingNoi": {"kind": "CURRENCY"},"updateNotes": {"kind": "NOTE"},"pipelineNotes": {"kind": "NOTE"},"propertyNotes": {"kind": "NOTE"},"askingSalePrice": {"kind": "CURRENCY"}}'
    WHERE id = 1;

UPDATE deal_schema
    SET view_schema = '{"sections": [{"name": "property", "type": "DEFAULT"}, {"name": "transaction", "type": "DEFAULT"}, {"name": "follow_up", "type": "CUSTOM", "title": "Deal Follow-up", "fields": ["askingSalePrice", "submarket", "om", "sourceType", "tourDate", "callForOffers", "units"]}, {"name": "notes", "type": "CUSTOM", "title": "Notes", "fields": ["propertyNotes", "updateNotes", "pipelineNotes"]}]}',
        json_schema = '{"type": "object", "properties": {"om": {"type": "string", "title": "OM"}, "units": {"type": "integer", "title": "units"}, "tourDate": {"type": "date", "title": "Tour Date"}, "submarket": {"type": "string", "title": "Submarket"}, "sourceType": {"type": "enum", "title": "Source Type"}, "updateNotes": {"type": "string", "title": "Update Notes"}, "callForOffers": {"type": "string", "title": "Call for offers"}, "pipelineNotes": {"type": "string", "title": "Pipeline Notes"}, "propertyNotes": {"type": "string", "title": "Property Notes"}, "askingSalePrice": {"type": "number", "title": "Asking Sale Price"}}}',
        field_definitions = '{"updateNotes": {"kind": "NOTE"},"pipelineNotes": {"kind": "NOTE"},"propertyNotes": {"kind": "NOTE"},"askingSalePrice": {"kind": "CURRENCY"}}'
    WHERE id = 2;

ALTER TABLE deal_schema ALTER COLUMN field_definitions SET NOT NULL;

