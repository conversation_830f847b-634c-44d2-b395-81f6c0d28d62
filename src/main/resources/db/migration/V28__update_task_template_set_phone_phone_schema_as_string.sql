------ UPDATE SCHEMA -------
UPDATE public.task_template
SET form_schema = '{"type": "object", "required": ["visitOn", "name", "phone", "email"], "properties": {"name": {"type": "string", "title": "Name"}, "email": {"type": "string", "title": "Email", "format": "email"}, "phone": {"type": "string", "title": "Phone", "format": "phone"}, "visitOn": {"type": "string", "title": "Keyway scheduled a site visit for", "format": "date"}}}'
WHERE key IN ('environmental', 'new_physical_inspection', 'phase_ii_environmental_assessment');
/*
The three templates have the same schema that's why we are using a IN (...)
It can be validated using this query:
    select form_schema from public.task_template where form_schema->'properties'->'phone' is not null;
*/

------ UPDATE TASK VALUES -------
update public.task
SET attached_form_data = jsonb_set(attached_form_data::jsonb, '{phone}', to_jsonb((attached_form_data->>'phone')::text))
WHERE jsonb_typeof((attached_form_data->'phone')::jsonb) = 'number' AND
      template_key IN ('environmental', 'new_physical_inspection', 'phase_ii_environmental_assessment');
