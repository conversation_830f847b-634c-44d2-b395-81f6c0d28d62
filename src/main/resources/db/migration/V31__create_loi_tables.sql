create table if not exists "loi"
(
    id                                  serial not null,
    deal_id                             bigint not null,
    tenant_name                         varchar not null,
    status                              varchar not null,
    date                                date not null,
    offer_sales_price                   numeric not null,
    offer_lease_rent                    numeric not null,
    offer_lease_type                    varchar not null,
    offer_lease_rent_increase           numeric not null,
    offer_lease_increase_every_year     numeric not null,
    offer_lease_length                  bigint not null,
    offer_lease_expiration_year         bigint not null,
    offer_lease_number_of_options          bigint not null,
    offer_lease_option_lengths          bigint not null,
    offer_closing_period                bigint not null,
    offer_closing_period_extension      bigint not null,
    offer_closing_extension_deposit     numeric not null,
    offer_contract_termination          date not null,
    offer_earnest_money_deposit         numeric not null,
    offer_comments                      varchar,
    offer_response_comments             varchar,
    offer_response_accepted             boolean,
    created_at                          timestamp with time zone not null,

    constraint loi_pk_id primary key (id),
    constraint loi_fk_deal foreign key (deal_id) references "deal"(id)
);

create table if not exists "loi_file"
(
    id                   serial not null,
    loi_id               bigint not null,
    user_id              bigint not null,
    name                 varchar not null,
    k_file_id            varchar not null,
    deal_team            varchar not null,

    constraint loi_file_pk_id primary key (id),
    constraint loi_file_fk_loi foreign key (loi_id) references "loi"(id),
    constraint loi_file_fk_user foreign key (user_id) references "user"(id)
);