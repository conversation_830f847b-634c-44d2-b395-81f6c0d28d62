insert into loi_history (deal_id, loi_id, type, description, created_at, file_name, file_id, member_name, member_team)
select
	l.deal_id as deal_id,
	l.id as loi_id,
	'OFFER' as type,
	l.offer_comments as description,
	l.created_at as created_at,
	lf.name as file_name,
	lf.k_file_id as file_id,
	m.full_name as member_name,
	m.type_key as member_team
from loi l
inner join loi_file lf on lf.loi_id = l.id
inner join member m on m.id = l.offer_created_by_member_id;

insert into loi_history (deal_id, loi_id, type, description, created_at, file_name, file_id, member_name, member_team)
select 
	l.deal_id as deal_id, 
	l.id as loi_id, 
	'OFFER_RESPONSE' as type,
	l.offer_response_comments as description, 
	l.offer_response_date as created_at, 
	null as file_name, 
	null as file_id, 
	m.full_name as member_name, 
	m.type_key as member_team
from loi l
inner join member m on m.id = l.offer_created_by_member_id 
where offer_response_by_member_id is not null;

insert into loi_history (deal_id, loi_id, type, description, created_at, file_name, file_id, member_name, member_team)
select 
	l.deal_id as deal_id, 
	l.id as loi_id, 
	'SIGNATURE' as type,
	'Signed the document' as description, 
	ls.seller_signer_completed as created_at, 
	null as file_name, 
	null as file_id, 
	m.full_name as member_name, 
	m.type_key as member_team
from loi l
inner join loi_sign ls on ls.loi_id = l.id 
inner join member m on m.id = ls.seller_signer_id 
where ls.seller_signer_completed is not null;

insert into loi_history (deal_id, loi_id, type, description, created_at, file_name, file_id, member_name, member_team)
select 
	l.deal_id as deal_id, 
	l.id as loi_id, 
	'SIGNATURE' as type,
	'Signed the document' as description, 
	ls.buyer_signer_completed as created_at, 
	null as file_name, 
	null as file_id, 
	m.full_name as member_name, 
	m.type_key as member_team
from loi l
inner join loi_sign ls on ls.loi_id = l.id 
inner join member m on m.id = ls.buyer_signer_id 
where ls.buyer_signer_completed is not null;

insert into loi_history (deal_id, loi_id, type, description, created_at, file_name, file_id, member_name, member_team)
select 
	l.deal_id as deal_id, 
	l.id as loi_id, 
	'EXECUTION' as type,
	'Loi Executed' as description, 
	el.executed_at as created_at, 
	el.signed_file_name as file_name, 
	el.signed_file_id as file_id, 
	null as member_name, 
	null as member_team
from loi l
inner join executed_loi el on el.loi_id = l.id;