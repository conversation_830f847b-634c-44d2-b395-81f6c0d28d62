UPDATE deal_schema
SET view_schema       = '{"sections":[{"name":"PROPERTY","type":"DEFAULT"},{"name":"ADDITIONAL_PROPERTY_DETAILS","type":"CUSTOM","title":"Additional Property Details","fields":["concept","sectorCode","sectorName","unitsUnderGuarantee","landAcreage","purchasePricePsf","purchasePricePerLandSf"]},{"name":"INTERNAL_DATA","type":"CUSTOM","title":"Internal Data","fields":["askingNoi","askingCapRate","offerNoi","offerNoiPsf","offerCapRate","creditType"]},{"name":"DEAL_DETAILS","type":"DEFAULT"},{"name":"LEASE","type":"DEFAULT"},{"name":"MARKET_INFORMATION","type":"CUSTOM","title":"Market Information","fields":["marketRentPsf","marketPricePsf","marketCapRate","vacancyPercentageWithinPropertyType","populationDensity","populationDensityPercentile","populationGrowth","populationGrowthPercentile"]},{"name":"NOTES","type":"CUSTOM","title":"Notes","fields":["propertyNotes","updateNotes","pipelineNotes"]}]}',
    json_schema       = '{"type":"object","properties":{"offerNoi":{"type":"number","title":"Offer NOI"},"offerNoiPsf":{"type":"number","title":"Offer NOI PSF","readOnly":true},"askingNoi":{"type":"number","title":"Asking NOI"},"creditType":{"type":"number","title":"Credit Type"},"updateNotes":{"type":"string","title":"Update Notes"},"offerCapRate":{"type":"number","title":"Offer Cap Rate"},"askingCapRate":{"type":"number","title":"Asking Cap Rate","readOnly":true},"concept":{"type":"string","title":"Concept"},"sectorCode":{"type":"number","title":"Sector Code"},"sectorName":{"type":"string","title":"Sector Name"},"unitsUnderGuarantee":{"type":"number","title":"Units Under Guarantee"},"landAcreage":{"type":"number","title":"Land acreage"},"purchasePricePsf":{"type":"number","title":"Purchase Price PSF"},"purchasePricePerLandSf":{"type":"number","title":"Purchase Price per Land SF"},"marketRentPsf":{"type":"number","title":"Market Rent PSF"},"marketPricePsf":{"type":"number","title":"Market Price PSF"},"marketCapRate":{"type":"number","title":"Market Cap Rate"},"vacancyPercentageWithinPropertyType":{"type":"number","title":"Vacancy % within Property Type"},"populationDensity":{"type":"number","title":"Population Density"},"populationDensityPercentile":{"type":"number","title":"Population Density Percentile"},"populationGrowth":{"type":"number","title":"Population Growth"},"populationGrowthPercentile":{"type":"number","title":"Population Growth Percentile"},"pipelineNotes":{"type":"string","title":"Pipeline Notes"},"propertyNotes":{"type":"string","title":"Property Notes"}}}',
    field_definitions = '{"offerNoi":{"kind":"CURRENCY"},"offerNoiPsf":{"kind":"CURRENCY"},"askingNoi":{"kind":"CURRENCY"},"purchasePricePsf":{"kind":"CURRENCY"},"purchasePricePerLandSf":{"kind":"CURRENCY"},"marketRentPsf":{"kind":"CURRENCY"},"marketPricePsf":{"kind":"CURRENCY"},"marketCapRate":{"kind":"PERCENTAGE"},"vacancyPercentageWithinPropertyType":{"kind":"PERCENTAGE"},"populationDensityPercentile":{"kind":"PERCENTAGE"},"populationGrowthPercentile":{"kind":"PERCENTAGE"},"updateNotes":{"kind":"NOTE"},"pipelineNotes":{"kind":"NOTE"},"propertyNotes":{"kind":"NOTE"},"askingSalePrice":{"kind":"CURRENCY"}}'
WHERE id = 1;
