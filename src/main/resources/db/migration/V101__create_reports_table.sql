create table if not exists "deal_report"
(
    id              bigint not null,
    deal_id         bigint not null,
    type            varchar not null,
    report_name     varchar,
    vendor          varchar not null,
    vendor_name     varchar,
    status          varchar not null,
    expected_date   date,
    cost_estimate   numeric,
    cost            numeric,
    findings        varchar,

    constraint deal_report_pk_id primary key (id),
    constraint deal_report_fk_deal foreign key (deal_id) references "deal"(id)
);

create table if not exists "deal_report_file"
(
    id                 serial not null,
    report_id          bigint not null,
    file_id            bigint not null,

    constraint deal_report_file_pk_id primary key (id),
    constraint deal_report_file_deal_report foreign key (report_id) references "deal_report"(id),
    constraint deal_report_file_file foreign key (file_id) references "file"(id)
);

CREATE SEQUENCE "seq_deal_report";
