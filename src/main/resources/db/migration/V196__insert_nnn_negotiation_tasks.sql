INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
     (
         'nnn_psa_elaboration',
         'upload_file',
         'buyer',
         'Information Gathering for PSA',
         '',
         'nnn_psa_information',
	       '{"type":"object","properties":{"text":{"type":"string","title":"Information Gathering for PSA","format":"textarea"}}}',
         null,
         'NA',
         24,
         false,
         false,
         'BUYER_TEAM'
     ),
      (
          'nnn_psa_elaboration',
          'upload_file',
          'buyer',
          'Request the PSA to Legal Team',
          '',
          'nnn_psa_legal_team',
          '{"type":"object","properties":{"text":{"type":"string","title":"Request the PSA to Legal Team","format":"textarea"}}}',
          null,
          'NA',
          25,
          false,
          false,
          'BUYER_TEAM'
      );

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('default', 'nnn_psa_information'),
    ('default', 'nnn_psa_legal_team');