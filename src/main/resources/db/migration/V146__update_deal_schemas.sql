
UPDATE deal_schema
    SET view_schema = '{"sections": [{"name": "property", "type": "DEFAULT"}, {"name": "transaction", "type": "DEFAULT"}, {"name": "lease", "type": "DEFAULT"}, {"name": "follow_up", "type": "CUSTOM", "title": "Deal Follow-up", "fields": ["askingSalePrice", "askingNoi", "om", "sourceType"]}, {"name": "notes", "type": "CUSTOM", "title": "Notes", "fields": ["propertyNotes", "updateNotes", "pipelineNotes"]}]}',
        json_schema = '{"type": "object", "properties": {"om": {"type": "string", "title": "OM"}, "askingNoi": {"type": "currency", "title": "Asking NOI"}, "sourceType": {"type": "enum", "title": "Source Type"}, "updateNotes": {"type": "string", "title": "Update Notes"}, "pipelineNotes": {"type": "string", "title": "Pipeline Notes"}, "propertyNotes": {"type": "string", "title": "Property Notes"}, "askingSalePrice": {"type": "currency", "title": "Asking Sale Price"}}}'
    WHERE id = 1;

UPDATE deal_schema
    SET view_schema = '{"sections": [{"name": "property", "type": "DEFAULT"}, {"name": "transaction", "type": "DEFAULT"}, {"name": "follow_up", "type": "CUSTOM", "title": "Deal Follow-up", "fields": ["askingSalePrice", "submarket", "om", "sourceType", "tourDate", "callForOffers", "units"]}, {"name": "notes", "type": "CUSTOM", "title": "Notes", "fields": ["propertyNotes", "updateNotes", "pipelineNotes"]}]}',
        json_schema = '{"type": "object", "properties": {"om": {"type": "string", "title": "OM"}, "units": {"type": "integer", "title": "units"}, "tourDate": {"type": "date", "title": "Tour Date"}, "submarket": {"type": "string", "title": "Submarket"}, "sourceType": {"type": "enum", "title": "Source Type"}, "updateNotes": {"type": "string", "title": "Update Notes"}, "callForOffers": {"type": "string", "title": "Call for offers"}, "pipelineNotes": {"type": "string", "title": "Pipeline Notes"}, "propertyNotes": {"type": "string", "title": "Property Notes"}, "askingSalePrice": {"type": "currency", "title": "Asking Sale Price"}}}'
    WHERE id = 2;
