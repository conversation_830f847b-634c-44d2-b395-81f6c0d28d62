INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
    (
	    'multifamily_asset_management',
	    'upload_file',
	    'buyer',
	    'Finalize Real Estate Tax True Ups',
	    '',
	    'multifamily_tax_true_ups',
	    '{"type":"object","required":["taxTrueUps"],"properties":{"taxTrueUps":{"type":"string","title":"Please upload Real Estate Tax True Ups","format":"data-url"}}}',
	    null,
	    'LOW',
	    114,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_asset_management',
	    'upload_file',
	    'buyer',
	    'Finalize Utility True Ups',
	    '',
	    'multifamily_utility_true_ups',
	    '{"type":"object","required":["utilityTrueUps"],"properties":{"utilityTrueUps":{"type":"string","title":"Please upload Utility True Ups","format":"data-url"}}}',
	    null,
	    'LOW',
	    115,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_asset_management',
	    'upload_file',
	    'buyer',
	    'Finalize Renovation Plan',
	    '',
	    'multifamily_renovation_plan',
	    '{"type":"object","required":["renovationPlan"],"properties":{"renovationPlan":{"type":"string","title":"Please upload Renovation Plan","format":"data-url"}}}',
	    null,
	    'LOW',
	    116,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_asset_management',
	    'upload_file',
	    'buyer',
	    'Finalize Capex Budget',
	    '',
	    'multifamily_capex_budget',
	    '{"type":"object","required":["capexBudget"],"properties":{"capexBudget":{"type":"string","title":"Please upload Capex Budget","format":"data-url"}}}',
	    null,
	    'LOW',
	    117,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_asset_management',
	    'upload_file',
	    'buyer',
	    'Finalize Capex Bids',
	    '',
	    'multifamily_capex_bids',
	    '{"type":"object","required":["capexBids"],"properties":{"capexBids":{"type":"string","title":"Please upload Capex Bids","format":"data-url"}}}',
	    null,
	    'LOW',
	    118,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_asset_management',
	    'upload_file',
	    'buyer',
	    '30/60/90 Day PM Plan',
	    '',
	    'multifamily_days_pm_plan',
	    '{"type":"object","required":["dayPMPlan"],"properties":{"dayPMPlan":{"type":"string","title":"Please upload 30/60/90 Day PM Plan","format":"data-url"}}}',
	    null,
	    'LOW',
	    119,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_asset_management',
	    'upload_file',
	    'buyer',
	    'Confirm On-Site Staffing/Transition Plan',
	    '',
	    'multifamily_transition_plan',
	    '{"type":"object","required":["transitionPlan"],"properties":{"transitionPlan":{"type":"string","title":"Please upload On-Site Staffing/Transition Plan","format":"data-url"}}}',
	    null,
	    'LOW',
	    120,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_branding',
	    'upload_file',
	    'buyer',
	    'Website/Branding',
	    '',
	    'multifamily_website_branding',
	    '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    121,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_branding',
	    'upload_file',
	    'buyer',
	    'News Articles and Social Media Posts',
	    '',
	    'multifamily_social_media_posts',
	    '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["","Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    null,
	    'LOW',
	    122,
	    false,
	    false,
	    'BUYER_TEAM'
	)
  ;

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('multifamily', 'multifamily_tax_true_ups'),
    ('multifamily', 'multifamily_utility_true_ups'),
    ('multifamily', 'multifamily_renovation_plan'),
    ('multifamily', 'multifamily_capex_budget'),
    ('multifamily', 'multifamily_capex_bids'),
    ('multifamily', 'multifamily_days_pm_plan'),
    ('multifamily', 'multifamily_transition_plan'),
    ('multifamily', 'multifamily_website_branding'),
    ('multifamily', 'multifamily_social_media_posts')
   ;
