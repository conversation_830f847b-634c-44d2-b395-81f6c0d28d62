SELECT * INTO loi_bkp FROM loi;

create table if not exists "loi_multifamily"
(
    loi_id                              integer not null,
    legal_entity                        varchar not null,
    seller                              varchar not null,
    property_name                       varchar,
    property_address                    varchar not null,
    loan                                varchar not null,
    offer_deposit                       numeric not null,
    due_diligence_extension_period      bigint not null,

    constraint loi_multifamily_fk_loi foreign key (loi_id) references "loi"(id)
);

CREATE TABLE IF NOT EXISTS "loi_medical"
(
    loi_id integer not null,
    tenant_name character varying COLLATE pg_catalog."default" NOT NULL,
    offer_lease_rent numeric NOT NULL,
    offer_lease_type character varying COLLATE pg_catalog."default" NOT NULL,
    offer_lease_rent_increase numeric,
    offer_lease_increase_every_year numeric NOT NULL,
    offer_lease_length bigint NOT NULL,
    offer_lease_expiration_year bigint NOT NULL,
    offer_lease_number_of_options bigint NOT NULL,
    offer_lease_option_lengths bigint NOT NULL,
    offer_contract_termination date NOT NULL,
    offer_earnest_money_deposit numeric NOT NULL,
    offer_comments character varying COLLATE pg_catalog."default",
    offer_lease_condition character varying COLLATE pg_catalog."default" NOT NULL,
    offer_rent_cpi numeric,
    offer_rent_step_type character varying COLLATE pg_catalog."default" NOT NULL,
    guarantee_type character varying COLLATE pg_catalog."default" DEFAULT 'NA'::character varying,
    property_square_footage numeric NOT NULL,
    vertical character varying COLLATE pg_catalog."default" NOT NULL,

    constraint loi_medical_fk_loi foreign key (loi_id) references "loi"(id)
);

INSERT INTO loi_medical(
loi_id,
tenant_name,
offer_lease_rent,
offer_lease_type,
offer_lease_rent_increase,
offer_lease_increase_every_year,
offer_lease_length,
offer_lease_expiration_year,
offer_lease_number_of_options,
offer_lease_option_lengths,
offer_contract_termination,
offer_earnest_money_deposit,
offer_comments,
offer_lease_condition,
offer_rent_cpi,
offer_rent_step_type,
guarantee_type,
property_square_footage,
vertical)
SELECT
id,
tenant_name,
offer_lease_rent,
offer_lease_type,
offer_lease_rent_increase,
offer_lease_increase_every_year,
offer_lease_length,
offer_lease_expiration_year,
offer_lease_number_of_options,
offer_lease_option_lengths,
offer_contract_termination,
offer_earnest_money_deposit,
offer_comments,
offer_lease_condition,
offer_rent_cpi,
offer_rent_step_type,
guarantee_type,
property_square_footage,
vertical
FROM LOI;

ALTER TABLE LOI ADD COLUMN due_date date;
ALTER TABLE loi
ALTER tenant_name DROP NOT NULL,
ALTER offer_lease_rent DROP NOT NULL,
ALTER offer_lease_type DROP NOT NULL,
ALTER offer_lease_rent_increase DROP NOT NULL,
ALTER offer_lease_increase_every_year DROP NOT NULL,
ALTER offer_lease_length DROP NOT NULL,
ALTER offer_lease_expiration_year DROP NOT NULL,
ALTER offer_lease_number_of_options DROP NOT NULL,
ALTER offer_lease_option_lengths DROP NOT NULL,
ALTER offer_contract_termination DROP NOT NULL,
ALTER offer_earnest_money_deposit DROP NOT NULL,
ALTER offer_comments DROP NOT NULL,
ALTER offer_lease_condition DROP NOT NULL,
ALTER offer_rent_cpi DROP NOT NULL,
ALTER offer_rent_step_type DROP NOT NULL,
ALTER guarantee_type DROP NOT NULL,
ALTER property_square_footage DROP NOT NULL,
ALTER vertical DROP NOT NULL;

