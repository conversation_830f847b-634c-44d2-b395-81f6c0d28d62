insert into finding_questions(key, property_type, entity, entity_type, query, prompt)
values
    ('LEASE_PROPERTY_ADDRESS', 'ALL', 'DOCUMENT', 'LEASE', 'Please provide a JSON,
Given the following deal information:
{
  "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
  "tenant_name": "$_deal.tenantName_",
  "lease_increase_every_year": $_deal.lease.increaseEveryYear_
}
The document is a lease an it should have all the relevant contract information, respond indicating whether there is an error in the document information and, if so, whether the deal property address match with the document address. Your response should follow this format:

{
  "property_address": "[the deal property_address]",
  "lease_property_address": "[the lease document address it maybe on inside the premises]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "property_address": "[the deal property_address]",
  "lease_property_address": "[the lease document address]",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the address don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": ""Unable to answer""
}', 'Act as an agent of commercial real estate expert in coontract leases analysis. Use your knowledge about commercial real estate for responding some question about the information you have
The term ""premises"" is often used to encompass not only the physical structure but also any associated land, fixtures, and sometimes even specific areas or rooms within a building. It is used to provide a clear and unambiguous description of what is being leased to avoid any confusion or disputes between the landlord and the tenant.'),
    ('LEASE_TENANT_NAME', 'ALL', 'DOCUMENT', 'LEASE', 'Please provide a JSON,
Given the following deal information:
{
  "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
  "tenant_name": "$_deal.tenantName_",
  "lease_increase_every_year": $_deal.lease.increaseEveryYear_
}
The document is a lease an it should have all the relevant contract information, respond indicating whether there is an error in the document information and, if so, whether the deal tenant name match with the tenant name on the document. Your response should follow this format:

{
  "tenant_name": "[the deal tenant_name]",
  "lease_tenant_name": "[the tenant name on lease document]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "tenant_name": "[the deal property_address]",
  "lease_tenant_name": "[the tenant name on lease document]",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the address don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}', 'Act as an agent of commercial real estate expert in coontract leases analysis. Use your knowledge about commercial real estate for responding some question about the information you have
The term "premises" is often used to encompass not only the physical structure but also any associated land, fixtures, and sometimes even specific areas or rooms within a building. It is used to provide a clear and unambiguous description of what is being leased to avoid any confusion or disputes between the landlord and the tenant.'),
    ('LEASE_INCREASE_EVERY_YEAR', 'ALL', 'DOCUMENT', 'LEASE', 'Please provide a JSON,
Given the following deal information:
{
  "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
  "tenant_name": "$_deal.tenantName_",
  "lease_increase_every_year": $_deal.lease.increaseEveryYear_
}
The document is a lease an it should have all the relevant contract information, respond indicating whether there is an error in the document information and, if so, whether the deal lease_increase_every_year match with the the information about the lease increase every year. Your response should follow this format:

{
  "lease_increase_every_year": "[the deal lease_increase_every_year]",
  "lease_increase_year": "[the information about the lease increase every year]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "lease_increase_every_year": "[the deal lease_increase_every_year]",
  "lease_increase_year": "[the information about the lease increase every year]",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the information about the increase lease every year does not match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}', 'Act as an agent of commercial real estate expert in coontract leases analysis. Use your knowledge about commercial real estate for responding some question about the information you have
The term "premises" is often used to encompass not only the physical structure but also any associated land, fixtures, and sometimes even specific areas or rooms within a building. It is used to provide a clear and unambiguous description of what is being leased to avoid any confusion or disputes between the landlord and the tenant.');