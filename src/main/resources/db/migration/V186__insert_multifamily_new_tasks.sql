INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility,
	    due_date_external_data_definition
	)
	VALUES
	(
    	'multifamily_loi',
    	'form',
    	'buyer',
        'Call for Offers',
    	'',
        'multifamily_loi_call_for_offers',
	    '{"type": "object", "properties": {"notes": {"type": "string", "title": "Notes", "format": "textarea"}}}',
    	null,
    	'HIGHEST',
    	1,
    	false,
    	false,
        'BUYER_TEAM',
        'deal.schemaData.data.callForOffers'
    );

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('multifamily', 'multifamily_loi_call_for_offers');

UPDATE task_template
SET task_title = 'Property Tour', -- was 'Property Tour Checklist',
task_type = 'form_and_file', -- was 'upload_file',
priority = 'HIGHEST',
due_date_external_data_definition = 'deal.schemaData.data.tourDate',
form_schema = '{"type": "object", "required": ["propertyTourChecklist"], "properties": {"propertyTourChecklist": {"type": "string", "title": "Property Tour Checklist", "format": "data-url"}, "notes": {"type": "string", "title": "Notes", "format": "textarea"}}}'
-- '{"type": "object", "required": ["propertyTourChecklist"], "properties": {"propertyTourChecklist": {"type": "string", "title": "Please upload Property Tour Checklist", "format": "data-url"}}}'
WHERE task_category = 'multifamily_main_analysis'
AND "key" = 'multifamily_property_tour_checklist'

