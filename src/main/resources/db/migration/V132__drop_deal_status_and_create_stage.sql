alter table deal add column stage varchar;
alter table deal add column is_archived boolean default false;

update deal set is_archived = true where status = 'ARCHIVED';

update deal set stage = 'UNDERWRITING' where status = 'NEW';
update deal set stage = 'OFFER' where status = 'LOI_SUBMITTED';
update deal set stage = 'NEGOTIATION' where status = 'CONTRACT_PENDING';
update deal set stage = 'NEGOTIATION' where status = 'CONTRACT_NEGOTIATION';
update deal set stage = 'DILIGENCE' where status = 'DILIGENCE';
update deal set stage = 'CLOSING' where status = 'CLOSING';
update deal set stage = 'EVALUATION' where status = 'ARCHIVED';
update deal set stage = 'POST_CLOSING' where status = 'CLOSED';

alter table deal alter column stage set not null;
alter table deal alter column status drop not null;

alter table category drop constraint category_fk_phase_key;

update category set phase = 'diligence' where phase = 'due_diligence';
update category set phase = 'evaluation' where phase = 'pre_evaluation';

update category_phase
set key = 'evaluation',
    name = 'Evaluation'
where key = 'pre_evaluation';

update category_phase
set key = 'diligence'
where key = 'due_diligence';

alter table category add constraint category_fk_phase_key foreign key (phase) references category_phase(key);

alter table deal_history rename column old_status to old_stage;
alter table deal_history rename column new_status to new_stage;

update deal_history set old_stage = 'UNDERWRITING' where old_stage = 'NEW';
update deal_history set old_stage = 'OFFER' where old_stage = 'LOI_SUBMITTED';
update deal_history set old_stage = 'NEGOTIATION' where old_stage = 'CONTRACT_PENDING';
update deal_history set old_stage = 'NEGOTIATION' where old_stage = 'CONTRACT_NEGOTIATION';
update deal_history set old_stage = 'DILIGENCE' where old_stage = 'DILIGENCE';
update deal_history set old_stage = 'CLOSING' where old_stage = 'CLOSING';
update deal_history set old_stage = 'EVALUATION' where old_stage = 'ARCHIVED';
update deal_history set old_stage = 'POST_CLOSING' where old_stage = 'CLOSED';

update deal_history set new_stage = 'UNDERWRITING' where new_stage = 'NEW';
update deal_history set new_stage = 'OFFER' where new_stage = 'LOI_SUBMITTED';
update deal_history set new_stage = 'NEGOTIATION' where new_stage = 'CONTRACT_PENDING';
update deal_history set new_stage = 'NEGOTIATION' where new_stage = 'CONTRACT_NEGOTIATION';
update deal_history set new_stage = 'DILIGENCE' where new_stage = 'DILIGENCE';
update deal_history set new_stage = 'CLOSING' where new_stage = 'CLOSING';
update deal_history set new_stage = 'EVALUATION' where new_stage = 'ARCHIVED';
update deal_history set new_stage = 'POST_CLOSING' where new_stage = 'CLOSED';
