
------ USER -------
alter table "user"
    drop column company_name,
    drop column full_name,
    drop column address,
    drop column phone_country_code,
    drop column phone_area_code,
    drop column phone_number;

------ TASK -------
alter table "task"
    drop column assigned_user_deal_id,
    add column assigned_user_id bigint not null,
    add constraint task_fk_user foreign key (assigned_user_id) references "user"(id);

------ USER_DEAL -------
drop table if exists "user_deal";

------ MEMBER_TYPE -------
create table if not exists "member_type"
(
    id      serial  not null,
    key     varchar not null,
    name    varchar not null,

    constraint member_type_pk_id primary key (id),
    constraint member_type_uk_key unique (key)
);

------ MEMBER -------
create table if not exists "member"
(
    id                  serial  not null,
    type_id             bigint  not null,
    company_name        varchar not null,
    full_name           varchar not null,
    address             varchar,
    phone_country_code  varchar,
    phone_area_code     varchar,
    phone_number        varchar,
    email               varchar,
    updated_at          timestamp with time zone not null,
    created_at          timestamp with time zone not null,

    constraint member_pk_id primary key (id),
    constraint member_fk_member_type foreign key (type_id) references "member_type"(id)
);

------ DEAL_MEMBER -------
create table if not exists "deal_member"
(
    id          serial not null,
    member_id   bigint not null,
    deal_id     bigint not null,

    constraint member_deal_pk_id primary key (id),
    constraint member_deal_fk_member foreign key (member_id) references "member"(id),
    constraint member_deal_fk_deal foreign key (deal_id) references "deal"(id)
);

------ DEAL_CATEGORY -------
alter table "deal_category"
    add column created_at   timestamp with time zone not null;