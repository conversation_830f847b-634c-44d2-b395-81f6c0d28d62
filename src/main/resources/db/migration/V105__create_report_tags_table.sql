create table if not exists "deal_report_tags"
(
    id                      serial not null,
    report_type_id          bigint not null,
    name                    varchar not null,
    variant                 varchar not null,

    constraint deal_report_tags_pk_id primary key (id),
    constraint deal_report_tags_report_type_key foreign key (report_type_id) references "report_type"(id)
);

create table if not exists "tags_by_deal_report"
(
    id                  serial not null,
    report_id           bigint not null,
    tag_id              bigint not null,

    constraint tags_by_deal_report_pk_id primary key (id),
    constraint tags_by_deal_report_report_id_key foreign key (report_id) references "deal_report"(id),
    constraint tags_by_deal_report_deal_report_relationship_key foreign key (tag_id) references "deal_report_tags"(id)
);

insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Legal Conforming', 'success' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No outstanding zoning code violations', 'success' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No outstanding building code violations', 'success' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No outstanding fire code violations', 'success' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Certificate of Occupancy available', 'success' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Legal Non-Conforming', 'error' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Non-Conforming', 'error' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Outstanding zoning code violations', 'error' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Outstanding building code violations', 'error' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Outstanding fire code violations', 'error' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Certificate of Occupancy unavailable', 'error' from report_type where key = 'ZONING';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No Recognized Environmental Conditions', 'error' from report_type where key = 'PHASE_I';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No Controlled Recognized Environmental Conditions', 'error' from report_type where key = 'PHASE_I';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No Historical Recognized Environmental Conditions', 'error' from report_type where key = 'PHASE_I';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No Business Environmental Risk', 'success' from report_type where key = 'PHASE_I';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Recognized Environmental Conditions', 'success' from report_type where key = 'PHASE_I';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Controlled Recognized Environmental Conditions', 'success' from report_type where key = 'PHASE_I';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Historical Recognized Environmental Conditions', 'success' from report_type where key = 'PHASE_I';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Business Environmental Risk', 'error' from report_type where key = 'PHASE_I';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No Recognized Environmental Conditions', 'error' from report_type where key = 'PCR';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No Controlled Recognized Environmental Conditions', 'error' from report_type where key = 'PCR';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No Historical Recognized Environmental Conditions', 'error' from report_type where key = 'PCR';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'No Business Environmental Risk', 'success' from report_type where key = 'PCR';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Recognized Environmental Conditions', 'success' from report_type where key = 'PCR';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Controlled Recognized Environmental Conditions', 'success' from report_type where key = 'PCR';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Historical Recognized Environmental Conditions', 'success' from report_type where key = 'PCR';
insert into deal_report_tags(report_type_id, name, variant) SELECT id, 'Business Environmental Risk', 'error' from report_type where key = 'PCR';
