UPDATE deal_schema
SET view_schema       = '{"sections": [{"name": "PROPERTY", "type": "DEFAULT"}, {"name": "INTERNAL_DATA", "type": "CUSTOM", "title": "Internal Data", "fields": ["submarket", "tourDate", "callForOffers"]}, {"name": "DEAL_DETAILS", "type": "DEFAULT"}, {"name": "UNDERWRITING", "type": "CUSTOM", "title": "Underwriting Summary", "fields": ["inPlaceRent", "marketRent", "marketRenewal", "renoRents", "marketReno", "capexUnit", "t12Noi", "t12CapRate", "t6CapRate", "t3CapRate", "adjustedCapRate", "capRateFY1", "capRateFY2", "capRateFY3", "stabilizedNoi", "stabilizedYoc", "exitCapRate", "capexExterior", "holdingPeriod"]}, {"name": "PRICING", "type": "CUSTOM", "title": "Pricing Guidance", "fields": ["askingPriceUnit", "askingPriceSf", "offerPriceUnit", "offerPriceSf", "totalCapitalization", "equityRequired", "goingInCap"]}, {"name": "DEBT", "type": "CUSTOM", "title": "Debt Assumptions", "fields": ["lender", "debtAmount", "ltv", "debtRate", "amortization", "term", "io", "supplemental", "refinance"]}, {"name": "DEAL_RETURNS", "type": "CUSTOM", "title": "Deal Level Returns", "fields": ["irr", "em"]}, {"name": "LP_RETURNS", "type": "CUSTOM", "title": "LP Returns", "fields": ["iirr", "iem", "avgCoc", "avgStabilizedCoc"]}, {"name": "FEES", "type": "CUSTOM", "title": "Fees", "fields": ["acquisitionFee", "assetManagementFee", "promote", "hurdleRate"]}, {"name": "SUBMARKET", "type": "CUSTOM", "title": "Submarket", "fields": ["medianIncome", "avgHousePrice", "vacancy", "violentCrime", "propertyCrime", "remaRisk", "gsRating", "supplyAsInventory", "rentMedianIncome"]}, {"name": "NOTES", "type": "CUSTOM", "title": "Notes", "fields": ["propertyNotes", "updateNotes", "pipelineNotes"]}, {"name": "VALUE_DRIVERS", "type": "CUSTOM", "title": "Value Drivers", "fields": ["operational", "renovations", "otherIncome", "rubs"]}, {"name": "COMMENTS", "type": "CUSTOM", "title": "Comments", "fields": ["pros", "cons", "recommendation"]}]}',
    json_schema       = '{"type": "object", "properties": {"em": {"type": "number", "title": "Multiple"}, "io": {"type": "number", "title": "I/O"}, "iem": {"type": "number", "title": "Multiple"}, "irr": {"type": "number", "title": "IRR"}, "ltv": {"type": "number", "title": "LTV"}, "cons": {"type": "string", "title": "Cons / Risks"}, "iirr": {"type": "number", "title": "IRR"}, "pros": {"type": "string", "title": "Pros"}, "rubs": {"type": "string", "title": "RUBS"}, "term": {"type": "number", "title": "Term"}, "avgCoc": {"type": "number", "title": "Avg CoC"}, "lender": {"type": "string", "title": "Lender"}, "t12Noi": {"type": "number", "title": "T12 NOI"}, "promote": {"type": "number", "title": "Promote"}, "vacancy": {"type": "number", "title": "Vacancy (sm)"}, "debtRate": {"type": "number", "title": "Debt Rate"}, "gsRating": {"type": "string", "title": "Greenstreet Rating (sm)"}, "remaRisk": {"type": "string", "title": "Flood Risk (sm)"}, "tourDate": {"type": "string", "title": "Tour Date", "format": "date"}, "capexUnit": {"type": "number", "title": "Capex $/Unit"}, "refinance": {"type": "string", "title": "Refinance"}, "renoRents": {"type": "number", "title": "Reno Rents"}, "submarket": {"type": "string", "title": "Submarket"}, "t3CapRate": {"type": "number", "title": "T3 Cap Rate"}, "t6CapRate": {"type": "number", "title": "T6 Cap Rate"}, "capRateFY1": {"type": "number", "title": "Cap Rate FY1"}, "capRateFY2": {"type": "number", "title": "Cap Rate FY2"}, "capRateFY3": {"type": "number", "title": "Cap Rate FY3"}, "debtAmount": {"type": "number", "title": "Debt Amount"}, "goingInCap": {"type": "number", "title": "Going-In Cap (T12)", "readOnly": true}, "hurdleRate": {"type": "number", "title": "Hurdle Rate"}, "marketReno": {"type": "number", "title": "Market Reno"}, "marketRent": {"type": "number", "title": "Market Rent"}, "t12CapRate": {"type": "number", "title": "T12 Cap Rate"}, "exitCapRate": {"type": "number", "title": "Exit Cap Rate"}, "capexExterior": {"type": "number", "title": "Capex Exterior"}, "holdingPeriod": {"type": "number", "title": "Holding Period"}, "inPlaceRent": {"type": "number", "title": "In-Place Rent"}, "onOffMarket": {"type": "string", "title": "On/Off Market"}, "operational": {"type": "string", "title": "Operational"}, "otherIncome": {"type": "string", "title": "Other Income"}, "renovations": {"type": "string", "title": "Renovations"}, "updateNotes": {"type": "string", "title": "Update Notes"}, "amortization": {"type": "number", "title": "Amortization"}, "medianIncome": {"type": "number", "title": "Median HH Income (Zip)"}, "offerPriceSf": {"type": "number", "title": "Offer Price $/SF", "readOnly": true}, "supplemental": {"type": "string", "title": "Supplemental"}, "violentCrime": {"type": "number", "title": "Violent Crime Score (Zip)"}, "askingPriceSf": {"type": "number", "title": "Asking Price $/SF", "readOnly": true}, "avgHousePrice": {"type": "number", "title": "Avg Home Prices (Zip)"}, "callForOffers": {"type": "string", "title": "Call for offers", "format": "date"}, "marketRenewal": {"type": "number", "title": "Market Renewal"}, "pipelineNotes": {"type": "string", "title": "Pipeline Notes"}, "propertyCrime": {"type": "number", "title": "Property Crime Score (Zip)"}, "propertyNotes": {"type": "string", "title": "Property Notes"}, "stabilizedNoi": {"type": "number", "title": "Stabilized NOI"}, "stabilizedYoc": {"type": "number", "title": "Stabilized YoC", "readOnly": true}, "acquisitionFee": {"type": "number", "title": "Acquisition Fee"}, "equityRequired": {"type": "number", "title": "Equity Required"}, "offerPriceUnit": {"type": "number", "title": "Offer Price $/Unit", "readOnly": true}, "recommendation": {"type": "string", "title": "Recommendation"}, "adjustedCapRate": {"type": "number", "title": "Adjusted Cap Rate"}, "askingPriceUnit": {"type": "number", "title": "Asking Price $/Unit", "readOnly": true}, "avgStabilizedCoc": {"type": "number", "title": "Avg Stabilized CoC"}, "rentMedianIncome": {"type": "number", "title": "Rent / Median Income (sm)"}, "supplyAsInventory": {"type": "number", "title": "Supply as % of inventory (sm)"}, "assetManagementFee": {"type": "number", "title": "Asset Management"}, "totalCapitalization": {"type": "number", "title": "Total Capitalization"}}}',
    field_definitions = '{"irr": {"kind": "PERCENTAGE"}, "ltv": {"kind": "PERCENTAGE"}, "cons": {"kind": "NOTE"}, "iirr": {"kind": "PERCENTAGE"}, "pros": {"kind": "NOTE"}, "rubs": {"kind": "NOTE"}, "avgCoc": {"kind": "PERCENTAGE"}, "promote": {"kind": "PERCENTAGE"}, "vacancy": {"kind": "PERCENTAGE"}, "debtRate": {"kind": "PERCENTAGE"}, "capexUnit": {"kind": "CURRENCY"}, "renoRents": {"kind": "CURRENCY"}, "t3CapRate": {"kind": "PERCENTAGE"}, "t6CapRate": {"kind": "PERCENTAGE"}, "capRateFY1": {"kind": "PERCENTAGE"}, "capRateFY2": {"kind": "PERCENTAGE"}, "capRateFY3": {"kind": "PERCENTAGE"}, "debtAmount": {"kind": "CURRENCY"}, "goingInCap": {"kind": "PERCENTAGE"}, "hurdleRate": {"kind": "PERCENTAGE"}, "marketReno": {"kind": "CURRENCY"}, "marketRent": {"kind": "CURRENCY"}, "t12CapRate": {"kind": "PERCENTAGE"}, "t12Noi": {"kind": "CURRENCY"}, "exitCapRate": {"kind": "PERCENTAGE"}, "inPlaceRent": {"kind": "CURRENCY"}, "operational": {"kind": "NOTE"}, "otherIncome": {"kind": "NOTE"}, "renovations": {"kind": "NOTE"}, "updateNotes": {"kind": "NOTE"}, "medianIncome": {"kind": "CURRENCY"}, "offerPriceSf": {"kind": "CURRENCY"}, "askingPriceSf": {"kind": "CURRENCY"}, "avgHousePrice": {"kind": "CURRENCY"}, "marketRenewal": {"kind": "CURRENCY"}, "pipelineNotes": {"kind": "NOTE"}, "propertyNotes": {"kind": "NOTE"}, "stabilizedNoi": {"kind": "CURRENCY"}, "stabilizedYoc": {"kind": "PERCENTAGE"}, "acquisitionFee": {"kind": "PERCENTAGE"}, "equityRequired": {"kind": "CURRENCY"}, "offerPriceUnit": {"kind": "CURRENCY"}, "recommendation": {"kind": "NOTE"}, "adjustedCapRate": {"kind": "PERCENTAGE"}, "askingPriceUnit": {"kind": "CURRENCY"}, "assetManagementFee": {"kind": "PERCENTAGE"}, "avgStabilizedCoc": {"kind": "PERCENTAGE"}, "rentMedianIncome": {"kind": "PERCENTAGE"}, "supplyAsInventory": {"kind": "PERCENTAGE"}, "totalCapitalization": {"kind": "CURRENCY"}, "capexExterior": {"kind": "CURRENCY"}}'
WHERE id = 2;