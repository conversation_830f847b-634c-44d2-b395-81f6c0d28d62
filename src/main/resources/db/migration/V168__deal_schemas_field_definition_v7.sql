UPDATE deal_schema
    SET
        view_schema = '{"sections":[{"name":"PROPERTY","type":"DEFAULT"},{"name":"INTERNAL_DATA","type":"CUSTOM","title":"Internal Data","fields":["submarket","tourDate","callForOffers","units"]},{"name":"DEAL_DETAILS","type":"DEFAULT"},{"name":"UNDERWRITING","type":"CUSTOM","title":"Underwriting Summary","fields":["inPlaceRent","marketRent","marketRenewal","marketReno","capexUnit","t12Noi","t12CapRate","t6CapRate","t3CapRate","adjustedCapRate","capRateFY1","capRateFY2","capRateFY3","stabilizedNoi","stabilizedYoc","exitCapRate"]},{"name":"PRICING","type":"CUSTOM","title":"Pricing Guidance","fields":["askingPriceUnit","askingPriceSf","offerPriceUnit","offerPriceSf","totalCapitalization","equityRequired","goingInCap"]},{"name":"DEBT","type":"CUSTOM","title":"Debt Assumptions","fields":["lender","debtAmount","ltv","debtRate","amortization","term","io","supplemental","refinance"]},{"name":"RETURNS","type":"CUSTOM","title":"Returns","fields":["irr","em","iirr","iem","avgCoc","avgStabilizedCoc"]},{"name":"SUBMARKET","type":"CUSTOM","title":"Submarket","fields":["medianIncome","avgHousePrice","vacancy","violentCrime","propertyCrime","remaRisk","gsRating","supplyAsInventory","rentMedianIncome"]},{"name":"NOTES","type":"CUSTOM","title":"Notes","fields":["propertyNotes","updateNotes","pipelineNotes"]}]}'
        WHERE id = 2;