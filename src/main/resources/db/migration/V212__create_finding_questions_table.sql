create table if not exists "finding_questions"
(
    id              serial not null,
    key             varchar not null,
    property_type   varchar not null,
    entity          varchar not null,
    entity_type     varchar not null,
    query           varchar not null,
    prompt          varchar not null
);

insert into finding_questions(key, property_type, entity, entity_type, query, prompt)
values
    (
        'VALIDATE_TENANT',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
            Given the following deal information:
            {
              "name": "$_property.name_",
               "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
               "offer_price": $_deal.offerPrice_,
               "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
               "closing_extension_deposit": $_deal.extensionDeposit_,
               "year_built": $_property.yearBuilt_,
               "type": "$_property.type_",
               "square_footage": $_property.squareFootage_,
               "asking_price": $_property.askingPrice_,
               "units": $_property.multifamilyData.units_,
               "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
               "occupancy": $_property.multifamilyData.occupancy_,
               "parking_spots": $_property.multifamilyData.parkingSpots_,
               "owner": "$_property.multifamilyData.owner_",
               "property_manager": "$_property.multifamilyData.propertyManager_",
               "tenant_name": "$_deal.tenantName_",
               "guarantee_type": "$_deal.guaranteeType_",
               "vertical": "$_deal.vertical_",
               "lease_rent": $_deal.lease.rent_,
               "lease_type": "$_deal.lease.type_",
               "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
               "lease_length": $_deal.lease.length_,
               "lease_expiration_year": $_deal.lease.expirationYear_,
               "lease_option_lengths": $_deal.lease.optionLengths_,
               "lease_number_of_options": $_deal.lease.numberOfOptions_,
               "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
               "closing_period": $_deal.lease.closingPeriod_,
               "closing_extension_period": $_deal.lease.closingPeriodExtension_
            }

            Respond indicating whether there is an error in the document information and, if so, whether the tenant name match. Your response should follow this format:

            {
              "tenant_name": "Tenant",
              "loi_document_tenant_name": "[replace with the tenant name in the LOI document, it could be the document subject or be on the regarding to/or Referring or Re:]",
              "error_found": true,
              "explanation": "[replace with the error explanation]"
            }
            {
              "tenant_name": "Tenant",
              "loi_document_tenant_name": "Tenant",
              "error_found": false,
              "explanation": "[replace with the error explanation]"
            }

            You use the example above as a template for your response. Make sure to set ''error_found'' to true if the tenant names don''t match and ''error_found'' to false in any other case.
            if you are unable to answer respond:
            {
              "error_found": false,
              "explanation": "Unable to answer"
            }
        ',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_ADDRESS',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the addresses match. Your response should follow this format:

        {
          "property_address": "1417 N Garrett Street, Dallas, TX 75206",
          "loi_document_address_value": "[replace with the LOI document address]]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "property_address": "1417 N Garrett Street, Dallas, TX 75206",
          "loi_document_address_value": "1417 N Garrett Street, Dallas, TX 75206",
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the addresses don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_SQUARE_FOOTAGE',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the square footage match. Your response should follow this format:

        {
          "square_footage": 67253,
          "loi_square_footage": "[The square footage in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "square_footage": 67253,
          "loi_square_footage": 67253,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the square footage don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_VERTICAL',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the vertical building match. Your response should follow this format:

        {
          "vertical": "Cardiology",
          "loi_vertical": "[The vertical building in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "vertical": "Cardiology",
          "loi_vertical": "Cardiology",
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the verticals building don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_GUARANTEE_TYPE',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the guarantee types match. Your response should follow this format:

        {
          "guarantee_type": "1417",
          "loi_guarantee_type": "[The guarantee type in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "guarantee_type": "PERSONAL",
          "loi_guarantee_type": "PERSONAL",
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the guarantee types don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_OFFER_PRICE',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }
        In addition, please consider the following information: In the LOI document, the Purchase Price may also be referred to as the Sales Price, Proposed Price, Buying Price, or Offered Amount, among other possible terms.

        Your task is to evaluate the document information and indicate whether there is an error in the document. If an error is found, check if there are any differences in the prices. Your response should follow this format:

        {
        "offer_price": [the deal information''s offer_price],
        "loi_purchase_price": "[The Purchase Price in the LOI document]",
        "error_found": true,
        "explanation": "[Replace with the error explanation]"
        }
        {
        "offer_price": [replace with the offer price],
        "loi_purchase_price": [replace with the Purchase Price in the LOI document],
        "error_found": false,
        "explanation": "[Replace with the error explanation]"
        }

        Please set ''error_found'' to true if the offer prices do not match and ''error_found'' to false in any other case. If you are unable to provide an answer, please respond with:

        {
        "error_found": false,
        "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_LEASE_RENT',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the leases rent match. Your response should follow this format:

        {
          "lease_rent": 343434,
          "loi_lease_rent": "[The lease rent in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "lease_rent": 343434,
          "loi_lease_rent": 343434,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the leases rent don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_EARNEST_MONEY_DEPOSIT',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Your task is to evaluate the document information and indicate whether there is an error in the document. If an error is found, check if there are any differences in the deposit prices. Your response should follow this format:

        {
        "earnest_money_deposit": [the deal information''s earnest_money_deposit],
        "loi_earnest_money_deposit": "[The deposit in the LOI document]",
        "error_found": true,
        "explanation": "[Replace with the error explanation]"
        }
        {
        "earnest_money_deposit": [the deal information''s earnest_money_deposit],
        "loi_earnest_money_deposit": "[The deposit in the LOI document]",
        "error_found": true,
        "explanation": "[Replace with the error explanation]"
        }

        Please set ''error_found'' to true if the offer prices do not match and ''error_found'' to false in any other case. If you are unable to provide an answer, please respond with:

        {
        "error_found": false,
        "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_LEASE_TYPE',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the lease types match. Your response should follow this format:

        {
          "lease_type": "NNN",
          "loi_lease_type": "[The lease structure type in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "lease_type": "NNN",
          "loi_lease_type": "NNN",
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the lease types don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_LEASE_INCREASE_EVERY_YEAR',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the amounts of lease increase every year match. Your response should follow this format:

        {
          "lease_increase_every_year": 30,
          "loi_lease_increase_every_year": "[The amount of lease increase every year in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "lease_increase_every_year": 30,
          "loi_lease_increase_every_year": 30,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the amounts of lease increase every year don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_LEASE_LENGTH',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the lease term periods match. Your response should follow this format:

        {
          "lease_length": 30,
          "loi_lease_length": "[The period of lease term in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "lease_length": 30,
          "loi_lease_length": 30,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the lease term periods don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_LEASE_EXPIRATION_YEAR',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the lease expiration years match. Your response should follow this format:

        {
          "lease_expiration_year": 2033,
          "loi_lease_expiration_year": "[The lease expiration year in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "lease_expiration_year": 2033,
          "loi_lease_expiration_year": 2033,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the lease expiration years don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_LEASE_OPTION_LENGTHS',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the lease options length match. Your response should follow this format:

        {
          "lease_option_lengths": 3,
          "loi_lease_option_lengths": "[The lease option length in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "lease_option_lengths": 3,
          "loi_lease_option_lengths": 3,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the lease options length don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_LEASE_NUMBER_OF_OPTIONS',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the lease numbers of options match. Your response should follow this format:

        {
          "lease_number_of_options": 88,
          "loi_lease_number_of_options": "[The lease number of options in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "lease_number_of_options": 88,
          "loi_lease_number_of_options": 88,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the lease numbers of options don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_DUE_DILIGENCE_PERIOD',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the due diligence periods match. Your response should follow this format:

        {
          "due_diligence_period": 200,
          "loi_due_diligence_period": "[The due diligence period in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "due_diligence_period": 200,
          "loi_due_diligence_period": 200,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the due diligence periods don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_CLOSING_PERIOD',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the closing days periods match. Your response should follow this format:

        {
          "closing_period": 100,
          "loi_closing_period": "[The closing days period in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "closing_period": 100,
          "loi_closing_period": 100,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the closing days periods don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    ),
    (
        'VALIDATE_CLOSING_EXTENSION_PERIOD',
        'ALL',
        'DOCUMENT',
        'LOI',
        'Please provide a JSON
        Given the following deal information:
        {
          "name": "$_property.name_",
           "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
           "offer_price": $_deal.offerPrice_,
           "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
           "closing_extension_deposit": $_deal.extensionDeposit_,
           "year_built": $_property.yearBuilt_,
           "type": "$_property.type_",
           "square_footage": $_property.squareFootage_,
           "asking_price": $_property.askingPrice_,
           "units": $_property.multifamilyData.units_,
           "average_square_footage": $_property.multifamilyData.averageSquareFootage_,
           "occupancy": $_property.multifamilyData.occupancy_,
           "parking_spots": $_property.multifamilyData.parkingSpots_,
           "owner": "$_property.multifamilyData.owner_",
           "property_manager": "$_property.multifamilyData.propertyManager_",
           "tenant_name": "$_deal.tenantName_",
           "guarantee_type": "$_deal.guaranteeType_",
           "vertical": "$_deal.vertical_",
           "lease_rent": $_deal.lease.rent_,
           "lease_type": "$_deal.lease.type_",
           "lease_increase_every_year": $_deal.lease.increaseEveryYear_,
           "lease_length": $_deal.lease.length_,
           "lease_expiration_year": $_deal.lease.expirationYear_,
           "lease_option_lengths": $_deal.lease.optionLengths_,
           "lease_number_of_options": $_deal.lease.numberOfOptions_,
           "due_diligence_period": $_deal.lease.dueDiligenceNumber_,
           "closing_period": $_deal.lease.closingPeriod_,
           "closing_extension_period": $_deal.lease.closingPeriodExtension_
        }

        Respond indicating whether there is an error in the document information and, if so, whether the closing extension days periods match. Your response should follow this format:

        {
          "closing_extension_period": 150,
          "loi_closing_extension_period": "[The closing extension days period in the LOI document]",
          "error_found": true,
          "explanation": "[replace with the error explanation]"
        }
        {
          "closing_extension_period": 150,
          "loi_closing_extension_period": 150,
          "error_found": false,
          "explanation": "[replace with the error explanation]"
        }

        You use the example above as a template for your response. Make sure to set ''error_found'' to true if the closing extension days periods don''t match and ''error_found'' to false in any other case.
        if you are unable to answer respond:
        {
          "error_found": false,
          "explanation": "Unable to answer"
        }',
        'Act as an agent of commercial real estate expert in diligence Letter of Intent (LOI) analysis. Use your knowledge about commercial real estate for responding some question about the information you have'
    );