
UPDATE deal d
    SET deal_schema_id = (SELECT s.id FROM deal_schema s WHERE s.property_type = 'MEDICAL' ORDER BY s.created_at DESC LIMIT 1)
    FROM property p
    WHERE d.property_id = p.id
        AND p.type = 'MEDICAL'
        AND d.deal_schema_id IS NULL;

UPDATE deal d
    SET deal_schema_id = (SELECT s.id FROM deal_schema s WHERE s.property_type = 'MULTIFAMILY' ORDER BY s.created_at DESC LIMIT 1)
    FROM property p
    WHERE d.property_id = p.id
        AND p.type = 'MULTIFAMILY'
        AND d.deal_schema_id IS NULL;

ALTER TABLE deal ALTER COLUMN deal_schema_id SET NOT NULL;
