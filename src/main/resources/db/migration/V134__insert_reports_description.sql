ALTER TABLE report_type ADD COLUMN description character varying;

UPDATE report_type SET description = 'Survey' where key ='SURVEY';
UPDATE report_type SET description = 'Zoning' where key ='ZONING';
UPDATE report_type SET description = 'Title' where key ='TITLE';
UPDATE report_type SET description = 'Phase I' where key ='PHASE_I';
UPDATE report_type SET description = 'Phase II' where key ='PHASE_II';
UPDATE report_type SET description = 'Appraisal' where key ='APPRAISAL';
UPDATE report_type SET description = 'PCR' where key ='PCR';
UPDATE report_type SET description = 'Seismic Report' where key ='SEISMIC_REPORT';
UPDATE report_type SET description = 'Soil & Geologic Report' where key ='SOIL_AND_GEOLOGIC_REPORT';
UPDATE report_type SET description = 'Mold Survey' where key ='MOLD_SURVEY';
UPDATE report_type SET description = 'Hazard Materials Records' where key ='HAZARDOUS_MATERIALS_RECORDS';
UPDATE report_type SET description = 'Asbestos Survey' where key ='ASBESTOS_SURVEY';
UPDATE report_type SET description = 'Other' where key ='OTHER';
UPDATE report_type SET description = 'Insurance Quote' where key ='INSURANCE_QUOTE';
UPDATE report_type SET description = 'Real Estate Property Tax Forecast' where key ='REAL_ESTATE_PROPERTY_TAX_FORECAST';
UPDATE report_type SET description = 'Property Onsite Due Diligence' where key ='PROPERTY_ONSITE_DUE_DILIGENCE';
UPDATE report_type SET description = 'Property Condition Assessment' where key ='PROPERTY_CONDITION_ASSESSMENT';
UPDATE report_type SET description = 'ALTA Survey' where key ='ALTA_SURVEY';
UPDATE report_type SET description = 'NOI Audit' where key ='NOI_AUDIT';
UPDATE report_type SET description = 'Physical' where key ='PHYSICAL';
UPDATE report_type SET description = 'Termite Report' where key ='TERMITE_REPORT';

ALTER TABLE report_type ALTER COLUMN description set not null;