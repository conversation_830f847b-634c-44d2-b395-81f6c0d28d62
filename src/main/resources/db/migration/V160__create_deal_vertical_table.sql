create table if not exists deal_vertical(
    name varchar not null unique
);

insert into deal_vertical(name)
values ('MULTIFAMILY'),
    ('ASC'),
    ('Autism Clinic'),
    ('Auto'),
    ('Behavioral'),
    ('Health'),
    ('Cardiology'),
    ('Childcare'),
    ('Chiropractic'),
    ('Dental'),
    ('Dentistry'),
    ('Dermatology'),
    ('Dialysis'),
    ('Emergency Room'),
    ('ENT'),
    ('Eye Center'),
    ('Gastroenterology'),
    ('Hospital'),
    ('Imaging'),
    ('Industrial'),
    ('Internal Medicine'),
    ('Interventional Radiology'),
    ('Medical'),
    ('Nephrology'),
    ('Neurosurgery'),
    ('Ophthalmology'),
    ('Optometry'),
    ('Orthopedic'),
    ('Other'),
    ('Pediatrics'),
    ('Pharmacy'),
    ('Physical Therapy'),
    ('Plastic Surgery'),
    ('Primary Care'),
    ('QSR'),
    ('Retail'),
    ('Rheumatology'),
    ('Surgery Center'),
    ('Urgent Care'),
    ('Urology'),
    ('Vascular'),
    ('Veterinary');

update deal set vertical = 'ENT' where vertical = 'Allergy & ENT Specialist';
update deal set vertical = 'Pediatrics' where vertical = 'Pediatric';
update deal set vertical = 'Surgery Center' where vertical = 'Surgery Center / Pediatric Dentistry';
update deal set vertical = 'Veterinary' where vertical = 'Vet';
update deal set vertical = 'Dentistry' where vertical = 'Dentist - Oral Surgeon';
update deal set vertical = 'Dentistry' where vertical = 'Dentists';
update deal set vertical = 'Eye Center' where vertical = 'Eye doctors';
update deal set vertical = 'Eye Center' where vertical = 'Eye Care';
update deal set vertical = 'ASC' where vertical = 'Ambulatory Surgery Center';
update deal set vertical = 'Imaging' where vertical = 'Imaging Center';
update deal set vertical = 'Dermatology' where vertical = 'Dermatologists';
update deal set vertical = 'Veterinary' where vertical = 'Veterinarian';
update deal set vertical = 'Autism Clinic' where vertical = 'Autism Center';
update deal set vertical = 'Other' where vertical not in (select name as vertical from deal_vertical);

alter table deal add constraint deal_fk_deal_vertical foreign key (vertical) references "deal_vertical"(name);
