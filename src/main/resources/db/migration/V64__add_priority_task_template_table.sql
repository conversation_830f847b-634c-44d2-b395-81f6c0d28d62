--- TASK TEMPLATE -----
ALTER TABLE task_template ADD COLUMN priority varchar;
ALTER TABLE task_template ADD COLUMN sorting numeric;
ALTER TABLE task_template ADD COLUMN required boolean;
ALTER TABLE task_template ADD COLUMN prioritize_after_closing boolean;

update task_template set sorting=1, priority='HIGH', required=true where key='lease';
update task_template set sorting=2, priority='HIGH', required=true where key='title';
update task_template set sorting=3, priority='HIGH', required=true where key='survey';
update task_template set sorting=5, priority='HIGH', required=true where key='floor_plans';
update task_template set sorting=5, priority='HIGH', prioritize_after_closing=true, required=true where key='payment_and_invoicing_details_tutorial';
update task_template set sorting=1, priority='HIGHEST', required=true where key='purchase_and_sale_contract';
update task_template set sorting=1, priority='HIGHEST', prioritize_after_closing=true, required=true where key='share_invoices_checklist';
update task_template set sorting=2, priority='HIGHEST', required=true where key='financial_statements';
update task_template set sorting=2, priority='HIGHEST', prioritize_after_closing=true, required=true where key='new_point_of_contact';
update task_template set sorting=3, priority='HIGHEST', required=true where key='personal_property_inventory';
update task_template set sorting=3, priority='HIGHEST', prioritize_after_closing=true, required=true where key='proof_of_flood_insurance';
update task_template set sorting=4, priority='HIGHEST', required=true where key='service_contracts';
update task_template set sorting=4, priority='HIGHEST', prioritize_after_closing=true, required=true where key='property_insurance';
update task_template set sorting=1, priority='MEDIUM', required=true where key='certificates_of_occupancy';
update task_template set sorting=2, priority='MEDIUM', required=true where key='additional_lease_documents';
update task_template set sorting=3, priority='MEDIUM', required=true where key='sublease_documents';
update task_template set sorting=4, priority='MEDIUM' where key='phase_i_report';
update task_template set sorting=5, priority='MEDIUM' where key='umbrella_insurance';
update task_template set sorting=6, priority='MEDIUM' where key='proof_of_property_insurance';
update task_template set sorting=6, priority='MEDIUM', prioritize_after_closing=true, required=true where key='wet_notarized_documents_tutorial';
update task_template set sorting=7, priority='MEDIUM' where key='liability_insurance';
update task_template set sorting=7, priority='MEDIUM', prioritize_after_closing=true, required=true where key='wet_documents_tutorial';
update task_template set sorting=8, priority='MEDIUM' where key='zoning_report';
update task_template set sorting=9, priority='MEDIUM' where key='zoning';
update task_template set sorting=10, priority='MEDIUM' where key='tenant_estoppel';
update task_template set sorting=11, priority='MEDIUM' where key='phase_ii_report';
update task_template set sorting=12, priority='MEDIUM' where key='physical';
update task_template set sorting=13, priority='MEDIUM' where key='any_existing_seismic_reports';
update task_template set sorting=14, priority='MEDIUM' where key='tenant_correspondence';
update task_template set sorting=15, priority='MEDIUM' where key='loss_run_report';
update task_template set sorting=1, priority='LOW' where key='insurance';
update task_template set sorting=2, priority='LOW' where key='utility_bills';
update task_template set sorting=3, priority='LOW' where key='tax_return';
update task_template set sorting=4, priority='LOW' where key='as_builts';
update task_template set sorting=4, priority='LOW' where key='previous_tenant_estoppels_and_sndas';
update task_template set sorting=5, priority='LOW' where key='real_estate_tax_bills';
update task_template set sorting=6, priority='LOW' where key='soil_and_geologic_report';
update task_template set sorting=7, priority='LOW' where key='other_reports';
update task_template set sorting=8, priority='LOW' where key='mold_survey';
update task_template set sorting=9, priority='LOW' where key='hazardous_materials_records';
update task_template set sorting=10, priority='LOW' where key='asbestos_survey';
update task_template set sorting=11, priority='LOW' where key='remaining_warranties_guarantees';
update task_template set sorting=12, priority='LOW' where key='parking_revenue_expenses_and_management';
update task_template set sorting=13, priority='LOW' where key='building_permits';
update task_template set sorting=14, priority='LOW' where key='records_and_correspondence_regarding_pending_or_threatened_litigation';

update task_template set sorting=99999 where sorting is null;
update task_template set priority='NA' where priority is null;
update task_template set required=false where required is null;
update task_template set prioritize_after_closing=false where prioritize_after_closing is null;

ALTER TABLE task_template ALTER COLUMN priority set not null;
ALTER TABLE task_template ALTER COLUMN sorting set not null;
ALTER TABLE task_template ALTER COLUMN required set not null;
ALTER TABLE task_template ALTER COLUMN prioritize_after_closing set not null;

---- TASK ---

ALTER TABLE task ADD COLUMN priority varchar;
ALTER TABLE task ADD COLUMN sorting numeric;
ALTER TABLE task ADD COLUMN required boolean;
ALTER TABLE task ADD COLUMN prioritize_after_closing boolean;

update task set sorting=1, priority='HIGH', required=true where template_key='lease';
update task set sorting=2, priority='HIGH', required=true where template_key='title';
update task set sorting=3, priority='HIGH', required=true where template_key='survey';
update task set sorting=5, priority='HIGH', required=true where template_key='floor_plans';
update task set sorting=5, priority='HIGH', prioritize_after_closing=true, required=true where template_key='payment_and_invoicing_details_tutorial';
update task set sorting=1, priority='HIGHEST', required=true where template_key='purchase_and_sale_contract';
update task set sorting=1, priority='HIGHEST', prioritize_after_closing=true, required=true where template_key='share_invoices_checklist';
update task set sorting=2, priority='HIGHEST', required=true where template_key='financial_statements';
update task set sorting=2, priority='HIGHEST', prioritize_after_closing=true, required=true where template_key='new_point_of_contact';
update task set sorting=3, priority='HIGHEST', required=true where template_key='personal_property_inventory';
update task set sorting=3, priority='HIGHEST', prioritize_after_closing=true, required=true where template_key='proof_of_flood_insurance';
update task set sorting=4, priority='HIGHEST', required=true where template_key='service_contracts';
update task set sorting=4, priority='HIGHEST', prioritize_after_closing=true, required=true where template_key='property_insurance';
update task set sorting=1, priority='MEDIUM', required=true where template_key='certificates_of_occupancy';
update task set sorting=2, priority='MEDIUM', required=true where template_key='additional_lease_documents';
update task set sorting=3, priority='MEDIUM', required=true where template_key='sublease_documents';
update task set sorting=4, priority='MEDIUM' where template_key='phase_i_report';
update task set sorting=5, priority='MEDIUM' where template_key='umbrella_insurance';
update task set sorting=6, priority='MEDIUM' where template_key='proof_of_property_insurance';
update task set sorting=6, priority='MEDIUM', prioritize_after_closing=true, required=true where template_key='wet_notarized_documents_tutorial';
update task set sorting=7, priority='MEDIUM' where template_key='liability_insurance';
update task set sorting=7, priority='MEDIUM', prioritize_after_closing=true, required=true where template_key='wet_documents_tutorial';
update task set sorting=8, priority='MEDIUM' where template_key='zoning_report';
update task set sorting=9, priority='MEDIUM' where template_key='zoning';
update task set sorting=10, priority='MEDIUM' where template_key='tenant_estoppel';
update task set sorting=11, priority='MEDIUM' where template_key='phase_ii_report';
update task set sorting=12, priority='MEDIUM' where template_key='physical';
update task set sorting=13, priority='MEDIUM' where template_key='any_existing_seismic_reports';
update task set sorting=14, priority='MEDIUM' where template_key='tenant_correspondence';
update task set sorting=15, priority='MEDIUM' where template_key='loss_run_report';
update task set sorting=1, priority='LOW' where template_key='insurance';
update task set sorting=2, priority='LOW' where template_key='utility_bills';
update task set sorting=3, priority='LOW' where template_key='tax_return';
update task set sorting=4, priority='LOW' where template_key='as_builts';
update task set sorting=4, priority='LOW' where template_key='previous_tenant_estoppels_and_sndas';
update task set sorting=5, priority='LOW' where template_key='real_estate_tax_bills';
update task set sorting=6, priority='LOW' where template_key='soil_and_geologic_report';
update task set sorting=7, priority='LOW' where template_key='other_reports';
update task set sorting=8, priority='LOW' where template_key='mold_survey';
update task set sorting=9, priority='LOW' where template_key='hazardous_materials_records';
update task set sorting=10, priority='LOW' where template_key='asbestos_survey';
update task set sorting=11, priority='LOW' where template_key='remaining_warranties_guarantees';
update task set sorting=12, priority='LOW' where template_key='parking_revenue_expenses_and_management';
update task set sorting=13, priority='LOW' where template_key='building_permits';
update task set sorting=14, priority='LOW' where template_key='records_and_correspondence_regarding_pending_or_threatened_litigation';

update task set sorting=99999 where sorting is null;
update task set priority='NA' where priority is null;
update task set required=false where required is null;
update task set prioritize_after_closing=false where prioritize_after_closing is null;

ALTER TABLE task ALTER COLUMN priority set not null;
ALTER TABLE task ALTER COLUMN sorting set not null;
ALTER TABLE task ALTER COLUMN required set not null;
ALTER TABLE task ALTER COLUMN prioritize_after_closing set not null;

