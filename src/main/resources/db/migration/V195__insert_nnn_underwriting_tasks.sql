INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
    (
	    'nnn_business_and_finances',
	    'upload_file',
	    'buyer',
	    'Client Financial Documentation Upload',
	    '',
	    'nnn_client_financial_documentation',
	    '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please Upload Client Financial Documentation","format":"data-url"}}}',
	    null,
	    'NA',
	    19,
	    false,
	    false,
	    'BUYER_TEAM'
	),
	(
    	'nnn_business_and_finances',
    	'upload_file',
    	'buyer',
        'Keyway Financial Documentation Upload',
    	'',
        'nnn_keyway_financial_documentation',
	    '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please Upload Keyway Financial Documentation","format":"data-url"}}}',
    	null,
    	'NA',
    	20,
    	false,
    	false,
        'BUYER_TEAM'
    ),
    (
        'nnn_pipeline',
        'form',
        'buyer',
        'Pipeline Meeting',
        '',
        'nnn_pipeline_meeting',
        '{"type":"object","properties":{"text":{"type":"string","title":"Catlyst For Saling","format":"textarea"}, "approved":{"enum":["","Yes","No"],"type":"string","title":"was approved?"}}}',
        null,
        'NA',
        21,
        false,
        false,
        'BUYER_TEAM'
    ),
     (
         'nnn_pipeline',
         'form',
         'buyer',
         'Investment Committee Meeting',
         '',
         'nnn_investment_committee_meeting',
         '{"type":"object","properties":{"text":{"type":"string","title":"Catlyst For Saling","format":"textarea"}, "approved":{"enum":["","Yes","No"],"type":"string","title":"was approved?"}}}',
         null,
         'NA',
         22,
         false,
         false,
         'BUYER_TEAM'
     ),
     (
         'nnn_icm1',
         'upload_file',
         'buyer',
         'ICM 1',
         '',
         'nnn_icm1',
	     '{"type":"object","required":["icm"],"properties":{"icm":{"type":"string","title":"Please Upload ICM 1","format":"data-url"}}}',
         null,
         'NA',
         23,
         false,
         false,
         'BUYER_TEAM'
     );

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('default', 'nnn_client_financial_documentation'),
    ('default', 'nnn_keyway_financial_documentation'),
    ('default', 'nnn_pipeline_meeting'),
    ('default', 'nnn_investment_committee_meeting'),
    ('default', 'nnn_icm1');