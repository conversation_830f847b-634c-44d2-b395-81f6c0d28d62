INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
    (
	    'multifamily_preliminary_analysis',
	    'form_and_file',
	    'buyer',
	    'Preliminary Micro-Location Analysis',
	    '',
	    'multifamily_preliminary_micro_location_analysis',
	    '{"type":"object","properties":{"averageHomeValue":{"type":"number","title":"Average Home Value"},"averageIncome":{"type":"number","title":"Average Income"},"crimeScore":{"type":"number","title":"Crime Score"},"microLocationAnalysis":{"type":"string","title":"Please Upload Micro-Location Analysis","format":"data-url"}}}',
	    null,
	    'LOW',
	    1,
	    false,
	    false,
	    'BUYER_TEAM'
	),
	(
    	'multifamily_preliminary_analysis',
    	'form_and_file',
    	'buyer',
        'Preliminary Rent Comp Analysis',
    	'',
        'multifamily_preliminary_rent_comp_analysis',
    	'{"type":"object","properties":{"averageRentOfProperty":{"type":"number","title":"Average Rent of Property"},"averageRentComp":{"type":"number","title":"Average Rent Comp"},"rentCompAnalysis":{"type":"string","title":"Please Upload Rent Comp Analysis","format":"data-url"}}}',
    	null,
    	'LOW',
    	2,
    	false,
    	false,
        'BUYER_TEAM'
    );

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('multifamily', 'multifamily_preliminary_micro_location_analysis'),
    ('multifamily', 'multifamily_preliminary_rent_comp_analysis');