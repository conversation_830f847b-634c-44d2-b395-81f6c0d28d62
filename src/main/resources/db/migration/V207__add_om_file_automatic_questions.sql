insert into document_type_prompt(document, prompt)
values ('OM', 'I want you to act as a real estate agent. I will provide you an offering memorandum file about a property, and your role is to help me to find all of values that I am gonna need. You should use your knowledge for finding them inside the file');

insert into document_questions(document, question, priority, ask_automatically)
values
    ('OM', 'Make a property summary including address, name, number of stories, submarket for knowing the place', 1, true),
    ('OM', 'Make a summary of submarket description', 2, true),
    ('OM', 'How many spaces does the property have? and wich is the number of parking spots per unit?', 3, true),
    ('OM', 'Make a list of fees per month with price', 4, true),
    ('OM', 'Please list the detail of Admin fee, Application fee, Pet non-reimbursable deposit, Pet deposit', 5, true),
    ('OM', 'Please tell me who pays that kind of services: Water/Sewer, Electric, Gas, Trash, the tenant or landlord', 6, true),
    ('OM', 'Provide summary of staff detailing number of employees with their roles/titles', 7, true),
    ('OM', 'If any LOAN exists please list the detail of Loan Amount, Interest Rate, Origination date, Interest only period, Amortization period', 8, true),
    ('OM', 'Describe value add proposal and please provide a list of the comparable properties, their year built, number of units and avg SF', 9, true),
    ('OM', 'Please provide a list of Total rental income for year 1, Effective Gross Income for year 1, Total expenses for year 1, Net operating income of year 1', 10, true),
    ('OM', 'Make a construction summary and list the detail of Framing, Foundation, Exterior Walls, Roof, Wiring, Plumbing, Ceiling Height', 11, true);