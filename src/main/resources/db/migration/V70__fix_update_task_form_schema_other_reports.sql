update task_template set form_schema ='{"type": "object", "required": ["zoningReport"], "properties": {"zoningReport": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have a zoning report"}}, "dependencies": {"zoningReport": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "zoningReport": {"enum": ["Yes"]}}}]}}}' where key = 'zoning_report';
update task_template set form_schema ='{"type": "object", "required": ["parcelNumber", "constructedOn"], "properties": {"lotBlock": {"type": "string", "title": "Lot & Block"}, "parcelNumber": {"type": "number", "title": "Tax Parcel Number"}, "constructedOn": {"type": "string", "title": "Date of Construction", "format": "date"}}}' where key = 'zoning';
update task_template set form_schema =NULL where key = 'wet_notarized_documents_tutorial';
update task_template set form_schema =NULL where key = 'wet_documents_tutorial';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'utility_bills';
update task_template set form_schema ='{"type": "object", "required": ["umbrellaInsurance"], "properties": {"umbrellaInsurance": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have Umbrella Insurance?"}}, "dependencies": {"umbrellaInsurance": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "umbrellaInsurance": {"enum": ["Yes"]}}}]}}}' where key = 'umbrella_insurance';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'title';
update task_template set form_schema ='{"type": "object", "required": ["liens"], "properties": {"liens": {"enum": ["", "Yes", "No"], "type": "string", "title": "Are there any outstanding liens?"}}, "dependencies": {"liens": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any documents relating to liens", "format": "data-url"}, "liens": {"enum": ["Yes"]}}}]}}}' where key = 'third_parties_with_respect_to_liens';
update task_template set form_schema ='{"type": "object", "required": ["estoppel"], "properties": {"estoppel": {"enum": ["", "Yes", "No"], "type": "string", "title": "Is there a form estoppel attached to the lease?"}}, "dependencies": {"estoppel": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload the form estoppel", "format": "data-url"}, "estoppel": {"enum": ["Yes"]}}}]}}}' where key = 'tenant_estoppel';
update task_template set form_schema ='{"type": "object", "title": "Tenant Correspondence", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any tenant correspondence", "format": "data-url"}}, "description": "Correspondence from the tenant related to missed payments, maintenance concerns, or anything else"}' where key = 'tenant_correspondence';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'tax_return';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'survey';
update task_template set form_schema ='{"type": "object", "required": ["sublease"], "properties": {"sublease": {"enum": ["", "Yes", "No"], "type": "string", "title": "Is there a sublease?"}}, "dependencies": {"sublease": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any sublease documents", "format": "data-url"}, "sublease": {"enum": ["Yes"]}}}]}}}' where key = 'sublease_documents';
update task_template set form_schema ='{"type": "object", "required": ["soilAndGeologicReport"], "properties": {"soilAndGeologicReport": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have a Soil and Geologic Report?"}}, "dependencies": {"soilAndGeologicReport": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "soilAndGeologicReport": {"enum": ["Yes"]}}}]}}}' where key = 'soil_and_geologic_report';
update task_template set form_schema ='{"type": "object", "required": ["snda"], "properties": {"snda": {"enum": ["", "Yes", "No"], "type": "string", "title": "Do you have an SNDA?"}}, "dependencies": {"snda": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any SNDA", "format": "data-url"}, "snda": {"enum": ["Yes"]}}}]}}}' where key = 'snda';
update task_template set form_schema =NULL where key = 'share_invoices_checklist';
update task_template set form_schema ='{"type": "object", "required": ["serviceContracts"], "properties": {"serviceContracts": {"enum": ["", "Yes", "No"], "type": "string", "title": "Are there any service contracts at the landlord level?"}}, "dependencies": {"serviceContracts": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any service contracts", "format": "data-url"}, "serviceContracts": {"enum": ["Yes"]}}}]}}}' where key = 'service_contracts';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'seller_environmental_information';
update task_template set form_schema ='{"type": "object", "required": ["warrantiesGuarantees"], "properties": {"warrantiesGuarantees": {"enum": ["", "Yes", "No"], "type": "string", "title": "Are there any current warranties or guarantees?"}}, "dependencies": {"warrantiesGuarantees": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any warranties or guarantees", "format": "data-url"}, "warrantiesGuarantees": {"enum": ["Yes"]}}}]}}}' where key = 'remaining_warranties_guarantees';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'records_and_correspondence_regarding_pending_or_threatened_litigation';
update task_template set form_schema ='{"type": "object", "required": ["propertyTaxReturn"], "properties": {"propertyTaxReturn": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have a Property Tax Return?"}}, "dependencies": {"propertyTaxReturn": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "propertyTaxReturn": {"enum": ["Yes"]}}}]}}}' where key = 'real_estate_tax_bills';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'purchase_and_sale_contract';
update task_template set form_schema ='{"type": "object", "required": ["propertyInsurance"], "properties": {"propertyInsurance": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have Property Insurance?"}}, "dependencies": {"propertyInsurance": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "propertyInsurance": {"enum": ["Yes"]}}}]}}}' where key = 'property_insurance';
update task_template set form_schema ='{"type": "object", "required": ["proofOfPropertyInsurance"], "properties": {"proofOfPropertyInsurance": {"enum": ["Yes", "No"], "type": "string", "title": "Do you want to upload the proof of property insurance?"}}, "dependencies": {"proofOfPropertyInsurance": {"oneOf": [{"required": ["llcName", "file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "llcName": {"type": "string", "title": "What is the name of the LLC being insured?", "description": "The LLC is the additional insured who will need to be insured at the file."}, "proofOfPropertyInsurance": {"enum": ["Yes"]}}}]}}}' where key = 'proof_of_property_insurance';
update task_template set form_schema ='{"type": "object", "required": ["proofOfFloodInsurance"], "properties": {"proofOfFloodInsurance": {"enum": ["Yes", "No"], "type": "string", "title": "Do you want to upload the proof of flood insurance?"}}, "dependencies": {"proofOfFloodInsurance": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "proofOfFloodInsurance": {"enum": ["Yes"]}}}]}}}' where key = 'proof_of_flood_insurance';
update task_template set form_schema ='{"type": "object", "required": ["previousTenantEstpels"], "properties": {"previousTenantEstpels": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have any Previous Tenant Estpels and SNDAs?"}}, "dependencies": {"previousTenantEstpels": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "previousTenantEstpels": {"enum": ["Yes"]}}}]}}}' where key = 'previous_tenant_estoppels_and_sndas';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'physical';
update task_template set form_schema ='{"type": "object", "required": ["phaseIReport"], "properties": {"phaseIReport": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have a Phase I report?"}}, "dependencies": {"phaseIReport": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "phaseIReport": {"enum": ["Yes"]}}}]}}}' where key = 'phase_i_report';
update task_template set form_schema ='{"type": "object", "required": ["phaseIIReport"], "properties": {"phaseIIReport": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have a Phase I report?"}}, "dependencies": {"phaseIIReport": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "phaseIIReport": {"enum": ["Yes"]}}}]}}}' where key = 'phase_ii_report';
update task_template set form_schema ='{"type": "object", "required": ["visitOn", "name", "phone", "email"], "properties": {"name": {"type": "string", "title": "Name"}, "email": {"type": "string", "title": "Email", "format": "email"}, "phone": {"type": "string", "title": "Phone", "format": "phone"}, "visitOn": {"type": "string", "title": "Keyway scheduled a site visit for", "format": "date"}}}' where key = 'phase_ii_environmental_assessment';
update task_template set form_schema ='{"type": "object", "required": ["personalProperty"], "properties": {"personalProperty": {"enum": ["", "Yes", "No"], "type": "string", "title": "Is there any personal property that would transfer ownership upon close?"}}, "dependencies": {"personalProperty": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload an inventory of the personal property", "format": "data-url"}, "personalProperty": {"enum": ["Yes"]}}}]}}}' where key = 'personal_property_inventory';
update task_template set form_schema =NULL where key = 'payment_and_invoicing_details_tutorial';
update task_template set form_schema ='{"type": "object", "required": ["parkingRevenue", "parkingExpenses", "parkingAgreement"], "properties": {"file": {"type": "string", "title": "Please upload any related parking agreements, revenue, or expenses", "format": "data-url"}, "parkingRevenue": {"enum": ["", "Yes", "No"], "type": "string", "title": "Is there any parking revenue?"}, "parkingExpenses": {"enum": ["", "Yes", "No"], "type": "string", "title": "Is there any parking expenses?"}, "parkingAgreement": {"enum": ["", "Yes", "No"], "type": "string", "title": "Is there any parking agreement?"}}}' where key = 'parking_revenue_expenses_and_management';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'operating_budgets';
update task_template set form_schema =NULL where key = 'new_point_of_contact';
update task_template set form_schema ='{"type": "object", "required": ["visitOn", "name", "phone", "email"], "properties": {"name": {"type": "string", "title": "Name"}, "email": {"type": "string", "title": "Email", "format": "email"}, "phone": {"type": "string", "title": "Phone", "format": "phone"}, "visitOn": {"type": "string", "title": "Keyway scheduled a site visit for", "format": "date"}}}' where key = 'new_physical_inspection';
update task_template set form_schema ='{"type": "object", "required": ["moldSurvey"], "properties": {"moldSurvey": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have a Mold Survey?"}}, "dependencies": {"moldSurvey": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "moldSurvey": {"enum": ["Yes"]}}}]}}}' where key = 'mold_survey';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'loss_run_report';
update task_template set form_schema ='{"type": "object", "required": ["liabilityInsurance"], "properties": {"liabilityInsurance": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have Liability Insurance?"}}, "dependencies": {"liabilityInsurance": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "liabilityInsurance": {"enum": ["Yes"]}}}]}}}' where key = 'liability_insurance';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'lease';
update task_template set form_schema ='{"type": "object", "required": ["constructionType", "enclosedParking", "openParking", "stories", "builtOn", "smokeDetector", "sprinklerCoverage", "emergencyLighting", "alarm", "wiringType", "security"], "properties": {"alarm": {"enum": ["", "CS", "Local"], "type": "string", "title": "Is the alarm CS or local?"}, "builtOn": {"type": "string", "title": "What year was it built? ", "format": "date"}, "stories": {"type": "number", "title": "How many stories is the asset?"}, "security": {"enum": ["", "Yes", "No"], "type": "string", "title": "Is there security?"}, "wiringType": {"type": "string", "title": "What is the wiring type?"}, "openParking": {"type": "number", "title": "How many square feet of open parking area is there?"}, "smokeDetector": {"enum": ["", "Battery", "Hardwired"], "type": "string", "title": "Is the smoke detector battery or hardwired?"}, "enclosedParking": {"type": "number", "title": "How many square feet of enclosed parking is there?"}, "wiringUpdatedOn": {"type": "string", "title": "What year was the wiring updated?", "format": "date"}, "constructionType": {"type": "string", "title": "What is the construction type?"}, "heatingUpdatedOn": {"type": "string", "title": "What year was the heating updated?", "format": "date"}, "emergencyLighting": {"enum": ["", "Yes", "No"], "type": "string", "title": "Is there emergency lighting?"}, "sprinklerCoverage": {"type": "number", "title": "What sprinkler coverage is there? (in percentage) "}}}' where key = 'insurance';
update task_template set form_schema ='{"type": "object", "required": ["hazardousMaterialsRecords"], "properties": {"hazardousMaterialsRecords": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have Hazardous Materials Records?"}}, "dependencies": {"hazardousMaterialsRecords": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "hazardousMaterialsRecords": {"enum": ["Yes"]}}}]}}}' where key = 'hazardous_materials_records';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'floor_plans';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'financial_statements';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'evidence_of_current_insurance';
update task_template set form_schema ='{"type": "object", "required": ["visitOn", "name", "phone", "email"], "properties": {"name": {"type": "string", "title": "Name"}, "email": {"type": "string", "title": "Email", "format": "email"}, "phone": {"type": "string", "title": "Phone", "format": "phone"}, "visitOn": {"type": "string", "title": "Keyway scheduled a site visit for", "format": "date"}}}' where key = 'environmental';
update task_template set form_schema ='{"type": "object", "required": ["rentRoll"], "properties": {"rentRoll": {"enum": ["", "Yes", "No"], "type": "string", "title": "Is there a rent roll?"}}, "dependencies": {"rentRoll": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload the tenant rent roll", "format": "data-url"}, "rentRoll": {"enum": ["Yes"]}}}]}}}' where key = 'current_property_rent_roll';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'certificates_of_occupancy';
update task_template set form_schema ='{"type": "object", "required": ["permits"], "properties": {"permits": {"enum": ["", "Yes", "No"], "type": "string", "title": "Are there any permits?"}}, "dependencies": {"permits": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any permits", "format": "data-url"}, "permits": {"enum": ["Yes"]}}}]}}}' where key = 'building_permits';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'building_area_calculations';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'as_builts';
update task_template set form_schema ='{"type": "object", "required": ["asbestosSurvey"], "properties": {"asbestosSurvey": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have an Asbestos Survey?"}}, "dependencies": {"asbestosSurvey": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "asbestosSurvey": {"enum": ["Yes"]}}}]}}}' where key = 'asbestos_survey';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any related document", "format": "data-url"}}}' where key = 'any_existing_seismic_reports';
update task_template set form_schema ='{"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "title": "Please upload any lease amendments or assignments", "format": "data-url"}}}' where key = 'additional_lease_documents';
update task_template set form_schema ='{"type": "object", "required": ["otherReports"], "properties": {"otherReports": {"enum": ["Yes", "No"], "type": "string", "title": "Do you have any other environmental reports?"}}, "dependencies": {"otherReports": {"oneOf": [{"required": ["file"], "properties": {"file": {"type": "string", "title": "Upload document", "format": "data-url"}, "otherReports": {"enum": ["Yes"]}}}]}}}' where key = 'physical_and_environmental';
