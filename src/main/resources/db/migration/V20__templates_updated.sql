----- TEMPLATE SCHEMA -----

update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'purchase_and_sale_contract';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'lease';
update public.task_template set form_schema = '{"type":"object","required":["parkingRevenue","parkingExpenses","parkingAgreement"],"properties":{"file":{"type":"string","title":"Please upload any related parking agreements, revenue, or expenses","format":"data-url"},"parkingRevenue":{"type":"string","title":"Is there any parking revenue?","enum":["","Yes","No"]},"parkingExpenses":{"type":"string","title":"Is there any parking expenses?","enum":["","Yes","No"]},"parkingAgreement":{"type":"string","title":"Is there any parking agreement?","enum":["","Yes","No"]}}}' where key = 'parking_revenue_expenses_and_management';
update public.task_template set form_schema = '{"type":"object","required":["serviceContracts"],"properties":{"serviceContracts":{"type":"string","title":"Are there any service contracts at the landlord level?","enum":["","Yes","No"]}},"dependencies":{"serviceContracts":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload any service contracts","format":"data-url"},"serviceContracts":{"enum":["Yes"]}}}]}}}' where key = 'service_contracts';
update public.task_template set form_schema = '{"type":"object","required":["personalProperty"],"properties":{"personalProperty":{"type":"string","title":"Is there any personal property that would transfer ownership upon close?","enum":["","Yes","No"]}},"dependencies":{"personalProperty":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload an inventory of the personal property","format":"data-url"},"personalProperty":{"enum":["Yes"]}}}]}}}' where key = 'personal_property_inventory';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'records_and_correspondence_regarding_pending_or_threatened_litigation';
update public.task_template set form_schema = '{"type":"object","required":["liens"],"properties":{"liens":{"type":"string","title":"Are there any outstanding liens?","enum":["","Yes","No"]}},"dependencies":{"liens":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload any documents relating to liens","format":"data-url"},"liens":{"enum":["Yes"]}}}]}}}' where key = 'third_parties_with_respect_to_liens';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload an inventory of the personal property","format":"data-url"}}}' where key = 'additional_lease_documents';
update public.task_template set form_schema = '{"type":"object","required":["sublease"],"properties":{"sublease":{"type":"string","title":"Is there a sublease?","enum":["","Yes","No"]}},"dependencies":{"sublease":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload any sublease documents","format":"data-url"},"sublease":{"enum":["Yes"]}}}]}}}' where key = 'sublease_documents';
update public.task_template set form_schema = '{"type":"object","title":"Tenant Correspondence","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any tenant correspondence","format":"data-url"}},"description":"Correspondence from the tenant related to missed payments, maintenance concerns, or anything else"}' where key = 'tenant_correspondence';
update public.task_template set form_schema = '{"type":"object","required":["estoppel"],"properties":{"estoppel":{"type":"string","title":"Is there a form estoppel attached to the lease?","enum":["","Yes","No"]}},"dependencies":{"estoppel":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload the form estoppel","format":"data-url"},"estoppel":{"enum":["Yes"]}}}]}}}' where key = 'tenant_estoppel';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'financial_statements';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'title';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'survey';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'operating_budgets';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'real_estate_tax_bills';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'utility_bills';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'tax_return';
update public.task_template set form_schema = '{"type":"object","required":["visitOn","name","phone","email"],"properties":{"name":{"type":"string","title":"Name"},"email":{"type":"string","title":"Email","format":"email"},"phone":{"type":"number","title":"Phone","format":"phone"},"visitOn":{"type":"string","title":"Keyway scheduled a site visit for","format":"date"}}}' where key = 'new_physical_inspection';
update public.task_template set form_schema = '{"type":"object","required":["visitOn","name","phone","email"],"properties":{"name":{"type":"string","title":"Name"},"email":{"type":"string","title":"Email","format":"email"},"phone":{"type":"number","title":"Phone","format":"phone"},"visitOn":{"type":"string","title":"Keyway scheduled a site visit for","format":"date"}}}' where key = 'environmental';
update public.task_template set form_schema = '{"type":"object","required":["visitOn","name","phone","email"],"properties":{"name":{"type":"string","title":"Name"},"email":{"type":"string","title":"Email","format":"email"},"phone":{"type":"number","title":"Phone","format":"phone"},"visitOn":{"type":"string","title":"Keyway scheduled a site visit for","format":"date"}}}' where key = 'phase_ii_environmental_assessment';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'physical';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'certificates_of_occupancy';
update public.task_template set form_schema = '{"type":"object","required":["rentRoll"],"properties":{"rentRoll":{"type":"string","title":"Is there a rent roll?","enum":["","Yes","No"]}},"dependencies":{"rentRoll":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload the tenant rent roll","format":"data-url"},"rentRoll":{"enum":["Yes"]}}}]}}}' where key = 'current_property_rent_roll';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'seller_environmental_information';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'as_builts';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'any_existing_seismic_reports';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'building_area_calculations';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'floor_plans';
update public.task_template set form_schema = '{"type":"object","required":["permits"],"properties":{"permits":{"type":"string","title":"Are there any permits?","enum":["","Yes","No"]}},"dependencies":{"permits":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload any permits","format":"data-url"},"permits":{"enum":["Yes"]}}}]}}}' where key = 'building_permits';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'loss_run_report';
update public.task_template set form_schema = '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please upload any related document","format":"data-url"}}}' where key = 'evidence_of_current_insurance';
update public.task_template set form_schema = '{"type":"object","required":["lotBlock","parcelNumber","constructedOn"],"properties":{"lotBlock":{"type":"string","title":"Lot & Block"},"parcelNumber":{"type":"number","title":"Tax Parcel Number"},"constructedOn":{"type":"string","title":"Date of Construction","format":"date"}}}' where key = 'zoning';
update public.task_template set form_schema = '{"type":"object","required":["warrantiesGuarantees"],"properties":{"warrantiesGuarantees":{"type":"string","title":"Are there any current warranties or guarantees?","enum":["","Yes","No"]}},"dependencies":{"warrantiesGuarantees":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload any warranties or guarantees","format":"data-url"},"warrantiesGuarantees":{"enum":["Yes"]}}}]}}}' where key = 'remaining_warranties_guarantees';
update public.task_template set form_schema = '{"type":"object","required":["constructionType","enclosedParking","openParking","stories","builtOn","smokeDetector","sprinklerCoverage","emergencyLighting","alarm","wiringType","security"],"properties":{"alarm":{"enum":["","CS","Local"],"type":"string","title":"Is the alarm CS or local?"},"builtOn":{"type":"string","title":"What year was it built? ","format":"date"},"stories":{"type":"number","title":"How many stories is the asset?"},"security":{"type":"string","title":"Is there security?","enum":["","Yes","No"]},"wiringType":{"type":"string","title":"What is the wiring type?"},"openParking":{"type":"number","title":"How many square feet of open parking area is there?"},"smokeDetector":{"enum":["","Battery","Hardwired"],"type":"string","title":"Is the smoke detector battery or hardwired?"},"enclosedParking":{"type":"number","title":"How many square feet of enclosed parking is there?"},"wiringUpdatedOn":{"type":"string","title":"What year was the wiring updated?","format":"date"},"constructionType":{"type":"string","title":"What is the construction type?"},"heatingUpdatedOn":{"type":"string","title":"What year was the heating updated?","format":"date"},"emergencyLighting":{"type":"string","title":"Is there emergency lighting?","enum":["","Yes","No"]},"sprinklerCoverage":{"type":"number","title":"What sprinkler coverage is there? (in percentage) "}}}' where key = 'insurance';

----- TASK DESCRIPTION -----
UPDATE public.task_template
SET task_description = 'Keyway to order a Phase II environmental inspection, if applicable'
WHERE key = 'phase_ii_environmental_assessment';

UPDATE public.task_template
SET task_description = 'Keyway to order a new physical inspection'
WHERE key = 'new_physical_inspection';

UPDATE public.task_template
SET task_description = 'Provide answers to the questions to inform Keyway''s insurance policy'
WHERE key = 'insurance';

UPDATE public.task_template
SET task_description = 'Lease agreement between Keyway and seller'
WHERE key = 'lease';

UPDATE public.task_template
SET task_description = 'Keyway to order a new Phase I environmental inspection'
WHERE key = 'environmental';

