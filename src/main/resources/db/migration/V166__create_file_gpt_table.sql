create table if not exists "file_gpt_token"(
    k_file_id         varchar unique not null,
    token             varchar unique not null,
    status            varchar not null
);

create table if not exists "gpt_questions_history"(
    k_file_id       varchar not null,
    question_type   varchar not null,
    question        varchar not null,
    answer          varchar not null,
    member_id       bigint,
    created_at      timestamp with time zone not null,

    constraint gpt_questions_history_fk_member foreign key (member_id) references "member"(id)
)