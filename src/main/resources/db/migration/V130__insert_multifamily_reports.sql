alter table "report_vendor_type" add constraint uq_vendor_by_report unique (type_key, vendor_key);

insert into "report_type"("key")
values
    ('INSURANCE_QUOTE'),
    ('REAL_ESTATE_PROPERTY_TAX_FORECAST'),
    ('PROPERTY_ONSITE_DUE_DILIGENCE'),
    ('PROPERTY_CONDITION_ASSESSMENT'),
    ('ALTA_SURVEY'),
    ('NOI_AUDIT'),
    ('PHYSICAL'),
    ('TERMITE_REPORT');

insert into "report_vendor"("key", name)
values
    ('PARTNERS', 'Partners'),
    ('INTEGRA_REALTY_RESOURCES', 'Integra Realty Resources'),
    ('PROVIDENCE_TITLE', 'Providence Title');

insert into "report_vendor_type"(type_key, vendor_key)
values
    ('PHASE_I', 'PARTNERS'),
    ('PHASE_II', 'PARTNERS'),
    ('SOIL_AND_GEOLOGIC_REPORT', 'PARTNERS'),
    ('PCR', 'PARTNERS'),
    ('TITLE', 'PROVIDENCE_TITLE'),
    ('ZONING', 'PARTNERS'),
    ('APPRAISAL', 'INTEGRA_REALTY_RESOURCES'),
    ('INSURANCE_QUOTE', 'OTHER'),
    ('REAL_ESTATE_PROPERTY_TAX_FORECAST', 'OTHER'),
    ('PROPERTY_ONSITE_DUE_DILIGENCE', 'OTHER'),
    ('PROPERTY_CONDITION_ASSESSMENT', 'OTHER'),
    ('ALTA_SURVEY', 'OTHER'),
    ('NOI_AUDIT', 'OTHER'),
    ('PHYSICAL', 'OTHER'),
    ('TERMITE_REPORT', 'OTHER')
    ;


