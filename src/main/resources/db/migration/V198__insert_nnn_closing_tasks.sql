INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
    (
         'nnn_legal',
         'upload_file',
         'buyer',
         'Legal Team Document validation',
         '',
         'nnn_legal',
         '{"type":"object","required":["approved"],"properties":{"approved":{"enum":["Yes","No"],"type":"string","title":"Approved by Legal Team?"},"text":{"type":"string","title":"Upload any related file","format":"data-url"}}}',
         null,
         'NA',
         27,
         false,
         false,
         'BUYER_TEAM'
    ),
    (
        'nnn_wiring',
        'upload_file',
        'buyer',
        'Wiring Instructions',
        '',
        'nnn_wiring',
        '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please Upload Instructions","format":"data-url"}, "comments":{"type":"string","title":"Comments","format":"textarea"}, "validated":{"enum":["Yes","No"],"type":"string","title":"Validated?"}}}',
        null,
        'NA',
        28,
        false,
        false,
        'BUYER_TEAM'
    ),
    (
        'nnn_verification',
        'upload_file',
        'buyer',
        'Settlement Statement',
        '',
        'nnn_settlement_statement',
        '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please Upload the file","format":"data-url"}, "validated":{"enum":["Yes","No"],"type":"string","title":"Validated?"}}}',
        null,
        'NA',
        29,
        false,
        false,
        'BUYER_TEAM'
    ),
    (
        'nnn_verification',
        'upload_file',
        'buyer',
        'Certificate Of Occupancy',
        '',
        'nnn_occupancy_certificate',
        '{"type":"object","required":["certificate"],"properties":{"certificate":{"type":"string","title":"Please Upload the Certificate","format":"data-url"}}}',
        null,
        'NA',
        30,
        false,
        false,
        'BUYER_TEAM'
    ),
    (
        'nnn_verification',
        'upload_file',
        'buyer',
        'Tenant`s Proof Of Insurance',
        '',
        'nnn_insurance_proof',
        '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please Upload the file","format":"data-url"}}}',
        null,
        'NA',
        31,
        false,
        false,
        'BUYER_TEAM'
     ),
    (
      'nnn_verification',
      'upload_file',
      'buyer',
      'Request Closing Binder From Outside',
      '',
      'nnn_closing_binder',
      '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please Upload the file","format":"data-url"}, "validated":{"enum":["Yes","No"],"type":"string","title":"Validated?"}}}',
      null,
      'NA',
      32,
      false,
      false,
      'BUYER_TEAM'
    ),
    (
    'nnn_welcome_letter',
    'upload_file',
    'buyer',
    'Welcome Letter',
    '',
    'nnn_welcome_letter',
    '{"type":"object","required":["file"],"properties":{"file":{"type":"string","title":"Please Upload the file","format":"data-url"}, "email":{"type":"string","title":"Please Upload the email","format":"data-url"}}}',
    null,
    'NA',
    33,
    false,
    false,
    'BUYER_TEAM'
    );

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('default', 'nnn_legal'),
    ('default', 'nnn_wiring'),
    ('default', 'nnn_settlement_statement'),
    ('default', 'nnn_occupancy_certificate'),
    ('default', 'nnn_insurance_proof'),
    ('default', 'nnn_closing_binder'),
    ('default', 'nnn_welcome_letter');