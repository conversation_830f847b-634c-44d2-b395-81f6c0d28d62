INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
    (
	    'multifamily_loi',
	    'form',
	    'buyer',
	    'Start Letter of Intent',
	    '',
	    'multifamily_start_loi',
	    '{"type":"object","required":["startLoi"],"properties":{"startLoi":{"enum":["","Yes","No"],"type":"string","title":"The first LOI was sent?"}}}',
	    null,
	    'LOW',
	    8,
	    false,
	    false,
	    'BUYER_TEAM'
	),
    (
	    'multifamily_loi',
	    'form',
	    'buyer',
	    'Execute Letter of Intent',
	    '',
	    'multifamily_execute_loi',
	    '{"type":"object","required":["executeLoi"],"properties":{"executeLoi":{"enum":["","Yes","No"],"type":"string","title":"Is the LOI closed and signed?"}}}',
	    null,
	    'LOW',
	    9,
	    false,
	    false,
	    'BUYER_TEAM'
	)	;

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('multifamily', 'multifamily_start_loi'),
    ('multifamily', 'multifamily_execute_loi');