
------ USER_TYPE -------
create table if not exists "user_type"
(
	id      serial  not null,
	key     varchar not null,
	name    varchar not null,

	constraint user_type_pk_id primary key (id),
	constraint user_type_uk_key unique (key)
);

------ USER -------
create table if not exists "user"
(
	id                  serial  not null,
	type_id             bigint  not null,
	google_uid          varchar not null,
	company_name        varchar not null,
	full_name           varchar not null,
	address             varchar not null,
	phone_country_code  varchar not null,
	phone_area_code     varchar not null,
	phone_number        varchar not null,
	email               varchar not null,
	updated_at          timestamp with time zone not null,
	created_at          timestamp with time zone not null,

	constraint user_pk_id primary key (id),
	constraint user_fk_user_type foreign key (type_id) references "user_type"(id),
	constraint user_uk_google_uid unique (google_uid),
	constraint user_uk_email unique (email)
);

------ PROPERTY -------
create table if not exists "property"
(
	id                      serial  not null,
	address                 varchar not null,
	parcel_id               varchar,
	year_built              integer,
	square_footage          numeric,
	acreage                 numeric,
	purchase_price          numeric,
	earnest_money_deposit   numeric,
	extension_deposit       numeric,
	updated_at              timestamp with time zone not null,
	created_at              timestamp with time zone not null,

	constraint property_pk_id primary key (id)
);

------ DEAL -------
create table if not exists "deal"
(
	id                          serial  not null,
	property_id                 bigint  not null,
	loi_executed_date           date    not null,
	contract_executed_date      date    not null,
	diligence_expiration_date   date    not null,
	closing_period_date         date    not null,
	initial_closing_date        date    not null,
	outside_closing_date        date    not null,
	updated_at                  timestamp with time zone not null,
	created_at                  timestamp with time zone not null,

	constraint deal_pk_id primary key (id),
	constraint deal_fk_property foreign key (property_id) references "property"(id)
);

------ USER_DEAL -------
create table if not exists "user_deal"
(
	id      serial not null,
	user_id bigint not null,
	deal_id bigint not null,

	constraint user_deal_pk_id primary key (id),
	constraint user_deal_fk_user foreign key (user_id) references "user"(id),
	constraint user_deal_fk_deal foreign key (deal_id) references "deal"(id)
);

------ CATEGORY -------
create table if not exists "category"
(
	id          serial  not null,
	key         varchar not null,
	name        varchar not null,
	description varchar not null,

	constraint category_pk_id primary key (id),
	constraint category_uk_key unique (key)
);

------ DEAL_CATEGORY -------
create table if not exists "deal_category"
(
	id          serial not null,
	category_id bigint not null,
	deal_id     bigint not null,

	constraint deal_category_pk_id primary key (id),
	constraint deal_category_fk_category foreign key (category_id) references "category"(id),
	constraint deal_category_fk_deal foreign key (deal_id) references "deal"(id)
);

------ TASK_TYPE -------
create table if not exists "task_type"
(
	id      serial  not null,
	key     varchar not null,
	name    varchar not null,

	constraint task_type_pk_id primary key (id),
	constraint task_type_uk_key unique (key)
);

------ TASK_STATUS -------
create table if not exists "task_status"
(
	id      serial  not null,
	key     varchar not null,
	name    varchar not null,

	constraint task_status_pk_id primary key (id),
	constraint task_status_uk_key unique (key)
);

------ TASK -------
create table if not exists "task"
(
    id                      serial  not null,
    type_id                 bigint  not null,
    assigned_user_deal_id   bigint  not null,
    deal_category_id        bigint  not null,
    status_id               bigint  not null,
    attached_form_data      json,
    due_date                date,
    updated_at              timestamp with time zone not null,
    created_at              timestamp with time zone not null,


    constraint task_pk_id primary key (id),
    constraint task_fk_task_type foreign key (type_id) references "task_type"(id),
    constraint task_fk_user_deal foreign key (assigned_user_deal_id) references "user_deal"(id),
    constraint task_fk_deal_category foreign key (deal_category_id) references "deal_category"(id),
    constraint task_fk_task_status foreign key (status_id) references "task_status"(id)
);

------ TASK_FILE -------
create table if not exists "task_file"
(
	id          serial  not null,
	name        varchar not null,
	url         varchar not null,
	task_id     bigint  not null,
	created_at  timestamp with time zone not null,

	constraint task_file_pk_id primary key (id),
	constraint task_file_fk_task foreign key (task_id) references "task"(id)
);
