ALTER TABLE member ADD COLUMN first_name varchar;
ALTER TABLE member ADD COLUMN last_name varchar;

UPDATE member set
    first_name = split_part(full_name, ' ', 1),
    last_name = replace(full_name, split_part(full_name, ' ', 1), '');

UPDATE member SET first_name = '<PERSON><PERSON><PERSON>', last_name = '<PERSON><PERSON><PERSON>' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Her<PERSON>', last_name = 'Ojeda' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Nadine', last_name = '<PERSON>' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Ezequiel', last_name = 'NA' WHERE email = '<EMAIL>';
UPDATE member SET first_name = '<PERSON><PERSON>', last_name = 'Mijac' WHERE email = '<EMAIL>';
UPDATE member SET first_name = '<PERSON>', last_name = '<PERSON><PERSON><PERSON>' WHERE email = '<PERSON><PERSON><PERSON><PERSON>@kslaw.com';
UPDATE member SET first_name = '<PERSON>', last_name = 'Klug' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Cathryn', last_name = 'Grishaber' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'David', last_name = 'Stone' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Nadine', last_name = 'Attorney' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Skylar', last_name = 'McClain' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Tristen', last_name = 'Pfeiffer' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Josefa', last_name = 'Mijac' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Mags', last_name = 'Sawicka' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Jacob', last_name = 'Lightman' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Jacob', last_name = 'Lightman' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Mags', last_name = 'Sawicka' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Mariana', last_name = 'Jones' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Sebastian', last_name = 'Wilner' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Eliza', last_name = 'Smith' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Tristen', last_name = 'Pfeifer' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Chris', last_name = 'Pisarski' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Adam', last_name = 'Letterman' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Clint', last_name = 'Herrema' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Ernest', last_name = 'Parson' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'AK Commercial', last_name = 'NA' WHERE email = '<EMAIL>';
UPDATE member SET first_name = 'Brooke', last_name = 'Bean' WHERE email = '<EMAIL>';

ALTER TABLE member ALTER COLUMN first_name SET NOT NULL;
ALTER TABLE member ALTER COLUMN last_name SET NOT NULL;
ALTER TABLE member ALTER COLUMN full_name DROP NOT NULL;



