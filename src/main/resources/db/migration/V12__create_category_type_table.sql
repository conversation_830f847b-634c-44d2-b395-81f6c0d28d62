------ CATEGORY_TYPE -------
create table if not exists "category_type"
(
    id      serial  not null,
    key     varchar not null,
    name    varchar not null,

    constraint category_type_pk_id primary key (id),
    constraint category_type_uk_key unique (key)
);

insert into category_type ("key", name)
values ('tasks_board', 'Tasks board'),
       ('back_and_forth', 'Back & forth');

------ CATEGORY -------
alter table category add column type_key varchar;

update category set "type_key" = 'back_and_forth' where "key" = 'purchase_and_sale_contract';
update category set "type_key" = 'back_and_forth' where "key" = 'lease';
update category set "type_key" = 'back_and_forth' where "key" = 'title';
update category set "type_key" = 'back_and_forth' where "key" = 'survey';

update category set "type_key" = 'tasks_board' where "key" = 'legal';
update category set "type_key" = 'tasks_board' where "key" = 'financial';
update category set "type_key" = 'tasks_board' where "key" = 'physical_and_environmental';
update category set "type_key" = 'tasks_board' where "key" = 'zoning';
update category set "type_key" = 'tasks_board' where "key" = 'building_documents';
update category set "type_key" = 'tasks_board' where "key" = 'insurance';

alter table category alter column "type_key" set not null;
alter table category add constraint category_fk_category_type foreign key ("type_key") references category_type("key");