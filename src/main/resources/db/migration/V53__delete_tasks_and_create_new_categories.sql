-- Remove tasks https://app.clickup.com/t/2n55mng
delete from deal_task_template where task_template_key in ('evidence_of_current_insurance',
    'seller_environmental_information',
    'current_property_rent_roll',
    'third_parties_with_respect_to_liens',
    'building_area_calculations',
    'operating_budgets',
    'zoning');

-- Rename, remove and create categories https://app.clickup.com/t/2n55ndu
update category set name = 'Business and Finances' where key = 'financial';
update category set name = 'Lease and Tenant' where key = 'lease';
update category set name = 'Physical and Environmental' where key = 'physical_and_environmental';

insert into category ("key", name, type_key, phase, description)
values ('property_taxes', 'Property Taxes', 'tasks_board', 'due_diligence', ''),
       ('key_documents', 'Key Documents', 'tasks_board', 'due_diligence', ''),
       ('permits_contracts_and_warranties', 'Permits, Contracts and Warranties', 'tasks_board', 'due_diligence', '');

update task_template set task_category = 'lease' where key = 'additional_lease_documents';
update task_template set task_category = 'permits_contracts_and_warranties' where key = 'parking_revenue_expenses_and_management';
update task_template set task_category = 'permits_contracts_and_warranties' where key = 'service_contracts';
update task_template set task_category = 'building_documents' where key = 'personal_property_inventory';
update task_template set task_category = 'lease' where key = 'tenant_correspondence';
update task_template set task_category = 'lease' where key = 'tenant_estoppel';
update task_template set task_category = 'lease' where key = 'sublease_documents';
update task_template set task_category = 'financial' where key = 'records_and_correspondence_regarding_pending_or_threatened_litigation';
update task_template set task_category = 'building_documents' where key = 'zoning';
update task_template set task_category = 'building_documents' where key = 'certificates_of_occupancy';
update task_template set task_category = 'key_documents' where key = 'survey';
update task_template set task_category = 'key_documents' where key = 'title';

-- create new tasks https://app.clickup.com/t/2n55tz3
insert into task_template (task_category, task_type, member_type_assignment, task_title, task_description, "key")
values ('lease', 'form_and_file', 'seller', 'Previous Tenant Estoppels and SNDAs', 'Provide any previous tenant estoppels or SNDAs', 'previous_tenant_estoppels_and_sndas'),
       ('building_documents', 'form_and_file', 'seller', 'Zoning Report', '', 'zoning_report'),
       ('physical_and_environmental', 'form_and_file', 'seller', 'Phase I', '', 'phase_i_report'),
       ('physical_and_environmental', 'form_and_file', 'seller', 'Phase II', '', 'phase_ii_report'),
       ('physical_and_environmental', 'form_and_file', 'seller', 'Asbestos Survey', '', 'asbestos_survey'),
       ('physical_and_environmental', 'form_and_file', 'seller', 'Mold Survey', '', 'mold_survey'),
       ('physical_and_environmental', 'form_and_file', 'seller', 'Hazardous Materials Records', '', 'hazardous_materials_records'),
       ('physical_and_environmental', 'form_and_file', 'seller', 'Soil & Geologic Report', '', 'soil_and_geologic_report'),
       ('physical_and_environmental', 'form_and_file', 'seller', 'Other Reports', '', 'other_reports'),
       ('insurance', 'form_and_file', 'seller', 'Liability Insurance', '', 'liability_insurance'),
       ('insurance', 'form_and_file', 'seller', 'Umbrella Insurance', '', 'umbrella_insurance'),
       ('insurance', 'form_and_file', 'seller', 'Property Insurance', '', 'property_insurance');

update task_template set form_schema = '{ "type": "object", "required": [ "previousTenantEstpels" ], "properties": { "previousTenantEstpels": { "type": "string", "title": "Do you have any Previous Tenant Estpels and SNDAs?", "enum": [ "Yes", "No" ] } }, "dependencies": { "previousTenantEstpels": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "previousTenantEstpels": { "enum": [ "Yes" ] } } } ] } } }' where key = 'previous_tenant_estoppels_and_sndas';
update task_template set form_schema = '{ "type": "object", "required": [ "zoningReport" ], "properties": { "zoningReport": { "type": "string", "title": "Do you have a zoning report", "enum": [ "Yes", "No" ] } }, "dependencies": { "zoningReport": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "zoningReport": { "enum": [ "Yes" ] } } } ] } } }' where key = 'zoning_report';
update task_template set form_schema = '{ "type": "object", "required": [ "phaseIReport" ], "properties": { "phaseIReport": { "type": "string", "title": "Do you have a Phase I report?", "enum": [ "Yes", "No" ] } }, "dependencies": { "phaseIReport": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "phaseIReport": { "enum": [ "Yes" ] } } } ] } } }' where key = 'phase_i_report';
update task_template set form_schema = '{ "type": "object", "required": [ "phaseIIReport" ], "properties": { "phaseIIReport": { "type": "string", "title": "Do you have a Phase I report?", "enum": [ "Yes", "No" ] } }, "dependencies": { "phaseIIReport": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "phaseIIReport": { "enum": [ "Yes" ] } } } ] } } }' where key = 'phase_ii_report';
update task_template set form_schema = '{ "type": "object", "required": [ "asbestosSurvey" ], "properties": { "asbestosSurvey": { "type": "string", "title": "Do you have an Asbestos Survey?", "enum": [ "Yes", "No" ] } }, "dependencies": { "asbestosSurvey": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "asbestosSurvey": { "enum": [ "Yes" ] } } } ] } } }' where key = 'asbestos_survey';
update task_template set form_schema = '{ "type": "object", "required": [ "moldSurvey" ], "properties": { "moldSurvey": { "type": "string", "title": "Do you have a Mold Survey?", "enum": [ "Yes", "No" ] } }, "dependencies": { "moldSurvey": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "moldSurvey": { "enum": [ "Yes" ] } } } ] } } }' where key = 'mold_survey';
update task_template set form_schema = '{ "type": "object", "required": [ "hazardousMaterialsRecords" ], "properties": { "hazardousMaterialsRecords": { "type": "string", "title": "Do you have Hazardous Materials Records?", "enum": [ "Yes", "No" ] } }, "dependencies": { "hazardousMaterialsRecords": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "hazardousMaterialsRecords": { "enum": [ "Yes" ] } } } ] } } }' where key = 'hazardous_materials_records';
update task_template set form_schema = '{ "type": "object", "required": [ "soilAndGeologicReport" ], "properties": { "soilAndGeologicReport": { "type": "string", "title": "Do you have a Soil and Geologic Report?", "enum": [ "Yes", "No" ] } }, "dependencies": { "soilAndGeologicReport": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "soilAndGeologicReport": { "enum": [ "Yes" ] } } } ] } } }' where key = 'soil_and_geologic_report';
update task_template set form_schema = '{ "type": "object", "required": [ "otherReports" ], "properties": { "otherReports": { "type": "string", "title": "Do you have any other reports?", "enum": [ "Yes", "No" ] } }, "dependencies": { "otherReports": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "otherReports": { "enum": [ "Yes" ] } } } ] } } }' where key = 'other_reports';
update task_template set form_schema = '{ "type": "object", "required": [ "liabilityInsurance" ], "properties": { "liabilityInsurance": { "type": "string", "title": "Do you have Liability Insurance?", "enum": [ "Yes", "No" ] } }, "dependencies": { "liabilityInsurance": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "liabilityInsurance": { "enum": [ "Yes" ] } } } ] } } }' where key = 'liability_insurance';
update task_template set form_schema = '{ "type": "object", "required": [ "umbrellaInsurance" ], "properties": { "umbrellaInsurance": { "type": "string", "title": "Do you have Umbrella Insurance?", "enum": [ "Yes", "No" ] } }, "dependencies": { "umbrellaInsurance": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "umbrellaInsurance": { "enum": [ "Yes" ] } } } ] } } }' where key = 'umbrella_insurance';
update task_template set form_schema = '{ "type": "object", "required": [ "propertyInsurance" ], "properties": { "propertyInsurance": { "type": "string", "title": "Do you have Property Insurance?", "enum": [ "Yes", "No" ] } }, "dependencies": { "propertyInsurance": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "propertyInsurance": { "enum": [ "Yes" ] } } } ] } } }' where key = 'property_insurance';

insert into deal_task_template (deal_template_key, task_template_key)
values ('default', 'previous_tenant_estoppels_and_sndas'),
       ('default', 'zoning_report'),
       ('default', 'phase_i_report'),
       ('default', 'phase_ii_report'),
       ('default', 'asbestos_survey'),
       ('default', 'mold_survey'),
       ('default', 'hazardous_materials_records'),
       ('default', 'soil_and_geologic_report'),
       ('default', 'other_reports'),
       ('default', 'liability_insurance'),
       ('default', 'umbrella_insurance'),
       ('default', 'property_insurance');

update task_template set task_category = 'property_taxes',
                         task_title = 'Property Tax Return',
                         task_description = 'Provide the last 2 years of property tax returns',
                         form_schema = '{ "type": "object", "required": [ "propertyTaxReturn" ], "properties": { "propertyTaxReturn": { "type": "string", "title": "Do you have a Property Tax Return?", "enum": [ "Yes", "No" ] } }, "dependencies": { "propertyTaxReturn": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "propertyTaxReturn": { "enum": [ "Yes" ] } } } ] } } }'
where key = 'real_estate_tax_bills';

update task_template set task_description = 'Provide the list of property that would transfer when the property is sold' where key = 'personal_property_inventory';
update task_template set task_description = 'Provide the last 2 years of utility bills (electricity, water, sewer, etc)' where key = 'utility_bills';
update task_template set task_description = 'Provide 3 years of income statements, balance sheets, cash flows and budgets vs. actuals' where key = 'financial_statements';
update task_template set task_description = 'Provide last 3 years of loss run reports from your insurance provider' where key = 'loss_run_report';
update task_template set task_description = 'Provide the existing title policy and any related ' where key = 'title';
update task_template set task_description = 'Provide any lease amendments or assignments' where key = 'additional_lease_documents';
update task_template set task_description = 'This is where Keyway will upload the first version of the lease' where key = 'lease';
update task_template set task_description = 'Any communications between you and the tenant related to missed payments, maintenance concerns or anything else ' where key = 'tenant_correspondence';
update task_template set task_description = 'Provide the tenant estoppel as a part of the current lease agreement' where key = 'tenant_estoppel';
update task_template set task_description = 'Provide any warranties/guarantees for property items such as roof facade, foundation, parking lot, etc.' where key = 'remaining_warranties_guarantees';
