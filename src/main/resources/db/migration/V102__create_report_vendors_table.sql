create table if not exists "report_vendor"
(
    id      serial not null,
    key     varchar not null,
    name    varchar not null,

    constraint report_vendor_pk_id primary key (id),
    constraint report_vendor_uk_key unique (key)
);

create table if not exists "report_type"
(
    id      serial not null,
    key     varchar not null,

    constraint report_type_pk_id primary key (id),
    constraint report_type_uk_key unique (key)
);

create table if not exists "report_vendor_type"
(
    id              serial not null,
    type_key        varchar not null,
    vendor_key      varchar not null,

    constraint report_vendor_type_pk_id primary key (id),
    constraint report_vendor_type_type_key foreign key (type_key) references "report_type"(key),
    constraint report_vendor_type_vendor_key foreign key (vendor_key) references "report_vendor"(key)
);

alter table deal_report add constraint deal_report_fk_report_type foreign key (type) references report_type(key);
alter table deal_report add constraint deal_report_fk_report_vendor foreign key (vendor) references report_vendor(key);

insert into "report_vendor"("key", name)
values
    ('CRETELLIGENT', 'Cretelligent'),
    ('EBI', 'EBI'),
    ('FIRST_AMERICAN', 'First American'),
    ('CHICAGO_TITLE', 'Chicago Title'),
    ('BOWERY_VALUATION', 'Bowery Valuation'),
    ('OTHER', 'Other');

insert into "report_type"("key")
values
    ('SURVEY'),
    ('ZONING'),
    ('TITLE'),
    ('PHASE_I'),
    ('PHASE_II'),
    ('APPRAISAL'),
    ('PCR'),
    ('SEISMIC_REPORT'),
    ('SOIL_AND_GEOLOGIC_REPORT'),
    ('MOLD_SURVEY'),
    ('HAZARDOUS_MATERIALS_RECORDS'),
    ('ASBESTOS_SURVEY'),
    ('OTHER');

insert into "report_vendor_type"(type_key, vendor_key)
values
    ('SURVEY', 'CRETELLIGENT'),
    ('SURVEY', 'EBI'),
    ('SURVEY', 'OTHER'),
    ('ZONING', 'CRETELLIGENT'),
    ('ZONING', 'EBI'),
    ('ZONING', 'OTHER'),
    ('TITLE', 'FIRST_AMERICAN'),
    ('TITLE', 'CHICAGO_TITLE'),
    ('TITLE', 'OTHER'),
    ('PHASE_I', 'CRETELLIGENT'),
    ('PHASE_I', 'EBI'),
    ('PHASE_I', 'OTHER'),
    ('PHASE_II', 'CRETELLIGENT'),
    ('PHASE_II', 'EBI'),
    ('PHASE_II', 'OTHER'),
    ('APPRAISAL', 'BOWERY_VALUATION'),
    ('APPRAISAL', 'OTHER'),
    ('PCR', 'CRETELLIGENT'),
    ('PCR', 'EBI'),
    ('PCR', 'OTHER'),
    ('SEISMIC_REPORT', 'CRETELLIGENT'),
    ('SEISMIC_REPORT', 'EBI'),
    ('SEISMIC_REPORT', 'OTHER'),
    ('SOIL_AND_GEOLOGIC_REPORT', 'CRETELLIGENT'),
    ('SOIL_AND_GEOLOGIC_REPORT', 'EBI'),
    ('SOIL_AND_GEOLOGIC_REPORT', 'OTHER'),
    ('MOLD_SURVEY', 'CRETELLIGENT'),
    ('MOLD_SURVEY', 'EBI'),
    ('MOLD_SURVEY', 'OTHER'),
    ('HAZARDOUS_MATERIALS_RECORDS', 'CRETELLIGENT'),
    ('HAZARDOUS_MATERIALS_RECORDS', 'EBI'),
    ('HAZARDOUS_MATERIALS_RECORDS', 'OTHER'),
    ('ASBESTOS_SURVEY', 'CRETELLIGENT'),
    ('ASBESTOS_SURVEY', 'EBI'),
    ('ASBESTOS_SURVEY', 'OTHER'),
    ('OTHER', 'CRETELLIGENT'),
    ('OTHER', 'CHICAGO_TITLE'),
    ('OTHER', 'FIRST_AMERICAN'),
    ('OTHER', 'EBI'),
    ('OTHER', 'BOWERY_VALUATION'),
    ('OTHER', 'OTHER');
