
------ TASK_TEMPLATE -------
alter table "deal_template" rename to "task_template";
alter table "task_template" rename constraint deal_template_pk_id to task_template_pk_id;
alter table "task_template" rename constraint deal_template_fk_category to task_template_fk_category;
alter table "task_template" rename constraint deal_template_fk_task_type to task_template_fk_task_type;
alter table "task_template" rename constraint deal_template_fk_member_type to task_template_fk_member_type;
alter table "task_template" add column "key" varchar;

update public.task_template set key = 'purchase_and_sale_contract' where id = 1;
update public.task_template set key = 'lease' where id = 2;
update public.task_template set key = 'records_and_correspondence_regarding_pending_or_threatened_litigation' where id = 3;
update public.task_template set key = 'third_parties_with_respect_to_liens' where id = 4;
update public.task_template set key = 'parking_revenue_expenses_and_management' where id = 5;
update public.task_template set key = 'service_contracts' where id = 6;
update public.task_template set key = 'personal_property_inventory' where id = 7;
update public.task_template set key = 'additional_lease_documents' where id = 8;
update public.task_template set key = 'sublease_documents' where id = 9;
update public.task_template set key = 'tenant_correspondence' where id = 10;
update public.task_template set key = 'tenant_estoppel' where id = 11;
update public.task_template set key = 'financial_statements' where id = 12;
update public.task_template set key = 'operating_budgets' where id = 13;
update public.task_template set key = 'real_estate_tax_bills' where id = 14;
update public.task_template set key = 'utility_bills' where id = 15;
update public.task_template set key = 'tax_return' where id = 16;
update public.task_template set key = 'current_property_rent_roll' where id = 17;
update public.task_template set key = 'title' where id = 18;
update public.task_template set key = 'survey' where id = 19;
update public.task_template set key = 'new_physical_inspection' where id = 20;
update public.task_template set key = 'environmental' where id = 21;
update public.task_template set key = 'phase_ii_environmental_assessment' where id = 22;
update public.task_template set key = 'physical' where id = 23;
update public.task_template set key = 'seller_environmental_information' where id = 24;
update public.task_template set key = 'zoning' where id = 25;
update public.task_template set key = 'certificates_of_occupancy' where id = 26;
update public.task_template set key = 'as_builts' where id = 27;
update public.task_template set key = 'any_existing_seismic_reports' where id = 28;
update public.task_template set key = 'building_area_calculations' where id = 29;
update public.task_template set key = 'floor_plans' where id = 30;
update public.task_template set key = 'remaining_warranties_guarantees' where id = 31;
update public.task_template set key = 'building_permits' where id = 32;
update public.task_template set key = 'loss_run_report' where id = 33;
update public.task_template set key = 'evidence_of_current_insurance' where id = 34;
update public.task_template set key = 'insurance' where id = 35;

alter table "task_template" alter column "key" set not null;
alter table "task_template" add constraint task_template_uk_key unique ("key");

------ DEAL_TEMPLATE -------
create table if not exists "deal_template"
(
    id          serial  not null,
    key         varchar not null,
    name        varchar not null,
    description varchar not null,

    constraint deal_template_pk_id primary key (id),
    constraint deal_template_uk_key unique (key)
);

insert into deal_template ("key", name, description)
values ('default', 'Default', 'Default template');

------ DEAL_TASK_TEMPLATE -------
create table if not exists "deal_task_template"
(
    id                  serial not null,
    deal_template_key   varchar not null,
    task_template_key   varchar not null,

    constraint deal_task_template_pk_id primary key (id),
    constraint deal_task_template_fk_deal_template foreign key (deal_template_key) references deal_template(key),
    constraint deal_task_template_fk_task_template foreign key (task_template_key) references task_template(key)
);

insert into deal_task_template (deal_template_key, task_template_key)
values
('default', 'purchase_and_sale_contract'),
('default', 'lease'),
('default', 'records_and_correspondence_regarding_pending_or_threatened_litigation'),
('default', 'third_parties_with_respect_to_liens'),
('default', 'parking_revenue_expenses_and_management'),
('default', 'service_contracts'),
('default', 'personal_property_inventory'),
('default', 'additional_lease_documents'),
('default', 'sublease_documents'),
('default', 'tenant_correspondence'),
('default', 'tenant_estoppel'),
('default', 'financial_statements'),
('default', 'operating_budgets'),
('default', 'real_estate_tax_bills'),
('default', 'utility_bills'),
('default', 'tax_return'),
('default', 'current_property_rent_roll'),
('default', 'title'),
('default', 'survey'),
('default', 'new_physical_inspection'),
('default', 'environmental'),
('default', 'phase_ii_environmental_assessment'),
('default', 'physical'),
('default', 'seller_environmental_information'),
('default', 'zoning'),
('default', 'certificates_of_occupancy'),
('default', 'as_builts'),
('default', 'any_existing_seismic_reports'),
('default', 'building_area_calculations'),
('default', 'floor_plans'),
('default', 'remaining_warranties_guarantees'),
('default', 'building_permits'),
('default', 'loss_run_report'),
('default', 'evidence_of_current_insurance'),
('default', 'insurance');

------ DEAL -------
alter table deal add constraint deal_uk_property_id unique (property_id);