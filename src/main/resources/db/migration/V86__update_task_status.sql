UPDATE task ta
SET assigned_member_id = buyer_of_deal.id
FROM deal_category dc INNER JOIN deal de
	ON dc.deal_id = de.id
INNER JOIN 
	(SELECT deal_member.deal_id, member.id, member.type_key from deal_member
	INNER JOIN member ON deal_member.member_id = member.id
		WHERE member.type_key = 'buyer') as buyer_of_deal
ON buyer_of_deal.deal_id = de.id
WHERE ta.status_key = 'in_review'
AND ta.type_key <> 'document_back_and_forth'
AND ta.assigned_member_id IN (SELECT me.id FROM member me WHERE me.type_key = 'seller')
AND dc.id = ta.deal_category_id;

UPDATE task
SET status_key ='in_review'
WHERE type_key ='document_back_and_forth'
AND status_key ='to_do'
AND attached_form_data IS NOT NULL;