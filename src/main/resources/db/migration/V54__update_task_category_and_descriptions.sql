update task_template set task_category = 'building_documents' where key = 'utility_bills';
update task_template set task_category = 'permits_contracts_and_warranties' where key = 'building_permits';
update task_template set task_category = 'permits_contracts_and_warranties' where key = 'remaining_warranties_guarantees';
update task_template set task_title = 'Seismic Report', task_category = 'physical_and_environmental' where key = 'any_existing_seismic_reports';
update task_template set task_title = 'Sublease' where key = 'sublease_documents';
update task_template set task_title = 'Purchase and Sale Contract', task_description = '' where key = 'purchase_and_sale_contract';

update task_template set task_description = '' where key = 'as_builts';
update task_template set task_description = '' where key = 'certificates_of_occupancy';
update task_template set task_description = '' where key = 'floor_plans';
update task_template set task_description = '' where key = 'records_and_correspondence_regarding_pending_or_threatened_litigation';
update task_template set task_description = '' where key = 'sublease_documents';
update task_template set task_description = '' where key = 'parking_revenue_expenses_and_management';
update task_template set task_description = '' where key = 'physical';
update task_template set task_description = '' where key = 'any_existing_seismic_reports';

update category set name = 'Lease' where key = 'lease';

insert into category ("key", name, type_key, phase, description)
values ('lease_documents', 'Lease documents', 'tasks_board', 'due_diligence', '');

update task_template set task_category = 'lease_documents' where key = 'additional_lease_documents';
update task_template set task_category = 'lease_documents' where key = 'previous_tenant_estoppels_and_sndas';
update task_template set task_category = 'lease_documents' where key = 'sublease_documents';
update task_template set task_category = 'lease_documents' where key = 'tenant_correspondence';
update task_template set task_category = 'lease_documents' where key = 'tenant_estoppel';
update task_template set task_category = 'title' where key = 'title';
update task_template set task_category = 'survey' where key = 'survey';