alter table file_gpt_token add column deal_id bigint;
alter table file_gpt_token add constraint file_gpt_token_fk_deal foreign key (deal_id) references deal(id);

update file_gpt_token fgt
set deal_id = (select id from deal where om_file_id = fgt.k_file_id)
where file_type = 'OM';

update file_gpt_token fgt
set deal_id = (
        select dr.deal_id from deal_report dr
            join deal_report_file drf on dr.id = drf.report_id
            join file f on drf.file_id = f.id
        where f.k_file_id = fgt.k_file_id
        limit 1
    )
where file_type = 'REPORT';

update file_gpt_token fgt
set deal_id = (
        select deal_id from loi
            join loi_file lf on loi.id = lf.loi_id
        where lf.k_file_id = fgt.k_file_id
        limit 1
    )
where file_type = 'LOI';

update file_gpt_token fgt
set deal_id = (
        select deal_id from task
            join deal_category dc on task.deal_category_id = dc.id
            join task_file tf on task.id = tf.task_id
        where tf.k_file_id = fgt.k_file_id
    )
where file_type = 'TASK';
