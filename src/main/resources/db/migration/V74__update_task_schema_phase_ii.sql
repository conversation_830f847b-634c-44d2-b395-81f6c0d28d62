update task set form_schema = '{ "type": "object", "required": [ "phaseIiReport" ], "properties": { "phaseIiReport": { "type": "string", "title": "Do you have a Phase II report?", "enum": [ "Yes", "No" ] } }, "dependencies": { "phaseIiReport": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "phaseIiReport": { "enum": [ "Yes" ] } } } ] } } }' where template_key = 'phase_ii_report';
