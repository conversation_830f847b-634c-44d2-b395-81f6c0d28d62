create table if not exists "deal_finding"(
    id              bigint not null,
    deal_id         bigint not null,
    entity          varchar not null,
    entity_type     varchar not null,
    entity_id       varchar not null,
    message         varchar not null,
    status          varchar not null,
    created_at      timestamp with time zone not null,

    constraint deal_findings_pk_id primary key (id),
    constraint deal_findings_fk_deal foreign key (deal_id) references deal(id)
);

create sequence "seq_deal_finding";