update task_template
	    set form_schema= '{"type":"object","required":["acquisitionsTeamReviewsUnderwriting"],"properties":{"acquisitionsTeamReviewsUnderwriting":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}'
	    where key = 'multifamily_acquisitions_team_reviews_underwriting';

update task_template
	    set form_schema= '{"type":"object","required":["startLoi"],"properties":{"startLoi":{"enum":["Yes","No"],"type":"string","title":"The first LOI was sent?"}}}'
	    where key = 'multifamily_start_loi';

update task_template
	    set form_schema= '{"type":"object","required":["executeLoi"],"properties":{"executeLoi":{"enum":["Yes","No"],"type":"string","title":"Is the LOI closed and signed?"}}}'
	    where key = 'multifamily_execute_loi';

update task_template
	    set form_schema= '{"type":"object","required":["startPsa"],"properties":{"startPsa":{"enum":["Yes","No"],"type":"string","title":"The first PSA was sent?"}}}'
	    where key = 'multifamily_start_psa';

update task_template
	    set form_schema= '{"type":"object","required":["executePsa"],"properties":{"executePsa":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}'
	    where key = 'multifamily_execute_psa';

update task_template
	    set form_schema= '{"type":"object","required":["moneyReceivedByTitle"],"properties":{"moneyReceivedByTitle":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}'
	    where key = 'multifamily_money_received_by_title';

update task_template
	    set task_type= 'upload_file'
	    where key = 'multifamily_begin_deal_structuring_checklist';

update task_template
	    set form_schema= '{"type":"object","required":["sendOutCalendarReminders"],"properties":{"sendOutCalendarReminders":{"enum":["Yes","No"],"type":"string","title":"Calendar reminders sent?"}}}'
	    where key = 'multifamily_send_out_calendar_reminders';

update task_template
	    set form_schema= '{"type":"object","required":["scheduleKickoffCall"],"properties":{"scheduleKickoffCall":{"enum":["Yes","No"],"type":"string","title":"Is the kickoff meeting already scheduled?"}}}'
	    where key = 'multifamily_schedule_kickoff_call';

update task_template
	    set form_schema= '{"type":"object","required":["scheduleWeeklyMeetings"],"properties":{"scheduleWeeklyMeetings":{"enum":["Yes","No"],"type":"string","title":"Is the weekly meeting already scheduled?"}}}'
	    where key = 'multifamily_schedule_weekly_meetings';

update task_template
	    set form_schema= '{"type":"object","required":["signOff"],"properties":{"executeCommitmentLetter":{"type":"string","title":"Please Upload Execute Commitment Letter","format":"data-url"},"signOff":{"enum":["Yes","No"],"type":"string","title":"Sign Off? "}}}',
	    task_type = 'form'
	    where key = 'multifamily_execute_commitment_letter';

update task_template
	    set form_schema= '{"type":"object","required":["negotiateLoanTerms"],"properties":{"negotiateLoanTerms":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}'
	    where key = 'multifamily_negotiate_loan_terms';

update task_template
	    set form_schema= '{"type":"object","required":["loanApp"],"properties":{"loanApp":{"enum":["Yes","No"],"type":"string","title":"Loan App?"}},"dependencies":{"loanApp":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload the Loan App","format":"data-url"},"loanApp":{"enum":["Yes"]}}}]}}}'
	    where key = 'multifamily_submit_loan_app';

update task_template
	    set form_schema= '{"type":"object","required":["survey"],"properties":{"survey":{"type":"string","title":"Please upload the Survey","format":"data-url"},"newSurvey":{"enum":["Yes","No"],"type":"string","title":"Have you already requested the new survey?"}}}'
	    where key = 'multifamily_seller_survey';

update task_template
	    set form_schema= '{"type":"object","required":["newSurvey"],"properties":{"newSurvey":{"enum":["Yes","No"],"type":"string","title":"Is the new Survey already reviewed?"}}}'
	    where key = 'multifamily_updated_survey';

update task_template
	    set task_type= 'upload_file'
	    where key = 'multifamily_prior_three_years_financial_statements';

update task_template
	    set form_schema= '{"type":"object","required":["mortgageLoanDocuments"],"properties":{"mortgageLoanDocuments":{"enum":["Yes","No"],"type":"string","title":"Loan assumption?"}},"dependencies":{"mortgageLoanDocuments":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload the Mortgage Loan Documents","format":"data-url"},"mortgageLoanDocuments":{"enum":["Yes"]}}}]}}}'
	    where key = 'multifamily_mortgage_loan_documents';

update task_template
	    set form_schema= '{"type":"object","required":["propertyTaxAppealRecords"],"properties":{"propertyTaxAppealRecords":{"enum":["Yes","No"],"type":"string","title":"Property Tax Appeal Records?"}},"dependencies":{"propertyTaxAppealRecords":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload Property Tax Appeal Records","format":"data-url"},"propertyTaxAppealRecords":{"enum":["Yes"]}}}]}}}'
	    where key = 'multifamily_property_tax_appeal_records';

update task_template
	    set form_schema= '{"type":"object","required":["coordinateItInspection"],"properties":{"coordinateItInspection":{"enum":["Yes","No"],"type":"string","title":"Have you already requested IT inspection?"}},"dependencies":{"coordinateItInspection":{"oneOf":[{"required":["file"],"properties":{"file":{"type":"string","title":"Please upload IT inspection of all computers/equipment","format":"data-url"},"coordinateItInspection":{"enum":["Yes"]}}}]}}}'
	    where key = 'multifamily_coordinate_it_inspection';

update task_template
	    set form_schema= '{"type":"object","properties":{"executeCommitmentLetter":{"type":"string","title":"Please Upload building permits","format":"data-url"},"reviewed":{"enum":["Yes","No"],"type":"string","title":"Is it already reviewed?"}}}'
	    where key = 'multifamily_check_building_permits';

update task_template
	    set form_schema= '{"type":"object","required":["propertyInsuranceQuote"],"properties":{"propertyInsuranceQuote":{"enum":["Yes","No"],"type":"string","title":"Have you already requested Property Insurance Quote?"},"reviewed":{"enum":["Yes","No"],"type":"string","title":"Is it already reviewed?"}}}',
	    task_type = 'form'
	    where key = 'multifamily_property_insurance_quote';

update task_template
	    set form_schema= '{"type":"object","required":["propertyTaxForecast"],"properties":{"propertyTaxForecast":{"enum":["Yes","No"],"type":"string","title":"Have you already requested Real Estate Property Tax Forecast?"},"reviewed":{"enum":["Yes","No"],"type":"string","title":"Is it already reviewed?"}}}',
	    task_type = 'form'
	    where key = 'multifamily_property_tax_forecast';

update task_template
	    set form_schema= '{"type":"object","required":["dueDiligenceEnds"],"properties":{"dueDiligenceEnds":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}'
	    where key = 'multifamily_due_diligence_ends';

update task_template
	    set form_schema= '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}'
	    where key = 'multifamily_equity_fully_committed';

update task_template
	    set form_schema= '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}'
	    where key = 'multifamily_equity_fully_called';

update task_template
	    set form_schema= '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}'
	    where key = 'multifamily_contract_closing_date';

update task_template
	    set form_schema= '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}'
	    where key = 'multifamily_closing_extension';

update task_template
	    set form_schema= '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    task_type = 'form'
	    where key = 'multifamily_website_branding';

update task_template
	    set form_schema= '{"type":"object","required":["signOff"],"properties":{"signOff":{"enum":["Yes","No"],"type":"string","title":"Sign Off?"}}}',
	    task_type = 'form'
	    where key = 'multifamily_social_media_posts';

