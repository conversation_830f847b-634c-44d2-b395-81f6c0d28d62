update task_template set task_type = 'form_and_file', form_schema = '{ "type": "object", "required": [ "additionalLeaseDocuments" ], "properties": { "additionalLeaseDocuments": { "enum": [ "", "Yes", "No" ], "type": "string", "title": "Do you have any lease amendments or assignments?" } }, "dependencies": { "additionalLeaseDocuments": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Please upload any lease amendments or assignments", "format": "data-url" }, "additionalLeaseDocuments": { "enum": [ "Yes" ] } } } ] } } }' where key = 'additional_lease_documents';
