update finding_questions
set query = 'Please provide a JSON
Given the following deal information:
{
    "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
    "offer_price": $_deal.offerPrice_,
    "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
    "tenant_name": "$_deal.tenantName_",
    "closing_date": "$_deal.initialClosingDate_",
    "due_diligence_expiration_date": "$_deal.diligenceExpirationDate_"
}


Respond indicating whether there is an error in the document information and, if so, whether the purchase price match. Your response should follow this format:

{
  "property_address": "[the deal information address]",
  "psa_property_address": "[the Real Property address in the Purchase and Sale Agreement document]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "property_address": "102 28th Street, Fair Lawn, NJ, 07410",
  "psa_property_address": "102 28th Street, Fair Lawn, NJ, 07410",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the addresses don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}'
where key = 'PSA_ADDRESS';

update finding_questions
set query = 'Please provide a JSON
Given the following deal information:
{
  "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
  "offer_price": $_deal.offerPrice_,
  "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
  "tenant_name": "$_deal.tenantName_",
  "closing_date": "$_deal.initialClosingDate_",
  "due_diligence_expiration_date": "$_deal.diligenceExpirationDate_"
}


Respond indicating whether there is an error in the document information and, if so, whether the purchase price match. Your response should follow this format:

{
  "offer_price": "[the deal information offer price]",
  "psa_purchase_price": "[purchase price for the real property in the Purchase and Sale Agreement document, an example could be $1,700,000. Please format price without decimals]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "offer_price": 343434,
  "psa_purchase_price": 343434,
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the prices don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}',
    key = 'PSA_OFFER_PRICE'
where key = 'LEASE_OFFER_PRICE';

update finding_questions
set query = 'Please provide a JSON
Given the following deal information:
{
  "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
  "offer_price": $_deal.offerPrice_,
  "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
  "tenant_name": "$_deal.tenantName_",
  "closing_date": "$_deal.initialClosingDate_",
  "due_diligence_expiration_date": "$_deal.diligenceExpirationDate_"
}



Respond indicating whether there is an error in the document information and, if so, whether the initial deposit price match. Your response should follow this format:

{
  "earnest_money_deposit": "[the deal information earnest money deposit]",
  "psa_earnest_money_deposit": "[The initial deposit price in the Purchase and Sale Agreement (PSA) document]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "earnest_money_deposit": 355555,
  "psa_earnest_money_deposit": 355555,
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the intial deposit prices don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}',
    key = 'PSA_EARNEST_MONEY_DEPOSIT'
where key = 'LEASE_EARNEST_MONEY_DEPOSIT';

update finding_questions
set query = 'Please provide a JSON
Given the following deal information:
{
  "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
  "offer_price": $_deal.offerPrice_,
  "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
  "tenant_name": "$_deal.tenantName_",
  "closing_date": "$_deal.initialClosingDate_",
  "due_diligence_expiration_date": "$_deal.diligenceExpirationDate_"
}


Before responding check if the Due Diligence Expiration Date in the Purchase and Sale Agreement document is invalid or is missed respond:
{
  "error_found": false,
  "explanation": "The PSA Due Diligence Expiration can''t be found properly because [replace with the error explanation]"
}

Respond indicating whether there is an error in the document information and, if so, whether the diligence expiration date refere to the same date. Your response should follow this format:

{
  "due_diligence_expiration_date": "[due_diligence_expiration_date from deal information json]",
  "psa_due_diligence_expiration_date": "[Due Diligence Expiration Date on the Purchase and Sale Agreement document (PSA)]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "due_diligence_expiration_date": "2007-12-03",
  "psa_due_diligence_expiration_date": "2007-12-03",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the dates don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}',
    key = 'PSA_DILIGENCE_EXPIRATION_DATE'
where key = 'LEASE_DILIGENCE_EXPIRATION_DATE';

update finding_questions
set query = 'Please provide a JSON
Given the following deal information:
{
  "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
  "offer_price": $_deal.offerPrice_,
  "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
  "tenant_name": "$_deal.tenantName_",
  "closing_date": "$_deal.initialClosingDate_",
  "due_diligence_expiration_date": "$_deal.diligenceExpirationDate_"
}


Before responding check if the Closing Date in the Purchase and Sale Agreement document is invalid or is missed respond:
{
  "error_found": false,
  "explanation": "The PSA Closing Date can''t be found properly because [replace with the error explanation]"
}

Respond indicating whether there is an error in the document information and, if so, whether the closing date refere to the same date. Your response should follow this format:

{
  "closing_date": "[closing_date from deal information json]",
  "psa_closing_date": "[Closing date on the Purchase and Sale Agreement document (PSA)]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "closing_date": "2007-12-10",
  "psa_closing_date": "2007-12-10",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the dates don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}',
    key = 'PSA_CLOSING_DATE'
where key = 'LEASE_CLOSING_DATE';

update finding_questions
set query = 'Please provide a JSON
Given the following deal information:
{
  "property_address": "$_property.address.street_ $_property.address.apartment_, $_property.address.city_, $_property.address.state_, $_property.address.zip_",
  "offer_price": $_deal.offerPrice_,
  "earnest_money_deposit": $_deal.earnestMoneyDeposit_,
  "tenant_name": "$_deal.tenantName_",
  "closing_date": "$_deal.initialClosingDate_",
  "due_diligence_expiration_date": "$_deal.diligenceExpirationDate_"
}

Respond indicating whether there is an error in the document information and, if so, whether the tenant name match. Your response should follow this format:

{
  "tenant_name": "[the deal information tenant name]",
  "psa_tenant_name": "[replace with the Purchase and Sale Agreement (PSA) document tenant name]",
  "error_found": true,
  "explanation": "[replace with the error explanation]"
}
{
  "tenant_name": "Tenant",
  "psa_tenant_name": "Tenant",
  "error_found": false,
  "explanation": "[replace with the error explanation]"
}

You use the example above as a template for your response. Make sure to set ''error_found'' to true if the tenant names don''t match and ''error_found'' to false in any other case.
if you are unable to answer respond:
{
  "error_found": false,
  "explanation": "Unable to answer"
}',
    key = 'PSA_TENANT_NAME'
where key = 'LEASE_TENANT_NAME';
