INSERT INTO task_template(
	    task_category,
	    task_type,
	    member_type_assignment,
	    task_title,
	    task_description,
	    key,
	    form_schema,
	    data,
	    priority,
	    sorting,
	    required,
	    prioritize_after_closing,
	    visibility
	)
	VALUES
     (
         'nnn_icm2',
         'upload_file',
         'buyer',
         'ICM 2',
         '',
         'nnn_icm2',
	     '{"type":"object","required":["icm"],"properties":{"icm":{"type":"string","title":"Please Upload ICM 2","format":"data-url"}}}',
         null,
         'NA',
         26,
         false,
         false,
         'BUYER_TEAM'
     );

INSERT INTO deal_task_template
    (deal_template_key, task_template_key)
VALUES
    ('default', 'nnn_icm2');