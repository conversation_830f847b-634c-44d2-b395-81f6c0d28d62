UPDATE deal_schema
    SET
        json_schema = '{"type":"object","properties":{"tourDate":{"type":"string","format":"date","title":"Tour Date"},"updateNotes":{"type":"string","title":"Update Notes"},"callForOffers":{"type":"string","format":"date","title":"Call for offers"},"pipelineNotes":{"type":"string","title":"Pipeline Notes"},"propertyNotes":{"type":"string","title":"Property Notes"},"submarket":{"type":"string","title":"Submarket"},"onOffMarket":{"type":"string","title":"On/Off Market"},"acquisitionFee":{"type":"number","title":"Acquisition Fee"},"assetManagementFee":{"type":"number","title":"Asset Management"},"promote":{"type":"number","title":"Promote"},"hurdleRate":{"type":"number","title":"Hurdle Rate"},"inPlaceRent":{"type":"number","title":"In-Place Rent"},"marketRent":{"type":"number","title":"Market Rent"},"marketRenewal":{"type":"number","title":"Market Renewal"},"renoRents":{"type":"number","title":"Reno Rents"},"marketReno":{"type":"number","title":"Market Reno"},"capexUnit":{"type":"number","title":"Capex $/Unit"},"t12Noi":{"type":"number","title":"T12 NOI"},"t12CapRate":{"type":"number","title":"T12 Cap Rate"},"t6CapRate":{"type":"number","title":"T6 Cap Rate"},"t3CapRate":{"type":"number","title":"T3 Cap Rate"},"adjustedCapRate":{"type":"number","title":"Adjusted Cap Rate"},"capRateFY1":{"type":"number","title":"Cap Rate FY1"},"capRateFY2":{"type":"number","title":"Cap Rate FY2"},"capRateFY3":{"type":"number","title":"Cap Rate FY3"},"stabilizedNoi":{"type":"number","title":"Stabilized NOI"},"stabilizedYoc":{"type":"number","title":"Stabilized YoC","readOnly":true},"exitCapRate":{"type":"number","title":"Exit Cap Rate"},"askingPriceUnit":{"type":"number","title":"Asking Price $/Unit","readOnly":true},"askingPriceSf":{"type":"number","title":"Asking Price $/SF","readOnly":true},"offerPriceUnit":{"type":"number","title":"Offer Price $/Unit","readOnly":true},"offerPriceSf":{"type":"number","title":"Offer Price $/SF","readOnly":true},"totalCapitalization":{"type":"number","title":"Total Capitalization"},"equityRequired":{"type":"number","title":"Equity Required"},"goingInCap":{"type":"number","title":"Going-In Cap (T12)","readOnly":true},"lender":{"type":"string","title":"Lender"},"debtAmount":{"type":"number","title":"Debt Amount"},"ltv":{"type":"number","title":"LTV"},"debtRate":{"type":"number","title":"Debt Rate"},"amortization":{"type":"number","title":"Amortization"},"term":{"type":"number","title":"Term"},"io":{"type":"number","title":"I/O"},"supplemental":{"type":"string","title":"Supplemental"},"refinance":{"type":"string","title":"Refinance"},"irr":{"type":"number","title":"IRR"},"em":{"type":"number","title":"Multiple"},"iirr":{"type":"number","title":"IRR"},"iem":{"type":"number","title":"Multiple"},"avgCoc":{"type":"number","title":"Avg CoC"},"avgStabilizedCoc":{"type":"number","title":"Avg Stabilized CoC"},"medianIncome":{"type":"number","title":"Median HH Income (Zip)"},"avgHousePrice":{"type":"number","title":"Avg Home Prices (Zip)"},"vacancy":{"type":"number","title":"Vacancy (sm)"},"violentCrime":{"type":"number","title":"Violent Crime Score (Zip)"},"propertyCrime":{"type":"number","title":"Property Crime Score (Zip)"},"remaRisk":{"type":"string","title":"Flood Risk (sm)"},"gsRating":{"type":"string","title":"Greenstreet Rating (sm)"},"supplyAsInventory":{"type":"number","title":"Supply as % of inventory (sm)"},"rentMedianIncome":{"type":"number","title":"Rent / Median Income (sm)"},"operational":{"type":"string","title":"Operational"},"renovations":{"type":"string","title":"Renovations"},"otherIncome":{"type":"string","title":"Other Income"},"rubs":{"type":"string","title":"RUBS"},"pros":{"type":"string","title":"Pros"},"cons":{"type":"string","title":"Cons / Risks"},"recommendation":{"type":"string","title":"Recommendation"}}}',
        view_schema = '{"sections":[{"name":"PROPERTY","type":"DEFAULT"},{"name":"INTERNAL_DATA","type":"CUSTOM","title":"Internal Data","fields":["submarket","tourDate","callForOffers"]},{"name":"DEAL_DETAILS","type":"DEFAULT"},{"name":"UNDERWRITING","type":"CUSTOM","title":"Underwriting Summary","fields":["inPlaceRent","marketRent","marketRenewal","renoRents","marketReno","capexUnit","t12Noi","t12CapRate","t6CapRate","t3CapRate","adjustedCapRate","capRateFY1","capRateFY2","capRateFY3","stabilizedNoi","stabilizedYoc","exitCapRate"]},{"name":"PRICING","type":"CUSTOM","title":"Pricing Guidance","fields":["askingPriceUnit","askingPriceSf","offerPriceUnit","offerPriceSf","totalCapitalization","equityRequired","goingInCap"]},{"name":"DEBT","type":"CUSTOM","title":"Debt Assumptions","fields":["lender","debtAmount","ltv","debtRate","amortization","term","io","supplemental","refinance"]},{"name":"DEAL_RETURNS","type":"CUSTOM","title":"Deal Level Returns","fields":["irr","em"]},{"name":"LP_RETURNS","type":"CUSTOM","title":"LP Returns","fields":["iirr","iem","avgCoc","avgStabilizedCoc"]},{"name":"FEES","type":"CUSTOM","title":"Fees","fields":["acquisitionFee","assetManagementFee","promote","hurdleRate"]},{"name":"SUBMARKET","type":"CUSTOM","title":"Submarket","fields":["medianIncome","avgHousePrice","vacancy","violentCrime","propertyCrime","remaRisk","gsRating","supplyAsInventory","rentMedianIncome"]},{"name":"NOTES","type":"CUSTOM","title":"Notes","fields":["propertyNotes","updateNotes","pipelineNotes"]},{"name":"VALUE_DRIVERS","type":"CUSTOM","title":"Value Drivers","fields":["operational","renovations","otherIncome","rubs"]},{"name":"COMMENTS","type":"CUSTOM","title":"Comments","fields":["pros","cons","recommendation"]}]}'
        WHERE id = 2;