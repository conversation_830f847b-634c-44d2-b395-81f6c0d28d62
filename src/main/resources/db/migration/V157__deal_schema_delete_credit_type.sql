UPDATE public.deal_schema
SET view_schema = '{"sections":[{"name":"PROPERTY","type":"DEFAULT"},{"name":"INTERNAL_DATA","type":"CUSTOM","title":"Internal Data","fields":["askingSalePrice","askingNoi","askingCapRate","offerNoi","offerCapRate"]},{"name":"DEAL_DETAILS","type":"DEFAULT"},{"name":"LEASE","type":"DEFAULT"},{"name":"NOTES","type":"CUSTOM","title":"Notes","fields":["propertyNotes","updateNotes","pipelineNotes"]}]}',
    json_schema = '{"type":"object","properties":{"offerNoi":{"type":"number","title":"Offer NOI"},"askingNoi":{"type":"number","title":"Asking NOI"},"updateNotes":{"type":"string","title":"Update Notes"},"offerCapRate":{"type":"number","title":"Offer Cap Rate"},"askingCapRate":{"type":"number","title":"Asking Cap Rate"},"pipelineNotes":{"type":"string","title":"Pipeline Notes"},"propertyNotes":{"type":"string","title":"Property Notes"},"askingSalePrice":{"type":"number","title":"Asking Sale Price"}}}'
WHERE id = 1;
