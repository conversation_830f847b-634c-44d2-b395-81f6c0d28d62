
-- Add assigned_team to snapshot.updated
UPDATE task_history h
SET snapshot = jsonb_set(
        h.snapshot,
        '{updated}',
        h.snapshot->'updated' || jsonb_build_object(
            'assigned_team',
            (SELECT upper(m.type_key) FROM member m WHERE m.id = CAST(h.snapshot->'updated'->>'assigned_member_id' AS int))
            )
        )
WHERE h.snapshot->'updated'->>'assigned_member_id' IS NOT NULL;

-- Add assigned_buyer_id to snapshot.updated
UPDATE task_history h
SET snapshot = jsonb_set(
        h.snapshot,
        '{updated}',
        h.snapshot->'updated' || jsonb_build_object(
                'assigned_buyer_id',
                CASE
                    WHEN 'BUYER' = h.snapshot->'updated'->>'assigned_team'
                        THEN CAST(h.snapshot->'updated'->>'assigned_member_id' AS int)
                        ELSE (SELECT t.assigned_buyer_id FROM task t WHERE t.id = h.task_id)
                    END
            )
    )
WHERE h.snapshot->'updated'->>'assigned_member_id' IS NOT NULL;

-- Remove assigned_member_id from snapshot.updated
UPDATE task_history h
SET snapshot = h.snapshot #- '{updated,assigned_member_id}';


-- Add assigned_team to snapshot.before_update
UPDATE task_history h
SET snapshot = jsonb_set(
        h.snapshot,
        '{before_update}',
        h.snapshot->'before_update' || jsonb_build_object(
                'assigned_team',
                (SELECT upper(m.type_key) FROM member m WHERE m.id = CAST(h.snapshot->'before_update'->>'assigned_member_id' AS int))
            )
    )
WHERE h.snapshot->'before_update'->>'assigned_member_id' IS NOT NULL;

-- Add assigned_buyer_id to snapshot.before_update
UPDATE task_history h
SET snapshot = jsonb_set(
        h.snapshot,
        '{before_update}',
        h.snapshot->'before_update' || jsonb_build_object(
                'assigned_buyer_id',
                CASE
                    WHEN 'BUYER' = h.snapshot->'before_update'->>'assigned_team'
                        THEN CAST(h.snapshot->'before_update'->>'assigned_member_id' AS int)
                    ELSE (SELECT t.assigned_buyer_id FROM task t WHERE t.id = h.task_id)
                    END
            )
    )
WHERE h.snapshot->'before_update'->>'assigned_member_id' IS NOT NULL;

-- Remove assigned_member_id from snapshot.before_update
UPDATE task_history h
SET snapshot = h.snapshot #- '{before_update,assigned_member_id}';


