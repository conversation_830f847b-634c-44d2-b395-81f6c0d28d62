alter table task_template add column data jsonb;
alter table task add column data jsonb;

insert into category ("key", name, type_key, phase, description)
values ('point_of_contact', 'Point of contact', 'tasks_board', 'closing', ''),
       ('payment_and_invoicing', 'Payment and invoicing', 'tasks_board', 'closing', ''),
       ('insurance_checklist', 'Insurance checklist', 'tasks_board', 'closing', ''),
       ('closing_documents', 'Closing documents', 'tasks_board', 'closing', '');

insert into task_type ("key", name)
values ('tutorial', 'Take a tutorial'),
       ('checklist', 'Ensure a given task is done'),
       ('dynamic_form', 'Answer questions');

insert into task_template (task_category, task_type, member_type_assignment, task_title, task_description, "key")
values ('point_of_contact', 'dynamic_form', 'seller', 'New point of contact', 'Confirm contact information for new point of contact', 'new_point_of_contact'),
       ('payment_and_invoicing', 'tutorial', 'seller', 'Invoicing details', 'Complete invoicing details', 'payment_and_invoicing_details_tutorial'),
       ('insurance_checklist', 'form_and_file', 'seller', 'Flood insurance', 'Upload proof of flood insurance', 'proof_of_flood_insurance'),
       ('insurance_checklist', 'form_and_file', 'seller', 'Property insurance', 'Upload proof of property insurance', 'proof_of_property_insurance'),
       ('closing_documents', 'checklist', 'seller', 'Share invoices', 'Share invoices with the company of any expenses related to the deal that you wish to pay out from your proceeds', 'share_invoices_checklist'),
       ('closing_documents', 'tutorial', 'seller', 'Tutorial for Wet Notarized Documents', 'Complete the tutorial for Wet Notarized Documents', 'wet_notarized_documents_tutorial'),
       ('closing_documents', 'tutorial', 'seller', 'Tutorial for Wet Documents', 'Complete the tutorial for Wet Documents', 'wet_documents_tutorial');

insert into deal_task_template (deal_template_key, task_template_key)
values ('default', 'new_point_of_contact'),
    ('default', 'payment_and_invoicing_details_tutorial'),
    ('default', 'proof_of_flood_insurance'),
    ('default', 'proof_of_property_insurance'),
    ('default', 'share_invoices_checklist'),
    ('default', 'wet_notarized_documents_tutorial'),
    ('default', 'wet_documents_tutorial');

update task_template set data = '{"tutorial_key":"invoicing_details"}' where key = 'payment_and_invoicing_details_tutorial';
update task_template set data = '{"tutorial_key":"wet_notarized_documents"}' where key = 'wet_notarized_documents_tutorial';
update task_template set data = '{"tutorial_key":"wet_documents"}' where key = 'wet_documents_tutorial';
update task_template set data = '{"form_key":"point_of_contact"}' where key = 'new_point_of_contact';
update task_template set form_schema = '{ "type": "object", "required": [ "proofOfFloodInsurance" ], "properties": { "proofOfFloodInsurance": { "type": "string", "title": "Do you want to upload the proof of flood insurance?", "enum": [ "Yes", "No" ] } }, "dependencies": { "proofOfFloodInsurance": { "oneOf": [ { "required": [ "file" ], "properties": { "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "proofOfFloodInsurance": { "enum": [ "Yes" ] } } } ] } } }' where key = 'proof_of_flood_insurance';
update task_template set form_schema = '{ "type": "object", "required": [ "proofOfPropertyInsurance" ], "properties": { "proofOfPropertyInsurance": { "type": "string", "title": "Do you want to upload the proof of property insurance?", "enum": [ "Yes", "No" ] } }, "dependencies": { "proofOfPropertyInsurance": { "oneOf": [ { "required": [ "llcName", "file" ], "properties": { "llcName": { "type": "string", "title": "Which is the Name of the LLC?", "description": "The LLC is the additional insured who will need to be insured at the file." }, "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "proofOfPropertyInsurance": { "enum": [ "Yes" ] } } } ] } } }' where key = 'proof_of_property_insurance';
