alter table task add column assigned_team varchar;
alter table task add column assigned_buyer_id bigint;

update task t set assigned_team = UPPER(split_part(m.type_key, '_', 1))
FROM "member" m WHERE t.assigned_member_id = m.id;

update task t set assigned_buyer_id = buyer.id
FROM  (select min(m.id) as id, dc.id as category_id from deal_category dc
     join deal_member dm
     on dc.deal_id = dm.deal_id
     join member m
     on dm.member_id=m.id
     where m.type_key like 'buyer%'  group by dc.id) buyer
WHERE t.assigned_team = 'SELLER' and buyer.category_id= t.deal_category_id;

update task set assigned_buyer_id = assigned_member_id WHERE assigned_team = 'BUYER';

ALTER TABLE task ALTER COLUMN assigned_member_id DROP NOT NULL;

alter table task alter column assigned_team set not null;
alter table task alter column assigned_buyer_id set not null;

update task set status_key = 'in_review' where status_key='in_second_review';