update task_template set form_schema = '{ "type": "object", "required": [ "proofOfPropertyInsurance" ], "properties": { "proofOfPropertyInsurance": { "type": "string", "title": "Do you want to upload the proof of property insurance?", "enum": [ "Yes", "No" ] } }, "dependencies": { "proofOfPropertyInsurance": { "oneOf": [ { "required": [ "llcName", "file" ], "properties": { "llcName": { "type": "string", "title": "What is the name of the LLC being insured?", "description": "The LLC is the additional insured who will need to be insured at the file." }, "file": { "type": "string", "title": "Upload document", "format": "data-url" }, "proofOfPropertyInsurance": { "enum": [ "Yes" ] } } } ] } } }' where key = 'proof_of_property_insurance';
