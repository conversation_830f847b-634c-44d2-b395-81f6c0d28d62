<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Appenders>
        <Console name="STDOUT" target="SYSTEM_OUT">
            <RegexFilter regex=".*The security token included in the request is invalid.*" onMatch="DENY" onMismatch="ACCEPT" />
            <PatternLayout pattern="%d %highlight{| %5p |} %style{[trace_id : %X{trace_id}]}{cyan} %highlight{|} %style{%c{1.}}{yellow} %highlight{|} %style{%m%n}{bright white}"/>
        </Console>
    </Appenders>
    <Loggers>
        <Logger name="realestate.unlock" level="info" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Logger name="com.keyway" level="info" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Logger name="io.javalin" level="info" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Root level="warn">
            <AppenderRef ref="STDOUT"/>
        </Root>
    </Loggers>
</Configuration>