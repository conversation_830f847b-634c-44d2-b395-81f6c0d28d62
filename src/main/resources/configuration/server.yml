system:
  http_port: "8080"
  timeout: 10000

service_name: "deal-room-api"

rest_clients:
  - name: "k_file_service"
    host: "${K_FILE_SERVICE_URL}"
    default_connection_timeout: 10000
    default_socket_timeout: 60000
  - name: "property_sage"
    host: "${PROPERTY_SAGE_URL}"
  - name: "keygen"
    host: "${KEYGEN_URL}"
  - name: "chat_gpt"
    default_connection_timeout: 30000
    default_socket_timeout: 600000
    host: "${CHAT_GPT_API_URL}"
  - name: "property_assets"
    default_connection_timeout: 10000
    default_socket_timeout: 10000
    host: "${PROPERTY_ASSETS_API_URL}"
  - name: "organizations"
    default_connection_timeout: 10000
    default_socket_timeout: 10000
    host: "${ORGANIZATIONS_API_URL}"

postgres_database:
  jdbc_url: "${POSTGRES_DB_URL}"
  username: "${POSTGRES_DB_USER}"
  password: "${POSTGRES_DB_PASSWORD}"
  driver_class_name: "org.postgresql.Driver"
  minimum_idle: "5"
  maximum_pool_size: "10"
  connection_timeout: "3000"
  idle_timeout: "60000"

aws_credentials:
  access_key: "${AWS_S3_ACCESS_KEY}"
  secret_key: "${AWS_S3_SECRET_KEY}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sns:
  task_changed_topic_arn: "${AWS_SNS_TASK_CHANGED_TOPIC_ARN}"
  deal_events_topic_arn: "${AWS_SNS_DEAL_EVENTS_TOPIC_ARN}"

sqs_task_changed:
  queue_name: "${AWS_SQS_TASK_CHANGED_QUEUE_NAME}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sqs_loi_signed:
  queue_name: "${AWS_SQS_LOI_SIGNED_QUEUE_NAME}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sqs_cron_jobs:
  queue_name: "${AWS_SQS_CRON_JOBS_QUEUE_NAME}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sqs_deal_events:
  queue_name: "${AWS_SQS_DEAL_EVENTS_QUEUE_NAME}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sqs_read_excel_file:
  queue_name: "${AWS_SQS_READ_EXCEL_FILE_QUEUE_NAME}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sqs_chat_gpt_file_sent:
  queue_name: "${AWS_SQS_CHAT_GPT_FILE_SENT_QUEUE_NAME}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sqs_chat_gpt_file_ready:
  queue_name: "${AWS_SQS_CHAT_GPT_FILE_READY_QUEUE_NAME}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sqs_excel_file_result:
  queue_name: "${AWS_SQS_EXCEL_FILE_RESULT_QUEUE_NAME}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sqs_user_event:
  queue_name: "${AWS_SQS_USER_EVENT}"
  account_id: "${AWS_ACCOUNT_ID}"
  region: "${AWS_REGION}"

sendgrid:
  api_key: "${SENDGRID_API_KEY}"
  email_from: "${SENDGRID_EMAIL}"
  app_url: "${SENDGRID_APP_URL}"
  templates:
    welcome_email: "${SENDGRID_WELCOME_EMAIL_TEMPLATE_ID}"
    welcome_email_seller: "${SENDGRID_WELCOME_EMAIL_SELLER_TEMPLATE_ID}"
    task_changed_email: "${SENDGRID_TASK_CHANGED_EMAIL_TEMPLATE_ID}"
    forgot_password_email: "${SENDGRID_FORGOT_PASSWORD_EMAIL_TEMPLATE_ID}"
    loi_with_lease_offer_email: "${SENDGRID_LOI_WITH_LEASE_OFFER_EMAIL_ID}"
    loi_with_lease_sign_email: "${SENDGRID_LOI_WITH_LEASE_SIGN_EMAIL}"
    loi_offer_email: "${SENDGRID_LOI_OFFER_EMAIL_ID}"

web_apps:
  deal_room: "${DEAL_ROOM_APP}"
  deal_room_admin: "${DEAL_ROOM_ADMIN_APP}"
  kos: "${KOS_URL}"

open_api:
  enabled: true

docu_sign:
  key_file_name: "docusign-rsa-priv.key"
  app_integration_key: "${DOCUSIGN_APP_INTEGRATION_KEY}"
  user_id: "${DOCUSIGN_USER_ID}"
  token_expiration_seconds: 3600
  account_id: "${DOCUSIGN_ACCOUNT_ID}"
  base_url: "${DOCUSIGN_BASE_URL}"

deal_tasks:
  top_priority_tasks_size: "${TOP_PRIORITY_TASKS_SIZE}"

loi_sent_email:
  email_from: ${LOI_SENT_EMAIL_FROM}
  carbon_copies_map: ${LOI_SENT_EMAIL_CCS}
  background_carbon_copies: ${LOI_SENT_EMAIL_BCCS}

split_io:
  token: "${SPLIT_IO_TOKEN}"
  timeout: "${SPLIT_IO_TIMEOUT}"

auth0_management_client:
  domain: "${AUTH0_DOMAIN}"
  client_id: "${AUTH0_CLIENT_ID}"
  client_secret: "${AUTH0_CLIENT_SECRET}"
  audience: "${AUTH0_MANAGEMENT_AUDIENCE}"
  users_database_name: "${AUTH0_USERS_DATABASE_NAME}"

auth0_app_client_ids:
  deal_room_client_id: "${AUTH0_DEAL_ROOM_CLIENT_ID}"
  deal_room_admin_client_id: "${AUTH0_DEAL_ROOM_ADMIN_CLIENT_ID}"

auth0_token_verification:
  issuer: "${AUTH0_ISSUER}"
  domain: "${AUTH0_DOMAIN}"
  audience: "${AUTH0_DEAL_ROOM_AUDIENCE}"
  audiences: ${AUTH0_DEAL_ROOM_AUDIENCES}

activate_account_token:
  secret: "${ACTIVATE_ACCOUNT_TOKEN_SECRET}"
  issuer: "${ACTIVATE_ACCOUNT_TOKEN_ISSUER}"
  token_expiration_in_seconds: "${ACTIVATE_ACCOUNT_TOKEN_EXPIRATION_IN_SECONDS}"

document_email_token:
  secret: "${EMAIL_TOKEN_SECRET}"
  issuer: "${EMAIL_TOKEN_ISSUER}"
  token_expiration_in_seconds: "${EMAIL_TOKEN_EXPIRATION_IN_SECONDS}"

chat_gpt_service_token:
  token: "${CHAT_GPT_SERVICE_TOKEN}"

google_calendar_config:
  json_key_env_var: "GOOGLE_CALENDAR_JSON_KEY"
  json_key_file_name: "google-calendar-json-key.json"