system:
  http_port: "8080"
  timeout: 3000

service_name: "deal-room-api"

rest_clients:
  - name: "k_file_service"
    host: "http://localhost:8081"
  - name: "property_sage"
    host: "http://localhost:8081"
  - name: "keygen"
    host: "http://localhost:8081"
  - name: "chat_gpt"
    default_connection_timeout: 10000
    default_socket_timeout: 600000
    host: "http://localhost:8081"
  - name: "property_assets"
    host: "http://localhost:8081"
  - name: "organizations"
    default_connection_timeout: 10000
    default_socket_timeout: 10000
    host: "http://localhost:8081"


postgres_database:
  jdbc_url: "*****************************************"
  username: "postgres"
  password: "postgres"
  driver_class_name: "org.postgresql.Driver"
  minimum_idle: "5"
  maximum_pool_size: "10"
  connection_timeout: "3000"
  idle_timeout: "60000"

aws_credentials:
  access_key: "test"
  secret_key: "test"
  account_id: "************"
  region: "us-west-2"

sns:
  task_changed_topic_arn: "arn:aws:sns:us-west-2:************:topic"
  deal_events_topic_arn: "arn:aws:sns:us-west-3:************:topic"

sqs_task_changed:
  queue_name: "sqs_task_changed"
  account_id: "************"
  region: "us-west-2"

sqs_loi_signed:
  queue_name: "sqs_loi_signed"
  account_id: "************"
  region: "us-west-2"

sqs_cron_jobs:
  queue_name: "sqs_cron_jobs"
  account_id: "************"
  region: "us-west-2"

sqs_deal_events:
  queue_name: "sqs_deal_events"
  account_id: "************"
  region: "us-west-2"

sqs_read_excel_file:
  queue_name: "sqs_read_excel_file"
  account_id: "************"
  region: "us-west-2"

sqs_chat_gpt_file_sent:
  queue_name: "sqs_chat_gpt_file_sent"
  account_id: "00000"
  region: "us-west-2"

sqs_chat_gpt_file_ready:
  queue_name: "sqs_chat_gpt_file_ready"
  account_id: "00000"
  region: "us-west-2"

sqs_excel_file_result:
  queue_name: "sqs_excel_file_result"
  account_id: "************"
  region: "us-west-2"

sqs_user_event:
  queue_name: "sqs_user_event"
  account_id: "************"
  region: "us-west-2"

sendgrid:
  api_key: "test"
  email_from: "<EMAIL>"
  app_url: "test"
  templates:
    welcome_email: "test"
    welcome_email_seller: "test"
    task_changed_email: "test_task_changed"
    forgot_password_email: "test"
    loi_with_lease_offer_email: "test"
    loi_with_lease_sign_email: "SENDGRID_LOI_WITH_LEASE_SIGN_EMAIL"
    loi_offer_email: "SENDGRID_LOI_OFFER_EMAIL_ID"

web_apps:
  deal_room: "http://localhost:8081/"
  deal_room_admin: "http://localhost:8081/"
  kos: "http://localhost:8081/"

open_api:
  enabled: false

docu_sign:
  key_file_name: "test"
  app_integration_key: "test"
  user_id: "test"
  token_expiration_seconds: 3600
  account_id: "test"
  base_url: "https://demo.docusign.net/restapi"

deal_tasks:
  top_priority_tasks_size: 6

loi_sent_email:
  email_from: "<EMAIL>"
  carbon_copies_map: { }
  background_carbon_copies: [ ]

split_io:
  token: "9n5m2ln00sojvcdgtvka543mbb02k1ejcr9p"
  timeout: "5000"

auth0_management_client:
  domain: "dev-sc56c7wcih8hpwg7.us.auth0.com"
  client_id: "844PsfuW2EnvSikBeyrses15sxPUXuve"
  client_secret: "****************************************************************"
  audience: "http://auth0.com"
  users_database_name: "users"

auth0_app_client_ids:
  deal_room_client_id: "DEAL_ROOM_CLIENT_ID"
  deal_room_admin_client_id: "DEAL_ROOM_ADMIN_CLIENT_ID"

auth0_token_verification:
  issuer: "auth0"
  domain: "dev-sc56c7wcih8hpwg7.us.auth0.com"
  audience: "http://localhost-auth0:8080"
  audiences: [ "http://auth0.com", "http://auth1.com" ]

activate_account_token:
  secret: "s3cr3t"
  issuer: "deal-room"
  token_expiration_in_seconds: 180

document_email_token:
  secret: "s3cr3t"
  issuer: "deal-room"
  token_expiration_in_seconds: 180

chat_gpt_service_token:
  token: "4n-4m4z1ng-t0k3n"

google_calendar_config:
  json_key_env_var: "GOOGLE_CALENDAR_JSON_KEY"
  json_key_file_name: "google-calendar-json-key.json"
