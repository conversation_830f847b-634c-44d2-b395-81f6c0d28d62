package realestate.unlock.dealroom.api.repository.database.task

import com.fasterxml.jackson.core.type.TypeReference
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.time.LocalDate
import java.time.OffsetDateTime

data class TaskDbModel(
    val id: Long,
    val dealCategoryId: Long,
    val assignedTeam: String,
    val buyerId: Long,
    val buyerTypeKey: String,
    val buyerFirstName: String,
    val buyerLastName: String,
    val buyerCompanyName: String,
    val buyerAddress: String?,
    val buyerPhoneNumber: String?,
    val buyerEmail: String,
    val buyerCreatedAt: OffsetDateTime,
    val buyerUpdatedAt: OffsetDateTime,
    val buyerEnabled: Boolean,
    val buyerAuthId: String?,
    val buyerWalkThroughDone: Boolean,
    val buyerOrganizationId: String,
    val typeKey: String,
    val statusKey: String,
    val templateKey: String?,
    val attachedFormData: Map<String, Any>?,
    val title: String,
    val description: String,
    val dueDate: LocalDate?,
    val formSchema: Map<String, Any>?,
    val rejectionReason: String?,
    val data: Map<String, Any>?,
    val priority: String,
    val sorting: Int,
    val required: Boolean,
    val prioritizeAfterClosing: Boolean,
    val updatedAt: OffsetDateTime,
    val createdAt: OffsetDateTime,
    val visibility: String,
    val enabled: Boolean,
    val dueDateExternalDataDefinition: String?,
    val formExternalDataDefinition: Map<String, Any>?
) {
    fun toTask() = Task(
        id = id,
        dealCategoryId = dealCategoryId,
        assignedBuyer = Member(
            id = buyerId,
            typeKey = buyerTypeKey,
            firstName = buyerFirstName,
            lastName = buyerLastName,
            companyName = buyerCompanyName,
            address = buyerAddress,
            phoneNumber = buyerPhoneNumber,
            email = buyerEmail,
            createdAt = buyerCreatedAt,
            updatedAt = buyerUpdatedAt,
            enabled = buyerEnabled,
            authId = buyerAuthId,
            walkThroughDone = buyerWalkThroughDone,
            organizationId = buyerOrganizationId
        ),
        assignedTeam = MemberDealTeam.valueOf(assignedTeam),
        typeKey = typeKey,
        statusKey = TaskStatus.valueOf(statusKey.uppercase()),
        templateKey = templateKey,
        attachedFormData = attachedFormData?.get("value")?.let { JsonMapper.decode(it.toString(), object : TypeReference<Map<String, Any>>() {}) },
        title = title,
        description = description,
        dueDate = dueDate,
        formSchema = formSchema?.get("value")?.let { JsonMapper.decode(it.toString(), object : TypeReference<Map<String, Any>>() {}) },
        rejectionReason = rejectionReason,
        data = data?.get("value")?.let { JsonMapper.decode(it.toString(), object : TypeReference<Map<String, Any>>() {}) },
        priority = Priority(PriorityValue.valueOf(priority), sorting),
        required = required,
        prioritizeAfterClosing = prioritizeAfterClosing,
        updatedAt = updatedAt,
        createdAt = createdAt,
        visibility = Task.Visibility.valueOf(visibility.uppercase()),
        enabled = enabled,
        externalDataDefinition = buildTaskExternalDataDefinition()
    )

    private fun buildTaskExternalDataDefinition(): TaskExternalDataDefinition? {
        if (dueDateExternalDataDefinition == null && formExternalDataDefinition == null) return null
        return TaskExternalDataDefinition(
            dueDate = dueDateExternalDataDefinition,
            formData = formExternalDataDefinition?.get("value")
                ?.let { JsonMapper.decode(it.toString(), object : TypeReference<Set<String>>() {}) } ?: emptySet()
        )
    }
}
