package realestate.unlock.dealroom.api.repository.database.task.template

import realestate.unlock.dealroom.api.core.entity.task.template.TaskTemplate
import realestate.unlock.dealroom.api.core.extension.mapTo
import realestate.unlock.dealroom.api.core.repository.task.template.TaskTemplateRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import javax.inject.Inject

class TaskTemplateDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : TaskTemplateRepository {

    private val query = """
        select
            tt.id,
            tt.task_category,
            tt.task_type,
            tt.member_type_assignment,
            tt.task_title,
            tt.task_description,
            tt.key,
            tt.form_schema,
            tt.data,
            tt.priority,
            tt.sorting,
            tt.required,
            tt.prioritize_after_closing,
            tt.visibility,
            dtt.deal_template_key,
            tt.due_date_external_data_definition,
            tt.form_external_data_definition
        from task_template tt
        join deal_task_template dtt on tt.key = dtt.task_template_key
        where dtt.deal_template_key = ?
    """.trimIndent()

    override fun findByDealTemplateKey(dealTemplateKey: String): List<TaskTemplate> =
        sqlClient.getAll(
            query = query,
            params = listOf(dealTemplateKey)
        ) { result ->
            result.mapTo(TaskTemplateDbModel::class.java)
                .toTaskTemplate()
        }
}
