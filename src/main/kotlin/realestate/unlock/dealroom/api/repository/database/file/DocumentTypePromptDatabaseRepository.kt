package realestate.unlock.dealroom.api.repository.database.file

import realestate.unlock.dealroom.api.core.repository.keypilot.DocumentTypePromptRepository
import realestate.unlock.dealroom.api.core.repository.keypilot.DocumentTypePromptRepository.Prompt
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.setNullableString
import javax.inject.Inject

class DocumentTypePromptDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : DocumentTypePromptRepository {

    override fun getPrompt(document: String, documentType: String?): Prompt {
        val query = """
            SELECT coalesce(max(prompt), (SELECT prompt from document_type_prompt where document_type = 'DEFAULT')) as prompt
            FROM document_type_prompt
            WHERE document = ? ${if (documentType != null) "AND document_type = ?" else ""}
        """.trimIndent()

        return sqlClient.getOneOrFail(
            query = query,
            params = listOfNotNull(document, documentType),
            clazz = Prompt::class.java,
            messageKey = "GET_PROMPT_FOR_FILE_TYPE"
        )
    }

    override fun getPromptForTaskFile(kFileId: String): Prompt {
        val query = """
            SELECT coalesce(max(prompt), (SELECT prompt from document_type_prompt where document_type = 'DEFAULT')) as prompt
            FROM document_type_prompt
            WHERE document_type = (
                SELECT type
                FROM deal_report dr
                JOIN deal_report_file drf ON dr.id = drf.report_id
                JOIN file f ON f.id = drf.file_id
                WHERE f.k_file_id = ?
            )
        """.trimIndent()

        return sqlClient.getOneOrFail(
            query = query,
            params = listOf(kFileId),
            clazz = Prompt::class.java,
            messageKey = "GET_PROMPT_FOR_TASK_FILE_ID"
        )
    }

    override fun save(document: String, documentType: String?, prompt: String) {
        val query = """
            INSERT INTO document_type_prompt(document, prompt, document_type)
            values (?, ?, ?)
        """.trimIndent()

        sqlClient.update(
            query = query
        ) { ps ->
            ps.setString(1, document)
            ps.setString(2, prompt)
            ps.setNullableString(3, documentType)
        }
    }
}
