package realestate.unlock.dealroom.api.repository.database.pipeline

import com.fasterxml.jackson.core.type.TypeReference
import realestate.unlock.dealroom.api.core.entity.pipeline.PipelineView
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.time.OffsetDateTime

data class PipelineViewDbModel(
    val id: Long,
    val name: String,
    val propertyType: PropertyType,
    val data: Map<String, Any>,
    val updatedAt: OffsetDateTime,
    val createdAt: OffsetDateTime,
    val totalCount: Int?,
    val organizationId: String
) {
    fun toPipelineView() = PipelineView(
        id = id,
        name = name,
        propertyType = propertyType,
        data = data["value"].let { JsonMapper.decode(it.toString(), object : TypeReference<Map<String, Any>>() {}) },
        updatedAt = updatedAt,
        createdAt = createdAt,
        organizationId = organizationId
    )
}
