package realestate.unlock.dealroom.api.repository.database.task.file

import realestate.unlock.dealroom.api.core.entity.task.file.TaskFile
import realestate.unlock.dealroom.api.core.entity.task.file.TaskFileToSave
import realestate.unlock.dealroom.api.core.repository.task.file.TaskFileRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import java.time.OffsetDateTime
import javax.inject.Inject

class TaskFileDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : TaskFileRepository {

    override fun findByDealId(dealId: Long): List<TaskFile> =
        sqlClient.getAll(
            query = """
                select ${selectFields("tf")}
                from deal d
                join deal_category dc on d.id = dc.deal_id
                join task t on dc.id = t.deal_category_id
                join task_file tf on t.id = tf.task_id
                where d.id = ?
            """.trimIndent(),
            clazz = TaskFile::class.java,
            params = listOf(dealId)
        )

    override fun findByDealIdAndTaskTemplate(dealId: Long, templateKey: String): List<TaskFile> =
        sqlClient.getAll(
            query = """
                select ${selectFields("tf")}
                from deal d
                join deal_category dc on d.id = dc.deal_id
                join task t on dc.id = t.deal_category_id
                join task_file tf on t.id = tf.task_id
                where d.id = ? and t.template_key = ?
            """.trimIndent(),
            clazz = TaskFile::class.java,
            params = listOf(dealId, templateKey)
        )

    override fun findByTaskId(taskId: Long): List<TaskFile> =
        sqlClient.getAll(
            query = """
                select ${selectFields()}
                from task_file
                where task_id = ?
            """.trimIndent(),
            clazz = TaskFile::class.java,
            params = listOf(taskId)
        )

    override fun save(taskFileToSave: TaskFileToSave): TaskFile {
        val (fields, questionMarks) = insertFields
        val query = """
            insert into task_file ($fields)
            values ($questionMarks)
            returning ${selectFields()}
        """.trimIndent()

        return OffsetDateTime.now().let { now ->
            sqlClient.update(
                query = query,
                clazz = TaskFile::class.java,
                messageKey = "SAVE_TASK_FILE",
                params = listOf(
                    taskFileToSave.name,
                    taskFileToSave.path,
                    taskFileToSave.kFileId,
                    taskFileToSave.taskId,
                    taskFileToSave.memberId,
                    now
                )
            )
        }
    }

    companion object {

        private fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }

        private val insertFields: Pair<String, String> = Fields.values()
            .filter { it != Fields.ID }
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private enum class Fields {
            ID,
            NAME,
            PATH,
            K_FILE_ID,
            TASK_ID,
            MEMBER_ID,
            CREATED_AT
        }
    }
}
