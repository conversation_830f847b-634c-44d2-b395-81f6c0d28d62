package realestate.unlock.dealroom.api.repository.database.property

import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyToSave
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.*
import realestate.unlock.dealroom.api.repository.database.deal.DealDatabaseRepository
import java.sql.Types
import java.time.OffsetDateTime
import javax.inject.Inject

class PropertyDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : PropertyRepository {

    override fun get(): List<Property> =
        sqlClient.getAll(
            query = """
                SELECT ${selectFields()}
                FROM property
            """.trimIndent(),
            clazz = PropertyDbModel::class.java
        ).map {
            it.toProperty()
        }

    override fun save(property: PropertyToSave): Property {
        val (fields, questionMarks) = insertFields
        return OffsetDateTime.now().let { now ->
            sqlClient.update(
                query = """
                        INSERT INTO property ($fields)
                        VALUES ($questionMarks)
                        RETURNING ${selectFields()}
                """.trimIndent(),
                messageKey = "SAVE_PROPERTY",
                clazz = PropertyDbModel::class.java
            ) { pst ->
                pst.setString(1, property.street)
                pst.setString(2, property.apartment)
                pst.setString(3, property.city)
                pst.setString(4, property.state)
                pst.setString(5, property.zip)
                pst.setNullableBigDecimal(6, property.latitude)
                pst.setNullableBigDecimal(7, property.longitude)
                pst.setString(8, property.keywayId)
                pst.setObject(9, property.yearBuilt, Types.INTEGER)
                pst.setObject(10, property.squareFootage, Types.NUMERIC)
                pst.setObject(11, property.askingPrice, Types.NUMERIC)
                pst.setNullableString(12, null)
                pst.setNullableString(13, null)
                pst.setObject(14, now)
                pst.setObject(15, now)
                pst.setNullableString(16, property.name)
                pst.setString(17, property.type.name)
                pst.setNullableString(18, property.multifamilyData?.brokerFirm)
                pst.setNullableLong(19, property.multifamilyData?.units)
                pst.setNullableBigDecimal(20, property.multifamilyData?.averageSquareFootage)
                pst.setNullableBigDecimal(21, property.multifamilyData?.occupancy)
                pst.setNullableInt(22, property.multifamilyData?.parkingSpots)
                pst.setNullableBigDecimal(23, property.multifamilyData?.parkingRatio)
                pst.setNullableString(24, property.multifamilyData?.owner)
                pst.setNullableString(25, property.multifamilyData?.propertyManager)
                pst.setObject(26, property.multifamilyData?.unitsMix.toJsonPGObject())
            }.toProperty()
        }
    }

    override fun update(property: Property): Property =
        sqlClient.update(
            query = """
                update property set $updateFields
                where ${Fields.ID} = ?
                returning ${selectFields()}
            """.trimIndent(),
            messageKey = "UPDATE_PROPERTY",
            clazz = PropertyDbModel::class.java
        ) { pst ->
            pst.setString(1, property.address.street)
            pst.setString(2, property.address.apartment)
            pst.setString(3, property.address.city)
            pst.setString(4, property.address.state)
            pst.setString(5, property.address.zip)
            pst.setNullableBigDecimal(6, property.address.coordinates?.latitude)
            pst.setNullableBigDecimal(7, property.address.coordinates?.longitude)
            pst.setString(8, property.keywayId)
            pst.setObject(9, property.yearBuilt, Types.INTEGER)
            pst.setObject(10, property.squareFootage, Types.NUMERIC)
            pst.setObject(11, property.askingPrice, Types.NUMERIC)
            pst.setNullableString(12, property.mainPhotoId)
            pst.setNullableString(13, property.interiorPhotoId)
            pst.setObject(14, property.updatedAt)
            pst.setNullableString(15, property.name)
            pst.setString(16, property.type.name)
            pst.setNullableString(17, property.multifamilyData?.brokerFirm)
            pst.setNullableLong(18, property.multifamilyData?.units)
            pst.setNullableBigDecimal(19, property.multifamilyData?.averageSquareFootage)
            pst.setNullableBigDecimal(20, property.multifamilyData?.occupancy)
            pst.setNullableInt(21, property.multifamilyData?.parkingSpots)
            pst.setNullableBigDecimal(22, property.multifamilyData?.parkingRatio)
            pst.setNullableString(23, property.multifamilyData?.owner)
            pst.setNullableString(24, property.multifamilyData?.propertyManager)
            pst.setObject(25, property.multifamilyData?.unitsMix.toJsonPGObject())
            pst.setLong(26, property.id)
        }.toProperty()

    override fun findById(propertyId: Long): Property =
        sqlClient.getOneOrFail(
            query = """
                SELECT ${selectFields()}
                FROM property
                WHERE ${Fields.ID} = ?
            """.trimIndent(),
            clazz = PropertyDbModel::class.java,
            messageKey = "FIND_PROPERTY_BY_ID",
            params = listOf(propertyId)
        ).toProperty()

    override fun findByDealId(dealId: Long): Property =
        sqlClient.getOneOrFail(
            query = """
                SELECT ${selectFields("prop")}
                FROM property prop INNER JOIN deal de
                    ON prop.${Fields.ID} = de.${DealDatabaseRepository.Fields.PROPERTY_ID}
                WHERE de.${DealDatabaseRepository.Fields.ID} = ?
            """.trimIndent(),
            clazz = PropertyDbModel::class.java,
            messageKey = "FIND_PROPERTY_BY_DEAL_ID",
            params = listOf(dealId)
        ).toProperty()

    override fun findByKeywayId(keywayId: String): List<Property> =
        sqlClient.getAll(
            query = """
                SELECT ${selectFields()}
                FROM property
                WHERE ${Fields.KEYWAY_ID} = ?
            """.trimIndent(),
            clazz = PropertyDbModel::class.java,
            params = listOf(keywayId)
        ).map(PropertyDbModel::toProperty)

    enum class Fields {
        ID,
        ADDRESS_STREET,
        ADDRESS_APARTMENT,
        ADDRESS_CITY,
        ADDRESS_STATE,
        ADDRESS_ZIP,
        ADDRESS_LATITUDE,
        ADDRESS_LONGITUDE,
        KEYWAY_ID,
        YEAR_BUILT,
        SQUARE_FOOTAGE,
        PURCHASE_PRICE,
        MAIN_PHOTO_ID,
        INTERIOR_PHOTO_ID,
        UPDATED_AT,
        CREATED_AT,
        NAME,
        TYPE,
        BROKER_FIRM,
        UNITS,
        AVERAGE_SQUARE_FOOTAGE,
        OCCUPANCY,
        PARKING_SPOTS,
        PARKING_RATIO,
        OWNER,
        PROPERTY_MANAGER,
        UNITS_MIX
    }

    companion object {
        private fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }

        private val insertFields: Pair<String, String> = Fields.values()
            .filter { it != Fields.ID }
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private val updateFields = setOf(
            Fields.ADDRESS_STREET,
            Fields.ADDRESS_APARTMENT,
            Fields.ADDRESS_CITY,
            Fields.ADDRESS_STATE,
            Fields.ADDRESS_ZIP,
            Fields.ADDRESS_LATITUDE,
            Fields.ADDRESS_LONGITUDE,
            Fields.KEYWAY_ID,
            Fields.YEAR_BUILT,
            Fields.SQUARE_FOOTAGE,
            Fields.PURCHASE_PRICE,
            Fields.MAIN_PHOTO_ID,
            Fields.INTERIOR_PHOTO_ID,
            Fields.UPDATED_AT,
            Fields.NAME,
            Fields.TYPE,
            Fields.BROKER_FIRM,
            Fields.UNITS,
            Fields.AVERAGE_SQUARE_FOOTAGE,
            Fields.OCCUPANCY,
            Fields.PARKING_SPOTS,
            Fields.PARKING_RATIO,
            Fields.OWNER,
            Fields.PROPERTY_MANAGER,
            Fields.UNITS_MIX
        ).joinToString(",") { "$it = ?" }
    }
}
