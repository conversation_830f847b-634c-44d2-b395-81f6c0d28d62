package realestate.unlock.dealroom.api.repository.database.category

import realestate.unlock.dealroom.api.core.entity.category.Category
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.repository.category.FindCategoryByKeyRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import javax.inject.Inject

class FindCategoryByKeyDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : FindCategoryByKeyRepository {

    private val query = """
        SELECT
            id, 
            "key",
            type_key,
            name, 
            description,
            stage
        FROM category
        WHERE key = ?
    """.trimIndent()

    override fun find(categoryKey: String): Category =
        sqlClient.getOneOrFail(
            query = query,
            clazz = CategoryDbModel::class.java,
            messageKey = "FIND_CATEGORY_BY_ID",
            params = listOf(categoryKey)
        ).toCategory()

    data class CategoryDbModel(
        val id: Long,
        val key: String,
        val name: String,
        val description: String,
        val typeKey: String,
        var stage: String
    ) {
        fun toCategory(): Category = Category(
            id = id,
            key = key,
            name = name,
            description = description,
            typeKey = typeKey,
            stage = Stage.valueOf(stage.uppercase())
        )
    }
}
