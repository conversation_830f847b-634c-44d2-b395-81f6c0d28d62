package realestate.unlock.dealroom.api.repository.database.deal

import realestate.unlock.dealroom.api.core.entity.deal.DealHistory
import realestate.unlock.dealroom.api.core.entity.deal.DealStatus
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.repository.deal.DealHistoryRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.setNullableLong
import realestate.unlock.dealroom.api.repository.database.setNullableString
import java.time.OffsetDateTime
import javax.inject.Inject

class DealDatabaseHistoryRepository
@Inject constructor(
    private val sqlClient: SqlClient
) : DealHistoryRepository {
    override fun findByDealId(dealId: Long): List<DealHistory> {
        return sqlClient.getAll(
            query = """
                SELECT ${selectFields()} 
                FROM $tableName
                WHERE ${Fields.DEAL_ID} = ?
            """.trimIndent(),
            params = listOf(dealId),
            clazz = DealHistoryModel::class.java
        ).map(DealHistoryModel::toDealHistory)
    }

    override fun save(dealHistory: DealHistory) {
        val (fields, questionMarks) = insertFields
        sqlClient.update(
            query = """
                INSERT INTO $tableName ($fields)
                VALUES ($questionMarks)
                returning ${selectFields()}
            """.trimIndent(),
            messageKey = "SAVE_DEAL_HISTORY",
            clazz = DealHistoryModel::class.java
        ) { ps ->
            ps.setLong(1, dealHistory.id)
            ps.setLong(2, dealHistory.dealId)
            ps.setString(3, dealHistory.oldStage.name)
            ps.setString(4, dealHistory.newStage.name)
            ps.setString(5, dealHistory.oldStatus!!.name)
            ps.setString(6, dealHistory.newStatus!!.name)
            ps.setNullableString(7, dealHistory.comment)
            ps.setNullableLong(8, dealHistory.memberId)
            ps.setObject(9, dealHistory.createdAt)
        }
    }

    override fun nextId(): Long = sqlClient.nextId("seq_$tableName")

    private enum class Fields {
        ID,
        DEAL_ID,
        OLD_STAGE,
        NEW_STAGE,
        OLD_STATUS,
        NEW_STATUS,
        COMMENT,
        MEMBER_ID,
        CREATED_AT
    }

    companion object {
        private const val tableName = "deal_history"

        private val insertFields: Pair<String, String> = Fields.values()
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }
    }

    data class DealHistoryModel(
        val id: Long,
        val dealId: Long,
        val oldStage: String,
        val newStage: String,
        val oldStatus: String,
        val newStatus: String,
        val comment: String?,
        val memberId: Long?,
        val createdAt: OffsetDateTime
    ) {
        fun toDealHistory(): DealHistory =
            DealHistory(
                id = this.id,
                dealId = this.dealId,
                oldStage = Stage.valueOf(this.oldStage),
                newStage = Stage.valueOf(this.newStage),
                oldStatus = DealStatus.valueOf(this.oldStatus),
                newStatus = DealStatus.valueOf(this.newStatus),
                comment = this.comment,
                memberId = this.memberId,
                createdAt = this.createdAt
            )
    }
}
