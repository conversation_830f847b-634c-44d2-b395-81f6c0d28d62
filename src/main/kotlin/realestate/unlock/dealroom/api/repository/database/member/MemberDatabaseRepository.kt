package realestate.unlock.dealroom.api.repository.database.member

import org.postgresql.util.PGobject
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.MemberToSave
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.infrastructure.client.database.mapper.mapFiltersForQuery
import java.time.OffsetDateTime
import javax.inject.Inject

class MemberDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : MemberRepository {

    override fun findByEmail(userEmail: String): Member? =
        sqlClient.get(
            query = """
                SELECT ${selectFields()}
                FROM member 
                WHERE ${MemberFields.EMAIL} ILIKE ?
            """.trimIndent(),
            clazz = Member::class.java,
            params = listOf(userEmail)
        )

    override fun findById(memberId: Long): Member =
        sqlClient.getOneOrFail(
            query = """
                SELECT ${selectFields()}
                FROM member
                WHERE ${MemberFields.ID} = ?
            """.trimIndent(),
            clazz = Member::class.java,
            messageKey = "GET_MEMBER_BY_ID",
            params = listOf(memberId)
        )

    override fun findByAuthId(authId: String): Member =
        sqlClient.getOneOrFail(
            query = """
                SELECT ${selectFields()}
                FROM member
                WHERE ${MemberFields.AUTH_ID} = ?
            """.trimIndent(),
            clazz = Member::class.java,
            messageKey = "GET_MEMBER_BY_AUTH_ID",
            params = listOf(authId)
        )

    override fun findByDealCategoryId(dealCategoryId: Long): Set<Member> =
        sqlClient.getAll(
            query = """
                SELECT ${selectFields("m")}
                FROM deal_category dc
                JOIN deal_member dm ON dc.deal_id = dm.deal_id
                JOIN member m ON dm.member_id = m.${MemberFields.ID}
                WHERE dc.id = ? ${hideEmailCondition("m")}
            """.trimIndent(),
            clazz = Member::class.java,
            params = listOf(dealCategoryId)
        ).toSet()

    override fun findByDealId(dealId: Long): Set<Member> =
        sqlClient.getAll(
            query = """
                SELECT ${selectFields("m")}
                FROM member m
                JOIN deal_member dm ON m.${MemberFields.ID} = dm.member_id
                WHERE dm.deal_id = ? ${hideEmailCondition("m")}
            """.trimIndent(),
            clazz = Member::class.java,
            params = listOf(dealId)
        ).toSet()

    override fun findByMemberIds(memberIds: Iterable<Long>): List<Member> {
        return sqlClient.getAll(
            query = """
                SELECT ${selectFields()}
                FROM member
                WHERE ${MemberFields.ID} = ANY(?) ${hideEmailCondition()}
            """.trimIndent(),
            clazz = Member::class.java,
            params = listOf(memberIds.toList().toTypedArray())
        )
    }

    override fun findFiltered(filters: Map<String, Any>, organizationId: String): Set<Member> =
        filters.mapFiltersForQuery(
            """
                SELECT ${selectFields()}
                FROM member
                WHERE organization_id = '$organizationId' ${hideEmailCondition()}
            """.trimIndent()
        )
            .let { queryWithParams ->
                sqlClient.getAll(
                    query = queryWithParams.query.toString(),
                    params = queryWithParams.params,
                    clazz = Member::class.java
                ).toSet()
            }

    override fun save(member: MemberToSave): Member {
        val (fields, questionMarks) = insertFields
        return OffsetDateTime.now().let { now ->
            sqlClient.update(
                query = """
                        INSERT INTO MEMBER ($fields)
                        VALUES ($questionMarks)
                        RETURNING ${selectFields()}
                """.trimIndent(),
                messageKey = "SAVE_MEMBER",
                clazz = Member::class.java,
                params = listOf(
                    member.type.key,
                    member.companyName,
                    member.firstName,
                    member.lastName,
                    PGobject().apply {
                        this.type = "varchar"
                        this.value = member.address
                    },
                    PGobject().apply {
                        this.type = "varchar"
                        this.value = member.phoneNumber
                    },
                    member.email,
                    now,
                    now,
                    true,
                    PGobject().apply {
                        this.type = "varchar"
                        this.value = member.authId
                    },
                    member.walkThroughDone,
                    member.organizationId
                )
            )
        }
    }

    override fun update(member: Member): Member {
        return sqlClient.update(
            query = """
                    update member set $updateFields
                    where ${MemberFields.ID} = ?
                    returning ${selectFields()};
            """.trimIndent(),
            messageKey = "UPDATE_MEMBER",
            clazz = Member::class.java,
            params = listOf(
                member.typeKey,
                member.companyName,
                member.firstName,
                member.lastName,
                PGobject().apply {
                    this.type = "varchar"
                    this.value = member.address
                },
                PGobject().apply {
                    this.type = "varchar"
                    this.value = member.phoneNumber
                },
                member.email,
                member.updatedAt,
                member.enabled,
                PGobject().apply {
                    this.type = "varchar"
                    this.value = member.authId
                },
                member.walkThroughDone,
                member.organizationId,
                member.id
            )
        )
    }

    companion object {
        private fun selectFields(prefix: String? = null): String = MemberFields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }

        private val insertFields: Pair<String, String> = MemberFields.values()
            .filter { it != MemberFields.ID }
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private val updateFields = MemberFields.values().filter { it.updateField }
            .joinToString(",") { "$it = ?" }

        private fun hideEmailCondition(prefix: String? = null) =
            " AND ${MemberFields.EMAIL.let { if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"}} !~* '^[a-zA-Z0-9._%-]+(\\+)+([a-zA-Z0-9._%-]+)?hide[a-zA-Z0-9._%+-]*@whykeyway\\.com\$' "
    }
}
