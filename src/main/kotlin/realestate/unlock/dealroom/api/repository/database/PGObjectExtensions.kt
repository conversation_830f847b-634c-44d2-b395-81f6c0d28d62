package realestate.unlock.dealroom.api.repository.database

import org.postgresql.util.PGobject
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.time.LocalDate

fun LocalDate.toPGObject(): PGobject =
    this.let { localDate ->
        PGobject().apply {
            this.type = "date"
            this.value = localDate.toString()
        }
    }
fun Any?.toJsonPGObject(): PGobject =
    this.let { any ->
        PGobject().apply {
            this.type = "json"
            this.value = any?.let(JsonMapper::encode)
        }
    }
