package realestate.unlock.dealroom.api.repository.database

import java.math.BigDecimal
import java.sql.PreparedStatement
import java.sql.Types

fun PreparedStatement.setNullableFloat(parameterIndex: Int, value: Float?) {
    value
        ?.also { setFloat(parameterIndex, value) }
        ?: setNull(parameterIndex, Types.FLOAT)
}

fun PreparedStatement.setNullableString(parameterIndex: Int, value: String?) {
    value
        ?.also { setString(parameterIndex, value) }
        ?: setNull(parameterIndex, Types.VARCHAR)
}

fun PreparedStatement.setNullableInt(parameterIndex: Int, value: Int?) {
    value
        ?.also { setInt(parameterIndex, value) }
        ?: setNull(parameterIndex, Types.INTEGER)
}

fun PreparedStatement.setNullableLong(parameterIndex: Int, value: Long?) {
    value
        ?.also { setLong(parameterIndex, value) }
        ?: setNull(parameterIndex, Types.BIGINT)
}

fun PreparedStatement.setNullableBigDecimal(parameterIndex: Int, value: BigDecimal?) {
    value
        ?.also { setBigDecimal(parameterIndex, value) }
        ?: setNull(parameterIndex, Types.NUMERIC)
}
