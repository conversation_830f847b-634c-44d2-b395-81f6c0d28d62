package realestate.unlock.dealroom.api.repository.database.file

import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.repository.file.FileRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.setNullableLong
import javax.inject.Inject

class FileDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : FileRepository {

    override fun save(file: File) {
        val (fields, questionMarks) = insertFields
        sqlClient.update(
            query = """
                    INSERT INTO file ($fields)
                    VALUES ($questionMarks)
                    returning ${selectFields()}
            """.trimIndent(),
            messageKey = "SAVE_FILE",
            clazz = File::class.java
        ) { ps ->
            ps.setLong(1, file.id)
            ps.setNullableLong(2, file.memberId)
            ps.setString(3, file.name)
            ps.setString(4, file.kFileId)
            ps.setObject(5, file.createdAt)
        }
    }

    override fun findById(id: Long): File {
        return sqlClient.getOneOrFail(
            query = """
                SELECT ${selectFields()}
                FROM file
                WHERE ${Fields.ID} = ?
            """.trimIndent(),
            clazz = File::class.java,
            params = listOf(id),
            messageKey = "GET_FILE_BY_ID"
        )
    }

    override fun findByFileId(fileId: String): File? {
        return sqlClient.get(
            query = """
                SELECT ${selectFields()}
                FROM file
                WHERE ${Fields.K_FILE_ID} = ?
            """.trimIndent(),
            clazz = File::class.java,
            params = listOf(fileId)
        )
    }

    override fun nextId() = sqlClient.nextId("seq_file")

    enum class Fields {
        ID,
        MEMBER_ID,
        NAME,
        K_FILE_ID,
        CREATED_AT
    }

    companion object {
        fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }

        private val insertFields: Pair<String, String> = Fields.values()
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }
    }
}
