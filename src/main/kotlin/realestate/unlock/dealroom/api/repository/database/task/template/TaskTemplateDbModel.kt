package realestate.unlock.dealroom.api.repository.database.task.template

import com.fasterxml.jackson.core.type.TypeReference
import realestate.unlock.dealroom.api.core.entity.task.Priority
import realestate.unlock.dealroom.api.core.entity.task.PriorityValue
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.task.TaskExternalDataDefinition
import realestate.unlock.dealroom.api.core.entity.task.template.TaskTemplate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper

data class TaskTemplateDbModel(
    val id: Long,
    val key: String,
    val taskCategory: String,
    val taskType: String,
    val memberTypeAssignment: String,
    val taskTitle: String,
    val taskDescription: String,
    val formSchema: Map<String, Any>?,
    val data: Map<String, Any>?,
    val priority: String,
    val sorting: Int,
    val required: Boolean,
    val visibility: String,
    val prioritizeAfterClosing: Boolean,
    val dueDateExternalDataDefinition: String?,
    val formExternalDataDefinition: Map<String, Any>?
) {
    fun toTaskTemplate(): TaskTemplate = TaskTemplate(
        id = id,
        key = key,
        taskCategory = taskCategory,
        taskType = taskType,
        memberTypeAssignment = memberTypeAssignment,
        taskTitle = taskTitle,
        taskDescription = taskDescription,
        formSchema = formSchema?.get("value")?.let { JsonMapper.decode(it.toString(), object : TypeReference<Map<String, Any>>() {}) },
        data = data?.get("value")?.let { JsonMapper.decode(it.toString(), object : TypeReference<Map<String, Any>>() {}) },
        priority = Priority(PriorityValue.valueOf(priority), sorting),
        required = required,
        prioritizeAfterClosing = prioritizeAfterClosing,
        visibility = Task.Visibility.valueOf(visibility.uppercase()),
        taskExternalDataDefinition = buildTaskExternalDataDefinition()
    )

    private fun buildTaskExternalDataDefinition(): TaskExternalDataDefinition {
        return TaskExternalDataDefinition(
            dueDate = dueDateExternalDataDefinition,
            formData = formExternalDataDefinition?.get("value")
                ?.let { JsonMapper.decode(it.toString(), object : TypeReference<Set<String>>() {}) } ?: emptySet()
        )
    }
}
