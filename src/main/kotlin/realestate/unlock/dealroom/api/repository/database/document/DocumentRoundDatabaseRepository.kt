package realestate.unlock.dealroom.api.repository.database.document

import realestate.unlock.dealroom.api.core.entity.document.DocumentRound
import realestate.unlock.dealroom.api.core.entity.document.DraftDocuments
import realestate.unlock.dealroom.api.core.entity.document.ExecutedDocument
import realestate.unlock.dealroom.api.core.entity.document.FinalDocument
import realestate.unlock.dealroom.api.core.repository.document.DocumentRoundRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.setNullableLong
import realestate.unlock.dealroom.api.repository.database.setNullableString
import java.sql.PreparedStatement
import javax.inject.Inject

class DocumentRoundDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : DocumentRoundRepository {

    override fun save(documentRound: DocumentRound) {
        val (fields, questionMarks) = insertFields
        sqlClient.update(
            query = """
                INSERT INTO deal_document_round ($fields)
                VALUES ($questionMarks)
                 returning ${selectFields()}
            """.trimIndent(),
            messageKey = "SAVE_DEAL_DOCUMENT",
            clazz = DocumentRoundDbModel::class.java
        ) { ps ->
            mapDocument(ps, documentRound)
        }
    }

    override fun findById(id: Long) =
        sqlClient.getOneOrFail(
            query = """
                SELECT ${selectFields()} 
                FROM deal_document_round
                WHERE ${Fields.ID} = ?
            """.trimIndent(),
            params = listOf(id),
            clazz = DocumentRoundDbModel::class.java,
            messageKey = "GET_DEAL_DOCUMENT_ROUND"
        ).toDocumentRound()

    override fun findByDocumentId(documentId: Long) =
        sqlClient.getAll(
            query = """
                SELECT ${selectFields()} 
                FROM deal_document_round
                WHERE ${Fields.DOCUMENT_ID} = ?
            """.trimIndent(),
            params = listOf(documentId),
            clazz = DocumentRoundDbModel::class.java
        ).map { it.toDocumentRound() }

    override fun findByDocumentAndContainsFileId(documentId: Long, fileId: Long) =
        sqlClient.getAll(
            query = """
                SELECT ${selectFields()} 
                FROM deal_document_round
                WHERE ${Fields.DOCUMENT_ID} = ?
                and (
                  ${Fields.FILE_ID} = ? or
                  ${Fields.CLEAN_VERSION_FILE_ID} = ? or
                  ${Fields.RED_LINE_VERSION_FILE_ID} = ? 
                )
            """.trimIndent(),
            params = listOf(documentId, fileId, fileId, fileId),
            clazz = DocumentRoundDbModel::class.java
        ).map { it.toDocumentRound() }

    override fun nextId() = sqlClient.nextId("seq_deal_document_round")

    private enum class Fields {
        ID,
        DOCUMENT_ID,
        MEMBER_ID,
        COMMENT,
        FILE_ID,
        CLEAN_VERSION_FILE_ID,
        RED_LINE_VERSION_FILE_ID,
        DOCUMENT_TYPE
    }

    companion object {
        private val insertFields: Pair<String, String> = Fields.values()
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }
    }

    private fun mapDocument(
        ps: PreparedStatement,
        documentRound: DocumentRound
    ) {
        ps.setLong(1, documentRound.id)
        ps.setLong(2, documentRound.documentId)
        when (documentRound) {
            is DraftDocuments -> {
                fieldsByDocumentType(
                    ps = ps,
                    memberId = documentRound.memberId,
                    comment = documentRound.comment,
                    cleanVersionFileId = documentRound.cleanVersionFileId,
                    redLineVersionFileId = documentRound.redLineVersionFileId,
                    documentType = DocumentRoundDbModel.DocumentType.DRAFT_DOCUMENT
                )
            }
            is FinalDocument -> {
                fieldsByDocumentType(
                    ps = ps,
                    memberId = documentRound.memberId,
                    comment = documentRound.comment,
                    fileId = documentRound.fileId,
                    documentType = DocumentRoundDbModel.DocumentType.FINAL_DOCUMENT
                )
            }
            is ExecutedDocument -> {
                fieldsByDocumentType(
                    ps = ps,
                    fileId = documentRound.fileId,
                    documentType = DocumentRoundDbModel.DocumentType.EXECUTED_DOCUMENT
                )
            }
        }
    }

    private fun fieldsByDocumentType(
        ps: PreparedStatement,
        memberId: Long? = null,
        comment: String? = null,
        fileId: Long? = null,
        cleanVersionFileId: Long? = null,
        redLineVersionFileId: Long? = null,
        documentType: DocumentRoundDbModel.DocumentType

    ) {
        ps.setNullableLong(3, memberId)
        ps.setNullableString(4, comment)
        ps.setNullableLong(5, fileId)
        ps.setNullableLong(6, cleanVersionFileId)
        ps.setNullableLong(7, redLineVersionFileId)
        ps.setString(8, documentType.name)
    }

    private fun DocumentRoundDbModel.toDocumentRound(): DocumentRound {
        return when (this.documentType) {
            DocumentRoundDbModel.DocumentType.DRAFT_DOCUMENT -> DraftDocuments(
                id = this.id,
                documentId = this.documentId,
                cleanVersionFileId = this.cleanVersionFileId,
                redLineVersionFileId = this.redLineVersionFileId,
                comment = this.comment,
                memberId = this.memberId!!
            )
            DocumentRoundDbModel.DocumentType.FINAL_DOCUMENT -> FinalDocument(
                id = this.id,
                documentId = this.documentId,
                fileId = this.fileId!!,
                comment = this.comment,
                memberId = this.memberId!!
            )
            DocumentRoundDbModel.DocumentType.EXECUTED_DOCUMENT -> ExecutedDocument(
                id = this.id,
                documentId = this.documentId,
                fileId = this.fileId!!
            )
        }
    }
}
