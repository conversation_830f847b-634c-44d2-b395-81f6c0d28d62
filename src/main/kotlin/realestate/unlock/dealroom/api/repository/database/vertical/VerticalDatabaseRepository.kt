package realestate.unlock.dealroom.api.repository.database.vertical

import realestate.unlock.dealroom.api.core.entity.vertical.Vertical
import realestate.unlock.dealroom.api.core.repository.vertical.VerticalRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import javax.inject.Inject

class VerticalDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : VerticalRepository {

    override fun findAll(): List<Vertical> {
        return sqlClient.getAll(
            query = "SELECT name FROM deal_vertical",
            clazz = Vertical::class.java
        )
    }
}
