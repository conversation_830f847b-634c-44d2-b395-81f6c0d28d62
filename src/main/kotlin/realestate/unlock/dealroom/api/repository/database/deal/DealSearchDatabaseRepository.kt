package realestate.unlock.dealroom.api.repository.database.deal

import realestate.unlock.dealroom.api.core.entity.deal.DealSearch
import realestate.unlock.dealroom.api.core.entity.deal.DealStatus
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedInput
import realestate.unlock.dealroom.api.core.entity.property.Address
import realestate.unlock.dealroom.api.core.entity.property.Coordinates
import realestate.unlock.dealroom.api.core.entity.property.PropertyType
import realestate.unlock.dealroom.api.core.extension.mapTo
import realestate.unlock.dealroom.api.core.repository.deal.DealSearchRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.getQueryFiltersAndParams
import java.math.BigDecimal
import java.time.LocalDate
import javax.inject.Inject

class DealSearchDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : DealSearchRepository {

    override fun findDeals(input: PaginatedInput, memberId: Long?, organizationId: String): List<DealSearch> {
        val (where, params) = input.filters.getQueryFiltersAndParams()
        val query = """
                   SELECT 
                        d.id,
                        d.status,
                        d.stage,
                        d.tenant_name,
                        d.vertical,
                        d.property_id,
                        d.offer_price,
                        d.earnest_money_deposit,
                        d.extension_deposit,
                        d.loi_executed_date,
                        d.contract_executed_date,
                        d.diligence_expiration_date,
                        d.initial_closing_date,
                        d.outside_closing_date,
                        d.updated_at,
                        d.buyer_company_name,
                        d.broker_company_name,
                        d.seller_company_name,
                        d.first_pass_id,
                        ${if (memberId != null) "dm.member_id," else ""}
                        p.type as property_type,
                        p.address_street,
                        p.address_apartment,
                        p.address_city,
                        p.address_state,
                        p.address_zip,
                        p.address_latitude,
                        p.address_longitude,
                        d.evaluation_due_date,
                        d.underwriting_due_date,
                        p.name,
                        p.main_photo_id,
                        p.type
                   FROM deal d
                   INNER JOIN property p ON p.id = d.property_id
                   ${if (memberId != null) "INNER JOIN deal_member dm ON d.id = dm.deal_id" else ""}
                   WHERE d.organization_id = '$organizationId'
                   ${if (memberId != null) "AND dm.member_id = $memberId" else ""}
        """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = params,
            size = input.size,
            offset = input.offset,
            orderBy = input.orderBy,
            order = input.order.name,
            where = where,
            withTotalCount = input.calculateTotal
        ) { result ->
            result.mapTo(DealSearchDbModel::class.java).toDealSearch()
        }
    }

    data class DealSearchDbModel(
        val id: Long,
        val propertyId: Long,
        val status: DealStatus,
        val stage: Stage,
        val addressStreet: String,
        val addressApartment: String?,
        val addressCity: String,
        val addressState: String,
        val addressZip: String,
        val addressLatitude: BigDecimal?,
        val addressLongitude: BigDecimal?,
        val loiExecutedDate: LocalDate?,
        val contractExecutedDate: LocalDate?,
        val diligenceExpirationDate: LocalDate?,
        val initialClosingDate: LocalDate?,
        val outsideClosingDate: LocalDate?,
        val tenantName: String?,
        val vertical: String?,
        val image: String?,
        val totalCount: Int,
        val evaluationDueDate: LocalDate?,
        val underwritingDueDate: LocalDate?,
        val offerPrice: BigDecimal?,
        val earnestMoneyDeposit: BigDecimal?,
        val extensionDeposit: BigDecimal?,
        val name: String?,
        val mainPhotoId: String?,
        val type: PropertyType,
        val brokerCompanyName: String?,
        val buyerCompanyName: String?,
        val sellerCompanyName: String?,
        val firstPassId: String?
    ) {
        fun toDealSearch() = DealSearch(
            id = id,
            status = status,
            stage = stage,
            propertyId = propertyId,
            loiExecutedDate = loiExecutedDate,
            contractExecutedDate = contractExecutedDate,
            diligenceExpirationDate = diligenceExpirationDate,
            initialClosingDate = initialClosingDate,
            outsideClosingDate = outsideClosingDate,
            address = Address(
                street = addressStreet,
                apartment = addressApartment,
                city = addressCity,
                state = addressState,
                zip = addressZip,
                coordinates = if (addressLatitude != null && addressLongitude != null) Coordinates(addressLatitude, addressLongitude) else null
            ),
            tenantName = tenantName,
            vertical = vertical,
            image = null,
            totalCount = totalCount,
            evaluationDueDate = evaluationDueDate,
            underwritingDueDate = underwritingDueDate,
            offerPrice = offerPrice,
            earnestMoneyDeposit = earnestMoneyDeposit,
            extensionDeposit = extensionDeposit,
            name = name,
            photo = mainPhotoId,
            type = type,
            buyerCompanyName = buyerCompanyName,
            brokerCompanyName = brokerCompanyName,
            sellerCompanyName = sellerCompanyName,
            firstPassId = firstPassId
        )
    }
}
