package realestate.unlock.dealroom.api.repository.database.member.type

import realestate.unlock.dealroom.api.core.entity.member.type.MemberType
import realestate.unlock.dealroom.api.core.repository.member.type.MemberTypeRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import javax.inject.Inject

class MemberTypeDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : MemberTypeRepository {

    override fun findByKey(key: String): MemberType =
        sqlClient.getOneOrFail(
            query = """SELECT id, key, name FROM member_type WHERE key = ?""".trimIndent(),
            clazz = MemberType::class.java,
            params = listOf(key.lowercase()),
            messageKey = "FIND_MEMBER_TYPE_BY_KEY"
        )

    override fun findAll(): List<MemberType> =
        sqlClient.getAll(
            query = """ SELECT id, key, name FROM member_type""".trimIndent(),
            clazz = MemberType::class.java
        )
}
