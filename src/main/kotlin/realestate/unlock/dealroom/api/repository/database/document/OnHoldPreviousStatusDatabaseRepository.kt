package realestate.unlock.dealroom.api.repository.database.document

import realestate.unlock.dealroom.api.core.entity.document.OnHoldPreviousStatus
import realestate.unlock.dealroom.api.core.repository.document.OnHoldPreviousStatusRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import javax.inject.Inject

class OnHoldPreviousStatusDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : OnHoldPreviousStatusRepository {

    override fun save(onHoldPreviousStatus: OnHoldPreviousStatus) {
        sqlClient.update(
            query = """
                INSERT INTO deal_document_on_hold_previous_status (document_id, document_status, task_status)
                VALUES (?,?,?)
                ON CONFLICT (document_id) DO 
                UPDATE SET document_status = EXCLUDED.document_status, task_status = EXCLUDED.task_status;
            """.trimIndent()
        ) { ps ->
            ps.setLong(1, onHoldPreviousStatus.documentId)
            ps.setString(2, onHoldPreviousStatus.documentStatus.name)
            ps.setString(3, onHoldPreviousStatus.taskStatus.name)
        }
    }

    override fun findByDocumentId(documentId: Long): OnHoldPreviousStatus? {
        return sqlClient.get(
            query = """
                SELECT * 
                FROM deal_document_on_hold_previous_status
                WHERE document_id = ?
            """.trimIndent(),
            params = listOf(documentId),
            clazz = OnHoldPreviousStatus::class.java
        )
    }
}
