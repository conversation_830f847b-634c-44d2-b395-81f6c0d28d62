package realestate.unlock.dealroom.api.repository.database.deal

import realestate.unlock.dealroom.api.core.entity.deal.*
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedInputByOrganization
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.deal.SaveDealData
import realestate.unlock.dealroom.api.core.usecase.exception.deal.DealNotFoundException
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.infrastructure.client.database.exception.NoRowFoundException
import realestate.unlock.dealroom.api.repository.database.*
import java.time.LocalDate
import java.time.OffsetDateTime
import javax.inject.Inject

class DealDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : DealRepository {

    override fun findById(dealId: Long): Deal {
        return try {
            sqlClient.getOneOrFail(
                query = """
                SELECT ${selectFields()}
                FROM deal 
                WHERE ${Fields.ID} = ?
                """.trimIndent(),
                clazz = DealDbModel::class.java,
                params = listOf(dealId),
                messageKey = "GET_DEAL_BY_ID"
            ).toDeal()
        } catch (nf: NoRowFoundException) {
            throw DealNotFoundException(dealId)
        }
    }

    override fun findByPropertyId(propertyId: Long): Deal? {
        return sqlClient.get(
            query = """
                SELECT ${selectFields()}
                FROM deal
                WHERE ${Fields.PROPERTY_ID} = ?
                AND ${Fields.STATUS} != '${DealStatus.DELETED}'
            """.trimIndent(),
            clazz = DealDbModel::class.java,
            params = listOf(propertyId)
        )?.toDeal()
    }

    override fun findAllNotArchivedByMemberId(memberId: Long): List<Deal> {
        return sqlClient.getAll(
            query = """
                SELECT ${selectFields("d")}
                from deal d
                join deal_member dm on d.id = dm.deal_id
                WHERE dm.member_id = ?
                AND d.${Fields.STATUS} != '${DealStatus.DELETED}'
                ORDER BY d.${Fields.CREATED_AT} DESC
            """.trimIndent(),
            clazz = DealDbModel::class.java,
            params = listOf(memberId)
        ).map { it.toDeal() }
    }

    override fun findAll(input: PaginatedInputByOrganization): PaginatedOutput<Deal> {
        val (where, params) = input.filters.getQueryFiltersAndParams()
        return sqlClient.getAll(
            query = """
                SELECT 
                ${selectFields("d")},
                p.address_street,
                p.address_city,
                p.address_state,
                p.type as property_type
                FROM deal d
                INNER JOIN property p ON ${Fields.PROPERTY_ID.withPrefix("d")} = p.id
                WHERE ${Fields.STATUS} != '${DealStatus.DELETED}'
                    AND d.organization_id = '${input.organizationId}'
            """.trimIndent(),
            params = params,
            size = input.size,
            offset = input.offset,
            orderBy = input.orderBy,
            order = input.order.name,
            where = where,
            withTotalCount = input.calculateTotal,
            clazz = DealDbModel::class.java
        ).let {
            PaginatedOutput(
                size = it.size,
                offset = input.offset,
                data = it.map { dealDbModel -> dealDbModel.toDeal() },
                total = input.getTotalForResponse(it.firstOrNull()?.totalCount)
            )
        }
    }

    override fun findByKeywayId(keywayId: String, organizationId: String): Deal? {
        return sqlClient.get(
            query = """
                SELECT ${selectFields("d")}
                FROM deal d join property p
                    on ${Fields.PROPERTY_ID.withPrefix("d")}  = p.id
                WHERE p.keyway_id = ?
                AND d.${Fields.ORGANIZATION_ID} = ?
                AND d.${Fields.STATUS} != '${DealStatus.DELETED}'
                order by ${Fields.CREATED_AT.withPrefix("d")} desc
                LIMIT 1
            """.trimIndent(),
            clazz = DealDbModel::class.java,
            params = listOf(keywayId, organizationId)
        )?.toDeal()
    }

    override fun findByExpiredClosingDate(closingDate: LocalDate): List<Deal> =
        """
            SELECT ${selectFields()}
            FROM deal d
            WHERE
                ${Fields.STAGE} = '${Stage.DILIGENCE}' AND
                ${Fields.INITIAL_CLOSING_DATE} <= ?
            AND d.${Fields.STATUS} != '${DealStatus.DELETED}'
        """.trimIndent().let { query ->
            sqlClient.getAll(
                query = query,
                clazz = DealDbModel::class.java,
                params = listOf(closingDate)
            ).map { it.toDeal() }
        }

    override fun save(deal: SaveDealData): Deal {
        val (fields, questionMarks) = insertFields
        return OffsetDateTime.now().let { now ->
            sqlClient.update(
                query = """
                    INSERT INTO deal ($fields)
                    VALUES ($questionMarks)
                    RETURNING ${selectFields()}
                """.trimIndent(),
                clazz = DealDbModel::class.java,
                messageKey = "SAVE_DEAL"
            ) { ps ->
                ps.setLong(1, deal.propertyId)
                ps.setString(2, deal.stage.name)
                ps.setObject(3, deal.loiExecutedDate?.toPGObject())
                ps.setObject(4, deal.contractExecutedDate?.toPGObject())
                ps.setObject(5, deal.diligenceExpirationDate?.toPGObject())
                ps.setObject(6, deal.initialClosingDate?.toPGObject())
                ps.setObject(7, deal.outsideClosingDate?.toPGObject())
                ps.setObject(8, now)
                ps.setObject(9, now)
                ps.setNullableString(10, deal.dealType?.name)
                ps.setNullableLong(11, deal.leaseExpirationYear)
                ps.setNullableLong(12, deal.leaseLength)
                ps.setNullableLong(13, deal.leaseNumberOfOptions)
                ps.setNullableLong(14, deal.leaseOptionLengths)
                ps.setNullableBigDecimal(15, deal.leaseRent)
                ps.setNullableFloat(16, deal.leaseRentCpi)
                ps.setNullableFloat(17, deal.leaseRentIncrease)
                ps.setNullableFloat(18, deal.leaseIncreaseEveryYear)
                ps.setNullableString(19, deal.leaseRentStepType?.name)
                ps.setNullableString(20, deal.leaseType)
                ps.setNullableString(21, deal.tenantName)
                ps.setNullableString(22, deal.vertical)
                ps.setNullableString(23, deal.guaranteeType?.name)
                ps.setNullableLong(24, deal.dueDiligenceNumber)
                ps.setNullableLong(25, deal.closingPeriod)
                ps.setNullableLong(26, deal.closingPeriodExtension)
                ps.setString(27, deal.status.name)
                ps.setObject(28, deal.evaluationDueDate?.toPGObject())
                ps.setObject(29, deal.underwritingDueDate?.toPGObject())
                ps.setLong(30, deal.dealSchemaId)
                ps.setObject(31, emptyMap<String, Any>().toJsonPGObject())
                ps.setNullableString(32, null)
                ps.setNullableString(33, null)
                ps.setObject(34, deal.tags.toJsonPGObject())
                ps.setLong(35, deal.leaderId)
                ps.setNullableBigDecimal(36, deal.offerPrice)
                ps.setNullableBigDecimal(37, deal.earnestMoneyDeposit)
                ps.setNullableBigDecimal(38, deal.extensionDeposit)
                ps.setString(39, deal.organizationId)
                ps.setNullableString(40, deal.buyerCompanyName)
                ps.setNullableString(41, deal.brokerCompanyName)
                ps.setNullableString(42, deal.sellerCompanyName)
                ps.setObject(43, deal.leaseDate?.toPGObject())
                ps.setNullableString(44, deal.firstPassId)
            }
        }.toDeal()
    }

    override fun update(deal: Deal): Deal {
        return OffsetDateTime.now().let { now ->
            sqlClient.update(
                query = """
                    UPDATE deal set $updateFields
                    WHERE ${Fields.ID} = ?
                    RETURNING ${selectFields()};
                """.trimIndent(),
                clazz = DealDbModel::class.java,
                messageKey = "UPDATE_DEAL"
            ) { ps ->
                ps.setString(1, deal.stage.name)
                ps.setObject(2, deal.loiExecutedDate?.toPGObject())
                ps.setObject(3, deal.contractExecutedDate?.toPGObject())
                ps.setObject(4, deal.diligenceExpirationDate?.toPGObject())
                ps.setObject(5, deal.initialClosingDate?.toPGObject())
                ps.setObject(6, deal.outsideClosingDate?.toPGObject())
                ps.setNullableString(7, deal.type.name)
                ps.setNullableString(8, deal.vertical)
                ps.setNullableBigDecimal(9, deal.lease.rent)
                ps.setNullableString(10, deal.lease.type)
                ps.setNullableFloat(11, deal.lease.rentIncrease)
                ps.setNullableFloat(12, deal.lease.increaseEveryYear)
                ps.setNullableLong(13, deal.lease.length)
                ps.setNullableLong(14, deal.lease.expirationYear)
                ps.setNullableLong(15, deal.lease.numberOfOptions)
                ps.setNullableLong(16, deal.lease.optionLengths)
                ps.setNullableFloat(17, deal.lease.rentCpi)
                ps.setNullableString(18, deal.lease.rentStepType?.name)
                ps.setNullableString(19, deal.guaranteeType?.name)
                ps.setNullableLong(20, deal.lease.closingPeriod)
                ps.setNullableLong(21, deal.lease.closingPeriodExtension)
                ps.setNullableLong(22, deal.lease.dueDiligenceNumber)
                ps.setObject(23, now)
                ps.setString(24, deal.status.name)
                ps.setObject(25, deal.evaluationDueDate?.toPGObject())
                ps.setObject(26, deal.underwritingDueDate?.toPGObject())
                ps.setNullableString(27, deal.omFileId)
                ps.setNullableString(28, deal.sourceType?.name)
                ps.setObject(29, deal.tags.toJsonPGObject())
                ps.setLong(30, deal.leaderId)
                ps.setNullableBigDecimal(31, deal.offerPrice)
                ps.setNullableBigDecimal(32, deal.earnestMoneyDeposit)
                ps.setNullableBigDecimal(33, deal.extensionDeposit)
                ps.setLong(34, deal.propertyId)
                ps.setNullableString(35, deal.buyerCompanyName)
                ps.setNullableString(36, deal.brokerCompanyName)
                ps.setNullableString(37, deal.sellerCompanyName)
                ps.setObject(38, deal.lease.date?.toPGObject())
                ps.setLong(39, deal.id)
            }
        }.toDeal()
    }

    override fun updateDealSchemaData(dealId: Long, dealSchemaData: SchemaData): Deal {
        return OffsetDateTime.now().let { now ->
            sqlClient.update(
                query = """
                    UPDATE deal SET ${
                listOf(
                    Fields.DEAL_SCHEMA_ID,
                    Fields.DEAL_SCHEMA_DATA,
                    Fields.UPDATED_AT
                ).joinToString(",") { "$it = ?" }
                }
                    WHERE ${Fields.ID} = ?
                    RETURNING ${selectFields()};
                """.trimIndent(),
                clazz = DealDbModel::class.java,
                messageKey = "UPDATE_DEAL"
            ) { ps ->

                ps.setLong(1, dealSchemaData.schemaId)
                ps.setObject(2, dealSchemaData.data.toJsonPGObject())
                ps.setObject(3, now)
                ps.setLong(4, dealId)
            }
        }.toDeal()
    }

    override fun saveCalendarId(dealId: Long, calendarId: String?) {
        val query = """
            UPDATE deal
            SET ${Fields.CALENDAR_ID} = ?
            WHERE ${Fields.ID} = ?
        """.trimIndent()
        sqlClient.update(
            query = query
        ) {
                ps ->
            ps.setNullableString(1, calendarId)
            ps.setLong(2, dealId)
        }
    }

    override fun getCalendarId(dealId: Long): String? =
        sqlClient.getAll(
            query = """
                SELECT ${Fields.CALENDAR_ID}
                FROM deal 
                WHERE ${Fields.ID} = ?
            """.trimIndent(),
            params = listOf(dealId),
            mapHandler = { response ->
                response["calendar_id"]?.toString()
            }
        ).first()

    enum class Fields {
        ID,
        PROPERTY_ID,
        STAGE,
        LOI_EXECUTED_DATE,
        CONTRACT_EXECUTED_DATE,
        DILIGENCE_EXPIRATION_DATE,
        INITIAL_CLOSING_DATE,
        OUTSIDE_CLOSING_DATE,
        UPDATED_AT,
        CREATED_AT,
        DEAL_TYPE,
        LEASE_EXPIRATION_YEAR,
        LEASE_LENGTH,
        LEASE_NUMBER_OF_OPTIONS,
        LEASE_OPTION_LENGTHS,
        LEASE_RENT,
        LEASE_RENT_CPI,
        LEASE_RENT_INCREASE,
        LEASE_INCREASE_EVERY_YEAR,
        LEASE_RENT_STEP_TYPE,
        LEASE_TYPE,
        TENANT_NAME,
        VERTICAL,
        GUARANTEE_TYPE,
        LEASE_DUE_DILIGENCE_NUMBER,
        LEASE_CLOSING_PERIOD,
        LEASE_CLOSING_PERIOD_EXTENSION,
        STATUS,
        EVALUATION_DUE_DATE,
        UNDERWRITING_DUE_DATE,
        DEAL_SCHEMA_ID,
        DEAL_SCHEMA_DATA,
        OM_FILE_ID,
        SOURCE_TYPE,
        TAGS,
        LEADER_ID,
        OFFER_PRICE,
        EARNEST_MONEY_DEPOSIT,
        EXTENSION_DEPOSIT,
        CALENDAR_ID,
        ORGANIZATION_ID,
        BUYER_COMPANY_NAME,
        BROKER_COMPANY_NAME,
        SELLER_COMPANY_NAME,
        LEASE_DATE,
        FIRST_PASS_ID;

        fun withPrefix(prefix: String?) = if (prefix.isNullOrEmpty()) "$this" else "$prefix.$this"
    }

    companion object {
        private val insertFields: Pair<String, String> = Fields.values()
            .filter { !setOf(Fields.ID, Fields.CALENDAR_ID).contains(it) }
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private val updateFields = setOf(
            Fields.STAGE,
            Fields.LOI_EXECUTED_DATE,
            Fields.CONTRACT_EXECUTED_DATE,
            Fields.DILIGENCE_EXPIRATION_DATE,
            Fields.INITIAL_CLOSING_DATE,
            Fields.OUTSIDE_CLOSING_DATE,
            Fields.DEAL_TYPE,
            Fields.VERTICAL,
            Fields.LEASE_RENT,
            Fields.LEASE_TYPE,
            Fields.LEASE_RENT_INCREASE,
            Fields.LEASE_INCREASE_EVERY_YEAR,
            Fields.LEASE_LENGTH,
            Fields.LEASE_EXPIRATION_YEAR,
            Fields.LEASE_NUMBER_OF_OPTIONS,
            Fields.LEASE_OPTION_LENGTHS,
            Fields.LEASE_RENT_CPI,
            Fields.LEASE_RENT_STEP_TYPE,
            Fields.GUARANTEE_TYPE,
            Fields.LEASE_CLOSING_PERIOD,
            Fields.LEASE_CLOSING_PERIOD_EXTENSION,
            Fields.LEASE_DUE_DILIGENCE_NUMBER,
            Fields.UPDATED_AT,
            Fields.STATUS,
            Fields.EVALUATION_DUE_DATE,
            Fields.UNDERWRITING_DUE_DATE,
            Fields.OM_FILE_ID,
            Fields.SOURCE_TYPE,
            Fields.TAGS,
            Fields.LEADER_ID,
            Fields.OFFER_PRICE,
            Fields.EARNEST_MONEY_DEPOSIT,
            Fields.EXTENSION_DEPOSIT,
            Fields.PROPERTY_ID,
            Fields.BUYER_COMPANY_NAME,
            Fields.BROKER_COMPANY_NAME,
            Fields.SELLER_COMPANY_NAME,
            Fields.LEASE_DATE
        ).joinToString(",") { "$it = ?" }

        private fun hasFindingsSubquery(prefix: String? = null) =
            ", EXISTS (SELECT 1 FROM deal_finding df WHERE df.deal_id = ${if (prefix.isNullOrEmpty()) "id" else "$prefix.id"} and df.status = 'OPEN') as has_findings"

        fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }.plus(hasFindingsSubquery(prefix))
    }
}
