package realestate.unlock.dealroom.api.repository.database.task

import realestate.unlock.dealroom.api.core.entity.dashboard.TaskSearch
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedInput
import realestate.unlock.dealroom.api.core.entity.property.Address
import realestate.unlock.dealroom.api.core.entity.task.Task
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.extension.mapTo
import realestate.unlock.dealroom.api.core.repository.task.TaskSearchRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.getQueryFiltersAndParams
import java.time.LocalDate
import java.time.OffsetDateTime
import javax.inject.Inject

class TaskSearchDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : TaskSearchRepository {

    override fun search(input: PaginatedInput, memberId: Long?, onlyEnabledTasks: Boolean): List<TaskSearch> {
        val query = """
        SELECT 
        	ta.id as task_id, 
        	ta.title, 
        	ta.due_date,
        	ta.status_key as task_status,
            ta.type_key as task_type_key,
            ta.visibility,
        	ca.stage,
            ta.assigned_team,
        	pr.type as property_type,
        	pr.address_street,
        	pr.address_city,
        	pr.address_state,
        	de.id as deal_id,
            de.stage as deal_stage,
            de.status as deal_status,
            me.id as member_id,
            me.type_key  as member_type_key,
            me.company_name  as member_company_name,
            me.first_name as member_first_name,
            me.last_name as member_last_name,
            me.address as member_address,
            me.phone_number as member_phone_number,
            me.email as member_email,
            me.updated_at as member_updated_at,
            me.created_at as member_created_at,
            me.enabled as member_enabled,
            me.auth_id as member_auth_id,
            me.walk_through_done as member_walk_through_done,
            me.organization_id as member_organization_id
        FROM 
        	task ta INNER JOIN deal_category dc
        		ON ta.deal_category_id = dc.id
        	INNER JOIN category ca
        		ON dc.category_key = ca.key
        	INNER JOIN deal de
        		ON dc.deal_id = de.id
        	INNER JOIN property pr  
        		ON de.property_id = pr.id
            INNER JOIN member me
                ON ta.assigned_buyer_id =  me.id
        WHERE 1 = 1
            ${ if (onlyEnabledTasks) "AND ta.enabled = true" else "" }
            ${ if (memberId != null) "AND ta.assigned_buyer_id = $memberId" else "" }
        ORDER BY ta.id
        """.trimIndent()

        val (where, params) = input.filters.getQueryFiltersAndParams()
        return sqlClient.getAll(
            query = query,
            params = params,
            size = input.size,
            offset = input.offset,
            orderBy = input.orderBy,
            order = input.order.name,
            where = where,
            withTotalCount = input.calculateTotal
        ) { result ->
            result.mapTo(SearchDbModel::class.java).toTaskSearch()
        }
    }

    data class SearchDbModel(
        val taskId: Long,
        val title: String,
        val dueDate: LocalDate?,
        val taskStatus: String,
        val stage: String,
        val assignedTeam: String,
        val addressStreet: String,
        val addressCity: String,
        val addressState: String,
        val dealId: Long,
        val dealStage: Stage,
        val totalCount: Int,
        val taskTypeKey: String,
        val memberId: Long,
        val memberTypeKey: String,
        val memberCompanyName: String,
        val memberFirstName: String,
        val memberLastName: String,
        val memberAddress: String?,
        val memberPhoneNumber: String?,
        val memberEmail: String,
        val memberUpdatedAt: OffsetDateTime,
        val memberCreatedAt: OffsetDateTime,
        val memberEnabled: Boolean,
        val memberAuthId: String?,
        val memberWalkThroughDone: Boolean,
        val memberOrganizationId: String,
        val visibility: String
    ) {
        fun toTaskSearch() =
            TaskSearch(
                taskId = this.taskId,
                title = this.title,
                dueDate = this.dueDate,
                taskStatus = TaskStatus.valueOf(this.taskStatus.uppercase()),
                stage = Stage.valueOf(this.stage.uppercase()),
                assignedTeam = MemberDealTeam.valueOf(this.assignedTeam.uppercase()),
                assignedBuyer = Member(
                    id = memberId,
                    typeKey = memberTypeKey,
                    firstName = memberFirstName,
                    lastName = memberLastName,
                    companyName = memberCompanyName,
                    address = memberAddress,
                    phoneNumber = memberPhoneNumber,
                    email = memberEmail,
                    createdAt = memberCreatedAt,
                    updatedAt = memberUpdatedAt,
                    enabled = memberEnabled,
                    authId = memberAuthId,
                    walkThroughDone = memberWalkThroughDone,
                    organizationId = memberOrganizationId
                ),
                address = Address(
                    street = this.addressStreet,
                    city = this.addressCity,
                    state = this.addressState,
                    coordinates = null,
                    zip = "",
                    apartment = null
                ),
                dealId = this.dealId,
                dealStage = this.dealStage,
                totalCount = this.totalCount,
                taskTypeKey = this.taskTypeKey,
                visibility = Task.Visibility.valueOf(this.visibility.uppercase())
            )
    }
}
