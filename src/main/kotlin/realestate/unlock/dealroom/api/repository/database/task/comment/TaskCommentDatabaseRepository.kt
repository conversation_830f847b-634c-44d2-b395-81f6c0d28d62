package realestate.unlock.dealroom.api.repository.database.task.comment

import realestate.unlock.dealroom.api.core.entity.task.comment.TaskComment
import realestate.unlock.dealroom.api.core.entity.task.comment.TaskCommentToSave
import realestate.unlock.dealroom.api.core.repository.task.comment.TaskCommentRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.member.MemberFields
import java.time.OffsetDateTime
import javax.inject.Inject

class TaskCommentDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : TaskCommentRepository {

    override fun findByTaskId(taskId: Long): List<TaskComment> {
        val query = """
            SELECT 
                ${Fields.ID.withPrefix("tc")},
                ${Fields.MEMBER_ID.withPrefix("tc")},
                ${Fields.TASK_ID.withPrefix("tc")},
                ${Fields.COMMENT.withPrefix("tc")},
                ${MemberFields.FIRST_NAME.withPrefix("m", "member")},
                ${MemberFields.LAST_NAME.withPrefix("m", "member")},
                ${MemberFields.TYPE_KEY.withPrefix("m", "member")},
                ${Fields.CREATED_AT.withPrefix("tc")}
            FROM task_comment tc
            JOIN member m
            ON tc.member_id = m.id
            WHERE tc.task_id = ?
            ORDER BY ${Fields.CREATED_AT.withPrefix("tc")} DESC
        """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = listOf(taskId),
            clazz = TaskCommentDbModel::class.java
        ).map { it.toTaskComment() }
    }

    override fun save(input: TaskCommentToSave) {
        val query = """
            INSERT INTO task_comment (
                ${Fields.TASK_ID},
                ${Fields.MEMBER_ID},
                ${Fields.COMMENT},
                ${Fields.CREATED_AT}
            )
            VALUES (?, ?, ?, ?)
        """.trimIndent()

        sqlClient.update(
            query = query
        ) { ps ->
            ps.setLong(1, input.taskId)
            ps.setLong(2, input.memberId)
            ps.setString(3, input.comment)
            ps.setObject(4, OffsetDateTime.now())
        }
    }

    enum class Fields {
        ID,
        TASK_ID,
        MEMBER_ID,
        COMMENT,
        CREATED_AT;

        fun withPrefix(prefix: String?) = if (prefix.isNullOrEmpty()) "$this" else "$prefix.$this"
    }
}
