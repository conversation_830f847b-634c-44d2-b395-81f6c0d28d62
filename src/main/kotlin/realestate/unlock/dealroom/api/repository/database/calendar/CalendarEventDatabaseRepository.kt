package realestate.unlock.dealroom.api.repository.database.calendar

import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput
import realestate.unlock.dealroom.api.core.entity.calendar.model.DealCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.calendar.model.DocumentCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.calendar.model.ReportCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.calendar.model.TaskCalendarEventModel
import realestate.unlock.dealroom.api.core.entity.deal.DealStatus
import realestate.unlock.dealroom.api.core.entity.deal.Stage
import realestate.unlock.dealroom.api.core.entity.deal.reports.ReportStatus
import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.entity.task.TaskStatus
import realestate.unlock.dealroom.api.core.repository.calendar.CalendarEventRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.getDateQueryFilter
import javax.inject.Inject

class CalendarEventDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : CalendarEventRepository {

    override fun findDealsEvents(input: CalendarEventsInput): List<DealCalendarEventModel> {
        val query = """
            SELECT d.id as deal_id, d.stage, d.diligence_expiration_date, d.initial_closing_date, ${getPropertyFields()}
            FROM deal d
            JOIN property p ON p.id = d.property_id
            ${if (input.memberId != null) "JOIN deal_member dm on d.id = dm.deal_id" else ""}
            WHERE
                d.status = '${DealStatus.ACTIVE}' AND
                (
                    (
                        ${input.getDateQueryFilter("d.diligence_expiration_date")}
                        AND
                        d.stage NOT IN ('${Stage.CLOSING}', '${Stage.POST_CLOSING}')
                    )
                    OR
                    (
                        ${input.getDateQueryFilter("d.initial_closing_date")}
                        AND
                        d.stage != '${Stage.POST_CLOSING}'
                    )
                )
                ${getCommonFilters(input)}
        """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = getCommonParams(input),
            clazz = DealCalendarEventModel::class.java
        )
    }

    override fun findReportsEvents(input: CalendarEventsInput): List<ReportCalendarEventModel> {
        val query = """
            SELECT dr.id, dr.deal_id, dr.type, dr.report_name, dr.expected_date, ${getPropertyFields()}
            FROM deal_report dr
            INNER JOIN deal d ON d.id = dr.deal_id
            ${if (input.memberId != null) "JOIN deal_member dm on dr.deal_id = dm.deal_id" else ""}
            JOIN property p ON p.id = d.property_id
            WHERE ${input.getDateQueryFilter("dr.expected_date")}
            AND d.status = '${DealStatus.ACTIVE}'
            AND dr.status != '${ReportStatus.REVIEWED}'
            ${getCommonFilters(input)}
        """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = getCommonParams(input),
            clazz = ReportCalendarEventModel::class.java
        )
    }

    override fun findDocumentsEvents(input: CalendarEventsInput): List<DocumentCalendarEventModel> {
        return sqlClient.getAll(
            query = """
                SELECT dd.id, dd.deal_id, dd.expected_by, dd.type,  ${getPropertyFields()}
                FROM deal_document dd
                JOIN deal d on dd.deal_id = d.id
                JOIN property p on d.property_id = p.id
                ${if (input.memberId != null) "JOIN deal_member dm on dd.deal_id = dm.deal_id" else ""}
                WHERE 1=1
                AND dd.status != '${DocumentStatus.EXECUTED}'
                AND ${input.getDateQueryFilter("dd.expected_by")}
                ${getCommonFilters(input)}
            """.trimIndent(),
            params = getCommonParams(input),
            clazz = DocumentCalendarEventModel::class.java
        )
    }

    override fun findTasksForEvents(input: CalendarEventsInput): List<TaskCalendarEventModel> {
        val query = """
            SELECT t.id, t.title, t.due_date, t.priority,
            d.id as deal_id, ${getPropertyFields()}
            FROM task t
            JOIN deal_category dc ON t.deal_category_id = dc.id
            JOIN deal d ON dc.deal_id = d.id
            JOIN property p ON d.property_id = p.id
            ${if (input.memberId != null) "JOIN deal_member dm on dm.deal_id = d.id" else ""}
            WHERE t.enabled = true
            AND d.status = '${DealStatus.ACTIVE}'
            AND t.status_key NOT IN ('${TaskStatus.DONE.key}', '${TaskStatus.REJECTED.key}')
            AND ${input.getDateQueryFilter("t.due_date")}
            ${getCommonFilters(input)}
        """.trimIndent()
        return sqlClient.getAll(
            query = query,
            clazz = TaskCalendarEventModel::class.java,
            params = getCommonParams(input)
        )
    }

    private fun getPropertyFields() = "p.name as property_name, p.address_street, p.address_apartment, p.address_city, p.address_state, p.address_zip"

    private fun getCommonParams(input: CalendarEventsInput) = listOfNotNull(
        input.organizationId,
        input.dealId,
        input.memberId,
        input.propertyType?.name,
        input.propertyTitle?.let { "%$it%" },
        input.propertyTitle?.let { "%$it%" }
    )

    private fun getCommonFilters(input: CalendarEventsInput) = """
        AND d.organization_id = ?
        ${if (input.dealId != null) "AND d.id = ?" else ""}
        ${if (input.memberId != null) "AND dm.member_id = ?" else ""}
        ${if (input.propertyType != null) "AND p.type = ?" else ""}
        ${if (input.propertyTitle != null) "AND (p.name ILIKE ? OR p.address_street ILIKE ?)" else ""}
    """.trimIndent()
}
