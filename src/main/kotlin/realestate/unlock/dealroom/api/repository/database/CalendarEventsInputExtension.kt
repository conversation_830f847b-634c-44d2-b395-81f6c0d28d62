package realestate.unlock.dealroom.api.repository.database

import realestate.unlock.dealroom.api.core.entity.calendar.CalendarEventsInput

fun CalendarEventsInput.getDateQueryFilter(field: String): String {
    val fromFilter = this.from?.let { "$field >= '$it' AND " } ?: ""
    val dateToUntilDays = this.from?.let { "DATE '$it'" } ?: "CURRENT_DATE"
    val toFilter = "$field < ($dateToUntilDays + ${this.untilDays}) "
    return "$fromFilter $toFilter"
}
