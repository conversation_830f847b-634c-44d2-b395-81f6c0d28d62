package realestate.unlock.dealroom.api.repository.database.member

enum class MemberFields(val updateField: <PERSON>olean = true) {
    ID(false),
    TYPE_KEY,
    COMPANY_NAME,
    FIRST_NAME,
    LAST_NAME,
    ADDRESS,
    PHONE_NUMBER,
    EMAIL,
    UPDATED_AT,
    CREATED_AT(false),
    ENABLED,
    AUTH_ID,
    WALK_THROUGH_DONE,
    ORGANIZATION_ID;

    fun withPrefix(prefix: String, variableNamePrefix: String) = "$prefix.$this as ${variableNamePrefix}_$this"
}
