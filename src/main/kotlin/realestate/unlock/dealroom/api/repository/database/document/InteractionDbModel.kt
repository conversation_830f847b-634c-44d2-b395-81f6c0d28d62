package realestate.unlock.dealroom.api.repository.database.document

import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.entity.file.File
import java.time.OffsetDateTime

data class InteractionDbModel(
    val id: Long,
    val roundId: Long,
    val type: Interaction.Type,
    val comment: String?,
    val date: OffsetDateTime,
    val memberId: Long?
) {
    fun toInteraction(files: List<File>) =
        Interaction(
            id = id,
            roundId = roundId,
            type = type,
            comment = comment,
            date = date,
            memberId = memberId,
            files = files
        )
}
