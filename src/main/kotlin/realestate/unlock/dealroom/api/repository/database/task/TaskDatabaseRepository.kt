package realestate.unlock.dealroom.api.repository.database.task

import realestate.unlock.dealroom.api.core.entity.search.SearchFieldParam
import realestate.unlock.dealroom.api.core.entity.task.*
import realestate.unlock.dealroom.api.core.extension.mapTo
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.*
import realestate.unlock.dealroom.api.repository.database.member.MemberFields
import java.time.OffsetDateTime
import javax.inject.Inject

class TaskDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : TaskRepository {

    override fun save(newTask: NewTaskToSave): Task {
        val (fields, questionMarks) = insertFields
        val query = """
        insert into task ($fields)
        values ($questionMarks)
        returning
            ${Fields.ID}
        """.trimIndent()

        return OffsetDateTime.now().let { now ->
            sqlClient.update(
                query = query,
                params = listOf(
                    newTask.dealCategoryId,
                    newTask.assignedBuyerId,
                    newTask.assignedTeam.name,
                    newTask.templateKey,
                    newTask.typeKey,
                    newTask.statusKey,
                    newTask.title,
                    newTask.description,
                    newTask.formSchema.toJsonPGObject(),
                    newTask.dueDate?.toPGObject(),
                    newTask.priority.value.name,
                    newTask.priority.sorting,
                    now,
                    now,
                    newTask.data.toJsonPGObject(),
                    newTask.required,
                    newTask.afterClosing,
                    newTask.visibility.name,
                    newTask.taskExternalDataDefinition?.dueDate,
                    newTask.taskExternalDataDefinition?.formData.toJsonPGObject()
                )
            ) { rs ->
                rs.next()
                findById(rs.getLong(1))
            }
        }
    }

    override fun update(taskToUpdate: TaskToUpdate): Task =
        sqlClient.update(
            query = """
        update task set $updateFields
        where ${Fields.ID} = ?
            """.trimIndent()
        ) { ps ->
            ps.setLong(1, taskToUpdate.dealCategoryId)
            ps.setLong(2, taskToUpdate.assignedBuyerId)
            ps.setString(3, taskToUpdate.assignedTeam.name)
            ps.setString(4, taskToUpdate.statusKey)
            ps.setString(5, taskToUpdate.title)
            ps.setString(6, taskToUpdate.description)
            ps.setNullableString(7, taskToUpdate.rejectionReason)
            ps.setObject(8, taskToUpdate.dueDate?.toPGObject())
            ps.setObject(9, taskToUpdate.attachedForm.toJsonPGObject())
            ps.setObject(10, taskToUpdate.data.toJsonPGObject())
            ps.setObject(11, OffsetDateTime.now())
            ps.setBoolean(12, taskToUpdate.enabled)
            ps.setLong(13, taskToUpdate.id)
        }.let { findById(taskToUpdate.id) }

    override fun findById(taskId: Long): Task {
        val query = """
        $findTaskBaseQuery
        WHERE ${Fields.ID.withPrefix()} = ?
        """.trimIndent()

        return sqlClient.getOneOrFail(
            query = query,
            messageKey = "GET_TASK_BY_ID",
            params = listOf(
                taskId
            )
        ) { result ->
            result
                .mapTo(TaskDbModel::class.java)
                .toTask()
        }
    }

    override fun findByDealCategories(categoriesIds: List<Long>): List<Task> {
        val query = """
        $findTaskBaseQuery
        WHERE ${Fields.DEAL_CATEGORY_ID.withPrefix()} = ANY(?)
        """.trimIndent()
        return sqlClient.getAll(
            query = query,
            clazz = TaskDbModel::class.java,
            params = listOf(categoriesIds.toTypedArray())
        ).map { it.toTask() }
    }

    override fun findByDeal(dealId: Long, filters: Map<String, SearchFieldParam>): List<Task> {
        val query = """
        $findTaskBaseQuery
         JOIN deal_category dc ON ${Fields.DEAL_CATEGORY_ID.withPrefix("t")} = dc.id
        WHERE dc.deal_id = $dealId
        order by ${Fields.PRIORITY.withPrefix("t")}, ${Fields.SORTING.withPrefix("t")}
        """.trimIndent()
        val (where, params) = filters.getQueryFiltersAndParams()
        return sqlClient.getAll(
            query = query,
            where = where,
            clazz = TaskDbModel::class.java,
            params = params
        ).map { it.toTask() }
    }

    override fun findDealTasksWithExternalDueDate(dealId: Long): List<Task> {
        val query = """
        $findTaskBaseQuery
         JOIN deal_category dc ON ${Fields.DEAL_CATEGORY_ID.withPrefix("t")} = dc.id
        WHERE dc.deal_id = $dealId AND ${Fields.DUE_DATE_EXTERNAL_DATA_DEFINITION} IS NOT NULL
        """.trimIndent()
        return sqlClient.getAll(
            query = query,
            clazz = TaskDbModel::class.java,
            params = emptyList()
        ).map { it.toTask() }
    }

    override fun findByFileId(kFileId: String): Task {
        val query = """
            $findTaskBaseQuery
            JOIN task_file tf ON tf.task_id = ${Fields.ID.withPrefix("t")}
            WHERE tf.k_file_id = ?
        """.trimIndent()

        return sqlClient.getOneOrFail(
            query = query,
            clazz = Task::class.java,
            params = listOf(kFileId),
            messageKey = "GET_TASK_BY_FILE"
        )
    }

    override fun findByDealIdAndTypeKey(dealId: Long, typeKey: String): Task? {
        val query = """
            $findTaskBaseQuery
            JOIN deal_category dc ON ${Fields.DEAL_CATEGORY_ID.withPrefix("t")} = dc.id
            WHERE ${Fields.TYPE_KEY.withPrefix("t")} = ? AND dc.deal_id = ?
        """.trimIndent()
        return sqlClient.get(
            query = query,
            params = listOf(typeKey, dealId),
            clazz = TaskDbModel::class.java
        )?.toTask()
    }

    private enum class Fields {
        ID,
        DEAL_CATEGORY_ID,
        ASSIGNED_BUYER_ID,
        ASSIGNED_TEAM,
        TEMPLATE_KEY,
        TYPE_KEY,
        STATUS_KEY,
        TITLE,
        DESCRIPTION,
        ATTACHED_FORM_DATA,
        FORM_SCHEMA,
        DUE_DATE,
        PRIORITY,
        SORTING,
        UPDATED_AT,
        CREATED_AT,
        REJECTION_REASON,
        DATA,
        REQUIRED,
        PRIORITIZE_AFTER_CLOSING,
        VISIBILITY,
        ENABLED,
        DUE_DATE_EXTERNAL_DATA_DEFINITION,
        FORM_EXTERNAL_DATA_DEFINITION;

        fun withPrefix(prefix: String = "t") = "$prefix.$this"
    }

    companion object {
        private val findTaskBaseQuery: String = """
        SELECT  ${selectFields(taskPrefix = "t", memberPrefix = "m")}
        FROM task t JOIN member m 
            ON ${Fields.ASSIGNED_BUYER_ID.withPrefix("t")} = m.id
        """.trimIndent()

        private fun selectFields(
            taskPrefix: String,
            memberPrefix: String
        ): String = Fields.values()
            .joinToString(",") { it.withPrefix(taskPrefix) }
            .plus(", ")
            .plus(
                MemberFields.values().joinToString(",") { it.withPrefix("$memberPrefix", "BUYER") }
            )

        private val insertFields: Pair<String, String> = Fields.values()
            .filter {
                it !in listOf(
                    Fields.ID,
                    Fields.ATTACHED_FORM_DATA,
                    Fields.REJECTION_REASON,
                    Fields.ENABLED
                )
            }
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private val updateFields = setOf(
            Fields.DEAL_CATEGORY_ID,
            Fields.ASSIGNED_BUYER_ID,
            Fields.ASSIGNED_TEAM,
            Fields.STATUS_KEY,
            Fields.TITLE,
            Fields.DESCRIPTION,
            Fields.REJECTION_REASON,
            Fields.DUE_DATE,
            Fields.ATTACHED_FORM_DATA,
            Fields.DATA,
            Fields.UPDATED_AT,
            Fields.ENABLED
        ).joinToString(",") { "$it = ?" }
    }
}
