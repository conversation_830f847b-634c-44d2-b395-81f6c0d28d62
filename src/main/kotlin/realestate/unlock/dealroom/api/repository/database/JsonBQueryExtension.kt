package realestate.unlock.dealroom.api.repository.database

fun Enum<*>.jsonb<PERSON>ield(
    jsonPath: List<String>,
    operator: String,
    cast: String = ""
): String = """(${
listOf(this.name).plus(jsonPath.map { "'$it'" })
    .let { fields ->
        fields.dropLast(1).joinToString(
            separator = "->"
        ) { it } + "->>${fields.last()})$cast"
    }
} $operator ?
""".trimIndent()
