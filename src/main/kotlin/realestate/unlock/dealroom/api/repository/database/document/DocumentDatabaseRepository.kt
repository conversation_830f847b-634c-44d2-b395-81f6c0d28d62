package realestate.unlock.dealroom.api.repository.database.document

import realestate.unlock.dealroom.api.core.entity.document.Document
import realestate.unlock.dealroom.api.core.entity.document.DocumentType
import realestate.unlock.dealroom.api.core.repository.document.DocumentRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.setNullableLong
import javax.inject.Inject

class DocumentDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : DocumentRepository {

    override fun findByDealIdAndDocumentType(dealId: Long, documentType: DocumentType): Document? {
        return sqlClient.get(
            query = """
                SELECT ${selectFields()} 
                FROM deal_document
                WHERE ${Fields.DEAL_ID} = ? AND ${Fields.TYPE} = ?
            """.trimIndent(),
            params = listOf(dealId, documentType.name),
            clazz = Document::class.java
        )
    }

    override fun findById(documentId: Long): Document? {
        return sqlClient.get(
            query = """
                SELECT ${selectFields()} 
                FROM deal_document
                WHERE ${Fields.ID} = ?
            """.trimIndent(),
            params = listOf(documentId),
            clazz = Document::class.java
        )
    }

    override fun save(document: Document) {
        val (fields, questionMarks) = insertFields
        sqlClient.update(
            query = """
                INSERT INTO deal_document ($fields)
                VALUES ($questionMarks)
                 returning ${selectFields()}
            """.trimIndent(),
            messageKey = "SAVE_DEAL_DOCUMENT",
            clazz = document::class.java
        ) { ps ->
            ps.setLong(1, document.id)
            ps.setLong(2, document.dealId)
            ps.setString(3, document.status.name)
            ps.setNullableLong(4, document.currentRoundId)
            ps.setObject(5, document.executedAt)
            ps.setObject(6, document.draftSubmittedAt)
            ps.setObject(7, document.expectedBy)
            ps.setObject(8, document.createdAt)
            ps.setObject(9, document.updatedAt)
            ps.setString(10, document.type.name)
        }
    }

    override fun update(document: Document) {
        sqlClient.update(
            query = """
                UPDATE deal_document
                SET $updateFields
                WHERE ${Fields.ID} = ?
                returning ${selectFields()}
            """.trimIndent(),
            clazz = document::class.java,
            messageKey = "UPDATE_DEAL_DOCUMENT"
        ) { ps ->
            ps.setLong(1, document.dealId)
            ps.setString(2, document.status.name)
            ps.setNullableLong(3, document.currentRoundId)
            ps.setObject(4, document.executedAt)
            ps.setObject(5, document.draftSubmittedAt)
            ps.setObject(6, document.expectedBy)
            ps.setObject(7, document.createdAt)
            ps.setObject(8, document.updatedAt)
            ps.setLong(9, document.id)
        }
    }

    override fun nextId() = sqlClient.nextId("seq_deal_document")

    private enum class Fields {
        ID,
        DEAL_ID,
        STATUS,
        CURRENT_ROUND_ID,
        EXECUTED_AT,
        DRAFT_SUBMITTED_AT,
        EXPECTED_BY,
        CREATED_AT,
        UPDATED_AT,
        TYPE
    }

    companion object {
        private val insertFields: Pair<String, String> = Fields.values()
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }

        private val updateFields = setOf(
            Fields.DEAL_ID,
            Fields.STATUS,
            Fields.CURRENT_ROUND_ID,
            Fields.EXECUTED_AT,
            Fields.DRAFT_SUBMITTED_AT,
            Fields.EXPECTED_BY,
            Fields.CREATED_AT,
            Fields.UPDATED_AT
        ).joinToString(",") { "$it = ?" }
    }
}
