package realestate.unlock.dealroom.api.repository.database.task.comment

import realestate.unlock.dealroom.api.core.entity.task.comment.TaskComment
import java.time.OffsetDateTime

data class TaskCommentDbModel(
    val id: Long,
    val taskId: Long,
    val memberId: Long,
    val comment: String,
    val memberFirstName: String,
    val memberLastName: String,
    val memberTypeKey: String,
    val createdAt: OffsetDateTime
) {
    fun toTaskComment(): TaskComment = TaskComment(
        id = this.id,
        taskId = this.taskId,
        memberId = this.memberId,
        comment = this.comment,
        memberName = "$memberFirstName $memberLastName",
        memberType = this.memberTypeKey,
        createdAt = this.createdAt
    )
}
