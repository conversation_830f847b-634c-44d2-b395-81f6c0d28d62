package realestate.unlock.dealroom.api.repository.database.document

import realestate.unlock.dealroom.api.core.entity.document.DocumentSigner
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.repository.document.DocumentSignRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.setNullableString
import java.time.OffsetDateTime
import javax.inject.Inject

class DocumentSignDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : DocumentSignRepository {
    override fun findByEnvelopeId(envelopeId: String): List<DocumentSigner> =
        sqlClient.getAll(
            query = """
                SELECT ${selectFields()}
                FROM deal_document_sign
                WHERE ${Fields.ENVELOPE_ID} = ?
            """.trimIndent(),
            clazz = DocumentSignDbModel::class.java,
            params = listOf(envelopeId)
        ).map(DocumentSignDbModel::toDocumentSigner)

    override fun findByDocumentId(documentId: Long): List<DocumentSigner> =
        sqlClient.getAll(
            query = """
                SELECT ${selectFields()}
                FROM deal_document_sign
                WHERE ${Fields.DOCUMENT_ID} = ?
            """.trimIndent(),
            clazz = DocumentSignDbModel::class.java,
            params = listOf(documentId)
        ).map(DocumentSignDbModel::toDocumentSigner)

    override fun save(signer: DocumentSigner) {
        val (fields, questionMarks) = insertFields
        sqlClient.update(
            query = """
                INSERT INTO deal_document_sign ($fields)
                VALUES ($questionMarks)
                 returning ${selectFields()}
            """.trimIndent(),
            messageKey = "SAVE_DEAL_DOCUMENT_SIGN",
            clazz = DocumentSignDbModel::class.java
        ) { ps ->
            ps.setLong(1, signer.documentId)
            ps.setNullableString(2, signer.envelopeId)
            ps.setNullableString(3, signer.docusignDocumentId)
            ps.setNullableString(4, signer.signerRecipientId)
            ps.setNullableString(5, signer.signerEmail)
            ps.setNullableString(6, signer.signerFirstName)
            ps.setNullableString(7, signer.signerLastName)
            ps.setString(8, signer.signerTeamType.key)
            ps.setObject(9, signer.createdAt)
        }
    }

    override fun update(signer: DocumentSigner) {
        sqlClient.update(
            query = """
                UPDATE deal_document_sign
                SET $updateFields
                WHERE ${Fields.DOCUMENT_ID} = ? AND ${Fields.SIGNER_TEAM_TYPE} = ?
                returning ${selectFields()}
            """.trimIndent(),
            clazz = DocumentSignDbModel::class.java,
            messageKey = "UPDATE_DEAL_DOCUMENT_SIGN"
        ) { ps ->
            ps.setNullableString(1, signer.envelopeId)
            ps.setNullableString(2, signer.docusignDocumentId)
            ps.setNullableString(3, signer.signerRecipientId)
            ps.setNullableString(4, signer.signerEmail)
            ps.setNullableString(5, signer.signerFirstName)
            ps.setNullableString(6, signer.signerLastName)
            ps.setObject(7, signer.signCompletedAt)
            ps.setLong(8, signer.documentId)
            ps.setString(9, signer.signerTeamType.key)
        }
    }

    private enum class Fields {
        DOCUMENT_ID,
        ENVELOPE_ID,
        DOCUSIGN_DOCUMENT_ID,
        SIGNER_RECIPIENT_ID,
        SIGNER_EMAIL,
        SIGNER_FIRST_NAME,
        SIGNER_LAST_NAME,
        SIGNER_TEAM_TYPE,
        SIGN_COMPLETED_AT,
        CREATED_AT
    }

    companion object {
        private val insertFields: Pair<String, String> = Fields.values()
            .filter { it != Fields.SIGN_COMPLETED_AT }
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }

        private val updateFields = setOf(
            Fields.ENVELOPE_ID,
            Fields.DOCUSIGN_DOCUMENT_ID,
            Fields.SIGNER_RECIPIENT_ID,
            Fields.SIGNER_EMAIL,
            Fields.SIGNER_FIRST_NAME,
            Fields.SIGNER_LAST_NAME,
            Fields.SIGN_COMPLETED_AT
        ).joinToString(",") { "$it = ?" }
    }

    data class DocumentSignDbModel(
        val documentId: Long,
        val envelopeId: String?,
        val docusignDocumentId: String?,
        val signerRecipientId: String,
        val signerEmail: String?,
        val signerFirstName: String?,
        val signerLastName: String?,
        val signerTeamType: String,
        val signCompletedAt: OffsetDateTime?,
        val createdAt: OffsetDateTime
    ) {

        fun toDocumentSigner() = DocumentSigner(
            documentId = documentId,
            envelopeId = envelopeId,
            docusignDocumentId = docusignDocumentId,
            signerRecipientId = signerRecipientId,
            signerEmail = signerEmail,
            signerFirstName = signerFirstName,
            signerLastName = signerLastName,
            signerTeamType = MemberDealTeam.valueOf(signerTeamType.uppercase()),
            signCompletedAt = signCompletedAt,
            createdAt = createdAt
        )
    }
}
