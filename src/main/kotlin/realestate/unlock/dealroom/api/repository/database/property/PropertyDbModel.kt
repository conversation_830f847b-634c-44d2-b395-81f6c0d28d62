package realestate.unlock.dealroom.api.repository.database.property

import com.fasterxml.jackson.core.type.TypeReference
import org.postgresql.util.PGobject
import realestate.unlock.dealroom.api.core.entity.property.*
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.math.BigDecimal
import java.time.OffsetDateTime

data class PropertyDbModel(
    val id: Long,
    val name: String?,
    val keywayId: String?,
    val addressStreet: String,
    val addressApartment: String?,
    val addressCity: String,
    val addressState: String,
    val addressZip: String,
    val addressLatitude: BigDecimal?,
    val addressLongitude: BigDecimal?,
    val yearBuilt: Int?,
    val squareFootage: BigDecimal?,
    val purchasePrice: BigDecimal?,
    val mainPhotoId: String?,
    val interiorPhotoId: String?,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
    val type: String,
    val brokerFirm: String?,
    val units: Long?,
    val averageSquareFootage: BigDecimal?,
    val occupancy: BigDecimal?,
    val parkingSpots: Int?,
    val parkingRatio: BigDecimal?,
    val owner: String?,
    val propertyManager: String?,
    val unitsMix: PGobject?
) {
    fun toProperty() = Property(
        id = id,
        name = name,
        address = Address(
            street = addressStreet,
            apartment = addressApartment,
            city = addressCity,
            state = addressState,
            zip = addressZip,
            coordinates = if (addressLatitude != null && addressLongitude != null) Coordinates(latitude = addressLatitude, longitude = addressLongitude) else null
        ),
        keywayId = keywayId,
        yearBuilt = yearBuilt,
        squareFootage = squareFootage,
        askingPrice = purchasePrice,
        createdAt = createdAt,
        updatedAt = updatedAt,
        mainPhotoId = mainPhotoId,
        interiorPhotoId = interiorPhotoId,
        type = PropertyType.valueOf(type),
        mainPhoto = null,
        interiorPhoto = null,
        multifamilyData = if (type != PropertyType.MULTIFAMILY.name) {
            null
        } else {
            MultifamilyData(
                brokerFirm = brokerFirm,
                units = units,
                averageSquareFootage = averageSquareFootage,
                occupancy = occupancy,
                parkingSpots = parkingSpots,
                parkingRatio = parkingRatio,
                owner = owner,
                propertyManager = propertyManager,
                unitsMix = unitsMix?.value?.let {
                    JsonMapper.decode(
                        it,
                        object : TypeReference<List<UnitsMix>>() {}
                    )
                } ?: listOf()

            )
        }
    )
}
