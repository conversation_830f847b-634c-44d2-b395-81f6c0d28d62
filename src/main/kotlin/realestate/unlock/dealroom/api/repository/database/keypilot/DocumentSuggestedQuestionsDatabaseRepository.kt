package realestate.unlock.dealroom.api.repository.database.keypilot

import realestate.unlock.dealroom.api.core.entity.keypilot.DocumentQuestion
import realestate.unlock.dealroom.api.core.repository.keypilot.DocumentSuggestedQuestionsRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import javax.inject.Inject

class DocumentSuggestedQuestionsDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : DocumentSuggestedQuestionsRepository {

    override fun find(document: String, documentType: String?): List<DocumentQuestion> {
        val query = """
            SELECT question, priority, ask_automatically
            FROM document_questions
            WHERE document = ? ${if (documentType != null) "AND document_type = ?" else ""}
            ORDER BY priority ASC
        """.trimIndent()

        return sqlClient.getAll(
            query = query,
            clazz = DocumentQuestion::class.java,
            params = listOfNotNull(document, documentType)
        )
    }
}
