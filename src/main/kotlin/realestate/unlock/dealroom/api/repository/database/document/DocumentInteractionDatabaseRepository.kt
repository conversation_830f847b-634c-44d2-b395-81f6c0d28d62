package realestate.unlock.dealroom.api.repository.database.document

import realestate.unlock.dealroom.api.core.entity.document.Interaction
import realestate.unlock.dealroom.api.core.entity.file.File
import realestate.unlock.dealroom.api.core.repository.document.DocumentInteractionRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.file.FileDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.setNullableLong
import realestate.unlock.dealroom.api.repository.database.setNullableString
import javax.inject.Inject

class DocumentInteractionDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : DocumentInteractionRepository {

    override fun save(interaction: Interaction) {
        val (fields, questionMarks) = insertFields

        sqlClient.update(
            query = """
                INSERT INTO deal_document_round_interaction ($fields)
                VALUES ($questionMarks)
                 returning ${selectFields()}
            """.trimIndent(),
            messageKey = "SAVE_DEAL_DOCUMENT_ROUND_INTERACTION",
            clazz = InteractionDbModel::class.java
        ) { ps ->
            ps.setLong(1, interaction.id)
            ps.setLong(2, interaction.roundId)
            ps.setNullableLong(3, interaction.memberId)
            ps.setNullableString(4, interaction.comment)
            ps.setObject(5, interaction.date)
            ps.setString(6, interaction.type.name)
        }
            .also {
                interaction.files
                    .forEach { file ->
                        saveInteractionFile(interactionId = interaction.id, fileId = file.id)
                    }
            }
    }

    override fun findById(id: Long): Interaction {
        return sqlClient.getOneOrFail(
            query = """
                SELECT ${selectFields()} 
                FROM deal_document_round_interaction
                WHERE ${Fields.ID} = ?
            """.trimIndent(),
            params = listOf(id),
            clazz = InteractionDbModel::class.java,
            messageKey = "GET_DEAL_DOCUMENT_ROUND_INTERACTION_BY_ID"
        )
            .let { it.toInteraction(files = getInteractionFiles(it.id)) }
    }

    override fun findByRoundIdAndDocumentId(roundId: Long, documentId: Long): List<Interaction> {
        return sqlClient.getAll(
            query = """
                SELECT ${selectFields("dd_ri")} 
                FROM deal_document_round_interaction dd_ri
                JOIN deal_document_round dd_r 
                ON dd_ri.${Fields.ROUND_ID} = dd_r.id
                WHERE dd_r.document_id = ? and dd_ri.${Fields.ROUND_ID} = ?
                ORDER BY dd_ri.${Fields.ROUND_ID}, dd_ri.${Fields.ID}
            """.trimIndent(),
            params = listOf(documentId, roundId),
            clazz = InteractionDbModel::class.java
        )
            .map { it.toInteraction(files = getInteractionFiles(it.id)) }
    }

    override fun findByDocumentIdAndFileId(documentId: Long, fileId: Long): List<Interaction> {
        return sqlClient.getAll(
            query = """
                SELECT ${selectFields("dd_ri")} 
                FROM deal_document_round_interaction dd_ri
                JOIN deal_document_round dd_r ON dd_ri.${Fields.ROUND_ID} = dd_r.id
                JOIN deal_document_round_interaction_file drif ON drif.interaction_id = dd_ri.id
                WHERE dd_r.document_id = ? AND drif.file_id = ?
                ORDER BY dd_ri.${Fields.ROUND_ID}, dd_ri.${Fields.ID}
            """.trimIndent(),
            params = listOf(documentId, fileId),
            clazz = InteractionDbModel::class.java
        )
            .map { it.toInteraction(files = getInteractionFiles(it.id)) }
    }

    override fun nextId() = sqlClient.nextId("seq_deal_document_round_interaction")

    private fun saveInteractionFile(interactionId: Long, fileId: Long) =
        sqlClient.update(
            query = """
                INSERT INTO deal_document_round_interaction_file (interaction_id, file_id)
                VALUES (?, ?)
            """.trimIndent(),
            params = listOf(interactionId, fileId)
        )

    private fun getInteractionFiles(interactionId: Long): List<File> =
        sqlClient.getAll(
            query = """
                SELECT ${FileDatabaseRepository.selectFields("f")}
                FROM file f JOIN deal_document_round_interaction_file if
                    ON f.${FileDatabaseRepository.Fields.ID} = if.file_id
                WHERE if.interaction_id = ?
            """.trimIndent(),
            clazz = File::class.java,
            params = listOf(interactionId)
        )

    private enum class Fields {
        ID,
        ROUND_ID,
        MEMBER_ID,
        COMMENT,
        DATE,
        TYPE
    }

    companion object {
        private val insertFields: Pair<String, String> = Fields.values()
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }
    }
}
