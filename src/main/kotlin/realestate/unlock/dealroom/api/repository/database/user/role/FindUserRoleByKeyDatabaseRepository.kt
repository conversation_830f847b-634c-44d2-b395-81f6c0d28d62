package realestate.unlock.dealroom.api.repository.database.user.role

import realestate.unlock.dealroom.api.core.entity.user.role.UserRole
import realestate.unlock.dealroom.api.core.repository.user.role.FindUserRoleByKeyRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import javax.inject.Inject

class FindUserRoleByKeyDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : FindUserRoleByKeyRepository {

    private val query = """
        select
            id,
            key,
            name
        from user_role
        where key = ?
    """.trimIndent()

    override fun find(typeKey: String): UserRole =
        sqlClient.getOneOrFail(
            query = query,
            clazz = UserRole::class.java,
            messageKey = "FIND_USER_ROLE_BY_KEY",
            params = listOf(typeKey)
        )
}
