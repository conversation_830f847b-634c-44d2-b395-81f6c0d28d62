package realestate.unlock.dealroom.api.repository.database.file

import realestate.unlock.dealroom.api.core.entity.file.gpt.FileQuestionHistory
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileQuestionType
import realestate.unlock.dealroom.api.core.repository.file.FileQuestionHistoryRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.repository.database.setNullableLong
import java.time.Instant
import javax.inject.Inject

class FileQuestionHistoryDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : FileQuestionHistoryRepository {

    override fun save(history: FileQuestionHistory) {
        val query = """
            INSERT INTO gpt_questions_history(
                k_file_id,
                question_type,
                question,
                answer,
                member_id,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?)
            RETURNING k_file_id, question_type, question, answer, member_id, created_at
        """.trimIndent()

        sqlClient.update(
            query = query,
            messageKey = "SAVE_FILE_QUESTION_HISTORY",
            clazz = FileQuestionHistoryDbModel::class.java
        ) { ps ->
            ps.setString(1, history.kFileId)
            ps.setString(2, history.questionType.name)
            ps.setString(3, history.question)
            ps.setString(4, history.answer)
            ps.setNullableLong(5, history.memberId)
            ps.setObject(6, history.createdAt.toEpochMilli())
        }
    }

    override fun get(fileId: String, memberId: Long): List<FileQuestionHistory> {
        val query = """
            SELECT 
                k_file_id,
                question_type,
                question,
                answer,
                member_id,
                created_at
            FROM gpt_questions_history
            WHERE k_file_id = ?
                AND (member_id = ? OR question_type = ?)
            ORDER BY created_at
        """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = listOfNotNull(fileId, memberId, FileQuestionType.GENERAL.name),
            clazz = FileQuestionHistoryDbModel::class.java
        ).map { it.toFileQuestionHistory() }
    }

    data class FileQuestionHistoryDbModel(
        val kFileId: String,
        val questionType: FileQuestionType,
        val question: String,
        val answer: String,
        val memberId: Long?,
        val createdAt: Long
    ) {
        fun toFileQuestionHistory() = FileQuestionHistory(
            kFileId = this.kFileId,
            questionType = this.questionType,
            question = this.question,
            answer = this.answer,
            memberId = this.memberId,
            createdAt = Instant.ofEpochMilli(createdAt)
        )
    }
}
