package realestate.unlock.dealroom.api.repository.database.document

data class DocumentRoundDbModel(
    val id: Long,
    val documentId: Long,
    val cleanVersionFileId: Long?,
    val redLineVersionFileId: Long?,
    val comment: String?,
    val memberId: Long?,
    val fileId: Long?,
    val documentType: DocumentType
) {
    enum class DocumentType {
        DRAFT_DOCUMENT,
        FINAL_DOCUMENT,
        EXECUTED_DOCUMENT
    }
}
