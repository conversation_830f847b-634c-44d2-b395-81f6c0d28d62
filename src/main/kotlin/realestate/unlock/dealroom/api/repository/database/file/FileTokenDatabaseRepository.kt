package realestate.unlock.dealroom.api.repository.database.file

import realestate.unlock.dealroom.api.core.entity.file.gpt.FileToken
import realestate.unlock.dealroom.api.core.repository.file.FileTokenRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import javax.inject.Inject

class FileTokenDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : FileTokenRepository {

    override fun save(fileToken: FileToken): FileToken {
        val query = """
            INSERT INTO file_gpt_token (k_file_id, token, status, file_type, deal_id)
            VALUES (?, ?, ?, ?, ?)
            ON CONFLICT (k_file_id) DO
            UPDATE SET k_file_id = EXCLUDED.k_file_id, token = EXCLUDED.token, status = EXCLUDED.status, file_type = EXCLUDED.file_type, deal_id = EXCLUDED.deal_id
            RETURNING k_file_id, token, status, file_type, deal_id
        """.trimIndent()

        return sqlClient.update(
            query = query,
            clazz = FileToken::class.java,
            messageKey = "SAVE_FILE_REPORT"
        ) { ps ->
            ps.setString(1, fileToken.kFileId)
            ps.setString(2, fileToken.token)
            ps.setString(3, fileToken.status.name)
            ps.setString(4, fileToken.fileType.name)
            ps.setLong(5, fileToken.dealId)
        }
    }

    override fun update(fileToken: FileToken): FileToken {
        val query = """
            UPDATE file_gpt_token
            SET status = ?
            WHERE k_file_id = ?
            RETURNING k_file_id, token, status, file_type, deal_id
        """.trimIndent()

        return sqlClient.update(
            query = query,
            clazz = FileToken::class.java,
            messageKey = "UPDATE_FILE_REPORT_STATUS"
        ) { ps ->
            ps.setString(1, fileToken.status.name)
            ps.setString(2, fileToken.kFileId)
        }
    }

    override fun findByFileId(fileId: String): FileToken? {
        val query = """
            SELECT k_file_id, token, status, file_type, deal_id
            FROM file_gpt_token
            WHERE k_file_id = ?
        """.trimIndent()

        return sqlClient.get(
            query = query,
            params = listOf(fileId),
            clazz = FileToken::class.java
        )
    }
}
