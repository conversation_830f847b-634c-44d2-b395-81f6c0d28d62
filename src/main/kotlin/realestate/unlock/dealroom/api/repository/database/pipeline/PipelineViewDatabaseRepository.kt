package realestate.unlock.dealroom.api.repository.database.pipeline

import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedInputByOrganization
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.core.entity.pipeline.PipelineView
import realestate.unlock.dealroom.api.core.exception.DuplicatedResourceException
import realestate.unlock.dealroom.api.core.exception.ResourceNotFoundException
import realestate.unlock.dealroom.api.core.repository.pipeline.PipelineViewRepository
import realestate.unlock.dealroom.api.core.repository.pipeline.PipelineViewToSave
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.infrastructure.client.database.exception.DuplicateKeyException
import realestate.unlock.dealroom.api.infrastructure.client.database.exception.NoRowFoundException
import realestate.unlock.dealroom.api.repository.database.*
import java.time.OffsetDateTime
import javax.inject.Inject

class PipelineViewDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : PipelineViewRepository {

    override fun findById(id: Long): PipelineView {
        return try {
            sqlClient.getOneOrFail(
                query = """
                SELECT ${selectFields()}
                FROM pipeline_view 
                WHERE ${Fields.ID} = ?
                """.trimIndent(),
                clazz = PipelineViewDbModel::class.java,
                params = listOf(id),
                messageKey = "GET_PIPELINE_VIEW_BY_ID"
            ).let(PipelineViewDbModel::toPipelineView)
        } catch (nf: NoRowFoundException) {
            throw ResourceNotFoundException(resourceName = "PipelineView", resourceId = id)
        }
    }
    override fun findAll(input: PaginatedInputByOrganization): PaginatedOutput<PipelineView> {
        val (where, params) = input.filters.getQueryFiltersAndParams()
        return sqlClient.getAll(
            query = """
                SELECT ${selectFields()} FROM pipeline_view
                WHERE ${Fields.ORGANIZATION_ID} = '${input.organizationId}'
            """.trimIndent(),
            params = params,
            size = input.size,
            offset = input.offset,
            orderBy = input.orderBy,
            order = input.order.name,
            where = where,
            withTotalCount = input.calculateTotal,
            clazz = PipelineViewDbModel::class.java
        ).let {
            PaginatedOutput(
                size = it.size,
                offset = input.offset,
                data = it.map(PipelineViewDbModel::toPipelineView),
                total = input.getTotalForResponse(it.firstOrNull()?.totalCount)
            )
        }
    }

    override fun delete(id: Long) {
        sqlClient.update(
            query = "DELETE FROM pipeline_view WHERE ${Fields.ID} = ?",
            params = listOf(id)
        )
    }

    override fun update(pipelineView: PipelineView): PipelineView {
        return OffsetDateTime.now().let { now ->
            sqlClient.update(
                query = """
                    UPDATE pipeline_view set $updateFields
                    WHERE ${Fields.ID} = ?
                    RETURNING ${selectFields()};
                """.trimIndent(),
                clazz = PipelineViewDbModel::class.java,
                messageKey = "UPDATE_PIPELINE_VIEW"
            ) { ps ->
                ps.setString(1, pipelineView.name)
                ps.setObject(2, pipelineView.propertyType.name)
                ps.setObject(3, pipelineView.data.toJsonPGObject())
                ps.setObject(4, now)
                ps.setLong(5, pipelineView.id)
            }
        }.let(PipelineViewDbModel::toPipelineView)
    }

    override fun create(input: PipelineViewToSave): PipelineView {
        val (fields, questionMarks) = insertFields
        return OffsetDateTime.now().let { now ->
            try {
                sqlClient.update(
                    query = """
                        INSERT INTO pipeline_view ($fields)
                        VALUES ($questionMarks)
                        RETURNING ${selectFields()};
                    """.trimIndent(),
                    clazz = PipelineViewDbModel::class.java,
                    messageKey = "SAVE_PIPELINE_VIEW"
                ) { ps ->
                    ps.setString(1, input.name)
                    ps.setObject(2, input.propertyType.name)
                    ps.setObject(3, input.data.toJsonPGObject())
                    ps.setObject(4, now)
                    ps.setObject(5, now)
                    ps.setString(6, input.organizationId)
                }
            } catch (e: DuplicateKeyException) {
                throw DuplicatedResourceException("Pipeline view with name '${input.name}' already exists", e)
            }
        }.let(PipelineViewDbModel::toPipelineView)
    }

    enum class Fields {
        ID,
        NAME,
        PROPERTY_TYPE,
        DATA,
        UPDATED_AT,
        CREATED_AT,
        ORGANIZATION_ID;
    }

    companion object {
        private val insertFields: Pair<String, String> = Fields.values()
            .filter { it != Fields.ID }
            .let {
                it.joinToString(",") to it.joinToString(",") { "?" }
            }

        private val updateFields = setOf(
            Fields.NAME,
            Fields.PROPERTY_TYPE,
            Fields.DATA,
            Fields.UPDATED_AT
        ).joinToString(",") { "$it = ?" }

        private fun selectFields(prefix: String? = null): String = Fields.values()
            .joinToString(",") {
                if (prefix.isNullOrEmpty()) "$it" else "$prefix.$it"
            }
    }
}
