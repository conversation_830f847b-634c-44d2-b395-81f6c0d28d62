package realestate.unlock.dealroom.api.repository.database

import realestate.unlock.dealroom.api.core.entity.search.*
import realestate.unlock.dealroom.api.core.exception.OperatorNotSupported

fun String.addPagination(
    size: Int,
    offset: Int,
    orderBy: String,
    order: String,
    where: String,
    withTotalCount: Boolean
) =
    """
        SELECT base_query.*, ${ if (withTotalCount) " COUNT(*) OVER () " else " -1 "} AS total_count  FROM ($this) AS base_query
        WHERE 1=1 ${if (where.isNullOrEmpty()) "" else " AND $where"}
        ORDER BY $orderBy $order
        OFFSET $offset
        LIMIT $size
    """.trimIndent()

fun String.addFilters(
    where: String
) =
    """
        SELECT base_query.*  FROM ($this) AS base_query
        WHERE 1=1 ${if (where.isNullOrEmpty()) "" else " AND $where"}
    """.trimIndent()

fun Map<String, SearchFieldParam>.getQueryFiltersAndParams(): Pair<String, List<Any>> {
    val queryWhere = ArrayList<String>()
    val queryParams = ArrayList<Any>()
    for ((field, condition) in this) {
        queryWhere.add(condition.getSqlQuery(field))
        queryParams.addAll(condition.getParamValues())
    }
    return Pair(queryWhere.joinToString(" AND "), queryParams)
}

fun SearchFieldParam.getSqlQuery(field: String) =
    when (this.getPaginatedOperator()) {
        DateTimeOperator.EQ, NumberOperator.EQ, StringOperator.EQ -> "$field = ? "
        DateTimeOperator.BETWEEN, NumberOperator.BETWEEN -> "$field BETWEEN ? AND ? "
        StringOperator.IN, NumberOperator.IN -> "$field IN (${this.getParamValues().joinToString(",") { "?" }}) "
        StringOperator.LIKE -> "$field ILIKE ? "
        DateTimeOperator.GT, NumberOperator.GT -> "$field > ? "
        DateTimeOperator.GTE, NumberOperator.GTE -> "$field >= ? "
        DateTimeOperator.LT, NumberOperator.LT -> "$field < ? "
        DateTimeOperator.LTE, NumberOperator.LTE -> "$field <= ? "
        JsonArrayOperator.IN -> "jsonb_exists_any($field, array[${this.getParamValues().joinToString(",") { "?" }}]) "
        else -> throw OperatorNotSupported()
    }
