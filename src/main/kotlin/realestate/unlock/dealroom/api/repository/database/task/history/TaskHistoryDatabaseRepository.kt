package realestate.unlock.dealroom.api.repository.database.task.history

import com.fasterxml.jackson.core.type.TypeReference
import org.postgresql.util.PGobject
import realestate.unlock.dealroom.api.core.entity.task.history.TaskHistory
import realestate.unlock.dealroom.api.core.entity.task.history.TaskHistoryToSave
import realestate.unlock.dealroom.api.core.extension.mapTo
import realestate.unlock.dealroom.api.core.repository.task.history.TaskHistoryRepository
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.infrastructure.client.database.extension.convertToMaps
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.time.OffsetDateTime
import javax.inject.Inject

class TaskHistoryDatabaseRepository @Inject constructor(
    private val sqlClient: SqlClient
) : TaskHistoryRepository {

    private val saveQuery = """
        insert into task_history (
            task_id,
            member_id,
            snapshot,
            created_at
        )
        values (?,?,?,?)
        returning
            id,
            task_id,
            member_id,
            snapshot,
            created_at
    """.trimIndent()

    override fun save(taskHistoryToSave: TaskHistoryToSave): TaskHistory =
        sqlClient.update(
            query = saveQuery,
            params = listOf(
                taskHistoryToSave.taskId,
                taskHistoryToSave.memberId,
                PGobject().apply {
                    this.type = "jsonb"
                    this.value = JsonMapper.encode(taskHistoryToSave.snapshot)
                },
                OffsetDateTime.now()
            )
        ) {
            it.convertToMaps()
                .first()
                .toMutableMap()
                .apply {
                    this["snapshot"] = JsonMapper.decode(
                        this["snapshot"].toString(),
                        object : TypeReference<Map<String, Any>>() {}
                    )
                }
                .mapTo(TaskHistory::class.java)
        }

    private val findQuery = """
        select
            id,
            task_id,
            member_id,
            snapshot,
            created_at
        from task_history
        where task_id = ?
        order by id
    """.trimIndent()

    override fun findByTaskId(taskId: Long): List<TaskHistory> =
        sqlClient.update(
            query = findQuery,
            params = listOf(taskId)
        ) {
            it.convertToMaps()
                .map { mappedRecord ->
                    mappedRecord.toMutableMap()
                        .apply {
                            this["snapshot"] = JsonMapper.decode(
                                this["snapshot"].toString(),
                                object : TypeReference<Map<String, Any>>() {}
                            )
                        }
                        .mapTo(TaskHistory::class.java)
                }
        }
}
