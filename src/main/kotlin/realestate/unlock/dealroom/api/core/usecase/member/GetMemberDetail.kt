package realestate.unlock.dealroom.api.core.usecase.member

import realestate.unlock.dealroom.api.core.entity.member.MemberDetail
import realestate.unlock.dealroom.api.core.exception.MemberNotFoundException
import realestate.unlock.dealroom.api.core.gateway.auth.UserService
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.repository.member.type.MemberTypeRepository
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealsByMemberId
import javax.inject.Inject

class GetMemberDetail @Inject constructor(
    private val memberRepository: MemberRepository,
    private val memberTypeRepository: MemberTypeRepository,
    private val userService: UserService,
    private val getDealsByMemberId: GetDealsByMemberId
) {

    fun getById(memberId: Long): MemberDetail =
        runCatching { memberRepository.findById(memberId) }
            .onFailure { throw MemberNotFoundException(memberId) }
            .getOrThrow()
            .let { member ->
                MemberDetail(
                    member = member,
                    memberType = memberTypeRepository.findByKey(member.typeKey),
                    deals = getDealsByMemberId.get(member.id),
                    authUser = member.authId?.let(userService::findById)
                )
            }

    fun getByEmail(email: String): MemberDetail =
        memberRepository.findByEmail(email)
            ?.let { member ->
                MemberDetail(
                    member = member,
                    memberType = memberTypeRepository.findByKey(member.typeKey),
                    deals = getDealsByMemberId.get(member.id),
                    authUser = member.authId?.let(userService::findById)
                )
            }
            ?: throw MemberNotFoundException(email)
}
