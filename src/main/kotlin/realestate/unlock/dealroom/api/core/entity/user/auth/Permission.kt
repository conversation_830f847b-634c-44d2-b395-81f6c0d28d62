package realestate.unlock.dealroom.api.core.entity.user.auth

enum class Permission(val key: String) {

    // Deals
    READ_OWN_DEALS("deal_room:read_own:deals"),
    READ_ALL_DEALS("deal_room:read_all:deals"),
    UPDATE_OWN_DEALS("deal_room:update_own:deals"),
    UPDATE_ALL_DEALS("deal_room:update_all:deals"),
    CREATE_DEALS("deal_room:create:deals"),

    // Dashboard
    READ_ALL_DASHBOARD("deal_room:read_all:dashboard"),
    READ_OWN_DASHBOARD("deal_room:read_own:dashboard"),

    // Members
    READ_MEMBERS("deal_room:read:members"),
    UPDATE_MEMBERS("deal_room:update:members"),
    CREATE_MEMBERS("deal_room:create:members"),
    CREATE_EXTERNAL_ORG_MEMBER("keyway:users:create_in_other_org"),

    // Users
    CREATE_USERS("deal_room:create:users");

    fun keys() = setOf(key)
}
