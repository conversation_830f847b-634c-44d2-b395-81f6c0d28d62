package realestate.unlock.dealroom.api.core.entity.member

import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.member.type.MemberType
import realestate.unlock.dealroom.api.core.entity.user.auth.AuthUser

data class MemberDetail(
    val member: Member,
    val memberType: MemberType,
    val deals: List<CompleteDeal>,
    val authUser: AuthUser?
)
