package realestate.unlock.dealroom.api.core.entity.member

import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import java.time.OffsetDateTime
import java.util.*

data class Member(
    val id: Long,
    val typeKey: String,
    val firstName: String,
    val lastName: String,
    val companyName: String,
    val address: String?,
    val phoneNumber: String?,
    val email: String,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
    val enabled: Boolean,
    val authId: String?,
    val walkThroughDone: Boolean,
    val organizationId: String
) {
    val fullName = "$firstName $lastName"
    val type = MemberTypeEnum.valueOf(typeKey.uppercase(Locale.getDefault()))
    val team = type.team

    fun isTeam(team: MemberDealTeam) = getDealTeam() == team
    fun getMemberType(): MemberTypeEnum = type
    fun getDealTeam(): MemberDealTeam = team
}

fun Collection<Member>.members(memberTypeEnum: MemberTypeEnum): Set<Member> =
    this.filter { member -> member.typeKey == memberTypeEnum.key }
        .toSet()

fun Collection<Member>.team(team: MemberDealTeam): Set<Member> =
    this.filter { it.isTeam(team) }
        .toSet()
