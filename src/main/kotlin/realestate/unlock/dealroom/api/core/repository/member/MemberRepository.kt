package realestate.unlock.dealroom.api.core.repository.member

import realestate.unlock.dealroom.api.core.entity.member.Member
import realestate.unlock.dealroom.api.core.entity.member.MemberToSave

interface MemberRepository {
    fun findByEmail(userEmail: String): Member?
    fun findById(memberId: Long): Member
    fun findByAuthId(authId: String): Member
    fun findByDealCategoryId(dealCategoryId: Long): Set<Member>
    fun findByDealId(dealId: Long): Set<Member>
    fun findByMemberIds(memberIds: Iterable<Long>): List<Member>
    fun findFiltered(filters: Map<String, Any>, organizationId: String): Set<Member>
    fun save(member: MemberToSave): Member
    fun update(member: Member): Member
}
