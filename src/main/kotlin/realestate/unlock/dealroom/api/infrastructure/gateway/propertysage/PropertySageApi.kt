package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage

import realestate.unlock.dealroom.api.infrastructure.client.rest.PropertySageRestClient
import realestate.unlock.dealroom.api.infrastructure.client.rest.parseBody
import realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.keyscore.GetPropertyKeyscoreResponse
import java.io.File
import java.util.*
import javax.inject.Inject

class PropertySageApi @Inject constructor(
    private val propertySageRestClient: PropertySageRestClient
) : PropertySageGateway {

    override fun getPropertyKeyscoreByKeywayId(propertyId: String): GetPropertyKeyscoreResponse =
        propertySageRestClient.restClient.get(
            path = "/properties/$propertyId/keyscore",
            responseType = String::class.java
        ).parseBody(GetPropertyKeyscoreResponse::class.java)

    override fun downloadPhoto(url: String): File =
        propertySageRestClient.getClientForUrl(url)
            .get(
                path = "",
                responseType = File::class.java,
                fileName = UUID.randomUUID().toString()
            ).body
}

interface PropertySageGateway {
    fun getPropertyKeyscoreByKeywayId(propertyId: String): GetPropertyKeyscoreResponse
    fun downloadPhoto(url: String): File
}
