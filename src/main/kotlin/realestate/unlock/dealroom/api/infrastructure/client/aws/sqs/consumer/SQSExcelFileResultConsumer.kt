package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer

import com.google.inject.name.Named
import com.keyway.kommons.aws.config.AwsConfig
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.ExcelFileResultListener
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class SQSExcelFileResultConsumer @Inject constructor(
    awsConfig: AwsConfig,
    @Named("sqsExcelFileResult") sqsExcelFileResult: AwsSQSConfig,
    excelFileResultListener: ExcelFileResultListener
) {
    companion object {
        private val logger by LoggerDelegate()
    }

    init {
        logger.trace("initializing sqs consumer with configuration $sqsExcelFileResult")
        val consumer = SQSConsumerBuilder(
            awsConfig = awsConfig,
            awsSQSConfig = sqsExcelFileResult,
            messageHandler = excelFileResultListener
        ).build()
        consumer.start()
        logger.trace("sqs consumer initialized successfully")
    }
}
