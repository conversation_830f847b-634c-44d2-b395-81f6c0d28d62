package realestate.unlock.dealroom.api.infrastructure.client.rest.exception

import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.base.ErrorRequestException

open class GenericErrorRequestException(
    message: String,
    errorCode: String = "GENERIC_ERROR",
    statusCode: Int,
    cause: Throwable? = null
) : ErrorRequestException(
    message = message,
    errorCode = errorCode,
    statusCode = statusCode,
    cause = cause
)
