package realestate.unlock.dealroom.api.infrastructure.gateway.auth0

import com.keyway.security.domain.user.*
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.user.auth.AuthUser
import realestate.unlock.dealroom.api.core.entity.user.auth.LoginUserAuthInput
import realestate.unlock.dealroom.api.core.entity.user.profile.UserPasswordToUpdate
import realestate.unlock.dealroom.api.core.entity.user.role.Role
import realestate.unlock.dealroom.api.core.gateway.auth.UserService
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Auth0ManagementClientConfig
import javax.inject.Inject

class UserAuth0Service @Inject constructor(
    private val userSdk: UserSdk,
    private val userImpersonationSdk: UserImpersonationSdk,
    private val auth0ManagementClientConfig: Auth0ManagementClientConfig
) : UserService {

    override fun findById(authId: String): AuthUser = userSdk.findById(authId).toAuthUser()

    override fun changePassword(userPasswordToUpdate: UserPasswordToUpdate): AuthUser =
        userImpersonationSdk.updatePassword(
            NewUserPasswordInput(
                id = userPasswordToUpdate.authId,
                password = userPasswordToUpdate.newPassword
            )
        )
            .toAuthUser()

    override fun login(input: LoginUserAuthInput) {
        userImpersonationSdk.login(LoginUserInput(email = input.email, password = input.password, connection = auth0ManagementClientConfig.usersDatabaseName))
    }

    private fun User.toAuthUser() = AuthUser(
        uid = this.id,
        email = this.email,
        displayName = this.name,
        emailVerified = this.emailVerified,
        disabled = this.blocked,
        tenantId = null
    )

    private fun findAuthRoleName(memberType: MemberTypeEnum): String = when (memberType) {
        MemberTypeEnum.BUYER -> Role.BUYER_ADMIN
        MemberTypeEnum.BUYER_COUNSEL -> Role.BUYER_COUNSEL
        else -> Role.SELLER
    }.title
}
