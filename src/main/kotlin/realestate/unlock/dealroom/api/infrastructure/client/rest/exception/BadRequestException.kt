package realestate.unlock.dealroom.api.infrastructure.client.rest.exception

import org.eclipse.jetty.http.HttpStatus
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.base.ErrorRequestException

open class BadRequestException(
    message: String = HttpStatus.Code.BAD_REQUEST.message,
    errorCode: String = "BAD_REQUEST",
    cause: Throwable? = null
) : ErrorRequestException(
    message = message,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.BAD_REQUEST.code,
    cause = cause
)
