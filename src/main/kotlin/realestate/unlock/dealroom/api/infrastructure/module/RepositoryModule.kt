package realestate.unlock.dealroom.api.infrastructure.module

import com.google.inject.AbstractModule
import realestate.unlock.dealroom.api.core.repository.calendar.CalendarEventRepository
import realestate.unlock.dealroom.api.core.repository.category.FindCategoryByKeyRepository
import realestate.unlock.dealroom.api.core.repository.deal.*
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.core.repository.deal.category.FindDealStageByKeysRepository
import realestate.unlock.dealroom.api.core.repository.deal.datasync.DataSyncRepository
import realestate.unlock.dealroom.api.core.repository.deal.file.LinkedFileRepository
import realestate.unlock.dealroom.api.core.repository.deal.finding.FindingQuestionsRepository
import realestate.unlock.dealroom.api.core.repository.deal.finding.FindingRepository
import realestate.unlock.dealroom.api.core.repository.deal.report.*
import realestate.unlock.dealroom.api.core.repository.deal.schema.DealSchemaRepository
import realestate.unlock.dealroom.api.core.repository.document.*
import realestate.unlock.dealroom.api.core.repository.file.FileQuestionHistoryRepository
import realestate.unlock.dealroom.api.core.repository.file.FileRepository
import realestate.unlock.dealroom.api.core.repository.file.FileTokenRepository
import realestate.unlock.dealroom.api.core.repository.keypilot.DocumentSuggestedQuestionsRepository
import realestate.unlock.dealroom.api.core.repository.keypilot.DocumentTypePromptRepository
import realestate.unlock.dealroom.api.core.repository.loi.*
import realestate.unlock.dealroom.api.core.repository.loi.MedicalLetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.repository.loi.MultifamilyLetterOfIntentRoundRepository
import realestate.unlock.dealroom.api.core.repository.member.MemberRepository
import realestate.unlock.dealroom.api.core.repository.member.type.MemberTypeRepository
import realestate.unlock.dealroom.api.core.repository.pipeline.PipelineViewRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.repository.tag.TagRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskRepository
import realestate.unlock.dealroom.api.core.repository.task.TaskSearchRepository
import realestate.unlock.dealroom.api.core.repository.task.comment.TaskCommentRepository
import realestate.unlock.dealroom.api.core.repository.task.file.TaskFileRepository
import realestate.unlock.dealroom.api.core.repository.task.history.TaskHistoryRepository
import realestate.unlock.dealroom.api.core.repository.task.template.TaskTemplateRepository
import realestate.unlock.dealroom.api.core.repository.user.role.FindUserRoleByKeyRepository
import realestate.unlock.dealroom.api.core.repository.vertical.VerticalRepository
import realestate.unlock.dealroom.api.repository.database.calendar.CalendarEventDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.category.FindCategoryByKeyDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.deal.*
import realestate.unlock.dealroom.api.repository.database.deal.category.DealCategoryDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.deal.category.FindDealStageByKeysDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.deal.datasync.DataSyncDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.deal.file.LinkedFileDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.deal.finding.FindingDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.deal.finding.FindingQuestionsDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.deal.reports.*
import realestate.unlock.dealroom.api.repository.database.deal.schema.DealSchemaDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.document.*
import realestate.unlock.dealroom.api.repository.database.file.DocumentTypePromptDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.file.FileDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.file.FileQuestionHistoryDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.file.FileTokenDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.keypilot.DocumentSuggestedQuestionsDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.loi.*
import realestate.unlock.dealroom.api.repository.database.member.MemberDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.member.type.MemberTypeDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.pipeline.PipelineViewDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.property.PropertyDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.tag.TagDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.task.TaskDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.task.TaskSearchDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.task.comment.TaskCommentDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.task.file.TaskFileDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.task.history.TaskHistoryDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.task.template.TaskTemplateDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.user.role.FindUserRoleByKeyDatabaseRepository
import realestate.unlock.dealroom.api.repository.database.vertical.VerticalDatabaseRepository

class RepositoryModule : AbstractModule() {

    override fun configure() {
        // Database
        bind(FindCategoryByKeyRepository::class.java).to(FindCategoryByKeyDatabaseRepository::class.java)

        bind(DealCategoryRepository::class.java).to(DealCategoryDatabaseRepository::class.java)
        bind(FindDealStageByKeysRepository::class.java).to(FindDealStageByKeysDatabaseRepository::class.java)

        bind(CalendarEventRepository::class.java).to(CalendarEventDatabaseRepository::class.java)
        bind(DealRepository::class.java).to(DealDatabaseRepository::class.java)
        bind(DealSchemaRepository::class.java).to(DealSchemaDatabaseRepository::class.java)
        bind(DealMemberRelationRepository::class.java).to(DealMemberRelationDatabaseRepository::class.java)
        bind(DealReportRepository::class.java).to(DealReportDatabaseRepository::class.java)
        bind(ReportVendorRepository::class.java).to(ReportVendorDatabaseRepository::class.java)
        bind(ReportTagsRepository::class.java).to(ReportTagsDatabaseRepository::class.java)
        bind(ReportTypeRepository::class.java).to(ReportTypeDatabaseRepository::class.java)
        bind(ReportVendorRepository::class.java).to(ReportVendorDatabaseRepository::class.java)
        bind(ReportByTaskRepository::class.java).to(ReportByTaskDatabaseRepository::class.java)
        bind(TagRepository::class.java).to(TagDatabaseRepository::class.java)
        bind(PipelineViewRepository::class.java).to(PipelineViewDatabaseRepository::class.java)
        bind(PipelineViewRepository::class.java).to(PipelineViewDatabaseRepository::class.java)
        bind(VerticalRepository::class.java).to(VerticalDatabaseRepository::class.java)
        bind(LinkedFileRepository::class.java).to(LinkedFileDatabaseRepository::class.java)
        bind(DataSyncRepository::class.java).to(DataSyncDatabaseRepository::class.java)
        bind(DocumentTypePromptRepository::class.java).to(DocumentTypePromptDatabaseRepository::class.java)
        bind(FindingRepository::class.java).to(FindingDatabaseRepository::class.java)
        bind(FindingQuestionsRepository::class.java).to(FindingQuestionsDatabaseRepository::class.java)

        bind(LetterOfIntentRoundRepository::class.java).to(LetterOfIntentRoundDatabaseRepository::class.java)
        bind(MedicalLetterOfIntentRoundRepository::class.java).to(MedicalLetterOfIntentRoundDatabaseRepository::class.java)
        bind(MultifamilyLetterOfIntentRoundRepository::class.java).to(MultifamilyLetterOfIntentRoundDatabaseRepository::class.java)
        bind(LetterOfIntentRoundFileRepository::class.java).to(LetterOfIntentRoundFileDatabaseRepository::class.java)
        bind(LetterOfIntentSignRepository::class.java).to(LetterOfIntentSignDatabaseRepository::class.java)
        bind(ExecutedLetterOfIntentRepository::class.java).to(ExecutedLetterOfIntentDatabaseRepository::class.java)
        bind(LetterOfIntentHistoryEntryRepository::class.java).to(LetterOfIntentHistoryEntryDatabaseRepository::class.java)
        bind(LetterOfIntentDraftRepository::class.java).to(LetterOfIntentDraftDatabaseRepository::class.java)

        bind(MemberTypeRepository::class.java).to(MemberTypeDatabaseRepository::class.java)

        bind(MemberRepository::class.java).to(MemberDatabaseRepository::class.java)

        bind(PropertyRepository::class.java).to(PropertyDatabaseRepository::class.java)

        bind(TaskFileRepository::class.java).to(TaskFileDatabaseRepository::class.java)
        bind(TaskCommentRepository::class.java).to(TaskCommentDatabaseRepository::class.java)
        bind(TaskRepository::class.java).to(TaskDatabaseRepository::class.java)
        bind(TaskSearchRepository::class.java).to(TaskSearchDatabaseRepository::class.java)
        bind(TaskHistoryRepository::class.java).to(TaskHistoryDatabaseRepository::class.java)

        bind(TaskTemplateRepository::class.java).to(TaskTemplateDatabaseRepository::class.java)

        bind(FindUserRoleByKeyRepository::class.java).to(FindUserRoleByKeyDatabaseRepository::class.java)

        bind(DocumentRepository::class.java).to(DocumentDatabaseRepository::class.java)
        bind(DocumentRoundRepository::class.java).to(DocumentRoundDatabaseRepository::class.java)
        bind(DocumentInteractionRepository::class.java).to(DocumentInteractionDatabaseRepository::class.java)
        bind(DocumentSignRepository::class.java).to(DocumentSignDatabaseRepository::class.java)
        bind(OnHoldPreviousStatusRepository::class.java).to(OnHoldPreviousStatusDatabaseRepository::class.java)

        bind(DealsSummaryRepository::class.java).to(DealsSummaryDatabaseRepository::class.java)

        bind(FileRepository::class.java).to(FileDatabaseRepository::class.java)
        bind(FileTokenRepository::class.java).to(FileTokenDatabaseRepository::class.java)
        bind(FileQuestionHistoryRepository::class.java).to(FileQuestionHistoryDatabaseRepository::class.java)
        bind(DocumentSuggestedQuestionsRepository::class.java).to(DocumentSuggestedQuestionsDatabaseRepository::class.java)

        bind(DealSearchRepository::class.java).to(DealSearchDatabaseRepository::class.java)

        bind(DealHistoryRepository::class.java).to(DealDatabaseHistoryRepository::class.java)
    }
}
