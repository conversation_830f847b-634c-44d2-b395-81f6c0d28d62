package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer

import com.google.inject.name.Named
import com.keyway.kommons.aws.config.AwsConfig
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.TaskChangedListener
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class SQSTaskChangedConsumer @Inject constructor(
    awsConfig: AwsConfig,
    @Named("sqsTaskChanged") sqsLoiSigned: AwsSQSConfig,
    taskChangedListener: TaskChangedListener
) {
    companion object {
        private val logger by LoggerDelegate()
    }

    init {
        logger.trace("initializing sqs consumer with configuration $sqsLoiSigned")
        val consumer = SQSConsumerBuilder(
            awsConfig = awsConfig,
            awsSQSConfig = sqsLoiSigned,
            messageHandler = taskChangedListener
        ).build()
        consumer.start()
        logger.trace("sqs consumer initialized successfully")
    }
}
