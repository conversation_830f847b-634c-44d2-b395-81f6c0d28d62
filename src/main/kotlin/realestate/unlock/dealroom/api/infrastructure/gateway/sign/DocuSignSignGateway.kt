package realestate.unlock.dealroom.api.infrastructure.gateway.sign

import com.docusign.esign.model.*
import com.docusign.esign.model.Recipients
import realestate.unlock.dealroom.api.core.gateway.sign.*
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DocuSignConfig
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.builders.CarbonCopyBuilder
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.builders.DocumentBuilder
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.builders.NotificationBuilder
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.builders.SignerBuilder
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.io.ByteArrayInputStream
import java.net.URI
import javax.inject.Inject

class DocuSignSignGateway @Inject constructor(
    private val docuSignConfig: DocuSignConfig,
    private val docuSignApiProvider: DocuSignApiProvider
) : SignGateway {

    companion object {
        private val logger by LoggerDelegate()
        const val TAB_ANCHOR_UNITS = "pixels"
        const val VIEW_AUTHENTICATION_METHOD = "none"
        const val EmbeddedRecipientStartURL = "SIGN_AT_DOCUSIGN"
        const val ROUTING_ORDER_FIRST = "1"
        const val CORRECT_VIEW_URL_ADDITIONAL_PARAMS = "&sendButtonAction=send" +
            "&backButtonAction=redirect" +
            "&showBackButton=false" +
            "&showEditRecipients=false" +
            "&showEditDocuments=true" +
            "&showEditDocumentVisibility=false" +
            "&showMatchingTemplatesPrompt=false" +
            "&showHeaderActions=false" +
            "&showDiscardAction=true" +
            "&send=1" +
            "&tabPaletteType=standard"
        const val CUSTOM_DOCUMENT_TYPE_METADATA = "deal_room_document_type"
    }

    override fun beginFlow(input: BeginSignFlowInput): BeginSignFlowOutput =
        runHandlingError("Error on beginFlow with input $input") {
            val envelopesApi = docuSignApiProvider.createEnvelopesApi()
            buildEnvelopeDefinition(input)
                .also { logger.info(JsonMapper.encode(it)) }
                .let { envelopesApi.createEnvelope(docuSignConfig.accountId, it) }
                .also { logger.info(JsonMapper.encode(it)) }
                .let { BeginSignFlowOutput(signingId = it.envelopeId) }
        }

    override fun retrieveSignerViewUrl(input: RetrieveViewUrlInput): RetrieveSignViewUrlOutput =
        runHandlingError("Error on retrieveSignViewUrl with input $input") {
            val envelopesApi = docuSignApiProvider.createEnvelopesApi()
            makeSignerRecipientViewRequest(input)
                .also { logger.info(JsonMapper.encode(it)) }
                .let { envelopesApi.createRecipientView(docuSignConfig.accountId, input.signingId, it) }
                .also { logger.info(JsonMapper.encode(it)) }
                .let { RetrieveSignViewUrlOutput(viewUrl = URI.create(it.url)) }
        }

    override fun retrieveCarbonCopyViewUrl(input: RetrieveViewUrlInput): RetrieveSignViewUrlOutput =
        runHandlingError("Error on retrieveCarbonCopyViewUrl with input $input") {
            val envelopesApi = docuSignApiProvider.createEnvelopesApi()
            makeCarbonCopyRecipientViewRequest(input)
                .also { logger.info(JsonMapper.encode(it)) }
                .let { envelopesApi.createRecipientView(docuSignConfig.accountId, input.signingId, it) }
                .also { logger.info(JsonMapper.encode(it)) }
                .let { RetrieveSignViewUrlOutput(viewUrl = URI.create(it.url)) }
        }

    override fun retrieveDocument(input: RetrieveDocumentInput): DocumentOutput =
        runHandlingError("Error on retrieveDocument with input $input") {
            val envelopesApi = docuSignApiProvider.createEnvelopesApi()
            val rawFile = envelopesApi.getDocument(docuSignConfig.accountId, input.signingId, input.documentId)
            DocumentOutput(documentId = input.documentId, content = ByteArrayInputStream(rawFile))
        }

    override fun resendDocument(input: ResendDocumentInput): ResendDocumentOutput =
        runHandlingError("Error on resending email with input $input") {
            docuSignApiProvider.createEnvelopesApi()
                .let { envelopesApi ->
                    envelopesApi.listRecipients(docuSignConfig.accountId, input.signingId)
                        .let { recipients ->
                            envelopesApi.updateRecipients(
                                docuSignConfig.accountId,
                                input.signingId,
                                recipients,
                                envelopesApi.UpdateRecipientsOptions().apply { resendEnvelope = "true" }
                            )
                        }
                }
                .let { ResendDocumentOutput(signingId = input.signingId) }
        }

    override fun retrieveEditEnvelopeViewUrl(input: RetrieveEditEnvelopeViewUrlInput): URI =
        runHandlingError("Error on beginFlow with input $input") {
            val envelopesApi = docuSignApiProvider.createEnvelopesApi()
            makeCorrectViewUrlDefinition(input)
                .let {
                    envelopesApi.createCorrectView(docuSignConfig.accountId, input.envelopId, it)
                        .addEditDocumentViewCustomization()
                }
        }

    fun retrieveEnvelopeData(envelopId: String): Envelope =
        runHandlingError("Error getting data for envelop: $envelopId") {
            val api = docuSignApiProvider.createEnvelopesApi()
            val options = api.GetEnvelopeOptions()
            options.include = "custom_fields,documents"

            api.getEnvelope(
                docuSignConfig.accountId,
                envelopId,
                options
            )
        }

    private fun makeCorrectViewUrlDefinition(input: RetrieveEditEnvelopeViewUrlInput): CorrectViewRequest =
        CorrectViewRequest()
            .returnUrl(input.integrationSettings.returnUrl.toString())

    private fun ViewUrl.addEditDocumentViewCustomization(): URI =
        URI.create(this.url.plus(CORRECT_VIEW_URL_ADDITIONAL_PARAMS))

    private fun buildEnvelopeDefinition(input: BeginSignFlowInput): EnvelopeDefinition {
        return EnvelopeDefinition()
            .status(input.envelopeStatus.toApiParameter())
            .customFields(
                CustomFields()
                    .addTextCustomFieldsItem(
                        TextCustomField()
                            .name(CUSTOM_DOCUMENT_TYPE_METADATA)
                            .value(input.signDocumentType.name)
                    )
            )
            .addDocumentsItem(
                DocumentBuilder()
                    .apply { this.input = input }
                    .build()
            )
            .recipients(
                Recipients()
                    .signers(
                        input.recipients.signers.map { signer ->
                            SignerBuilder()
                                .apply { this.input = input }
                                .apply { this.signer = signer }
                                .build()
                        }
                    )
                    .carbonCopies(
                        input.recipients.carbonCopies.map { contact ->
                            CarbonCopyBuilder()
                                .apply { this.contact = contact }
                                .build()
                        }
                    )
            )
            .notification(
                input.reminder?.let { reminder ->
                    NotificationBuilder()
                        .apply { this.reminder = reminder }
                        .build()
                }
            )
    }

    private fun makeSignerRecipientViewRequest(input: RetrieveViewUrlInput): RecipientViewRequest =
        RecipientViewRequest()
            .clientUserId(input.recipient.id)
            .email(input.recipient.email)
            .userName(input.recipient.name)
            .returnUrl(input.integrationSettings.returnUrl.toString())
            .authenticationMethod(VIEW_AUTHENTICATION_METHOD)
            .pingFrequency(input.integrationSettings.pingFrequency.toString())
            .pingUrl(input.integrationSettings.pingUrl.toString())

    private fun makeCarbonCopyRecipientViewRequest(input: RetrieveViewUrlInput): RecipientViewRequest {
        return RecipientViewRequest()
            .recipientId(input.recipient.id)
            .email(input.recipient.email)
            .userName(input.recipient.name)
            .returnUrl(input.integrationSettings.returnUrl.toString())
            .authenticationMethod(VIEW_AUTHENTICATION_METHOD)
            .pingFrequency(input.integrationSettings.pingFrequency.toString())
            .pingUrl(input.integrationSettings.pingUrl.toString())
    }

    private fun <T> runHandlingError(messageError: String, block: () -> T) = try {
        block()
    } catch (t: Throwable) {
        logger.error(messageError, t)
        throw DocusignException(messageError, t)
    }
}
