package realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource

import java.sql.Connection
import java.sql.SQLException
import javax.sql.DataSource

interface TransactionalDataSource : DataSource, AutoCloseable {

    fun isTransactionOpen(): <PERSON><PERSON><PERSON>

    @Throws(SQLException::class)
    fun beginTransaction()

    @Throws(SQLException::class)
    fun finalizeTransaction()

    @Throws(SQLException::class)
    fun rollback()

    fun <T> useConnection(operation: (Connection) -> T): T
}
