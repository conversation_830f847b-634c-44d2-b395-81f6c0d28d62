package realestate.unlock.dealroom.api.infrastructure.client.database.mapper

fun Map<String, Any>.mapFiltersForQuery(query: String): QueryWithFilters =
    this.entries.fold(QueryWithFilters(StringBuilder(query), mutableListOf())) { appliedFilters, filter ->
        appliedFilters.also {
            it.query.append(" ".plus(filter.key))
            it.params.add(filter.value)
        }
    }

data class QueryWithFilters(
    val query: StringBuilder,
    val params: MutableList<Any>
)
