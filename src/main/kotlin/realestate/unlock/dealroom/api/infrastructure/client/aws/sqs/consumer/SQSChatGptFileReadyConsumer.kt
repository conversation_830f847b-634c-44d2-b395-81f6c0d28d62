package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer

import com.google.inject.name.Named
import com.keyway.kommons.aws.config.AwsConfig
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.ChatGptFileReadyListener
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class SQSChatGptFileReadyConsumer @Inject constructor(
    awsConfig: AwsConfig,
    @Named("sqsChatGptFileReady") sqsChatGptFileReady: AwsSQSConfig,
    chatGptFileReadyListener: ChatGptFileReadyListener
) {

    companion object {
        private val logger by LoggerDelegate()
    }

    init {
        logger.trace("initializing sqs consumer with configuration $sqsChatGptFileReady")
        val consumer = SQSConsumerBuilder(
            awsConfig = awsConfig,
            awsSQSConfig = sqsChatGptFileReady,
            messageHandler = chatGptFileReadyListener
        ).build()
        consumer.start()
        logger.trace("sqs consumer initialized successfully")
    }
}
