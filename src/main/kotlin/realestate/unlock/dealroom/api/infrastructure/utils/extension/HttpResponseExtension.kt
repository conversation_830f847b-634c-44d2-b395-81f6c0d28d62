package realestate.unlock.dealroom.api.infrastructure.utils.extension

import kong.unirest.HttpResponse
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.Mapper

fun <T> HttpResponse<String>.mapTo(clazz: Class<T>, jsonMapper: Mapper = JsonMapper): T =
    this.runCatching {
        jsonMapper.decode(this.body, clazz)
    }.onFailure {
        it.manageDecodeException(clazz)
    }.getOrThrow()
