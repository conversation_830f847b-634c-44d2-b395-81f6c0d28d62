package realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource

import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.exception.*
import java.io.PrintWriter
import java.sql.Connection
import java.sql.SQLException
import java.sql.SQLFeatureNotSupportedException
import java.util.logging.Logger
import javax.sql.DataSource
import kotlin.concurrent.getOrSet

class DataSourceProxy(
    private var connectionContext: ThreadLocal<ArrayDeque<Connection>>,
    private val dataSource: DataSource
) : TransactionalDataSource {

    constructor(dataSource: DataSource) : this(
        connectionContext = ThreadLocal<ArrayDeque<Connection>>(),
        dataSource = dataSource
    )

    override fun isTransactionOpen(): Boolean = getCurrentConnection() != null

    override fun beginTransaction() {
        dataSource.runCatching {
            val connection = dataSource.connection
            connection.autoCommit = false
            connectionContext.getOrSet { ArrayDeque() }.addLast(connection)
        }.onFailure { e ->
            throw BeginTransactionException(e)
        }
    }

    @Throws(SQLException::class)
    override fun finalizeTransaction() {
        (getCurrentConnection() ?: throw TransactionNotInitializedException())
            .also { connection ->
                if (connection.isClosed) throw TransactionCloseException(message = "due to connection is already closed.")
            }
            .runCatching {
                this.commit()
            }
            .onFailure { e ->
                throw TransactionCommitException(e)
            }
            .getOrThrow()
            .also {
                closeAndRemove()
            }
    }

    @Throws(SQLException::class)
    override fun rollback() {
        (getCurrentConnection() ?: throw TransactionNotInitializedException(message = "Can't rollback transaction because doesn't have available connection."))
            .runCatching {
                this.rollback()
                closeAndRemove()
            }.onFailure { e ->
                throw TransactionRollbackException(e)
            }
    }

    @Throws(SQLException::class)
    override fun getConnection(): Connection =
        getCurrentConnection() ?: dataSource.connection

    @Throws(SQLException::class)
    override fun getConnection(username: String?, password: String?): Connection =
        getCurrentConnection() ?: dataSource.getConnection(username, password)

    @Throws(SQLException::class)
    override fun <T> unwrap(iface: Class<T>?): T {
        return dataSource.unwrap(iface)
    }

    @Throws(SQLException::class)
    override fun isWrapperFor(iface: Class<*>?): Boolean {
        return dataSource.isWrapperFor(iface)
    }

    @Throws(SQLException::class)
    override fun getLogWriter(): PrintWriter {
        return dataSource.logWriter
    }

    @Throws(SQLException::class)
    override fun setLogWriter(out: PrintWriter?) {
        dataSource.logWriter = out
    }

    @Throws(SQLException::class)
    override fun setLoginTimeout(seconds: Int) {
        dataSource.loginTimeout = seconds
    }

    @Throws(SQLException::class)
    override fun getLoginTimeout(): Int {
        return dataSource.loginTimeout
    }

    @Throws(SQLFeatureNotSupportedException::class)
    override fun getParentLogger(): Logger {
        return dataSource.parentLogger
    }

    override fun <T> useConnection(operation: (Connection) -> T): T =
        this.connection.let { connection ->
            when {
                isTransactionOpen() -> connection.let(operation)
                else -> connection.use(operation)
            }
        }

    override fun close() {
        runCatching {
            getCurrentConnection()?.let { connection ->
                if (!connection.isClosed) {
                    connection.rollback()
                    closeAndRemove()
                }
            }
        }.onFailure { e ->
            throw TransactionCloseException(cause = e)
        }
    }

    private fun closeAndRemove() {
        val connection = connectionContext.get().removeLast()
        connection.close()
    }

    private fun getCurrentConnection() = connectionContext.get()?.lastOrNull()
}
