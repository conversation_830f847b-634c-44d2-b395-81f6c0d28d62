package realestate.unlock.dealroom.api.infrastructure.client.aws.sns

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import realestate.unlock.dealroom.api.core.event.task.TaskChanged
import realestate.unlock.dealroom.api.core.event.task.TaskChangedPublisher
import realestate.unlock.dealroom.api.core.repository.deal.category.DealCategoryRepository
import realestate.unlock.dealroom.api.infrastructure.client.aws.sns.contract.TaskChangedMessage
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sns.AwsSNSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import javax.inject.Inject

class SnsTaskChangedPublisher @Inject constructor(
    private val snsClient: AwsSNSClient,
    private val snsConfig: AwsSNSConfig,
    private val dealCategoryRepository: DealCategoryRepository,
    private val coroutineScope: CoroutineScope
) : TaskChangedPublisher {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun publish(taskChanged: TaskChanged) {
        coroutineScope.launch {
            kotlin.runCatching {
                val taskChangedMessage = taskChanged.toTaskChangedMessage()
                val message = JsonMapper.encode(taskChangedMessage)
                snsClient.publish(message, snsConfig.taskChangedTopicArn)
            }.onFailure {
                logger.error("Error publishing task change", it)
            }
        }
    }

    private fun TaskChanged.toTaskChangedMessage() = with(task) {
        val dealCategory = dealCategoryRepository.findById(dealCategoryId)
        TaskChangedMessage(
            id = id,
            dealCategoryId = dealCategory.id,
            dealId = dealCategory.dealId,
            title = title,
            status = statusKey.key,
            updatedAt = updatedAt
        )
    }
}
