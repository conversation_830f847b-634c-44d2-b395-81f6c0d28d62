package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer

import com.keyway.kommons.aws.config.AwsConfig
import com.keyway.kommons.sqs.IMessageHandler
import com.keyway.kommons.sqs.SqsConsumer
import com.keyway.kommons.sqs.buildSqsClient
import com.keyway.kommons.sqs.configuration.DefaultSqsUrlBuilder
import com.keyway.kommons.sqs.configuration.SqsQueueConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import kotlin.coroutines.CoroutineContext

class SQSConsumerBuilder(
    private val awsConfig: AwsConfig,
    private val awsSQSConfig: AwsSQSConfig,
    private val messageHandler: IMessageHandler
) {

    private var coroutineContext: CoroutineContext? = null

    fun setCoroutineContext(coroutineContext: CoroutineContext) =
        apply { this.coroutineContext = coroutineContext }

    fun build(): SqsConsumer {
        val awsConfig = AwsConfig(
            accountId = awsSQSConfig.accountId,
            accessKey = awsConfig.accessKey,
            secretKey = awsConfig.secretKey,
            region = awsSQSConfig.region
        )
        val defaultSqsQueryBuilder = DefaultSqsUrlBuilder(awsConfig)
        val sqsClient = buildSqsClient(awsConfig)
        val sqsQueueConfig = SqsQueueConfig.default(awsSQSConfig.queueName)
        return coroutineContext?.let {
            SqsConsumer(
                sqs = sqsClient,
                sqsQueueConfig = sqsQueueConfig,
                sqsUrlBuilder = defaultSqsQueryBuilder,
                messageHandler = messageHandler,
                context = it
            )
        }
            ?: SqsConsumer(
                sqs = sqsClient,
                sqsQueueConfig = sqsQueueConfig,
                sqsUrlBuilder = defaultSqsQueryBuilder,
                messageHandler = messageHandler
            )
    }
}
