package realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.medical

import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.medical.owners.MedicalOfficeOwnersResponse
import java.math.BigDecimal

data class MedicalOfficeRealEstateDataResponse(
    val source: String,
    val constructionYear: Int? = null,
    val renovationYear: Int? = null,
    val buildingType: String? = null,
    val buildingSubtype: String? = null,
    val askingPrice: BigDecimal? = null,
    val askingPricePerSquareFoot: BigDecimal? = null,
    val askingCapRate: BigDecimal? = null,
    val askingNoi: BigDecimal? = null,
    val askingRentPerSquareFoot: BigDecimal? = null,
    val images: List<String> = listOf(),
    val reportedOwners: MedicalOfficeOwnersResponse? = null
)
