package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener

import com.keyway.kommons.sqs.IMessageHandler
import realestate.unlock.dealroom.api.core.usecase.deal.ChangeDealsByClosingDate
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import software.amazon.awssdk.services.sqs.model.Message
import javax.inject.Inject

class CronJobsListener @Inject constructor(
    private val changeDealsByClosingDate: ChangeDealsByClosingDate
) : IMessageHandler {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun processMessage(message: Message): Boolean {
        logger.trace("Listening cron job message $message")
        return try {
            val job = JobTypes.valueOf(getCronJobToExecute(message))

            if (job == JobTypes.CLOSING_DEAL) {
                changeDealsByClosingDate.findAndUpdate()
            }

            logger.trace("Cron job notified successful")
            true
        } catch (e: Throwable) {
            logger.error("error on process cron job message", e)
            false
        }
    }

    private fun getCronJobToExecute(sqsMessage: Message): String =
        JsonMapper.getMapper().readTree(sqsMessage.body())["job"].asText().uppercase()

    private enum class JobTypes {
        CLOSING_DEAL
    }
}
