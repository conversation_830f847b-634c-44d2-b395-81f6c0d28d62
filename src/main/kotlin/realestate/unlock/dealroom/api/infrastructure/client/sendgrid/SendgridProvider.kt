package realestate.unlock.dealroom.api.infrastructure.client.sendgrid

import com.sendgrid.SendGrid
import realestate.unlock.dealroom.api.infrastructure.configuration.model.SendgridConfig
import javax.inject.Inject
import javax.inject.Provider

class SendgridProvider @Inject constructor(
    private val sendgridConfig: SendgridConfig
) : Provider<SendGrid> {
    override fun get(): SendGrid = SendGrid(sendgridConfig.apiKey)
}
