package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.property

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.math.BigDecimal
import java.time.LocalDate

@JsonNaming(
    PropertyNamingStrategies.LowerCaseStrategy::class
)
data class PropertyResponse(
    val id: String,
    val address: String,
    val fullAddress: String,
    val keywayScore: Int,
    val city: String,
    val state: String,
    val price: BigDecimal?,
    val capRate: BigDecimal? = null,
    val squareFootage: BigDecimal? = null,
    val propertyType: String,
    val operatorName: String? = null,
    val sourceType: String,
    val pricePerSquareFoot: BigDecimal? = null,
    val rentPerSquareFoot: BigDecimal? = null,
    val leaseType: String?,
    val remainingLeaseLength: Int? = null,
    val rentBumpsDescription: String? = null,
    val constructionYear: Int? = null,
    val renovationYear: Int? = null,
    val leaseLength: Int?,
    val latitude: BigDecimal,
    val longitude: BigDecimal,
    val rentBumpsFrequencyInYears: BigDecimal?,
    val numberOfOptions: BigDecimal?,
    val optionLength: BigDecimal?,
    val diligencePeriod: BigDecimal?,
    val closingPeriod: BigDecimal?,
    val closingPeriodExtension: BigDecimal?,
    val earnestMoneyDeposit: BigDecimal?,
    val closingExtensionDeposit: BigDecimal?,
    val contractExecutionDate: LocalDate?,
    val guaranteeDetails: String?,
    val leaseCondition: String?,
    val creditType: String?,
    val rentStepType: String?,
    val cpiMultiplier: BigDecimal?,
    val rentBumpsAmount: BigDecimal?
) {
    constructor(property: MultifamilyProperty) : this(
        property.id,
        property.fullAddress,
        property.fullAddress,
        property.score ?: 0,
        property.city,
        property.state,
        property.askingPrice,
        null,
        property.squareFootage?.let { BigDecimal.valueOf(it.toLong()) },
        "MULTIFAMILY",
        null,
        property.sourceType ?: "ON_MARKET",
        null,
        null,
        null,
        null,
        null,
        property.constructionYear,
        null,
        null,
        property.latitude,
        property.longitude,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null
    )
}
