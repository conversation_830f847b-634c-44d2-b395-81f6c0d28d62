package realestate.unlock.dealroom.api.infrastructure.gateway.auth0

import com.auth0.client.auth.AuthAPI
import com.google.inject.Provider
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Auth0ManagementClientConfig
import javax.inject.Inject

class AuthAPIProvider @Inject constructor(
    private val auth0ManagementClientConfig: Auth0ManagementClientConfig
) : Provider<AuthAPI> {
    override fun get(): AuthAPI {
        return AuthAPI(
            auth0ManagementClientConfig.domain,
            auth0ManagementClientConfig.clientId,
            auth0ManagementClientConfig.clientSecret
        )
    }
}
