package realestate.unlock.dealroom.api.infrastructure.utils.mapper

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.*
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer
import com.fasterxml.jackson.module.kotlin.KotlinModule
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

fun ObjectMapper.applyCamelCaseConfig(): ObjectMapper = sharedConfig(this, PropertyNamingStrategies.LowerCamelCaseStrategy())
fun ObjectMapper.applyDefaultDealRoomConfig() = sharedConfig(this, PropertyNamingStrategies.SnakeCaseStrategy())

private fun sharedConfig(mapper: ObjectMapper, strategy: PropertyNamingStrategy): ObjectMapper {
    val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    return mapper.apply {
        registerModule(
            JavaTimeModule()
                .addDeserializer(LocalDateTime::class.java, LocalDateTimeDeserializer(dateTimeFormatter))
                .addSerializer(LocalDateTime::class.java, LocalDateTimeSerializer(dateTimeFormatter))
        )
        configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
        configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, true)
        configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true)
        configure(DeserializationFeature.FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY, false)
        configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true)
        disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        registerModule(JavaTimeModule())
        registerModule(KotlinModule.Builder().build())
        propertyNamingStrategy = strategy
        setSerializationInclusion(JsonInclude.Include.NON_NULL)
    }
}
