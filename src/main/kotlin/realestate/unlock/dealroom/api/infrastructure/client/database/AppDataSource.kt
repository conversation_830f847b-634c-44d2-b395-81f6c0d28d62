package realestate.unlock.dealroom.api.infrastructure.client.database

import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.TransactionalDataSource
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DatabaseConfig

object AppDataSource {

    private lateinit var dataSource: TransactionalDataSource

    fun getInstance(databaseConfig: DatabaseConfig): TransactionalDataSource =
        takeIf { this::dataSource.isInitialized }?.dataSource
            ?: DataSourceMaker.createDataSource(databaseConfig).also {
                dataSource = it
            }
}
