package realestate.unlock.dealroom.api.infrastructure.gateway.email

import com.sendgrid.Method
import com.sendgrid.Request
import com.sendgrid.SendGrid
import com.sendgrid.helpers.mail.Mail
import realestate.unlock.dealroom.api.infrastructure.client.sendgrid.exception.SendGridException
import javax.inject.Inject

class SendgridMailSender @Inject constructor(
    private val sendGrid: SendGrid
) {

    companion object {
        private const val ENDPOINT = "mail/send"
        private const val VALID_STATUS = 202
    }

    operator fun invoke(mail: Mail) {
        val request = Request().apply {
            method = Method.POST
            endpoint = ENDPOINT
            body = mail.build()
        }

        sendGrid.api(request)
            .takeIf { it.statusCode != VALID_STATUS }?.let { throw SendGridException(it.statusCode) }
    }
}
