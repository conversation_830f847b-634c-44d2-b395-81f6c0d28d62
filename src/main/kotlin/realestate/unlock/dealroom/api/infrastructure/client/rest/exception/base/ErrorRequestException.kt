package realestate.unlock.dealroom.api.infrastructure.client.rest.exception.base

import realestate.unlock.dealroom.api.infrastructure.client.rest.contract.Response

abstract class ErrorRequestException(
    override val message: String,
    val statusCode: Int,
    val errorCode: String,
    val data: Any? = null,
    cause: Throwable? = null
) : RuntimeException("$errorCode - $message", cause) {
    fun getResponse(): Response<Any?> = Response(
        status = statusCode,
        message = message,
        errorCode = errorCode,
        data = data
    )
}
