package realestate.unlock.dealroom.api.infrastructure.client.database

import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.DataSourceProxy
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.TransactionalDataSource
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DatabaseConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate

object DataSourceMaker {
    private val logger by LoggerDelegate()

    fun createDataSource(databaseConfig: DatabaseConfig): TransactionalDataSource =
        HikariConfig().also { config ->
            config.driverClassName = databaseConfig.driverClassName
            config.jdbcUrl = databaseConfig.jdbcUrl
            config.username = databaseConfig.username
            config.password = databaseConfig.password
            config.minimumIdle = databaseConfig.minimumIdle
            config.maximumPoolSize = databaseConfig.maximumPoolSize
            config.connectionTimeout = databaseConfig.connectionTimeout
            config.idleTimeout = databaseConfig.idleTimeout
            config.isAutoCommit = databaseConfig.autocommit

            // Datasource Properties
            config.addDataSourceProperty("serverTimezone", "GMT-3")
            config.addDataSourceProperty("cachePrepStmts", true)
            config.addDataSourceProperty("prepStmtCacheSize", "250")
            config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048")
            config.addDataSourceProperty("useServerPrepStmts", true)
            config.addDataSourceProperty("useLocalSessionState", true)
            config.addDataSourceProperty("useLocalTransactionState", true)
            config.addDataSourceProperty("rewriteBatchedStatements", true)
            config.addDataSourceProperty("cacheResultSetMetadata", true)
            config.addDataSourceProperty("cacheServerConfiguration", true)
            config.addDataSourceProperty("elideSetAutoCommits", true)
            config.addDataSourceProperty("maintainTimeStats", false)
            logger.info("Connecting to: " + config.jdbcUrl)
        }
            .let(::HikariDataSource)
            .let(::DataSourceProxy)
}
