package realestate.unlock.dealroom.api.infrastructure.client.rest.exception

import org.eclipse.jetty.http.HttpStatus
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.base.ErrorRequestException

open class ConflictException(
    message: String = HttpStatus.Code.CONFLICT.message,
    errorCode: String = "CONFLICT",
    cause: Throwable? = null
) : ErrorRequestException(
    message = message,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.CONFLICT.code,
    cause = cause
)
