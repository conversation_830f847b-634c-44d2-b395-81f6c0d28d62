package realestate.unlock.dealroom.api.infrastructure.client.rest

import com.fasterxml.jackson.core.type.TypeReference
import com.keyway.kommons.http.RestClient
import com.keyway.kommons.http.config.RestClientConfig
import kong.unirest.HttpResponse
import kong.unirest.UnirestInstance
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import javax.inject.Inject

class KeyGenRestClient @Inject constructor(
    restClientConfig: RestClientConfig,
    unirestInstance: UnirestInstance
) : BaseRestClient(restClientConfig = restClientConfig, unirestInstance = unirestInstance)

class KFileRestClient @Inject constructor(
    restClientConfig: RestClientConfig,
    unirestInstance: UnirestInstance
) : BaseRestClient(restClientConfig = restClientConfig, unirestInstance = unirestInstance)

class PropertySageRestClient @Inject constructor(
    restClientConfig: RestClientConfig,
    unirestInstance: UnirestInstance
) : BaseRestClient(restClientConfig = restClientConfig, unirestInstance = unirestInstance)

class ChatGptRestClient @Inject constructor(
    restClientConfig: RestClientConfig,
    unirestInstance: UnirestInstance
) : BaseRestClient(restClientConfig = restClientConfig, unirestInstance = unirestInstance)

class PropertyAssetsRestClient @Inject constructor(
    restClientConfig: RestClientConfig,
    unirestInstance: UnirestInstance
) : BaseRestClient(restClientConfig = restClientConfig, unirestInstance = unirestInstance)

class OrganizationsRestClient @Inject constructor(
    restClientConfig: RestClientConfig,
    unirestInstance: UnirestInstance
) : BaseRestClient(restClientConfig = restClientConfig, unirestInstance = unirestInstance)

open class BaseRestClient @Inject constructor(
    val unirestInstance: UnirestInstance,
    val restClientConfig: RestClientConfig
) {
    val restClient: RestClient =
        RestClient(client = unirestInstance, restClientConfig = restClientConfig)

    fun getClientForUrl(url: String): RestClient =
        BaseRestClient(
            unirestInstance = unirestInstance,
            restClientConfig = restClientConfig.copy(host = url)
        ).restClient
}

fun <T> HttpResponse<String>.parseBody(clazz: Class<T>) =
    JsonMapper.decode(this.body, clazz)

fun <T> HttpResponse<String>.parseBody(typeReference: TypeReference<T>) =
    JsonMapper.decode(this.body, typeReference)
