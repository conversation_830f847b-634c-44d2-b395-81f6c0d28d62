package realestate.unlock.dealroom.api.infrastructure.gateway.property

import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyAssetsResponse

interface PropertyAssetsGateway {
    fun findOrCreateByAddress(input: Input): PropertyAssetsResponse
    fun findById(keywayId: String): PropertyAssetsResponse

    data class Input(
        val propertyType: String,
        val fullAddress: String? = null,
        val normalizedAddress: NormalizedAddress
    )

    data class NormalizedAddress(
        val streetAddress: String,
        val city: String,
        val state: String,
        val zipCode: String
    )
}
