package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener

import com.keyway.kommons.sqs.IMessageHandler
import com.keyway.security.domain.authentication.AuthenticationSdk
import realestate.unlock.dealroom.api.core.entity.file.gpt.FileTokenStatus
import realestate.unlock.dealroom.api.core.repository.file.FileTokenRepository
import realestate.unlock.dealroom.api.core.usecase.file.gpt.RunFindings
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SetFileAsReady
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Auth0TokenVerificationConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import software.amazon.awssdk.services.sqs.model.Message
import javax.inject.Inject

class ChatGptFileReadyListener @Inject constructor(
    private val setFileAsReady: SetFileAsReady,
    private val fileTokenRepository: FileTokenRepository,
    private val runFindings: RunFindings,
    private val authenticationSdk: AuthenticationSdk,
    private val auth0TokenVerificationConfig: Auth0TokenVerificationConfig
) : IMessageHandler {

    companion object {
        private val logger by LoggerDelegate()
        private val READY_STATUSES = listOf(FileTokenStatus.READY, FileTokenStatus.INITIAL_QUESTIONS_READY)
    }

    override fun processMessage(message: Message): Boolean {
        logger.trace("Listening ChatGPT File Ready message $message")
        return try {
            val event = JsonMapper.decode(message.body(), FileReadyEvent::class.java)
            if (event.status == EventStatus.SUCCESS) {
                handleSuccess(event, authenticationSdk.accessTokenFor(auth0TokenVerificationConfig.audience))
            } else {
                fileTokenRepository.findByFileId(event.fileId)?.let {
                    fileTokenRepository.update(it.copy(status = event.status.toFileTokenStatus()))
                }
            }
            true
        } catch (e: Throwable) {
            logger.error("error on process ChatGPT File Ready message", e)
            false
        }
    }

    private fun handleSuccess(event: FileReadyEvent, authToken: String) =
        fileTokenRepository.findByFileId(event.fileId)
            .takeIf { it!!.status !in READY_STATUSES }
            ?.also { setFileAsReady(SetFileAsReady.Input(fileId = event.fileId, token = event.token, authToken = authToken)) }
            ?.also { runFindings(it, authToken) }

    data class FileReadyEvent(
        val fileId: String,
        val token: String,
        val status: EventStatus
    )

    enum class EventStatus {
        SUCCESS,
        FAILED,
        CONTENT_NOT_PROCESSABLE;

        fun toFileTokenStatus(): FileTokenStatus = when (this) {
            SUCCESS -> FileTokenStatus.READY
            FAILED -> FileTokenStatus.FAILED
            CONTENT_NOT_PROCESSABLE -> FileTokenStatus.CONTENT_NOT_PROCESSABLE
        }
    }
}
