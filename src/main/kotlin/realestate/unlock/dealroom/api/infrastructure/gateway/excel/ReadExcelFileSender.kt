package realestate.unlock.dealroom.api.infrastructure.gateway.excel

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.client.SQSReadExcelFileClient
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapperCC
import javax.inject.Inject

class ReadExcelFileSender @Inject constructor(
    private val sqsReadExcelFileClient: SQSReadExcelFileClient
) {

    companion object {
        private val logger by LoggerDelegate()
    }
    fun send(message: Message) {
        logger.info("Sending ${JsonMapperCC.encode(message)}")
        sqsReadExcelFileClient.send(JsonMapperCC.encode(message))
    }

    @JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy::class)
    data class Message(
        val referenceId: String,
        val fileUrl: String,
        val sheetName: String,
        val limitRowNumber: Int,
        val columns: String,
        val responseSqs: String
    )
}
