package realestate.unlock.dealroom.api.infrastructure.gateway.auth0

import com.auth0.jwk.UrlJwkProvider
import com.google.inject.Provider
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Auth0ManagementClientConfig
import javax.inject.Inject

class UrlJwkProviderProvider @Inject constructor(
    private val auth0ManagementClientConfig: Auth0ManagementClientConfig
) : Provider<UrlJwkProvider> {
    override fun get(): UrlJwkProvider {
        return UrlJwkProvider(auth0ManagementClientConfig.domain)
    }
}
