package realestate.unlock.dealroom.api.infrastructure.client.rest.exception

import org.eclipse.jetty.http.HttpStatus
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.base.ErrorRequestException

open class ForbiddenException(
    message: String = HttpStatus.Code.FORBIDDEN.message,
    errorCode: String = "FORBIDDEN",
    cause: Throwable? = null
) : ErrorRequestException(
    message = message,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.FORBIDDEN.code,
    cause = cause
)
