package realestate.unlock.dealroom.api.infrastructure.client.rest.exception

import org.eclipse.jetty.http.HttpStatus
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.base.ErrorRequestException

open class NotFoundException(
    message: String = HttpStatus.Code.NOT_FOUND.message,
    errorCode: String = "NOT_FOUND",
    cause: Throwable? = null
) : ErrorRequestException(
    message = message,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.NOT_FOUND.code,
    cause = cause
)
