package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener

import realestate.unlock.dealroom.api.core.entity.loi.ExecuteLetterOfIntentInput
import realestate.unlock.dealroom.api.core.entity.loi.SignLetterOfIntentInput
import realestate.unlock.dealroom.api.core.usecase.loi.ExecuteLetterOfIntentFromSign
import realestate.unlock.dealroom.api.core.usecase.loi.SignLetterOfIntent
import javax.inject.Inject

class LoiDocuSignListener @Inject constructor(
    private val signLetterOfIntent: SignLetterOfIntent,
    private val executeLetterOfIntentFromSign: ExecuteLetterOfIntentFromSign
) : DocumentListener {
    override fun execute(envelopeId: String, documentId: String, authToken: String) =
        executeLetterOfIntentFromSign.execute(ExecuteLetterOfIntentInput(envelopeId = envelopeId), authToken = authToken)

    override fun sign(envelopeId: String, recipientId: String) = signLetterOfIntent.sign(
        SignLetterOfIntentInput(
            envelopeId = envelopeId,
            recipientId = recipientId
        )
    )

    override fun sent(envelopeId: String) {
    }
}
