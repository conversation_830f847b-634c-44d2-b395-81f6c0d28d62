package realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.units

import java.math.BigDecimal

data class MultifamilyUnitsRecordResponse(
    val marketRent: BigDecimal? = null,
    val bathrooms: BigDecimal? = null,
    val quantity: Int? = null,
    val bedrooms: BigDecimal,
    val squareFootage: BigDecimal? = null,
    val rent: BigDecimal? = null,
    val renoRent: BigDecimal? = null,
    val marketRenoRent: BigDecimal? = null,
    val studio: Boolean? = null,
    val penthouse: Boolean? = null,
    val loft: Boolean? = null,
    val den: Boolean? = null,
    val townhouse: Boolean? = null
)
