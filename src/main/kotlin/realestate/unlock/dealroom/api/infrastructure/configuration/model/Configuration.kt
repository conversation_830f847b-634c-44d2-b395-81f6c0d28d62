package realestate.unlock.dealroom.api.infrastructure.configuration.model

import com.keyway.kommons.aws.config.AwsConfig
import com.keyway.kommons.http.config.RestClientConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sns.AwsSNSConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig

data class Configuration(
    val system: SystemConfig,
    val serviceName: String,
    val restClients: List<RestClientConfig>,
    val postgresDatabase: DatabaseConfig,
    val awsCredentials: AwsConfig,
    val sns: AwsSNSConfig,
    val sqsTaskChanged: AwsSQSConfig,
    val sqsLoiSigned: AwsSQSConfig,
    val sqsCronJobs: AwsSQSConfig,
    val sqsDealEvents: AwsSQSConfig,
    val sqsReadExcelFile: AwsSQSConfig,
    val sqsChatGptFileSent: AwsSQSConfig,
    val sqsChatGptFileReady: AwsSQSConfig,
    val sqsExcelFileResult: AwsSQSConfig,
    val sqsUserEvent: AwsSQSConfig,
    val sendgrid: SendgridConfig,
    val webApps: WebAppsConfig,
    val openApi: OpenApiConfig,
    val docuSign: DocuSignConfig,
    val dealTasks: DealTaskConfig,
    val loiSentEmail: LoiSentEmailConfig,
    val splitIo: SplitIoConfig,
    val auth0TokenVerification: Auth0TokenVerificationConfig,
    val auth0ManagementClient: Auth0ManagementClientConfig,
    val auth0AppClientIds: Auth0AppClientIdsConfig,
    val activateAccountToken: JwtGatewayConfig,
    val documentEmailToken: JwtGatewayConfig,
    val chatGptServiceToken: ChatGptServiceTokenConfig,
    val googleCalendarConfig: GoogleCalendarConfig
)
