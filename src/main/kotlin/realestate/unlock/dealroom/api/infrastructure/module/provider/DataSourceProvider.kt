package realestate.unlock.dealroom.api.infrastructure.module.provider

import realestate.unlock.dealroom.api.infrastructure.client.database.AppDataSource
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.TransactionalDataSource
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager.TransactionManager
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DatabaseConfig
import javax.inject.Inject
import javax.inject.Provider

class DataSourceProvider @Inject constructor(
    private val databaseConfig: DatabaseConfig
) : Provider<TransactionalDataSource> {

    override fun get(): TransactionalDataSource =
        AppDataSource.getInstance(databaseConfig)
            .also(TransactionManager::initialize)
}
