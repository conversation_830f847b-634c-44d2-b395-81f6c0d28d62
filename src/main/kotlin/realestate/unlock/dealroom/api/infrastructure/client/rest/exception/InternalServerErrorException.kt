package realestate.unlock.dealroom.api.infrastructure.client.rest.exception

import org.eclipse.jetty.http.HttpStatus
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.base.ErrorRequestException

open class InternalServerErrorException(
    message: String = HttpStatus.Code.INTERNAL_SERVER_ERROR.message,
    errorCode: String = "INTERNAL_SERVER_ERROR",
    cause: Throwable? = null
) : ErrorRequestException(
    message = message,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.INTERNAL_SERVER_ERROR.code,
    cause = cause
)
