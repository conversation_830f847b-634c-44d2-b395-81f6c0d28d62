package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.tenant

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(
    PropertyNamingStrategies.LowerCaseStrategy::class
)
data class GetPropertyTenantsResponse(
    val tenants: List<TenantResponse>
) {
    fun getSelectedTenant(): TenantResponse? =
        tenants.find { it.selected }
}
