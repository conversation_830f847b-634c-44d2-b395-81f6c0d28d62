package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.client

import com.google.inject.name.Named
import com.keyway.kommons.aws.config.AwsConfig
import com.keyway.kommons.sqs.configuration.DefaultSqsUrlBuilder
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.SendMessageRequest
import javax.inject.Inject

class SQSReadExcelFileClient @Inject constructor(
    awsConfig: AwsConfig,
    @Named("sqsReadExcelFile") private val sqsReadExcelFile: AwsSQSConfig
) {
    companion object {
        private val logger by LoggerDelegate()
    }

    private var sqsClient: SqsClient
    private var sqsUrl: String

    init {
        logger.trace("initializing sqs client with configuration $sqsReadExcelFile")

        val awsConfig = AwsConfig(
            accountId = sqsReadExcelFile.accountId,
            accessKey = awsConfig.accessKey,
            secretKey = awsConfig.secretKey,
            region = sqsReadExcelFile.region
        )

        val builder = SqsClient.builder()
            .region(Region.of(sqsReadExcelFile.region))

        builder.credentialsProvider(
            StaticCredentialsProvider.create(
                AwsBasicCredentials.create(awsConfig.accessKey, awsConfig.secretKey)
            )
        )
        sqsUrl = DefaultSqsUrlBuilder(awsConfig).buildUrl(sqsReadExcelFile.queueName)
        sqsClient = builder.build()
        logger.trace("sqs client initialized successfully")
    }

    fun send(message: String): Unit = kotlin.run {
        val request = SendMessageRequest.builder()
            .queueUrl(sqsUrl)
            .messageBody(message)
            .build()
        sqsClient.sendMessage(request)
    }
}
