package realestate.unlock.dealroom.api.infrastructure.gateway.auth0

import com.auth0.client.auth.AuthAPI
import com.google.inject.Inject
import com.google.inject.Provider
import com.keyway.security.domain.ticket.TicketSdk
import com.keyway.security.infra.auth0.management.ManagementAPIProvider
import com.keyway.security.infra.auth0.ticket.TicketAuth0Sdk
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Auth0ManagementClientConfig
import java.time.Clock

class TicketSdkProvider @Inject constructor(
    private val authAPI: AuthAPI,
    private val auth0ManagementClientConfig: Auth0ManagementClientConfig,
    private val clock: Clock
) : Provider<TicketSdk> {

    override fun get(): TicketSdk =
        TicketAuth0Sdk(
            managementAPIProvider = ManagementAPIProvider(
                authAPI = authAPI,
                managementApiAudience = auth0ManagementClientConfig.audience,
                domain = auth0ManagementClientConfig.domain,
                clock = clock
            )
        )
}
