package realestate.unlock.dealroom.api.infrastructure.module

import com.google.inject.AbstractModule
import com.google.inject.name.Names
import com.keyway.kommons.aws.config.AwsConfig
import realestate.unlock.dealroom.api.core.event.deal.DealEventPublisher
import realestate.unlock.dealroom.api.core.event.task.TaskChangedPublisher
import realestate.unlock.dealroom.api.infrastructure.client.aws.sns.AwsSNSClientProvider
import realestate.unlock.dealroom.api.infrastructure.client.aws.sns.SnsDealEventPublisher
import realestate.unlock.dealroom.api.infrastructure.client.aws.sns.SnsTaskChangedPublisher
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.*
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sns.AwsSNSConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.configuration.parser.ConfigParser
import software.amazon.awssdk.services.sns.SnsAsyncClient

class AwsModule : AbstractModule() {
    override fun configure() {
        // Configurations
        val config = ConfigParser.read()
        bind(AwsConfig::class.java).toInstance(config.awsCredentials)
        bind(AwsSNSConfig::class.java).toInstance(config.sns)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsTaskChanged")).toInstance(config.sqsTaskChanged)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsLoiSigned")).toInstance(config.sqsLoiSigned)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsCronJobs")).toInstance(config.sqsCronJobs)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsDealEvents")).toInstance(config.sqsDealEvents)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsReadExcelFile")).toInstance(config.sqsReadExcelFile)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsChatGptFileSent")).toInstance(config.sqsChatGptFileSent)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsChatGptFileReady")).toInstance(config.sqsChatGptFileReady)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsExcelFileResult")).toInstance(config.sqsExcelFileResult)
        bind(AwsSQSConfig::class.java).annotatedWith(Names.named("sqsUserEvent")).toInstance(config.sqsUserEvent)

        // AWS SNS

        bind(SnsAsyncClient::class.java).toProvider(AwsSNSClientProvider::class.java).asEagerSingleton()
        bind(TaskChangedPublisher::class.java).to(SnsTaskChangedPublisher::class.java).asEagerSingleton()
        bind(DealEventPublisher::class.java).to(SnsDealEventPublisher::class.java).asEagerSingleton()

        // SQS listeners
        bind(SQSTaskChangedConsumer::class.java).asEagerSingleton()
        bind(SQSLoiSignedConsumer::class.java).asEagerSingleton()
        bind(SQSCronJobsConsumer::class.java).asEagerSingleton()
        bind(SQSChatGptFileReadyConsumer::class.java).asEagerSingleton()
        bind(SQSExcelFileResultConsumer::class.java).asEagerSingleton()
        bind(SQSUserEventsConsumer::class.java).asEagerSingleton()
    }
}
