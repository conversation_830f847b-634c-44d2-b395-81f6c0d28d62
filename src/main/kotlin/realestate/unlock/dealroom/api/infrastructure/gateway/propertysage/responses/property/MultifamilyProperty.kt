package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.property

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.math.BigDecimal

@JsonNaming(
    PropertyNamingStrategies.LowerCaseStrategy::class
)
data class MultifamilyProperty(
    var id: String,
    var name: String? = null,
    var address: String,
    var fullAddress: String,
    var latitude: BigDecimal,
    var longitude: BigDecimal,
    var squareFootage: Int? = null,
    var score: Int? = null,
    var zipCode: Int? = null,
    var city: String,
    var county: String,
    var state: String,
    var askingPrice: BigDecimal? = null,
    var askingPricePerUnit: Double? = null,
    var constructionYear: Int? = null,
    var totalUnitsQuantity: Int? = null,
    var occupancyPercentage: Int? = null,
    var tractCode: Long? = null,
    var sourceType: String? = null,
    var propertyType: String? = null,
    var offerCapRate: Double? = null,
    var squareFootagePerUnit: Double? = null,
    var productType: String? = null
)
