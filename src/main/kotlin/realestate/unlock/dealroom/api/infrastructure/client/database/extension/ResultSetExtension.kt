package realestate.unlock.dealroom.api.infrastructure.client.database.extension

import realestate.unlock.dealroom.api.core.extension.mapTo
import realestate.unlock.dealroom.api.infrastructure.client.database.exception.NoKeyValueResultSetException
import java.sql.ResultSet
import java.sql.ResultSetMetaData

fun <T> ResultSet.map(handler: (ResultSet) -> T): List<T> {
    val results = mutableListOf<T>()
    while (this.next()) results.add(handler(this))
    return results
}

fun <T> ResultSet.mapToListOf(clazz: Class<out T>): List<T> {
    return this.convertToMaps().mapTo(clazz)
}

fun <T> ResultSet.mapTo(clazz: Class<out T>): T? {
    return convertToMap()?.mapTo(clazz)
}

fun ResultSet.convertToMaps(): List<Map<String, Any>> {
    val resultSetMetaData: ResultSetMetaData = this.metaData
    val list = mutableListOf<Map<String, Any>>()
    while (this.next()) {
        list.add(
            (1..resultSetMetaData.columnCount).associate { index ->
                resultSetMetaData.getColumnName(index) to this.getObject(index)
            }
        )
    }
    return list
}

fun ResultSet.convertToMap(): Map<String, Any>? =
    this.metaData.let { resultSetMetaData ->
        if (this.next()) {
            (1..resultSetMetaData.columnCount).associate { index ->
                resultSetMetaData.getColumnName(index) to this.getObject(index)
            }
        } else {
            null
        }
    }

fun <T> ResultSet.keyValueToMap(): Map<String, T> =
    this.metaData.let { resultSetMetaData ->
        if (resultSetMetaData.columnCount != 2) {
            throw NoKeyValueResultSetException("ResultSet should have 2 columns, but it has ${resultSetMetaData.columnCount}")
        }
        val result = mutableMapOf<String, T>()
        while (this.next()) {
            result[this.getString(1)] = (this.getObject(2) as T)
        }
        result.toMap()
    }
