package realestate.unlock.dealroom.api.infrastructure.gateway.sign.builders

import com.docusign.esign.model.CarbonCopy
import com.docusign.esign.model.RecipientEmailNotification
import realestate.unlock.dealroom.api.core.gateway.sign.Contact
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.DocuSignSignGateway

class CarbonCopyBuilder {
    lateinit var contact: Contact

    fun build() = CarbonCopy()
        .recipientId(contact.id.toString())
        .name(contact.name)
        .email(contact.email)
        .routingOrder(DocuSignSignGateway.ROUTING_ORDER_FIRST)
        .emailNotification(
            RecipientEmailNotification()
                .emailSubject(contact.emailParams.subject)
                .emailBody(contact.emailParams.body)
        )
}
