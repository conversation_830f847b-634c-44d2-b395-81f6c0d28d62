package realestate.unlock.dealroom.api.infrastructure.client.database.extension

import java.sql.ResultSet
import java.sql.Statement
import javax.sql.DataSource

fun <T> DataSource.executeQuery(query: String, autoCommit: Boolean = false, handler: (ResultSet) -> T): T =
    this.connection.use { connection ->
        connection.autoCommit = autoCommit
        connection.createStatement().use { statement ->
            statement.executeQuery(query).use(handler)
        }
    }

fun DataSource.execute(query: String, autoCommit: Boolean = true) =
    this.connection.use { connection ->
        connection.autoCommit = autoCommit
        connection.createStatement().use { statement ->
            statement.execute(query)
        }
    }

fun <T> DataSource.execute(query: String, autoCommit: Boolean = true, handler: (Boolean, Statement) -> T): T =
    this.connection.use { connection ->
        connection.autoCommit = autoCommit
        connection.createStatement().use { statement ->
            val queryResult = statement.execute(query)
            val curriedHandler: (<PERSON><PERSON><PERSON>) -> (Statement) -> T = { boolean -> { statement -> handler(boolean, statement) } }
            statement.use(curriedHandler(queryResult))
        }
    }
