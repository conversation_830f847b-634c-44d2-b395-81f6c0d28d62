package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.tenant

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.time.OffsetDateTime

@JsonNaming(
    PropertyNamingStrategies.LowerCaseStrategy::class
)
data class TenantResponse(
    val id: Long,
    val propertyId: String,
    val name: String,
    val industryNaicsCode: Long,
    val industrySicsCode: Long,
    val employeeQuantity: Int,
    val selected: Boolean,
    val industryClusterName: String,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime
)
