package realestate.unlock.dealroom.api.infrastructure.gateway.calendar

import com.google.api.client.util.DateTime
import com.google.api.services.calendar.Calendar
import com.google.api.services.calendar.model.AclRule
import com.google.api.services.calendar.model.AclRule.Scope
import com.google.api.services.calendar.model.Event
import com.google.api.services.calendar.model.EventDateTime
import realestate.unlock.dealroom.api.core.gateway.calendar.CalendarGateway
import realestate.unlock.dealroom.api.core.gateway.calendar.CreateDealCalendarInput
import realestate.unlock.dealroom.api.core.gateway.calendar.DealCalendar
import realestate.unlock.dealroom.api.core.gateway.calendar.DealCalendarEvent
import java.time.LocalDate
import java.time.ZoneId
import java.util.*
import javax.inject.Inject

class GoogleCalendarGateway @Inject constructor(
    private val googleCalendarService: Calendar
) : CalendarGateway {

    private val READER_ROLE = "reader"

    override fun createCalendar(input: CreateDealCalendarInput): String {
        val calendar = com.google.api.services.calendar.model.Calendar().apply {
            description = input.description
            summary = input.summary
            timeZone = "America/New_York"
        }.let {
            googleCalendarService.calendars().insert(it).execute()
        }
        input.members.forEach {
            googleCalendarService.acl().insert(calendar.id, getACLRule(it)).execute()
        }
        return calendar.id
    }

    override fun updateCalendar(dealCalendar: DealCalendar) {
        googleCalendarService.calendars().get(dealCalendar.id).execute()?.let {
            googleCalendarService.calendars().update(
                it.id,
                it.apply {
                    summary = dealCalendar.summary
                    description = dealCalendar.description
                }
            )
        }

        googleCalendarService.acl().list(dealCalendar.id).execute()?.let { acl ->
            val currentRules = acl.items
                .filter { filter -> filter.role == READER_ROLE }
                .associateBy { it.scope.value }
            dealCalendar.members.forEach {
                if (!currentRules.containsKey(it)) {
                    googleCalendarService.acl().insert(dealCalendar.id, getACLRule(it)).execute()
                }
            }
            currentRules.keys.forEach {
                if (!dealCalendar.members.contains(it)) {
                    googleCalendarService.acl().delete(dealCalendar.id, currentRules[it]!!.id).execute()
                }
            }
        }
    }

    override fun deleteCalendar(calendarId: String) {
        googleCalendarService.calendars().delete(calendarId).execute()
    }

    override fun createEvent(dealCalendarEvent: DealCalendarEvent) {
        googleCalendarService.events()
            .insert(dealCalendarEvent.calendarId, dealCalendarEvent.toGEvent())
            .execute()
    }

    override fun findEvent(calendarId: String, eventId: String): DealCalendarEvent? =
        runCatching {
            googleCalendarService.events()
                .get(calendarId, eventId)
                .execute()
        }.getOrNull()?.toDealEvent(calendarId)

    override fun updateEvent(dealCalendarEvent: DealCalendarEvent) {
        googleCalendarService.events().update(
            dealCalendarEvent.calendarId,
            dealCalendarEvent.id,
            dealCalendarEvent.toGEvent()
        ).execute()
    }

    override fun deleteEvent(calendarId: String, eventId: String) {
        googleCalendarService.events().delete(calendarId, eventId).execute()
    }

    private fun Event.toDealEvent(calendarId: String): DealCalendarEvent =
        DealCalendarEvent(
            id = this.id,
            startDate = LocalDate.parse(this.start.date.toStringRfc3339()),
            endDate = LocalDate.parse(this.end.date.toStringRfc3339()),
            title = this.summary,
            description = this.description,
            calendarId = calendarId
        )

    private fun DealCalendarEvent.toGEvent(): Event =
        this.let { dealEvent ->
            Event().apply {
                this.id = dealEvent.id
                this.summary = dealEvent.title
                this.description = dealEvent.description
                this.start = dealEvent.startDate.toEventDateTime()
                this.end = dealEvent.endDate.toEventDateTime()
            }
        }

    private fun getACLRule(email: String): AclRule =
        AclRule().apply {
            scope = Scope().apply {
                type = "user"
                value = email
            }
            role = READER_ROLE
        }

    private fun LocalDate.toEventDateTime(): EventDateTime =
        this.atStartOfDay(ZoneId.systemDefault()).toInstant()
            .let {
                DateTime(true, it.toEpochMilli(), null)
            }.let {
                EventDateTime().setDate(it)
            }
}
