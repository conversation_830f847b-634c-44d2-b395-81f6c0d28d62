package realestate.unlock.dealroom.api.infrastructure.gateway.auth0

import com.auth0.client.auth.AuthAPI
import com.google.inject.Inject
import com.google.inject.Provider
import com.keyway.security.domain.user.UserImpersonationSdk
import com.keyway.security.infra.auth0.authentication.AuthAPIConfig
import com.keyway.security.infra.auth0.authentication.AuthAPIProvider
import com.keyway.security.infra.auth0.management.ManagementAPIProvider
import com.keyway.security.infra.auth0.user.UserImpersonationAuth0Sdk
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Auth0ManagementClientConfig
import java.time.Clock

class UserImpersonationSdkProvider @Inject constructor(
    private val authAPI: AuthAPI,
    private val auth0ManagementClientConfig: Auth0ManagementClientConfig,
    private val clock: Clock
) : Provider<UserImpersonationSdk> {

    override fun get(): UserImpersonationSdk = UserImpersonationAuth0Sdk(
        authAPIProvider = AuthAPIProvider(
            AuthAPIConfig(
                domain = auth0ManagementClientConfig.domain,
                clientId = auth0ManagementClientConfig.clientId,
                clientSecret = auth0ManagementClientConfig.clientSecret
            )
        ),
        managementAPIProvider = ManagementAPIProvider(
            authAPI = authAPI,
            managementApiAudience = auth0ManagementClientConfig.audience,
            domain = auth0ManagementClientConfig.domain,
            clock = clock
        )
    )
}
