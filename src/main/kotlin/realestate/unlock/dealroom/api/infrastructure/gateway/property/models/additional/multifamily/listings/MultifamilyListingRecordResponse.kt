package realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.listings

import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.units.MultifamilyUnitsResponse
import java.math.BigDecimal

data class MultifamilyListingRecordResponse(
    val source: String,
    val name: String? = null,
    val occupancyPercentage: BigDecimal? = null,
    val constructionYear: Int? = null,
    val renovationYear: Int? = null,
    val leaseRatePercentage: BigDecimal? = null,
    val askingPrice: BigDecimal? = null,
    val productType: String? = null,
    val propertyClass: String? = null,
    val parkingRatio: BigDecimal? = null,
    val communityAmenities: List<String> = listOf(),
    val apartmentsAmenities: List<String> = listOf(),
    val amenities: List<String> = listOf(),
    val images: List<String> = listOf(),
    val units: MultifamilyUnitsResponse? = null
)
