package realestate.unlock.dealroom.api.infrastructure.utils.extension

import org.apache.logging.log4j.util.Strings

private val FEFF = "\uFEFF"
private val FFFE = "\uFFFE"
private val SLASH = "/"

fun String.trimIndentWithoutLineBreaks(): String = this.trimIndent().replace("\n", " ")

fun String.deleteBOM(): String = this.replace(FEFF, Strings.EMPTY).replace(FFFE, Strings.EMPTY)

fun String.fixPath(): String = this.replace("/{2,}".toRegex(), SLASH)
