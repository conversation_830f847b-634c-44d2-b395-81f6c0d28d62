package realestate.unlock.dealroom.api.infrastructure.module.provider

import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.TransactionalDataSource
import javax.inject.Inject
import javax.inject.Provider

class SqlClientProvider @Inject constructor(
    val dataSource: TransactionalDataSource
) : Provider<SqlClient> {

    override fun get(): SqlClient = SqlClient(dataSource)
}
