package realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.responses.keyscore

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.math.BigDecimal

@JsonNaming(
    PropertyNamingStrategies.LowerCaseStrategy::class
)
data class GetPropertyKeyscoreResponse(
    val score: Int,
    val keywayOffer: BigDecimal,
    val listedNoi: BigDecimal? = null,
    val listedPrice: BigDecimal? = null,
    val listedCapRate: BigDecimal? = null,
    val offerNoi: BigDecimal? = null,
    val offerPrice: BigDecimal? = null,
    val offerCapRate: BigDecimal? = null,
    val imageUrl: String? = null
)
