package realestate.unlock.dealroom.api.infrastructure.gateway.gpt

import realestate.unlock.dealroom.api.core.gateway.gpt.ChatGptGateway
import realestate.unlock.dealroom.api.core.gateway.gpt.ChatGptGateway.AskQuestionInput
import realestate.unlock.dealroom.api.infrastructure.client.rest.ChatGptRestClient
import realestate.unlock.dealroom.api.infrastructure.configuration.model.ChatGptServiceTokenConfig
import realestate.unlock.dealroom.api.infrastructure.utils.extension.mapTo
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import javax.inject.Inject

class ChatGptServiceGateway @Inject constructor(
    private val chatGptRestClient: ChatGptRestClient,
    private val chatGptServiceTokenConfig: ChatGptServiceTokenConfig
) : ChatGptGateway {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun uploadFileToProcess(url: String, authToken: String): String {
        return chatGptRestClient.restClient.post(
            path = "/files",
            body = JsonMapper.encode(UploadFileBody(url)),
            headers = mapOf(
                "X-Auth" to chatGptServiceTokenConfig.token,
                "Authorization" to authToken.getBearerToken()
            )
        ).mapTo(UploadFileResponse::class.java)
            .token
    }

    override fun askQuestion(input: AskQuestionInput): String {
        val body = JsonMapper.encode(
            AskQuestionBody(
                token = input.token,
                query = input.question,
                prompt = input.prompt
            )
        ).also {
            logger.info("Asking to ChatGPT about {}", it)
        }
        return chatGptRestClient.restClient.post(
            path = "/query",
            body = body,
            headers = mapOf(
                "X-Auth" to chatGptServiceTokenConfig.token,
                "Authorization" to input.authToken.getBearerToken()
            )
        ).mapTo(AskQuestionResponse::class.java)
            .answer
    }

    private fun String.getBearerToken(): String =
        this.takeIf { it.startsWith("Bearer") } ?: "Bearer $this"

    data class UploadFileBody(
        val s3Url: String
    )

    data class UploadFileResponse(
        val token: String
    )

    data class AskQuestionBody(
        val token: String,
        val query: String,
        val prompt: String?
    )

    data class AskQuestionResponse(
        val token: String,
        val answer: String
    )
}
