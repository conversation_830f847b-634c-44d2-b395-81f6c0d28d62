package realestate.unlock.dealroom.api.infrastructure

import com.google.inject.Guice
import com.google.inject.Injector
import realestate.unlock.dealroom.api.infrastructure.module.AppModule
import realestate.unlock.dealroom.api.infrastructure.module.AwsModule
import realestate.unlock.dealroom.api.infrastructure.module.RepositoryModule

class Context {

    companion object {
        lateinit var injector: Injector

        fun init(): Injector {
            if (this::injector.isInitialized.not()) {
                injector = Guice.createInjector(
                    AppModule(),
                    AwsModule(),
                    RepositoryModule()
                )
            }
            return injector
        }
    }
}
