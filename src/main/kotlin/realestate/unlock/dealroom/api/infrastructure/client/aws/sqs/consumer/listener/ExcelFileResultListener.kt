package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener

import com.keyway.kommons.sqs.IMessageHandler
import realestate.unlock.dealroom.api.core.entity.deal.datasync.ExcelFileResult
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.ProcessDataSync
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import software.amazon.awssdk.services.sqs.model.Message
import javax.inject.Inject

class ExcelFileResultListener @Inject constructor(
    private val processDataSync: ProcessDataSync
) : IMessageHandler {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun processMessage(message: Message): Boolean {
        logger.info("Listening excel file result message ${message.messageId()}")
        return try {
            val result = JsonMapper.decode(message.body(), ExcelFileResult::class.java)
            processDataSync.process(result)
            true
        } catch (e: Throwable) {
            logger.error("error on process excel file result message", e)
            false
        }
    }
}
