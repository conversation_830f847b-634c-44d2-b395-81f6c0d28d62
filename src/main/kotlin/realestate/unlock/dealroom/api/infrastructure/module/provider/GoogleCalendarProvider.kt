package realestate.unlock.dealroom.api.infrastructure.module.provider

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport
import com.google.api.client.json.gson.GsonFactory
import com.google.api.services.calendar.Calendar
import com.google.api.services.calendar.CalendarScopes
import com.google.auth.http.HttpCredentialsAdapter
import com.google.auth.oauth2.GoogleCredentials
import realestate.unlock.dealroom.api.infrastructure.configuration.model.GoogleCalendarConfig
import javax.inject.Inject
import javax.inject.Provider

class GoogleCalendarProvider @Inject constructor(
    private val googleCalendarConfig: GoogleCalendarConfig
) : Provider<Calendar> {
    override fun get(): Calendar =
        GoogleCredentials.fromStream(getGoogleCalendarJsonKey().byteInputStream())
            .createScoped(CalendarScopes.all())
            .let { credentials ->
                Calendar.Builder(
                    GoogleNetHttpTransport.newTrustedTransport(),
                    GsonFactory.getDefaultInstance(),
                    HttpCredentialsAdapter(credentials)
                ).apply {
                    applicationName = "DealRoom"
                }.build()
            }

    private fun getGoogleCalendarJsonKey(): String =
        System.getenv(googleCalendarConfig.jsonKeyEnvVar)
            ?: javaClass.classLoader.getResourceAsStream(googleCalendarConfig.jsonKeyFileName)?.reader()?.readText()
            ?: throw RuntimeException(
                """Unable to find gCalendar json key in the following locations:
                    ENV_VAR:${googleCalendarConfig.jsonKeyEnvVar}
                    src/main/resources/${googleCalendarConfig.jsonKeyFileName}"""
            )
}
