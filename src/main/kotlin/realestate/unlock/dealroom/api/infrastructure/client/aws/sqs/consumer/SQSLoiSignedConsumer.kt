package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer

import com.google.inject.name.Named
import com.keyway.kommons.aws.config.AwsConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asContextElement
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.DocuSignListener
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import java.sql.Connection
import javax.inject.Inject

class SQSLoiSignedConsumer @Inject constructor(
    awsConfig: AwsConfig,
    @Named("sqsLoiSigned") sqsLoiSigned: AwsSQSConfig,
    docuSignListener: DocuSignListener
) {
    companion object {
        private val logger by LoggerDelegate()
    }

    private val connectionContext = ThreadLocal<Connection>()

    init {
        logger.trace("initializing sqs consumer with configuration $sqsLoiSigned")

        val consumer = SQSConsumerBuilder(
            awsConfig = awsConfig,
            awsSQSConfig = sqsLoiSigned,
            messageHandler = docuSignListener
        )
            .setCoroutineContext(Dispatchers.Default + connectionContext.asContextElement())
            .build()

        consumer.start()
        logger.trace("sqs consumer initialized successfully")
    }
}
