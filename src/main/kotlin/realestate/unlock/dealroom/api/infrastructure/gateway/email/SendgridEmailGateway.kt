package realestate.unlock.dealroom.api.infrastructure.gateway.email

import com.sendgrid.helpers.mail.Mail
import com.sendgrid.helpers.mail.objects.Attachments
import com.sendgrid.helpers.mail.objects.Content
import com.sendgrid.helpers.mail.objects.Email
import com.sendgrid.helpers.mail.objects.Personalization
import realestate.unlock.dealroom.api.core.gateway.email.EmailData
import realestate.unlock.dealroom.api.core.gateway.email.EmailGateway
import realestate.unlock.dealroom.api.core.gateway.email.TemplatedEmailData
import realestate.unlock.dealroom.api.infrastructure.configuration.model.SendgridConfig
import javax.inject.Inject

class SendgridEmailGateway @Inject constructor(
    private val sendgridMailSender: SendgridMailSender,
    private val sendgridConfig: SendgridConfig
) : EmailGateway {
    override fun sendTemplated(templatedEmailData: TemplatedEmailData) {
        Mail().apply {
            setFrom(Email(templatedEmailData.fromEmailAddress ?: sendgridConfig.emailFrom))
            setTemplateId(sendgridConfig.templates[templatedEmailData.templateKey])
            addPersonalization(
                Personalization().apply {
                    templatedEmailData.toEmailAddress.forEach {
                        addTo(Email(it))
                    }
                    templatedEmailData.carbonCopies.forEach {
                        addCc(Email(it))
                    }
                    templatedEmailData.backgroundCarbonCopies.forEach {
                        addBcc(Email(it))
                    }
                    templatedEmailData.templateParams.forEach {
                        addDynamicTemplateData(it.key, it.value)
                    }
                }
            )
            templatedEmailData.attachments.forEach {
                addAttachments(
                    Attachments
                        .Builder(it.filename.value, it.content)
                        .build()
                )
            }
        }.let(sendgridMailSender::invoke)
    }

    override fun send(emailData: EmailData) {
        Mail().apply {
            setFrom(Email(emailData.fromEmailAddress ?: sendgridConfig.emailFrom))
            addPersonalization(
                Personalization().apply {
                    addTo(Email(emailData.toEmailAddress))
                    emailData.carbonCopies.forEach {
                        addCc(Email(it))
                    }
                }
            )
            setSubject(
                replaceTokens(
                    emailData.subject,
                    emailData.replacementTokens
                )
            )
            addContent(
                Content(
                    emailData.body.contentType.value,
                    replaceTokens(
                        emailData.body.content,
                        emailData.replacementTokens
                    )
                )
            )
            emailData.attachments.forEach {
                addAttachments(
                    Attachments
                        .Builder(it.filename.value, it.content)
                        .build()
                )
            }
        }.let(sendgridMailSender::invoke)
    }

    private fun replaceTokens(source: String, tokens: Map<String, String>) =
        tokens.entries.fold(source, ::replaceToken)

    private fun replaceToken(
        source: String,
        token: Map.Entry<String, String>
    ) = source.replace(token.key, token.value)
}
