package realestate.unlock.dealroom.api.infrastructure.utils.extension

import com.fasterxml.jackson.core.JsonParseException
import com.fasterxml.jackson.databind.JsonMappingException
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.exception.DecodeFieldException

fun Throwable.manageDecodeException(clazz: Class<*>) {
    when (this) {
        is JsonParseException, is JsonMappingException -> {
            throw DecodeFieldException(
                className = clazz.simpleName,
                properties = clazz.declaredFields.map { it.name },
                cause = this
            )
        }
        else -> throw this
    }
}
