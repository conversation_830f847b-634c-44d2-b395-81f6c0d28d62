package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener

import com.docusign.esign.model.Envelope
import com.fasterxml.jackson.databind.JsonNode
import com.keyway.kommons.sqs.IMessageHandler
import com.keyway.security.domain.authentication.AuthenticationSdk
import realestate.unlock.dealroom.api.core.gateway.sign.SignDocumentType
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Auth0TokenVerificationConfig
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.DocuSignSignGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.DocuSignSignGateway.Companion.CUSTOM_DOCUMENT_TYPE_METADATA
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import software.amazon.awssdk.services.sqs.model.Message
import javax.inject.Inject

class DocuSignListener @Inject constructor(
    private val docuSignSignGateway: DocuSignSignGateway,
    private val psaDocuSignListener: PsaDocuSignListener,
    private val loiDocuSignListener: LoiDocuSignListener,
    private val leaseDocuSignListener: LeaseDocuSignListener,
    private val authenticationSdk: AuthenticationSdk,
    private val auth0TokenVerificationConfig: Auth0TokenVerificationConfig
) : IMessageHandler {

    companion object {
        private val logger by LoggerDelegate()
        private const val RECIPIENT_COMPLETED_EVENT = "recipient-completed"
        private const val ENVELOPE_COMPLETED_EVENT = "envelope-completed"
        private const val ENVELOPE_SENT_EVENT = "envelope-sent"
    }

    override fun processMessage(message: Message): Boolean {
        return try {
            onNewDocusignMessage(message).also { logger.info("DocuSign message processed successful: $message") }.let { true }
        } catch (e: Throwable) {
            logger.error("error on process docuSign message", e)
            false
        }
    }

    private fun parseMessage(message: JsonNode): DocusignMessage =
        DocusignMessage(
            event = message["event"].asText(),
            envelopeId = message["data"]["envelopeId"].asText(),
            recipientId = message["data"]["recipientId"]?.asText()
        )

    private fun onNewDocusignMessage(sqsMessage: Message) {
        val body = JsonMapper.getMapper().readTree(sqsMessage.body())
        val message = parseMessage(JsonMapper.getMapper().readTree(body.asText()))

        if (!listOf(ENVELOPE_COMPLETED_EVENT, RECIPIENT_COMPLETED_EVENT, ENVELOPE_SENT_EVENT).contains(message.event)) {
            throw RuntimeException("DocuSign event '${message.event}' not supported")
        }

        val envelopeResult = docuSignSignGateway.retrieveEnvelopeData(message.envelopeId)

        val listener: DocumentListener = getListener(envelopeResult)

        when (message.event) {
            RECIPIENT_COMPLETED_EVENT -> listener.sign(message.envelopeId, message.recipientId!!)
            ENVELOPE_COMPLETED_EVENT -> listener.execute(
                message.envelopeId,
                envelopeResult.envelopeDocuments.first().documentId,
                authenticationSdk.accessTokenFor(auth0TokenVerificationConfig.audience)
            )
            ENVELOPE_SENT_EVENT -> listener.sent(envelopeId = message.envelopeId)
        }
    }

    private fun getListener(envelopeResult: Envelope): DocumentListener = kotlin.runCatching {
        envelopeResult.customFields.textCustomFields.find { it.name == CUSTOM_DOCUMENT_TYPE_METADATA }?.value.let {
                docType ->
            when (docType) {
                SignDocumentType.LEASE.name -> leaseDocuSignListener
                SignDocumentType.PSA.name -> psaDocuSignListener
                SignDocumentType.LOI.name -> loiDocuSignListener
                else -> loiDocuSignListener
            }
        }
    }.onFailure { e -> logger.error("error in getListener", e) }
        .getOrDefault(loiDocuSignListener)

    data class DocusignMessage(
        val event: String = "EMPTY_EVENT",
        val envelopeId: String,
        val recipientId: String?
    )
}
