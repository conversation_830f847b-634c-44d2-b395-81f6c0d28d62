package realestate.unlock.dealroom.api.infrastructure.gateway.files

import com.fasterxml.jackson.core.type.TypeReference
import realestate.unlock.dealroom.api.core.entity.directory.UrlType
import realestate.unlock.dealroom.api.core.entity.kfile.*
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.kfileservice.StagingFileToConfirm
import realestate.unlock.dealroom.api.infrastructure.client.rest.KFileRestClient
import realestate.unlock.dealroom.api.infrastructure.client.rest.parseBody
import realestate.unlock.dealroom.api.infrastructure.utils.extension.mapTo
import realestate.unlock.dealroom.api.infrastructure.utils.file.SanitizedFilename
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import java.io.File
import java.time.Instant
import javax.inject.Inject

class KFileServiceFileGateway @Inject constructor(
    private val kFileRestClient: KFileRestClient
) : FileGateway {

    companion object {
        private const val APP_ID = "deal-room"
    }

    override fun getFileWithUrl(kFileId: String, downloadName: SanitizedFilename?): KFileWithUrl {
        return kFileRestClient.restClient.get(
            path = "files/$APP_ID/$kFileId",
            responseType = String::class.java,
            queryParams = downloadName
                ?.let {
                    mapOf("download-name" to it.value)
                } ?: mapOf()
        ).parseBody(KFileResponse::class.java)
            .let {
                KFileWithUrl(kFileId, it.url!!, it.name)
            }
    }

    override fun getFiles(path: String): List<KPathFile> {
        return kFileRestClient.restClient.get(
            path = "files/$APP_ID",
            responseType = String::class.java,
            queryParams = mapOf("path" to path)
        ).parseBody(object : TypeReference<List<KFileResponse>>() {}).map {
            KPathFile(
                uid = it.id,
                name = it.name,
                path = it.path,
                sizeInBytes = it.sizeInBytes,
                lastModified = it.lastModified
            )
        }
    }

    override fun getFileUploadUrl(path: String): String =
        getFileURL(path = path, urlType = UrlType.UPLOAD).url

    override fun getFileDownloadUrl(path: String): String =
        getFileURL(path = path, urlType = UrlType.DOWNLOAD).url

    override fun confirmStagingFile(files: List<StagingFileToConfirm>) {
        kFileRestClient.restClient.put(
            path = "files/$APP_ID/staged",
            body = JsonMapper.encode(
                ConfirmStagingFiles(
                    applicationId = APP_ID,
                    stagedFiles = files.map { StagedFiles(id = it.kFileId, path = it.path) }
                )
            )
        )
    }

    override fun getStageUploadFileUrl(fileName: String): KFileWithUrl {
        return kFileRestClient.restClient.post(
            path = "files/$APP_ID/staged",
            body = JsonMapper.encode(StageFileUrlRequest(fileName))
        ).mapTo(StageFileUpload::class.java).let {
            KFileWithUrl(
                kFileId = it.id,
                name = fileName,
                url = it.uploadUrl!!
            )
        }
    }

    override fun uploadStagedFile(stagingFile: FileToStaging): KFile {
        return kFileRestClient.restClient.post(
            path = "files/$APP_ID/staged",
            fileName = stagingFile.filename,
            contentType = stagingFile.contentType,
            body = stagingFile.content
        ).mapTo(StageFileUpload::class.java).let {
            KFile(uid = it.id, stagingFile.filename)
        }
    }

    override fun uploadFile(fileToUpload: FileToUpload): KFile =
        kFileRestClient.restClient.post(
            path = "files/$APP_ID",
            queryParams = mapOf("path" to fileToUpload.path),
            fileName = fileToUpload.filename,
            contentType = fileToUpload.contentType,
            body = fileToUpload.content
        ).mapTo(StageFileUpload::class.java).let {
            KFile(uid = it.id, fileToUpload.filename)
        }

    override fun deleteFiles(paths: Set<String>) {
        kFileRestClient.restClient.delete(
            path = "files/$APP_ID",
            body = JsonMapper.encode(
                mapOf("paths" to paths)
            )
        )
    }

    override fun moveFiles(input: FileGateway.MoveFilesInput) {
        kFileRestClient.restClient.put(
            path = "files/$APP_ID",
            body = JsonMapper.encode(
                mapOf(
                    "files" to input.files.map {
                        mapOf(
                            "current_path" to it.sourcePath,
                            "new_path" to it.destinationPath
                        )
                    }
                )
            )
        )
    }

    override fun getFile(kFileId: String, fileName: String): KFileWithFile {
        val kFileUrl = getFileWithUrl(kFileId, downloadName = null)
        return KFileWithFile(
            kFileId = kFileId,
            file = downloadFile(kFileUrl.url, fileName)
        )
    }

    private fun downloadFile(url: String, fileName: String): File =
        kFileRestClient.getClientForUrl(url)
            .get(
                path = "",
                responseType = File::class.java,
                fileName = fileName
            ).body

    private fun getFileURL(path: String, urlType: UrlType): FileUrl =
        kFileRestClient.restClient.get(
            path = "files-url/$APP_ID",
            responseType = String::class.java,
            queryParams = mapOf(
                "path" to path,
                "url-type" to urlType.name
            )
        ).parseBody(FileUrl::class.java)

    data class FileUrl(
        val urlType: UrlType,
        val url: String
    )

    data class ConfirmStagingFiles(
        val applicationId: String,
        val stagedFiles: List<StagedFiles>
    )

    data class StagedFiles(
        val id: String,
        val path: String
    )

    data class KFileResponse(
        val id: String,
        val url: String? = null,
        val name: String,
        val sizeInBytes: Long,
        val lastModified: Instant,
        val path: String
    )

    data class StageFileUpload(
        val id: String,
        val uploadUrl: String?
    )

    data class StageFileUrlRequest(
        val filename: String
    )
}
