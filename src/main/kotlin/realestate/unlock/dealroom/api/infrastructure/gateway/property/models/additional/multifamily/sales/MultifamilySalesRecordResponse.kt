package realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.sales

import java.math.BigDecimal
import java.time.LocalDate

data class MultifamilySalesRecordResponse(
    val saleId: String? = null,
    val amount: BigDecimal? = null,
    val saleDate: LocalDate? = null,
    val buyer: String? = null,
    val seller: String? = null,
    val price: BigDecimal? = null,
    val pricePerSquareFoot: BigDecimal? = null,
    val saleTransactionType: String? = null
)
