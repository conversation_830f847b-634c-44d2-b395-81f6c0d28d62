package realestate.unlock.dealroom.api.infrastructure.gateway.property

import realestate.unlock.dealroom.api.infrastructure.client.rest.PropertyAssetsRestClient
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyAssetsResponse
import realestate.unlock.dealroom.api.infrastructure.utils.extension.mapTo
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapperCC
import javax.inject.Inject

class PropertyAssetsApi @Inject constructor(
    private val propertyAssetsRestClient: PropertyAssetsRestClient
) : PropertyAssetsGateway {

    override fun findOrCreateByAddress(input: PropertyAssetsGateway.Input) =
        propertyAssetsRestClient.restClient.post(
            path = "/properties",
            body = JsonMapperCC.encode(input)
        ).mapTo(PropertyAssetsResponse::class.java, JsonMapperCC)

    override fun findById(keywayId: String) =
        propertyAssetsRestClient.restClient.get(
            path = "/properties/$keywayId",
            responseType = String::class.java
        ).mapTo(PropertyAssetsResponse::class.java, JsonMapperCC)
}
