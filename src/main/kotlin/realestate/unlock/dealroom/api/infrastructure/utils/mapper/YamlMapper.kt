package realestate.unlock.dealroom.api.infrastructure.utils.mapper

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import com.fasterxml.jackson.module.kotlin.KotlinModule

object YamlMapper {
    private lateinit var objectMapper: ObjectMapper

    fun getMapper(): ObjectMapper {
        if (!this::objectMapper.isInitialized) {
            objectMapper = ObjectMapper(YAMLFactory()).apply {
                configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
                configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, true)
                disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                registerModule(KotlinModule.Builder().build())
                propertyNamingStrategy = PropertyNamingStrategies.SnakeCaseStrategy()
            }
        }
        return objectMapper
    }
}
