package realestate.unlock.dealroom.api.infrastructure.javalin

import io.javalin.Javalin
import io.javalin.plugin.json.JavalinJackson
import io.javalin.plugin.openapi.OpenApiPlugin
import realestate.unlock.dealroom.api.entrypoint.rest.security.AuthorizationManager
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Configuration
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import javax.inject.Inject
import javax.inject.Provider

class JavalinProvider @Inject constructor(
    val configuration: Configuration,
    val authorizationManager: AuthorizationManager
) : Provider<Javalin> {

    override fun get(): Javalin =
        Javalin.create { config ->
            config.asyncRequestTimeout = configuration.system.timeout
            config.accessManager(authorizationManager)
            config.jsonMapper(JavalinJackson(JsonMapper.getMapper()))
            config.enableCorsForAllOrigins()
            if (configuration.openApi.enabled) {
                config.registerPlugin(OpenApiPlugin(OpenApiDealRoomConfig.get()))
            }
        }
}
