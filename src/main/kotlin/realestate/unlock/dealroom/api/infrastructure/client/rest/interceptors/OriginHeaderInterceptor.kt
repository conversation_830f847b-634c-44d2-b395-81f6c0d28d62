package realestate.unlock.dealroom.api.infrastructure.client.rest.Interceptors

import kong.unirest.Config
import kong.unirest.HttpRequest
import kong.unirest.Interceptor
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Configuration

class OriginHeaderInterceptor(
    private val configuration: Configuration
) : Interceptor {

    override fun onRequest(request: HttpRequest<*>, config: Config?) {
        if (!request.headers.containsKey(ORIGIN_HEADER_NAME)) {
            request.headers.add(ORIGIN_HEADER_NAME, configuration.serviceName)
        }
        super.onRequest(request, config)
    }

    companion object {
        private const val ORIGIN_HEADER_NAME = "Origin"
    }
}
