package realestate.unlock.dealroom.api.infrastructure.gateway.auth0

import com.google.inject.Provider
import com.keyway.security.domain.authentication.AuthenticationSdk
import com.keyway.security.infra.auth0.authentication.AuthAPIConfig
import com.keyway.security.infra.auth0.authentication.AuthAPIProvider
import com.keyway.security.infra.auth0.authentication.AuthenticationAuth0Sdk
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Auth0ManagementClientConfig
import java.time.Clock
import javax.inject.Inject

class AuthenticationSdkProvider @Inject constructor(
    private val auth0ManagementClientConfig: Auth0ManagementClientConfig,
    private val clock: Clock
) : Provider<AuthenticationSdk> {
    override fun get(): AuthenticationSdk {
        return AuthenticationAuth0Sdk(
            authAPIProvider = AuthAPIProvider(
                AuthAPIConfig(
                    domain = auth0ManagementClientConfig.domain,
                    clientId = auth0ManagementClientConfig.clientId,
                    clientSecret = auth0ManagementClientConfig.clientSecret
                )
            ),
            clock
        )
    }
}
