package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener

import realestate.unlock.dealroom.api.core.entity.document.DocumentStatus
import realestate.unlock.dealroom.api.core.usecase.deal.documents.DocumentReadyToSign
import realestate.unlock.dealroom.api.core.usecase.deal.documents.ExecutePsa
import realestate.unlock.dealroom.api.core.usecase.deal.documents.SignDocument
import realestate.unlock.dealroom.api.core.usecase.exception.documents.InvalidDocumentStatusException
import javax.inject.Inject

class PsaDocuSignListener @Inject constructor(
    private val signDocument: SignDocument,
    private val executePsa: ExecutePsa,
    private val documentReadyToSign: DocumentReadyToSign
) : DocumentListener {

    companion object {
        private val statusToAvoidRetry = setOf(
            DocumentStatus.PENDING_SIGN,
            DocumentStatus.PENDING_BUYER_SIGN,
            DocumentStatus.PENDING_SELLER_SIGN
        )
    }

    override fun execute(envelopeId: String, documentId: String, authToken: String) =
        executePsa.execute(ExecutePsa.SignInput(envelopeId = envelopeId, documentId = documentId, authToken))

    override fun sign(envelopeId: String, recipientId: String) = signDocument.sign(
        SignDocument.Input(
            envelopeId = envelopeId,
            recipientId = recipientId
        )
    )

    override fun sent(envelopeId: String) {
        try {
            documentReadyToSign.markAsSent(envelopeId)
        } catch (e: InvalidDocumentStatusException) {
            if (e.status !in statusToAvoidRetry) {
                throw e
            }
        }
    }
}
