package realestate.unlock.dealroom.api.infrastructure.client.rest.exception

import org.eclipse.jetty.http.HttpStatus
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.base.ErrorRequestException

open class UnprocessableEntityException(
    message: String = HttpStatus.Code.UNPROCESSABLE_ENTITY.message,
    errorCode: String = "UNPROCESSABLE_ENTITY",
    data: Any? = null,
    cause: Throwable? = null
) : ErrorRequestException(
    message = message,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.UNPROCESSABLE_ENTITY.code,
    cause = cause,
    data = data
)
