package realestate.unlock.dealroom.api.infrastructure.gateway.auth0

import com.auth0.jwk.UrlJwkProvider
import com.google.inject.Inject
import com.google.inject.Provider
import com.keyway.security.domain.token.TokenSdk
import com.keyway.security.infra.auth0.token.Auth0TokenSdk
import com.keyway.security.repository.algorithm.AlgorithmCacheRepository
import realestate.unlock.dealroom.api.infrastructure.configuration.model.Auth0TokenVerificationConfig

class TokenSdkProvider @Inject constructor(
    private val auth0TokenVerificationConfig: Auth0TokenVerificationConfig
) : Provider<TokenSdk> {

    override fun get(): TokenSdk =
        Auth0TokenSdk(
            issuer = auth0TokenVerificationConfig.issuer,
            audience = auth0TokenVerificationConfig.audiences,
            algorithmRepository = AlgorithmCacheRepository(UrlJwkProvider(auth0TokenVerificationConfig.domain))
        )
}
