package realestate.unlock.dealroom.api.infrastructure.javalin

import io.javalin.plugin.openapi.InitialConfigurationCreator
import io.javalin.plugin.openapi.OpenApiOptions
import io.javalin.plugin.openapi.jackson.JacksonModelConverterFactory
import io.javalin.plugin.openapi.ui.ReDocOptions
import io.javalin.plugin.openapi.ui.SwaggerOptions
import io.swagger.v3.oas.models.Components
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.Info
import io.swagger.v3.oas.models.security.SecurityScheme
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.infrastructure.client.rest.contract.Response
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper

object OpenApiDealRoomConfig {

    fun get(): OpenApiOptions =
        InitialConfigurationCreator {
            OpenAPI()
                .info(Info().title("Deal Room API").version("1.0.0").description("Deal Room API"))
                .components(
                    Components()
                        .addSecuritySchemes(
                            "bearerAuth",
                            SecurityScheme()
                                .`in`(SecurityScheme.In.HEADER)
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                        )
                )
        }
            .let(::OpenApiOptions)
            .path("/api/docs")
            .swagger(SwaggerOptions("/swagger").title("Deal Room API Docs"))
            .reDoc(ReDocOptions("/redoc").title("Deal Room API Docs"))
            .modelConverterFactory(JacksonModelConverterFactory(JsonMapper.getMapper()))
            .defaultDocumentation {
                it.json(HttpStatus.INTERNAL_SERVER_ERROR.toString(), returnType = Response::class.java)
            }
}
