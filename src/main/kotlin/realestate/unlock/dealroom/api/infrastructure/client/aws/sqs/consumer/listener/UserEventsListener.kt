package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener

import com.keyway.kommons.sqs.IMessageHandler
import realestate.unlock.dealroom.api.core.entity.member.MemberCreationInput
import realestate.unlock.dealroom.api.core.entity.member.type.MemberTypeEnum
import realestate.unlock.dealroom.api.core.entity.user.profile.ProfileUpdatingInput
import realestate.unlock.dealroom.api.core.usecase.member.CreateMember
import realestate.unlock.dealroom.api.core.usecase.member.DisableMember
import realestate.unlock.dealroom.api.core.usecase.member.GetMemberByAuthId
import realestate.unlock.dealroom.api.core.usecase.member.UpdateMemberProfile
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapperCC
import software.amazon.awssdk.services.sqs.model.Message
import javax.inject.Inject

class UserEventsListener @Inject constructor(
    private val createMember: CreateMember,
    private val getMemberByAuthId: GetMemberByAuthId,
    private val disableMember: DisableMember,
    private val updateMemberProfile: UpdateMemberProfile
) : IMessageHandler {

    private val logger by LoggerDelegate()

    override fun processMessage(message: Message): Boolean =

        runCatching {
            processMessage(JsonMapperCC.decode(message.body(), UserEventInput::class.java))
            true
        }.onFailure {
            logger.error("Error while processing user event", it)
        }.getOrDefault(false)

    private fun processMessage(input: UserEventInput) {
        when (input.type) {
            UserEventType.NEW_USER -> createMember.create(
                input = MemberCreationInput(
                    memberType = MemberTypeEnum.BUYER.key,
                    email = input.email,
                    firstName = input.name.split(" ").firstOrNull() ?: input.name,
                    lastName = input.name.split(" ").lastOrNull() ?: input.name,
                    address = null,
                    phoneNumber = null,
                    companyName = "",
                    organizationId = input.organizationId,
                    needUser = false,
                    authId = input.externalUserId
                )
            )
            UserEventType.UPDATED_USER ->
                getMemberByAuthId.get(input.id).let {
                    updateMemberProfile.update(
                        profileUpdatingInput = ProfileUpdatingInput(
                            memberId = it.id,
                            firstName = input.name.split(" ").firstOrNull() ?: input.name,
                            lastName = input.name.split(" ").lastOrNull() ?: input.name,
                            phoneNumber = null
                        )
                    )
                }
            UserEventType.DELETED_USER ->
                getMemberByAuthId.get(input.id).let {
                    disableMember.disable(it.id)
                }
        }
    }

    data class UserEventInput(
        val id: String,
        val externalUserId: String,
        val email: String,
        val name: String,
        val type: UserEventType,
        val organizationId: String
    )

    enum class UserEventType {
        NEW_USER,
        UPDATED_USER,
        DELETED_USER
    }
}
