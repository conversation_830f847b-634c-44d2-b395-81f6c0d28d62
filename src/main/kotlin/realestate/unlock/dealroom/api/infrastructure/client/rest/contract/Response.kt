package realestate.unlock.dealroom.api.infrastructure.client.rest.contract

import org.eclipse.jetty.http.HttpStatus
import java.time.OffsetDateTime

data class Response<T>(
    val status: Int,
    val message: String = "",
    val errorCode: String? = null,
    val timestamp: OffsetDateTime = OffsetDateTime.now(),
    val data: T
) {
    constructor(data: T, httpStatus: HttpStatus.Code) :
        this(
            data = data,
            message = httpStatus.message,
            status = httpStatus.code
        )
}
