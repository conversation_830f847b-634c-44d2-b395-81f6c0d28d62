package realestate.unlock.dealroom.api.infrastructure.gateway.jwt

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions.JWTVerificationException
import realestate.unlock.dealroom.api.core.exception.InvalidTokenException
import realestate.unlock.dealroom.api.core.gateway.jwt.JWTGateway
import realestate.unlock.dealroom.api.infrastructure.configuration.model.JwtGatewayConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import java.time.Clock
import javax.inject.Inject

class JWTAuthGateway @Inject constructor(
    private val clock: Clock,
    private val jwtGatewayConfig: JwtGatewayConfig
) : JWTGateway {

    companion object {
        private val logger by LoggerDelegate()
    }

    private val algorithm = Algorithm.HMAC256(jwtGatewayConfig.secret)

    override fun generateToken(claims: Map<String, String>): String {
        val expirationDate = clock.instant()
            .plusSeconds(jwtGatewayConfig.tokenExpirationInSeconds)
        val token = JWT.create()
            .withIssuer(jwtGatewayConfig.issuer)
            .withExpiresAt(expirationDate)

        claims.entries.forEach {
            token.withClaim(it.key, it.value)
        }

        return token.sign(algorithm)
    }

    override fun getClaimsFromToken(token: String): Map<String, String> {
        if (!verifyJWTToken(token)) {
            throw InvalidTokenException("Invalid token: $token")
        }
        return JWT.decode(token).claims.map { it.key to it.value.asString() }.toMap()
    }

    private fun verifyJWTToken(token: String): Boolean {
        return try {
            val verifier = JWT.require(algorithm)
                .withIssuer(jwtGatewayConfig.issuer)
                .acceptExpiresAt(jwtGatewayConfig.tokenExpirationInSeconds)
                .build()
            verifier.verify(token)
            true
        } catch (ex: JWTVerificationException) {
            logger.warn("invalid token", ex)
            false
        }
    }
}
