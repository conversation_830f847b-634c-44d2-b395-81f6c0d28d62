package realestate.unlock.dealroom.api.infrastructure.client.aws.sns

import realestate.unlock.dealroom.api.core.entity.deal.Deal
import realestate.unlock.dealroom.api.core.entity.property.Property
import realestate.unlock.dealroom.api.core.event.deal.DealEvent
import realestate.unlock.dealroom.api.core.event.deal.DealEventPublisher
import realestate.unlock.dealroom.api.core.event.deal.EventType
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.core.repository.property.PropertyRepository
import realestate.unlock.dealroom.api.core.usecase.exception.deal.DealNotFoundException
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sns.AwsSNSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapperCC
import javax.inject.Inject

class SnsDealEventPublisher @Inject constructor(
    private val snsClient: AwsSNSClient,
    private val snsConfig: AwsSNSConfig,
    private val propertyRepository: PropertyRepository,
    private val dealRepository: DealRepository
) : DealEventPublisher {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun publish(eventType: EventType, deal: Deal, property: Property) {
        publish(
            DealEvent(
                type = eventType,
                deal = deal,
                property = property
            )
        )
    }

    override fun publish(deal: Deal) {
        publish(
            DealEvent(
                type = EventType.DEAL_UPDATE,
                deal = deal,
                property = propertyRepository.findById(deal.propertyId)
            )
        )
    }

    override fun publish(property: Property) {
        val deal = dealRepository.findByPropertyId(property.id)
            ?: throw DealNotFoundException("Deal for property [${property.id}] was not found")

        publish(
            DealEvent(
                type = EventType.DEAL_UPDATE,
                deal = deal,
                property = property
            )
        )
    }

    private fun publish(dealEvent: DealEvent) {
        kotlin.runCatching {
            val message = JsonMapperCC.encode(dealEvent)
            snsClient.publish(message, snsConfig.dealEventsTopicArn)
        }.onFailure {
            logger.error("Error publishing deal event", it)
        }
    }
}
