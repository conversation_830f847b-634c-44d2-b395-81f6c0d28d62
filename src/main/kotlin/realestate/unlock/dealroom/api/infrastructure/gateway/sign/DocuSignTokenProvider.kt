package realestate.unlock.dealroom.api.infrastructure.gateway.sign

import com.docusign.esign.client.ApiClient
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DocuSignConfig
import javax.inject.Inject

class DocuSignTokenProvider @Inject constructor(
    private val docuSignConfig: DocuSignConfig
) {
    companion object {
        private val SCOPES = listOf("signature")
    }

    // TODO: Store the token and evaluate if we need to fetch it again
    fun getToken(apiClient: ApiClient, userId: String): AccessToken =
        apiClient.requestJWTUserToken(
            docuSignConfig.appIntegrationKey,
            userId,
            SCOPES,
            getPrivateKeyBytes(),
            docuSignConfig.tokenExpirationSeconds
        ).let { AccessToken(it.accessToken, it.tokenType) }

    private fun getPrivateKeyBytes(): ByteArray {
        return javaClass.classLoader.getResourceAsStream(docuSignConfig.keyFileName)?.reader()?.readText()?.byteInputStream()?.readBytes()
            ?: throw DocusignKeyNotFoundException(
                listOf(
                    "src/main/resources/${docuSignConfig.keyFileName}"
                )
            )
    }
}

data class AccessToken(val token: String, val type: String)
