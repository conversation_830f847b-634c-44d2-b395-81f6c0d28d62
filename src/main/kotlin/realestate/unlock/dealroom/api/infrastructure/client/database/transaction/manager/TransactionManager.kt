package realestate.unlock.dealroom.api.infrastructure.client.database.transaction.manager

import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.TransactionalDataSource
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate

object TransactionManager {

    private lateinit var dataSource: TransactionalDataSource

    private val logger by LoggerDelegate()

    fun initialize(dataSource: TransactionalDataSource) {
        this.dataSource = dataSource
    }

    fun <T> useTransaction(
        propagateTransaction: Boolean = true,
        operation: () -> T
    ): T =
        dataSource.let { txDataSource ->
            val alreadyExistsATransaction = txDataSource.isTransactionOpen()
            try {
                if (!alreadyExistsATransaction || !propagateTransaction) {
                    txDataSource.beginTransaction()
                }
                operation().also {
                    if (!alreadyExistsATransaction || !propagateTransaction) {
                        txDataSource.finalizeTransaction()
                    }
                }
            } catch (e: Throwable) {
                if (!alreadyExistsATransaction || !propagateTransaction) {
                    logger.error("transaction will be rollback", e)
                    txDataSource.rollback()
                }
                throw e
            }
        }
}
