package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener

import com.keyway.kommons.sqs.IMessageHandler
import realestate.unlock.dealroom.api.core.usecase.task.update.NotifyMembersAboutTaskChanged
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import software.amazon.awssdk.services.sqs.model.Message
import javax.inject.Inject

class TaskChangedListener @Inject constructor(
    private val notifyMembersAboutTaskChanged: NotifyMembersAboutTaskChanged
) : IMessageHandler {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun processMessage(message: Message): Boolean {
        logger.trace("Listening task changed message $message")
        return try {
            val taskId = getTaskId(message)
            notifyMembersAboutTaskChanged(taskId)
            logger.trace("Task changed notified successful")
            true
        } catch (e: Throwable) {
            logger.error("error on process task changed message", e)
            false
        }
    }

    private fun getTaskId(sqsMessage: Message): Long {
        val body = JsonMapper.getMapper().readTree(sqsMessage.body())
        val message = JsonMapper.getMapper().readTree(body["Message"].asText())
        return message["id"].asLong()
    }
}
