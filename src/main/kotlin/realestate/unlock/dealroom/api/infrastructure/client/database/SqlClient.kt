package realestate.unlock.dealroom.api.infrastructure.client.database

import realestate.unlock.dealroom.api.core.extension.mapTo
import realestate.unlock.dealroom.api.infrastructure.client.database.exception.DuplicateKeyException
import realestate.unlock.dealroom.api.infrastructure.client.database.exception.InvalidRowsNumberException
import realestate.unlock.dealroom.api.infrastructure.client.database.exception.NoRowFoundException
import realestate.unlock.dealroom.api.infrastructure.client.database.extension.convertToMap
import realestate.unlock.dealroom.api.infrastructure.client.database.extension.convertToMaps
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.TransactionalDataSource
import realestate.unlock.dealroom.api.infrastructure.utils.extension.trimIndentWithoutLineBreaks
import realestate.unlock.dealroom.api.repository.database.addFilters
import realestate.unlock.dealroom.api.repository.database.addPagination
import java.sql.PreparedStatement
import java.sql.ResultSet
import java.sql.SQLException

class SqlClient(
    private val dataSource: TransactionalDataSource
) {

    @Throws(SQLException::class)
    fun <T> get(
        query: String,
        params: List<Any> = listOf(),
        resultSetHandler: (ResultSet) -> T
    ): T = dataSource.useConnection { connection ->
        connection.prepareStatement(query).use { preparedStatement ->
            params.forEachIndexed { index, param ->
                preparedStatement.setObject(index.inc(), param)
            }
            preparedStatement.executeQuery().use(resultSetHandler)
        }
    }

    @Throws(NoRowFoundException::class, InvalidRowsNumberException::class, SQLException::class)
    fun <T> getOneOrFail(
        query: String,
        params: List<Any> = listOf(),
        clazz: Class<T>,
        messageKey: String
    ): T {
        return this.get(query, params) { resultSet ->
            val mappedResultSet = resultSet.convertToMaps()

            when (mappedResultSet.size) {
                0 -> throw NoRowFoundException("$messageKey - There were no rows found for the query [query: ${query.trimIndentWithoutLineBreaks()}, params: $params, class_name= $clazz]")
                1 -> mappedResultSet.mapTo(clazz).first()
                else -> throw InvalidRowsNumberException("$messageKey - There was more than 1 row found for the query [query: $query, params: $params, class_name= $clazz]")
            }
        }
    }

    @Throws(NoRowFoundException::class, InvalidRowsNumberException::class, SQLException::class)
    fun <T> getOneOrFail(
        query: String,
        params: List<Any> = listOf(),
        messageKey: String,
        mapHandler: (Map<String, Any>) -> T
    ): T {
        return this.get(query, params) { resultSet ->
            val mappedResultSet = resultSet.convertToMaps()

            when (mappedResultSet.size) {
                0 -> throw NoRowFoundException("$messageKey - There were no rows found for the query [query: ${query.trimIndentWithoutLineBreaks()}, params: $params]")
                1 -> mappedResultSet.first().let(mapHandler)
                else -> throw InvalidRowsNumberException("$messageKey - There was more than 1 row found for the query [query: $query, params: $params]")
            }
        }
    }

    @Throws(NoRowFoundException::class, InvalidRowsNumberException::class, SQLException::class)
    fun <T> get(
        query: String,
        params: List<Any> = listOf(),
        clazz: Class<T>
    ): T? {
        return this.get(query, params) { resultSet ->
            resultSet.convertToMap()?.mapTo(clazz)
        }
    }

    @Throws(SQLException::class)
    fun <T> getAll(
        query: String,
        params: List<Any> = listOf(),
        clazz: Class<T>
    ): List<T> {
        return this.get(query, params) { it.convertToMaps().mapTo(clazz) }
    }

    @Throws(SQLException::class)
    fun <T> getAll(
        query: String,
        params: List<Any> = listOf(),
        clazz: Class<T>,
        size: Int,
        offset: Int,
        orderBy: String,
        order: String,
        where: String,
        withTotalCount: Boolean
    ): List<T> {
        return this.get(
            query.addPagination(
                size = size,
                offset = offset,
                orderBy = orderBy,
                order = order,
                where = where,
                withTotalCount = withTotalCount
            ),
            params
        ) { it.convertToMaps().mapTo(clazz) }
    }

    @Throws(SQLException::class)
    fun <T> getAll(
        query: String,
        params: List<Any> = listOf(),
        mapHandler: (Map<String, Any>) -> T
    ): List<T> {
        return this.get(query, params) { it.convertToMaps().map(mapHandler) }
    }

    @Throws(SQLException::class)
    fun <T> getAll(
        query: String,
        params: List<Any> = listOf(),
        size: Int,
        offset: Int,
        orderBy: String,
        order: String,
        where: String,
        withTotalCount: Boolean,
        mapHandler: (Map<String, Any>) -> T
    ): List<T> {
        return this.get(
            query.addPagination(
                size = size,
                offset = offset,
                orderBy = orderBy,
                order = order,
                where = where,
                withTotalCount = withTotalCount
            ),
            params
        ) { it.convertToMaps().map(mapHandler) }
    }

    @Throws(SQLException::class)
    fun update(query: String, params: List<Any>): Int {
        return dataSource.useConnection { connection ->
            connection.prepareStatement(query).use { preparedStatement ->
                params.forEachIndexed { index, param ->
                    preparedStatement.setObject(index.inc(), param)
                }
                preparedStatement.executeUpdate()
            }
        }
    }

    @Throws(SQLException::class)
    fun <T> getAll(
        query: String,
        params: List<Any> = listOf(),
        where: String,
        clazz: Class<T>
    ): List<T> = this.get(query.addFilters(where), params) { it.convertToMaps().mapTo(clazz) }

    @Throws(SQLException::class)
    fun <T> update(
        query: String,
        params: List<Any?>,
        resultSetHandler: (ResultSet) -> T
    ): T {
        return dataSource.useConnection { connection ->
            connection.prepareStatement(query).use { preparedStatement ->
                params.forEachIndexed { index, param ->
                    preparedStatement.setObject(index.inc(), param)
                }
                preparedStatement.catchingDuplicatedKey { it.executeQuery() }.use(resultSetHandler)
            }
        }
    }

    @Throws(SQLException::class)
    fun <T> update(
        query: String,
        params: List<Any>,
        messageKey: String,
        clazz: Class<T>
    ): T {
        return dataSource.useConnection { connection ->
            connection.prepareStatement(query).use { preparedStatement ->
                preparedStatement.also {
                    params.forEachIndexed { index, param ->
                        it.setObject(index.inc(), param)
                    }
                }.catchingDuplicatedKey { it.executeQuery() }.use { it.convertToMaps() }.let { mappedResultSet ->
                    when (mappedResultSet.size) {
                        0 -> throw NoRowFoundException("$messageKey - There were no rows returned for query [query: ${query.trimIndentWithoutLineBreaks()}, class_name= $clazz]")
                        1 -> mappedResultSet.mapTo(clazz).first()
                        else -> throw InvalidRowsNumberException("$messageKey - There was more than 1 row returned for query [query: $query, class_name= $clazz]")
                    }
                }
            }
        }
    }

    @Throws(SQLException::class)
    fun update(query: String, preparedStatementHandler: (PreparedStatement) -> Unit): Int {
        return dataSource.useConnection { connection ->
            connection.prepareStatement(query).use { preparedStatement ->
                preparedStatement.also(preparedStatementHandler).executeUpdate()
            }
        }
    }

    @Throws(SQLException::class)
    fun batchUpdate(query: String, preparedStatementHandler: (PreparedStatement) -> Unit): IntArray {
        return dataSource.useConnection { connection ->
            connection.prepareStatement(query).use { preparedStatement ->
                preparedStatement.also(preparedStatementHandler).executeBatch()
            }
        }
    }

    @Throws(SQLException::class)
    fun <T> update(
        query: String,
        messageKey: String,
        clazz: Class<T>,
        preparedStatementHandler: (PreparedStatement) -> Unit
    ): T = dataSource.useConnection { connection ->
        connection.prepareStatement(query).use { preparedStatement ->
            preparedStatement.also(preparedStatementHandler).catchingDuplicatedKey { it.executeQuery() }.use { it.convertToMaps() }.let { mappedResultSet ->
                when (mappedResultSet.size) {
                    0 -> throw NoRowFoundException("$messageKey - There were no rows returned for query [query: ${query.trimIndentWithoutLineBreaks()}, class_name= $clazz]")
                    1 -> mappedResultSet.mapTo(clazz).first()
                    else -> throw InvalidRowsNumberException("$messageKey - There was more than 1 row returned for query [query: $query, class_name= $clazz]")
                }
            }
        }
    }

    @Throws(SQLException::class)
    fun updateOneOrFail(query: String, params: List<Any>) {
        dataSource.useConnection { connection ->
            connection.prepareStatement(query).use { preparedStatement ->
                params.forEachIndexed { index, param ->
                    preparedStatement.setObject(index.inc(), param)
                }
                preparedStatement.executeUpdate().let { rowsAffected ->
                    if (rowsAffected != 1) {
                        throw InvalidRowsNumberException("There was more than 1 row returned for query [query: $query]")
                    }
                }
            }
        }
    }

    private fun <T, R> T.catchingDuplicatedKey(block: (T) -> R): R = try {
        block.invoke(this)
    } catch (e: SQLException) {
        throw if (e.sqlState == "23505") DuplicateKeyException(e.message!!, e) else e
    }

    fun nextId(sequence: String): Long {
        return getOneOrFail(
            query = """
                SELECT nextval(?); 
            """.trimIndent(),
            clazz = SequenceDbModel::class.java,
            params = listOf(sequence),
            messageKey = "GET_NEXT_ID"
        ).nextval
    }

    private data class SequenceDbModel(
        val nextval: Long
    )
}
