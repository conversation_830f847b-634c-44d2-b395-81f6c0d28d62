package realestate.unlock.dealroom.api.infrastructure.gateway.sign.builders

import com.docusign.esign.model.Notification
import com.docusign.esign.model.Reminders
import realestate.unlock.dealroom.api.core.gateway.sign.Reminder

class NotificationBuilder {
    lateinit var reminder: Reminder

    fun build(): Notification {
        return Notification()
            .reminders(
                Reminders()
                    .reminderEnabled("true")
                    .reminderDelay(reminder.delay.toDays().toString())
            )
    }
}
