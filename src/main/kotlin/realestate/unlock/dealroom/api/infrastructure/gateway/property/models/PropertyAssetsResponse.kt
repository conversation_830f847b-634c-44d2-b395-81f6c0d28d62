package realestate.unlock.dealroom.api.infrastructure.gateway.property.models

import com.fasterxml.jackson.annotation.JsonTypeInfo
import realestate.unlock.dealroom.api.core.entity.deal.SourceType
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.PropertyAdditionalDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.images.PropertyImagesResponse
import java.math.BigDecimal

data class PropertyAssetsResponse(
    val id: String,
    val address: String,
    val fullAddress: String,
    val city: String,
    val county: String,
    val zipCode: Long,
    val state: String,
    val tractCode: Long?,
    val location: GeoPointResponse,
    val propertyType: PropertyType,
    val squareFootage: BigDecimal?,
    val sourceType: SourceType?,
    val constructionYear: Int?,
    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXTERNAL_PROPERTY, property = "propertyType")
    val additionalData: PropertyAdditionalDataResponse?,
    val images: PropertyImagesResponse?
)

enum class PropertyType {
    MULTIFAMILY,
    MEDICAL_OFFICE
}
