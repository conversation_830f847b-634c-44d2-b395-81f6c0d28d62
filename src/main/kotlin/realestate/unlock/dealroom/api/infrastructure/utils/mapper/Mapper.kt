package realestate.unlock.dealroom.api.infrastructure.utils.mapper

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper

interface Mapper {
    fun getMapper(): ObjectMapper
    fun encode(obj: Any): String
    fun <T> decode(str: String, clazz: Class<T>): T
    fun <T> decode(str: String, typeReference: TypeReference<T>): T
    fun <T> mapTo(map: Map<String, Any?>, clazz: Class<out T>): T
    fun <K, V> convertToMap(any: Any): Map<K, V>
}
