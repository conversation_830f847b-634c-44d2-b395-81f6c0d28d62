package realestate.unlock.dealroom.api.infrastructure.configuration.parser

import realestate.unlock.dealroom.api.infrastructure.configuration.model.Configuration
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.YamlMapper

object ConfigParser {

    private val logger by LoggerDelegate()

    private lateinit var configuration: Configuration

    private const val PREFIX_ENV_KEY = "\${"
    private const val SUFFIX_ENV_KEY = "}"
    private const val SERVER_SCOPE = "server"
    private const val TEST_SCOPE = "test"

    fun read(basePath: String = "configuration", isTest: Boolean = false): Configuration {
        if (!this::configuration.isInitialized) {
            configuration = resolveScope(isTest)
                .let { configFileName ->
                    logger.info("Reading configurations from: $configFileName")
                    javaClass.classLoader.getResourceAsStream("$basePath/$configFileName.yml").reader().readText()
                }.let(this::replaceEnvVars).let { configString ->
                    YamlMapper.getMapper().readValue(configString, Configuration::class.java)
                }
        }

        return configuration
    }

    private fun resolveScope(isTest: Boolean): String = if (isTest) {
        TEST_SCOPE
    } else {
        SERVER_SCOPE
    }

    private fun replaceEnvVars(config: String): String =
        config.indexOf(PREFIX_ENV_KEY)
            .let { prefixIdx ->
                if (prefixIdx >= 0) {
                    config.indexOf(SUFFIX_ENV_KEY, prefixIdx).let { suffixIdx ->
                        config.substring(prefixIdx.plus(PREFIX_ENV_KEY.length), suffixIdx)
                    }.let { envKey ->
                        (
                            System.getenv(envKey)
                                ?: throw IllegalStateException("Environment variable $envKey was not defined.")
                            )
                            .let { envValue ->
                                replaceEnvVars(
                                    config.replace(
                                        oldValue = "$PREFIX_ENV_KEY$envKey$SUFFIX_ENV_KEY",
                                        newValue = envValue
                                    )
                                )
                            }
                    }
                } else {
                    config
                }
            }
}
