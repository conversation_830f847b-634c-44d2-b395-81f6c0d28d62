package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer

import com.google.inject.name.Named
import com.keyway.kommons.aws.config.AwsConfig
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.UserEventsListener
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class SQSUserEventsConsumer @Inject constructor(
    awsConfig: AwsConfig,
    @Named("sqsUserEvent") sqsUserEvent: AwsSQSConfig,
    userEventsListener: UserEventsListener
) {
    companion object {
        private val logger by LoggerDelegate()
    }

    init {
        logger.trace("initializing sqs consumer with configuration $sqsUserEvent")
        val consumer = SQSConsumerBuilder(
            awsConfig = awsConfig,
            awsSQSConfig = sqsUserEvent,
            messageHandler = userEventsListener
        ).build()
        consumer.start()
        logger.trace("sqs consumer initialized successfully")
    }
}
