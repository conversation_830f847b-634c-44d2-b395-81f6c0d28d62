package realestate.unlock.dealroom.api.infrastructure.module

import com.auth0.client.auth.AuthAPI
import com.auth0.jwk.UrlJwkProvider
import com.google.api.services.calendar.Calendar
import com.google.inject.AbstractModule
import com.google.inject.multibindings.Multibinder
import com.google.inject.name.Names
import com.keyway.kommons.http.interceptors.HeadersInterceptor
import com.keyway.kommons.http.interceptors.ResponseStatusInterceptor
import com.keyway.kommons.http.interceptors.UUIDCorrelationIdProvider
import com.keyway.security.domain.authentication.AuthenticationSdk
import com.keyway.security.domain.token.TokenSdk
import com.keyway.security.domain.user.UserImpersonationSdk
import com.keyway.security.domain.user.UserSdk
import com.sendgrid.SendGrid
import io.javalin.Javalin
import io.split.client.SplitClient
import kong.unirest.Config
import kong.unirest.UnirestInstance
import kotlinx.coroutines.CoroutineScope
import realestate.unlock.dealroom.api.core.gateway.auth.AuthorizationService
import realestate.unlock.dealroom.api.core.gateway.auth.UserService
import realestate.unlock.dealroom.api.core.gateway.calendar.CalendarGateway
import realestate.unlock.dealroom.api.core.gateway.email.EmailGateway
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags
import realestate.unlock.dealroom.api.core.gateway.gpt.ChatGptGateway
import realestate.unlock.dealroom.api.core.gateway.jwt.JWTGateway
import realestate.unlock.dealroom.api.core.gateway.kfileservice.FileGateway
import realestate.unlock.dealroom.api.core.gateway.property.PropertyDetailsGateway
import realestate.unlock.dealroom.api.core.gateway.sign.SignGateway
import realestate.unlock.dealroom.api.core.usecase.calendar.eventfinder.ClosingDateExpirationCalendarEventFinder
import realestate.unlock.dealroom.api.core.usecase.calendar.eventfinder.DealDateExpirationCalendarEventFinder
import realestate.unlock.dealroom.api.core.usecase.calendar.eventfinder.DiligenceExpirationCalendarEventFinder
import realestate.unlock.dealroom.api.core.usecase.member.filter.FilterMember
import realestate.unlock.dealroom.api.core.usecase.member.filter.FilterMemberByEnabled
import realestate.unlock.dealroom.api.core.usecase.member.filter.FilterMemberByMemberType
import realestate.unlock.dealroom.api.entrypoint.rest.handler.error.ErrorHandler
import realestate.unlock.dealroom.api.entrypoint.rest.router.base.HealthCheckRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.calendar.CalendarRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.dashboard.DashboardRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.deal.*
import realestate.unlock.dealroom.api.entrypoint.rest.router.deal.loi.LoiRoundRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.deal.loi.LoiRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.deal.loi.file.LoiFileRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.file.FileRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.keypilot.KeyPilotRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.member.MemberRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.pipeline.PipelineViewRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.property.PropertyRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.prospect.ProspectRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.tag.TagRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.task.TaskRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.task.file.TaskFileRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.user.UserRouter
import realestate.unlock.dealroom.api.entrypoint.rest.router.vertical.VerticalRouter
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.DocuSignListener
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.TaskChangedListener
import realestate.unlock.dealroom.api.infrastructure.client.database.SqlClient
import realestate.unlock.dealroom.api.infrastructure.client.database.transaction.datasource.TransactionalDataSource
import realestate.unlock.dealroom.api.infrastructure.client.rest.*
import realestate.unlock.dealroom.api.infrastructure.client.sendgrid.SendgridProvider
import realestate.unlock.dealroom.api.infrastructure.configuration.model.*
import realestate.unlock.dealroom.api.infrastructure.configuration.parser.ConfigParser
import realestate.unlock.dealroom.api.infrastructure.gateway.auth0.*
import realestate.unlock.dealroom.api.infrastructure.gateway.calendar.GoogleCalendarGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.email.SendgridEmailGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.email.SendgridMailSender
import realestate.unlock.dealroom.api.infrastructure.gateway.files.KFileServiceFileGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.gpt.ChatGptServiceGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.jwt.JWTAuthGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.property.PropertyAssetsApi
import realestate.unlock.dealroom.api.infrastructure.gateway.property.PropertyAssetsGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.property.PropertyGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.PropertySageApi
import realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.PropertySageGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.DocuSignSignGateway
import realestate.unlock.dealroom.api.infrastructure.gateway.splitio.FeatureFlagsSplitIo
import realestate.unlock.dealroom.api.infrastructure.javalin.JavalinProvider
import realestate.unlock.dealroom.api.infrastructure.javalin.Router
import realestate.unlock.dealroom.api.infrastructure.module.provider.DataSourceProvider
import realestate.unlock.dealroom.api.infrastructure.module.provider.GoogleCalendarProvider
import realestate.unlock.dealroom.api.infrastructure.module.provider.SplitIoClientProvider
import realestate.unlock.dealroom.api.infrastructure.module.provider.SqlClientProvider
import realestate.unlock.dealroom.api.infrastructure.utils.coroutine.scope.provider.CoroutineScopeProvider
import java.time.Clock

class AppModule : AbstractModule() {

    override fun configure() {
        // Clock
        val clock = Clock.systemUTC()
        bind(Clock::class.java).toInstance(clock)

        // Configurations
        val config = ConfigParser.read()
        bind(Configuration::class.java).toInstance(config)
        bind(SystemConfig::class.java).toInstance(config.system)
        bind(DatabaseConfig::class.java).toInstance(config.postgresDatabase)
        bind(SendgridConfig::class.java).toInstance(config.sendgrid)
        bind(WebAppsConfig::class.java).toInstance(config.webApps)
        bind(OpenApiConfig::class.java).toInstance(config.openApi)
        bind(DocuSignConfig::class.java).toInstance(config.docuSign)
        bind(DealTaskConfig::class.java).toInstance(config.dealTasks)
        bind(LoiSentEmailConfig::class.java).toInstance(config.loiSentEmail)
        bind(SplitIoConfig::class.java).toInstance(config.splitIo)
        bind(Auth0ManagementClientConfig::class.java).toInstance(config.auth0ManagementClient)
        bind(Auth0AppClientIdsConfig::class.java).toInstance(config.auth0AppClientIds)
        bind(Auth0TokenVerificationConfig::class.java).toInstance(config.auth0TokenVerification)
        bind(DealFilesAndFoldersConfig::class.java)
            .toInstance(DealFilesAndFoldersConfig("__deal_room_pixel_folder__.txt"))
        bind(ChatGptServiceTokenConfig::class.java).toInstance(config.chatGptServiceToken)
        bind(GoogleCalendarConfig::class.java).toInstance(config.googleCalendarConfig)

        // Javalin
        bind(Javalin::class.java).toProvider(JavalinProvider::class.java).asEagerSingleton()

        // Routers
        val routerBinder = Multibinder.newSetBinder(binder(), Router::class.java)
        routerBinder.addBinding().to(HealthCheckRouter::class.java)
        routerBinder.addBinding().to(MemberRouter::class.java)
        routerBinder.addBinding().to(PropertyRouter::class.java)
        routerBinder.addBinding().to(DealRouter::class.java)
        routerBinder.addBinding().to(DealFilesRouter::class.java)
        routerBinder.addBinding().to(DealSchemaRouter::class.java)
        routerBinder.addBinding().to(DealCategoriesRouter::class.java)
        routerBinder.addBinding().to(ReportsRouter::class.java)
        routerBinder.addBinding().to(TaskRouter::class.java)
        routerBinder.addBinding().to(UserRouter::class.java)
        routerBinder.addBinding().to(TaskFileRouter::class.java)
        routerBinder.addBinding().to(LoiRouter::class.java)
        routerBinder.addBinding().to(LoiRoundRouter::class.java)
        routerBinder.addBinding().to(LoiFileRouter::class.java)
        routerBinder.addBinding().to(ProspectRouter::class.java)
        routerBinder.addBinding().to(DocumentsRouter::class.java)
        routerBinder.addBinding().to(CalendarRouter::class.java)
        routerBinder.addBinding().to(DashboardRouter::class.java)
        routerBinder.addBinding().to(PropertiesRouter::class.java)
        routerBinder.addBinding().to(TagRouter::class.java)
        routerBinder.addBinding().to(PipelineViewRouter::class.java)
        routerBinder.addBinding().to(VerticalRouter::class.java)
        routerBinder.addBinding().to(FileRouter::class.java)
        routerBinder.addBinding().to(KeyPilotRouter::class.java)
        routerBinder.addBinding().to(DealDataSyncRouter::class.java)
        routerBinder.addBinding().to(FindingsRouter::class.java)

        val unirestInstance = UnirestInstance(
            Config()
                .interceptor(HeadersInterceptor(config.serviceName, UUIDCorrelationIdProvider()))
                .interceptor(ResponseStatusInterceptor())
        )

        val restClientConfigs = config.restClients.associateBy { it.name }

        // Unirest
        bind(UnirestInstance::class.java)
            .toInstance(unirestInstance)

        bind(KeyGenRestClient::class.java).toInstance(KeyGenRestClient(unirestInstance = unirestInstance, restClientConfig = restClientConfigs["keygen"]!!))
        bind(KFileRestClient::class.java).toInstance(KFileRestClient(unirestInstance = unirestInstance, restClientConfig = restClientConfigs["k_file_service"]!!))
        bind(PropertySageRestClient::class.java).toInstance(PropertySageRestClient(unirestInstance = unirestInstance, restClientConfig = restClientConfigs["property_sage"]!!))
        bind(ChatGptRestClient::class.java).toInstance(ChatGptRestClient(unirestInstance = unirestInstance, restClientConfig = restClientConfigs["chat_gpt"]!!))
        bind(PropertyAssetsRestClient::class.java).toInstance(PropertyAssetsRestClient(unirestInstance = unirestInstance, restClientConfig = restClientConfigs["property_assets"]!!))
        bind(OrganizationsRestClient::class.java).toInstance(OrganizationsRestClient(unirestInstance = unirestInstance, restClientConfig = restClientConfigs["organizations"]!!))

        // Request error handler
        bind(ErrorHandler::class.java).asEagerSingleton()

        // Database
        bind(TransactionalDataSource::class.java).toProvider(DataSourceProvider::class.java).asEagerSingleton()
        bind(SqlClient::class.java).toProvider(SqlClientProvider::class.java).asEagerSingleton()

        // DocuSign
        bind(SignGateway::class.java).to(DocuSignSignGateway::class.java).asEagerSingleton()

        // Property Information for template
        bind(PropertyDetailsGateway::class.java).to(PropertyGateway::class.java).asEagerSingleton()
        bind(PropertySageGateway::class.java).to(PropertySageApi::class.java)
        bind(PropertyAssetsGateway::class.java).to(PropertyAssetsApi::class.java)

        // File presigned url provider
        bind(FileGateway::class.java).to(KFileServiceFileGateway::class.java).asEagerSingleton()

        // Chat GPT
        bind(ChatGptGateway::class.java).to(ChatGptServiceGateway::class.java).asEagerSingleton()

        bind(TaskChangedListener::class.java).asEagerSingleton()
        bind(DocuSignListener::class.java).asEagerSingleton()

        // Kotlin coroutines
        bind(CoroutineScope::class.java).toProvider(CoroutineScopeProvider::class.java).asEagerSingleton()

        // Member Filter
        val memberFilterBinder = Multibinder.newSetBinder(binder(), FilterMember::class.java)
        memberFilterBinder.addBinding().to(FilterMemberByMemberType::class.java)
        memberFilterBinder.addBinding().to(FilterMemberByEnabled::class.java)

        // Sendgrid Email
        bind(SendGrid::class.java).toProvider(SendgridProvider::class.java)
        bind(SendgridMailSender::class.java).asEagerSingleton()
        bind(EmailGateway::class.java).to(SendgridEmailGateway::class.java).asEagerSingleton()

        // splitIo
        bind(SplitClient::class.java).toProvider(SplitIoClientProvider::class.java)
        bind(FeatureFlags::class.java).to(FeatureFlagsSplitIo::class.java).asEagerSingleton()

        val dashboardCalendarDatesValidatorBinder = Multibinder.newSetBinder(binder(), DealDateExpirationCalendarEventFinder::class.java)
        dashboardCalendarDatesValidatorBinder.addBinding().to(DiligenceExpirationCalendarEventFinder::class.java)
        dashboardCalendarDatesValidatorBinder.addBinding().to(ClosingDateExpirationCalendarEventFinder::class.java)

        // Auth0
        bind(JWTGateway::class.java)
            .annotatedWith(Names.named("emailJwt"))
            .toInstance(
                JWTAuthGateway(
                    clock = clock,
                    jwtGatewayConfig = config.documentEmailToken
                )
            )

        bind(JWTGateway::class.java)
            .annotatedWith(Names.named("activateAccountJwt"))
            .toInstance(
                JWTAuthGateway(
                    clock = clock,
                    jwtGatewayConfig = config.activateAccountToken
                )
            )

        bind(CalendarGateway::class.java).to(GoogleCalendarGateway::class.java)

        bind(UserSdk::class.java).toProvider(UserSdkProvider::class.java).asEagerSingleton()
        bind(UserImpersonationSdk::class.java).toProvider(UserImpersonationSdkProvider::class.java).asEagerSingleton()
        bind(TokenSdk::class.java).toProvider(TokenSdkProvider::class.java).asEagerSingleton()
        bind(AuthAPI::class.java).toProvider(AuthAPIProvider::class.java).asEagerSingleton()
        bind(AuthenticationSdk::class.java).toProvider(AuthenticationSdkProvider::class.java).asEagerSingleton()
        bind(UrlJwkProvider::class.java).toProvider(UrlJwkProviderProvider::class.java).asEagerSingleton()
        bind(UserService::class.java).to(UserAuth0Service::class.java)
        bind(AuthorizationService::class.java).to(AuthorizationAuth0Service::class.java)
        bind(Calendar::class.java).toProvider(GoogleCalendarProvider::class.java).asEagerSingleton()
    }
}
