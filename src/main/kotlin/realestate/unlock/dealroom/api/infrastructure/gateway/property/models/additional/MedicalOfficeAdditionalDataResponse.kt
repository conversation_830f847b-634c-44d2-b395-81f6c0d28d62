package realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional

import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.medical.MedicalOfficeRealEstateDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.medical.sales.MedicalOfficeSalesResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.medical.tenants.MedicalOfficeTenantsResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.shared.contacts.ContactsResponse

data class MedicalOfficeAdditionalDataResponse(
    val realEstateData: MedicalOfficeRealEstateDataResponse? = null,
    val tenants: MedicalOfficeTenantsResponse? = null,
    val contacts: ContactsResponse? = null,
    val sales: MedicalOfficeSalesResponse? = null
) : PropertyAdditionalDataResponse
