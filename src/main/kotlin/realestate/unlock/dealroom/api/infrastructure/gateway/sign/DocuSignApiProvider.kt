package realestate.unlock.dealroom.api.infrastructure.gateway.sign

import com.docusign.esign.api.EnvelopesApi
import com.docusign.esign.client.ApiClient
import org.apache.http.HttpHeaders
import realestate.unlock.dealroom.api.infrastructure.configuration.model.DocuSignConfig
import javax.inject.Inject

class DocuSignApiProvider @Inject constructor(
    private val docuSignConfig: DocuSignConfig,
    private val docuSignTokenProvider: DocuSignTokenProvider
) {
    fun createEnvelopesApi(): EnvelopesApi {
        return EnvelopesApi(create(docuSignConfig.userId))
    }

    private fun create(userId: String): ApiClient =
        ApiClient(docuSignConfig.baseUrl)
            .also { setToken(it, userId) }

    private fun setToken(apiClient: ApiClient, userId: String) {
        docuSignTokenProvider.getToken(apiClient, userId).also { accessToken ->
            apiClient.addDefaultHeader(HttpHeaders.AUTHORIZATION, "${accessToken.type} ${accessToken.token}")
        }
    }
}
