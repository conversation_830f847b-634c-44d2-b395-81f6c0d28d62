package realestate.unlock.dealroom.api.infrastructure.gateway.splitio

import io.split.client.SplitClient
import realestate.unlock.dealroom.api.core.gateway.featureflags.Feature
import realestate.unlock.dealroom.api.core.gateway.featureflags.FeatureFlags
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class FeatureFlagsSplitIo @Inject constructor(
    val client: SplitClient
) : FeatureFlags {

    companion object {
        private val logger by LoggerDelegate()
        private const val ON = "on"
    }

    override fun isOn(feature: Feature, user: String, context: Map<String, Any>): Boolean {
        val splitIoTreatment = client.getTreatment(user, feature.value, context)
        logger.debug("Feature $feature for user $user has treatment $splitIoTreatment")
        return splitIoTreatment == ON
    }
}
