package realestate.unlock.dealroom.api.infrastructure.client.rest.exception

import realestate.unlock.dealroom.api.core.entity.user.auth.Permission

open class ForbiddenByPermissionsException(
    endpointOrAction: String,
    permissionsNeeded: List<Permission>
) : ForbiddenException(
    message = "Not enough permissions: $endpointOrAction - Permissions needed: ${permissionsNeeded.flatMap { it.keys() }}"
)
