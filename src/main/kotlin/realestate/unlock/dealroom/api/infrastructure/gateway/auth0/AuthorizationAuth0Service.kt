package realestate.unlock.dealroom.api.infrastructure.gateway.auth0

import com.keyway.security.domain.token.TokenSdk
import realestate.unlock.dealroom.api.core.entity.user.auth.AuthorizationToken
import realestate.unlock.dealroom.api.core.entity.user.auth.Claims
import realestate.unlock.dealroom.api.core.gateway.auth.AuthorizationService
import javax.inject.Inject

class AuthorizationAuth0Service @Inject constructor(
    private val tokenSdk: TokenSdk
) : AuthorizationService {

    override fun validate(token: String): AuthorizationToken =
        tokenSdk.validate(token).let {
            AuthorizationToken(
                id = it.id,
                claims = Claims(
                    iss = it.claims.iss,
                    aud = it.claims.aud,
                    exp = it.claims.exp,
                    permissions = it.claims.permissions,
                    organizationId = it.claims.getOrganizationIdOrFail()
                )
            )
        }
}
