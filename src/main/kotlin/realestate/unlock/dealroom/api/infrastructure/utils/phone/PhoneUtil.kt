package realestate.unlock.dealroom.api.infrastructure.utils.phone

import com.google.i18n.phonenumbers.PhoneNumberUtil
import com.google.i18n.phonenumbers.Phonenumber.PhoneNumber.CountryCodeSource

object PhoneUtil {

    private val phoneNumberUtil = PhoneNumberUtil.getInstance()

    fun parse(phone: String): String =
        phoneNumberUtil.parse(phone, CountryCodeSource.UNSPECIFIED.name)
            .let { phoneNumber ->
                phoneNumberUtil.format(phoneNumber, PhoneNumberUtil.PhoneNumberFormat.E164)
            }
}
