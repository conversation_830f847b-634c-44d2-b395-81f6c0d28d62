package realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional

import com.keyway.adapters.dtos.response.property.additional.multifamily.mortgages.MultifamilyMortgagesResponse
import com.keyway.adapters.dtos.response.property.additional.multifamily.sales.MultifamilySalesResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.MultifamilyRealEstateDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.listings.MultifamilyListingsResponse

data class MultifamilyAdditionalDataResponse(
    val realEstateData: MultifamilyRealEstateDataResponse? = null,
    val listings: MultifamilyListingsResponse? = null,
    val sales: MultifamilySalesResponse? = null,
    val mortgages: MultifamilyMortgagesResponse? = null
) : PropertyAdditionalDataResponse
