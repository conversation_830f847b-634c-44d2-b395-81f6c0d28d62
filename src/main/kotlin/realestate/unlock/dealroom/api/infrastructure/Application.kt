package realestate.unlock.dealroom.api.infrastructure

import io.javalin.Javalin
import org.apache.commons.lang3.RandomStringUtils
import org.slf4j.MDC
import realestate.unlock.dealroom.api.entrypoint.rest.router.MainRouter
import realestate.unlock.dealroom.api.infrastructure.configuration.model.SystemConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate

class Application {

    private val logger by LoggerDelegate()

    companion object {
        const val TRACE_ID = "trace_id"
        private const val TRACE_SIZE = 16

        fun generateTraceId(): String = RandomStringUtils.randomAlphanumeric(TRACE_SIZE)

        @JvmStatic
        fun main(args: Array<String>) {
            Application().init()
        }
    }

    fun init() {
        MDC.put(TRACE_ID, generateTraceId())
        runCatching {
            logger.info("APP_INIT: Waiting for initialization...")
            Context.init().let { injector ->
                val config = injector.getInstance(SystemConfig::class.java)
                injector.getInstance(Javalin::class.java).let { app ->
                    injector.getInstance(MainRouter::class.java).setUpRoutes()
                    app.start(config.httpPort)
                }.also { app ->
                    logger.info("Application already initialized and listen at port: ${config.httpPort}")
                    Runtime.getRuntime().addShutdownHook(Thread { app.stop() })
                }
            }
        }.onFailure { e ->
            logger.error("BOOT_ERROR: " + e.message, e)
            throw RuntimeException(e)
        }.getOrDefault(Unit)
            .run { MDC.clear() }
    }
}
