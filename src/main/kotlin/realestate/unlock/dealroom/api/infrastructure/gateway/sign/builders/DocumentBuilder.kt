package realestate.unlock.dealroom.api.infrastructure.gateway.sign.builders

import com.docusign.esign.model.Document
import realestate.unlock.dealroom.api.core.gateway.sign.BeginSignFlowInput

class DocumentBuilder {
    lateinit var input: BeginSignFlowInput

    fun build() = Document()
        .documentId(input.docusignDocument.id.toString())
        .name(input.docusignDocument.name.value)
        .fileExtension(input.docusignDocument.extension)
        .remoteUrl(input.docusignDocument.sourceUrl.toString().replace("http://localstack:4567", "https://1449-186-19-206-20.ngrok.io"))
}
