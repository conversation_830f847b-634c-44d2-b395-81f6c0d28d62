package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.client

import com.google.inject.name.Named
import com.keyway.kommons.aws.config.AwsConfig
import com.keyway.kommons.sqs.configuration.DefaultSqsUrlBuilder
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.SendMessageRequest
import javax.inject.Inject

// once everything is done I will create an SQSClientBuilder to avoid the duplicated code
class SQSChatGptFileSentClient @Inject constructor(
    awsConfig: AwsConfig,
    @Named("sqsChatGptFileSent") private val sqsChatGptFileSentConfig: AwsSQSConfig
) {
    companion object {
        private val logger by LoggerDelegate()
    }

    private var sqsClient: SqsClient
    private var sqsUrl: String

    init {
        logger.trace("initializing sqs client with configuration $sqsChatGptFileSentConfig")

        val awsConfig = AwsConfig(
            accountId = sqsChatGptFileSentConfig.accountId,
            accessKey = awsConfig.accessKey,
            secretKey = awsConfig.secretKey,
            region = sqsChatGptFileSentConfig.region
        )

        val builder = SqsClient.builder()
            .region(Region.of(sqsChatGptFileSentConfig.region))

        builder.credentialsProvider(
            StaticCredentialsProvider.create(
                AwsBasicCredentials.create(awsConfig.accessKey, awsConfig.secretKey)
            )
        )
        sqsUrl = DefaultSqsUrlBuilder(awsConfig).buildUrl(sqsChatGptFileSentConfig.queueName)
        sqsClient = builder.build()
        logger.trace("sqs client initialized successfully")
    }

    fun send(message: String): Unit = kotlin.run {
        val request = SendMessageRequest.builder()
            .queueUrl(sqsUrl)
            .messageBody(message)
            .build()
        sqsClient.sendMessage(request)
    }
}
