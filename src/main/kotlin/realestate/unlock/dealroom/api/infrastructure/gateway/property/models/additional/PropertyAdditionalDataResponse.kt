package realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional

import com.fasterxml.jackson.annotation.JsonSubTypes
import io.swagger.v3.oas.annotations.media.Schema

@Schema(
    oneOf = [MultifamilyAdditionalDataResponse::class, MedicalOfficeAdditionalDataResponse::class]
)
@JsonSubTypes(
    JsonSubTypes.Type(value = MultifamilyAdditionalDataResponse::class, name = "MULTIFAMILY"),
    JsonSubTypes.Type(value = MedicalOfficeAdditionalDataResponse::class, name = "MEDICAL_OFFICE")
)
sealed interface PropertyAdditionalDataResponse
