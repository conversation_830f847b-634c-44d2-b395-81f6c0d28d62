package realestate.unlock.dealroom.api.infrastructure.client.aws.sns

import com.keyway.kommons.aws.config.AwsConfig
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sns.SnsAsyncClient
import java.net.URI
import javax.inject.Inject
import javax.inject.Provider

class AwsSNSClientProvider @Inject constructor(
    private val awsCredentials: AwsConfig
) : Provider<SnsAsyncClient> {

    override fun get(): SnsAsyncClient {
        val builder = SnsAsyncClient.builder()
            .region(Region.of(awsCredentials.region))

        if (awsCredentials.endpointOverride != null) {
            builder.endpointOverride(URI.create(awsCredentials.endpointOverride))
        }

        builder.credentialsProvider(
            StaticCredentialsProvider.create(
                AwsBasicCredentials.create(
                    awsCredentials.accessKey,
                    awsCredentials.secretKey
                )
            )
        )

        return builder.build()
    }
}
