package realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer

import com.google.inject.name.Named
import com.keyway.kommons.aws.config.AwsConfig
import realestate.unlock.dealroom.api.infrastructure.client.aws.sqs.consumer.listener.CronJobsListener
import realestate.unlock.dealroom.api.infrastructure.configuration.model.aws.sqs.AwsSQSConfig
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class SQSCronJobsConsumer @Inject constructor(
    awsConfig: AwsConfig,
    @Named("sqsCronJobs") sqsCronJobs: AwsSQSConfig,
    cronJobsListener: CronJobsListener
) {
    companion object {
        private val logger by LoggerDelegate()
    }

    init {
        logger.trace("initializing sqs consumer with configuration $sqsCronJobs")
        val consumer = SQSConsumerBuilder(
            awsConfig = awsConfig,
            awsSQSConfig = sqsCronJobs,
            messageHandler = cronJobsListener
        ).build()
        consumer.start()
        logger.trace("sqs consumer initialized successfully")
    }
}
