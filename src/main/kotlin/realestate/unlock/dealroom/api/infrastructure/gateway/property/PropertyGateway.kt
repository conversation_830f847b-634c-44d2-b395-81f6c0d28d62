package realestate.unlock.dealroom.api.infrastructure.gateway.property

import realestate.unlock.dealroom.api.core.gateway.property.PropertyDetails
import realestate.unlock.dealroom.api.core.gateway.property.PropertyDetailsGateway
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.NotFoundException
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyAssetsResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.PropertyType
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.MedicalOfficeAdditionalDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.MultifamilyAdditionalDataResponse
import realestate.unlock.dealroom.api.infrastructure.gateway.propertysage.PropertySageGateway
import java.io.File
import javax.inject.Inject

class PropertyGateway @Inject constructor(
    private val propertyAssetsApi: PropertyAssetsApi,
    private val propertySageGatewayApi: PropertySageGateway
) : PropertyDetailsGateway {

    override fun findByPropertyKeywayId(propertyKeywayId: String): PropertyDetails =
        runCatching {
            propertyAssetsApi.findById(propertyKeywayId)
        }
            .onFailure {
                throw NotFoundException(message = "Property with id [$propertyKeywayId] not found")
            }
            .getOrThrow()
            .toPropertyDetails()

    override fun getPropertyPhoto(propertyKeywayId: String): File? {
        val url = getOrNull { propertySageGatewayApi.getPropertyKeyscoreByKeywayId(propertyKeywayId) }
            ?.imageUrl
        if (url.isNullOrEmpty()) return null
        return propertySageGatewayApi.downloadPhoto(url)
    }

    private fun <T> getOrNull(block: () -> T): T? =
        this.runCatching {
            block()
        }.getOrNull()
}

private fun PropertyAssetsResponse.toPropertyDetails(): PropertyDetails {
    val property = PropertyDetails(
        keywayId = this.id,
        squareFootage = this.squareFootage,
        constructionYear = this.constructionYear,
        name = null,
        address = this.address,
        city = this.city,
        state = this.state,
        zipCode = this.zipCode.toString(),
        coordinates = this.location.toCoordinates(),
        tenant = null,
        vertical = null,
        units = null,
        askingPrice = null
    )

    return when (this.propertyType) {
        PropertyType.MULTIFAMILY -> property.addMultifamilyAdditionalData(this.additionalData as MultifamilyAdditionalDataResponse)
        PropertyType.MEDICAL_OFFICE -> property.addMedicalAdditionalData(this.additionalData as MedicalOfficeAdditionalDataResponse)
    }
}

private fun PropertyDetails.addMultifamilyAdditionalData(additionalData: MultifamilyAdditionalDataResponse): PropertyDetails {
    return this.copy(
        name = additionalData.realEstateData?.name,
        units = additionalData.realEstateData?.units?.quantity,
        askingPrice = additionalData.realEstateData?.askingPrice
    )
}

private fun PropertyDetails.addMedicalAdditionalData(additionalData: MedicalOfficeAdditionalDataResponse): PropertyDetails {
    return this.copy(
        askingPrice = additionalData.realEstateData?.askingPrice
    )
}
