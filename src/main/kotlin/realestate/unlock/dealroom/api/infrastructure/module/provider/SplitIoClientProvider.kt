package realestate.unlock.dealroom.api.infrastructure.module.provider

import com.google.inject.Provider
import io.split.client.SplitClient
import io.split.client.SplitClientConfig
import io.split.client.SplitFactoryBuilder
import realestate.unlock.dealroom.api.infrastructure.configuration.model.SplitIoConfig
import javax.inject.Inject

class SplitIoClientProvider @Inject constructor(private val splitIoConfig: SplitIoConfig) : Provider<SplitClient> {
    override fun get(): SplitClient {
        val splitClientConfig = SplitClientConfig.builder()
            .setBlockUntilReadyTimeout(splitIoConfig.timeout ?: 5000)
            .build()
        return SplitFactoryBuilder.build(splitIoConfig.token, splitClientConfig).client().also { it.blockUntilReady() }
    }
}
