package realestate.unlock.dealroom.api.infrastructure.client.aws.sns

import realestate.unlock.dealroom.api.infrastructure.client.aws.sns.exception.PublishSNSError
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import software.amazon.awssdk.services.sns.SnsAsyncClient
import software.amazon.awssdk.services.sns.model.PublishRequest
import javax.inject.Inject

class AwsSNSClient @Inject constructor(
    private val sns: SnsAsyncClient
) {

    companion object {
        private val logger by LoggerDelegate()
    }

    fun publish(message: String, topicArn: String) {
        logger.trace("Publishing to sns $topicArn message: $message")
        try {
            PublishRequest.builder().message(message).topicArn(topicArn).build().let { sns.publish(it) }
            logger.trace("Message published to sns successful")
        } catch (e: Throwable) {
            logger.error("Error on publish sns message to topic $topicArn", e)
            throw PublishSNSError("Error publishing to $topicArn", e)
        }
    }
}
