package realestate.unlock.dealroom.api.infrastructure.client.rest.exception

import org.eclipse.jetty.http.HttpStatus
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.base.ErrorRequestException

open class UnauthorizedException(
    message: String = HttpStatus.Code.UNAUTHORIZED.message,
    errorCode: String = "UNAUTHORIZED",
    cause: Throwable? = null
) : ErrorRequestException(
    message = message,
    errorCode = errorCode,
    statusCode = HttpStatus.Code.UNAUTHORIZED.code,
    cause = cause
)
