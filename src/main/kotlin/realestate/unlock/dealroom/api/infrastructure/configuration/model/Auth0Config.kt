package realestate.unlock.dealroom.api.infrastructure.configuration.model

data class Auth0TokenVerificationConfig(
    val issuer: String,
    val domain: String,
    // TODO: deprecated after configure by rest client
    val audience: String,
    val audiences: List<String>
)

data class Auth0ManagementClientConfig(
    val domain: String,
    val clientId: String,
    val clientSecret: String,
    val audience: String,
    val usersDatabaseName: String
)

data class Auth0AppClientIdsConfig(
    val dealRoomClientId: String,
    val dealRoomAdminClientId: String
)
