package realestate.unlock.dealroom.api.infrastructure.gateway.property.models.additional.multifamily.mortgages

import java.math.BigDecimal
import java.time.LocalDate

data class MultifamilyMortgagesRecordResponse(
    val mortgageId: String? = null,
    val amount: BigDecimal? = null,
    val ownerName: String? = null,
    val lenderName: String? = null,
    val dateOfRecord: LocalDate? = null,
    val dueDate: LocalDate? = null,
    val transactionType: String? = null,
    val type: String? = null
)
