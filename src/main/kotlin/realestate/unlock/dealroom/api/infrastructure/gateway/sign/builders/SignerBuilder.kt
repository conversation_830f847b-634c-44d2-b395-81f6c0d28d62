package realestate.unlock.dealroom.api.infrastructure.gateway.sign.builders

import com.docusign.esign.model.*
import realestate.unlock.dealroom.api.core.gateway.sign.BeginSignFlowInput
import realestate.unlock.dealroom.api.core.gateway.sign.Signer
import realestate.unlock.dealroom.api.infrastructure.gateway.sign.DocuSignSignGateway
import com.docusign.esign.model.Signer as DocuSignSigner

class SignerBuilder {
    lateinit var input: BeginSignFlowInput
    lateinit var signer: Signer

    fun build(): DocuSignSigner {
        return DocuSignSigner()
            .clientUserId(signer.id)
            .embeddedRecipientStartURL(DocuSignSignGateway.EmbeddedRecipientStartURL)
            .name(signer.name)
            .email(signer.email)
            .recipientId(signer.id)
            .routingOrder(DocuSignSignGateway.ROUTING_ORDER_FIRST)
            .emailNotification(
                RecipientEmailNotification()
                    .emailSubject(signer.emailParams.subject)
                    .emailBody(signer.emailParams.body)
            )
            .tabs(
                Tabs()
                    .signHereTabs(
                        input.tabs.signHere
                            .filter { it.signerId == signer.id }
                            .map { tab ->
                                SignHere()
                                    .documentId(input.docusignDocument.id.toString())
                                    .name(tab.name)
                                    .anchorString(tab.anchorString)
                                    .anchorUnits(DocuSignSignGateway.TAB_ANCHOR_UNITS)
                                    .anchorYOffset(tab.offsetY.toString())
                                    .anchorXOffset(tab.offsetX.toString())
                                    .recipientId(tab.signerId)
                                    .width(tab.width?.toString())
                                    .height(tab.height?.toString())
                            }
                    )
                    .dateSignedTabs(
                        input.tabs.dateSigned
                            .filter { it.signerId == signer.id }
                            .map { tab ->
                                DateSigned()
                                    .documentId(input.docusignDocument.id.toString())
                                    .name(tab.name)
                                    .anchorString(tab.anchorString)
                                    .anchorUnits(DocuSignSignGateway.TAB_ANCHOR_UNITS)
                                    .anchorYOffset(tab.offsetY.toString())
                                    .anchorXOffset(tab.offsetX.toString())
                                    .recipientId(tab.signerId)
                                    .width(tab.width?.toString())
                                    .height(tab.height?.toString())
                            }
                    )
                    .companyTabs(
                        input.tabs.company
                            .filter { it.signerId == signer.id }
                            .map { tab ->
                                Company()
                                    .documentId(input.docusignDocument.id.toString())
                                    .name(tab.name)
                                    .anchorString(tab.anchorString)
                                    .anchorUnits(DocuSignSignGateway.TAB_ANCHOR_UNITS)
                                    .anchorYOffset(tab.offsetY.toString())
                                    .anchorXOffset(tab.offsetX.toString())
                                    .recipientId(tab.signerId)
                                    .width(tab.width?.toString())
                                    .height(tab.height?.toString())
                            }
                    )
                    .fullNameTabs(
                        input.tabs.fullName
                            .filter { it.signerId == signer.id }
                            .map { tab ->
                                FullName()
                                    .documentId(input.docusignDocument.id.toString())
                                    .name(tab.name)
                                    .anchorString(tab.anchorString)
                                    .anchorUnits(DocuSignSignGateway.TAB_ANCHOR_UNITS)
                                    .anchorYOffset(tab.offsetY.toString())
                                    .anchorXOffset(tab.offsetX.toString())
                                    .recipientId(tab.signerId)
                                    .width(tab.width?.toString())
                                    .height(tab.height?.toString())
                            }
                    )
                    .titleTabs(
                        input.tabs.title
                            .filter { it.signerId == signer.id }
                            .map { tab ->
                                Title()
                                    .documentId(input.docusignDocument.id.toString())
                                    .name(tab.name)
                                    .anchorString(tab.anchorString)
                                    .anchorUnits(DocuSignSignGateway.TAB_ANCHOR_UNITS)
                                    .anchorYOffset(tab.offsetY.toString())
                                    .anchorXOffset(tab.offsetX.toString())
                                    .recipientId(tab.signerId)
                                    .width(tab.width?.toString())
                                    .height(tab.height?.toString())
                            }
                    )
            )
    }
}
