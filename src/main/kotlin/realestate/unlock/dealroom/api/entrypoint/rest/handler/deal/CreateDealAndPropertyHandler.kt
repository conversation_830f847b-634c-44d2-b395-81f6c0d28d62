package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal

import com.keyway.kommons.http.exception.BadRequestException
import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.deal.CompleteDeal
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.create.CreateAcquisitionDeal
import realestate.unlock.dealroom.api.core.usecase.deal.create.CreateLeasebackDeal
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.create.CreateAcquisitionDealInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.create.CreateDealInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.create.CreateSaleLeasebackDealInput
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.create.DealCreationType
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import javax.inject.Inject

class CreateDealAndPropertyHandler @Inject constructor(
    private val createLeasebackDeal: CreateLeasebackDeal,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val createAcquisitionDeal: CreateAcquisitionDeal
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
        private const val RESPONSE_STATUS = HttpStatus.CREATED
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .body(CreateDealInput::class.java)
            .json(status = RESPONSE_STATUS.toString(), returnType = CompleteDeal::class.java)
            .operation {
                it.description("Creates Deal and Property")
                it.operationId("postDeal")
                it.addTagsItem("deals")
                it.summary(
                    """
                    Creates a new Deal and its Property
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.CREATE_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(onlyBuyerSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()} - Body: ${ctx.body()}")
        createDeal(ctx.body(), user.member.organizationId)
            .let(ctx::json)
            .status(RESPONSE_STATUS)
    }

    private fun createDeal(body: String, organizationId: String): CompleteDeal =
        when (val input = body.decode()) {
            is CreateSaleLeasebackDealInput -> createLeasebackDeal.create(
                CreateLeasebackDeal.Input(
                    property = input.property,
                    dealData = input.dealData,
                    leaseData = input.leaseData,
                    contractExecution = input.contractExecution,
                    members = input.members,
                    organizationId = organizationId
                )
            )

            is CreateAcquisitionDealInput -> createAcquisitionDeal.create(
                CreateAcquisitionDeal.Input(
                    property = input.property,
                    dealData = input.dealData,
                    contractExecution = input.contractExecution,
                    members = input.members,
                    organizationId = organizationId
                )
            )

            else -> throw BadRequestException()
        }

    data class CreateDealType(override val type: String) : CreateDealInput

    private fun interactionType(body: String) =
        when (JsonMapper.decode(body, CreateDealType::class.java).type) {
            DealCreationType.saleLeaseback -> CreateSaleLeasebackDealInput::class.java
            DealCreationType.acquisition -> CreateAcquisitionDealInput::class.java
            else -> logger.error("POST DEAL CREATION: $body").let { throw BadRequestException() }
        }

    private fun String.decode() = JsonMapper.decode(this, interactionType(this))
}
