package realestate.unlock.dealroom.api.entrypoint.rest.handler.member

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.member.MemberCreationInput
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.member.CreateMember
import realestate.unlock.dealroom.api.entrypoint.rest.contract.mappers.Mapper
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.post.PostMemberRequest
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.bodyTo
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ForbiddenByPermissionsException
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class CreateMemberHandler @Inject constructor(
    private val createMember: CreateMember,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
        private val permissionToCreateUser = Permission.CREATE_USERS
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .operation {
                it.description("Create Member")
                it.operationId("postMember")
                it.addTagsItem("members")
                it.summary(
                    """
                    Creates a new Member and creates a user if needed
                    """.trimIndent()
                )
            }
            .body<PostMemberRequest>()
            .json<MemberDto>(status = HttpStatus.CREATED.toString())

    override val permissionsNeeded = listOf(Permission.CREATE_MEMBERS)

    override val securityFilters = listOf(FilterExecutor(onlyBuyerSecurityFilter))

    private fun hasPermissionToCreateUser(user: AuthenticatedUser) = user.hasAnyPermissionOf(listOf(permissionToCreateUser))
    private fun hasPermissionToCreateExternalOrganizationMember(user: AuthenticatedUser) = user.hasAnyPermissionOf(listOf(Permission.CREATE_EXTERNAL_ORG_MEMBER))

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()} - Body: ${ctx.body()}")
        ctx.bodyTo(PostMemberRequest::class.java)
            .let { it.toMemberCreationInput() }
            .also { runValidations(it, user) }
            .let(createMember::create)
            .let(Mapper.toMemberDto)
            .let(ctx::json)
            .status(HttpStatus.CREATED)
    }

    private fun runValidations(input: MemberCreationInput, user: AuthenticatedUser) {
        validateUserCreation(input, user)
        validateExternalOrganizationMemberCreation(input, user)
    }

    private fun validateUserCreation(input: MemberCreationInput, user: AuthenticatedUser) {
        if (input.needUser && !hasPermissionToCreateUser(user)) {
            throw ForbiddenByPermissionsException("POST /member", listOf(permissionToCreateUser))
        }
    }

    private fun validateExternalOrganizationMemberCreation(input: MemberCreationInput, user: AuthenticatedUser) {
        if (input.organizationId != user.member.organizationId && !hasPermissionToCreateExternalOrganizationMember(user)) {
            throw ForbiddenByPermissionsException("POST /member", listOf(Permission.CREATE_EXTERNAL_ORG_MEMBER))
        }
    }
}
