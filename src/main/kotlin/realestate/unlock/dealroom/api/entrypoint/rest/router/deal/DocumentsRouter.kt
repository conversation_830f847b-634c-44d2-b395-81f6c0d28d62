package realestate.unlock.dealroom.api.entrypoint.rest.router.deal

import io.javalin.Javalin
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.documents.*
import realestate.unlock.dealroom.api.infrastructure.javalin.Router
import javax.inject.Inject

class DocumentsRouter @Inject constructor(
    private val app: <PERSON>lin,
    private val getDealMainDocumentsHandler: GetMainDocumentsHandler,
    private val postDocumentRoundInteractionHandler: PostDocumentRoundInteractionHandler,
    private val getDocumentRoundInteractionsHandler: GetDocumentRoundInteractionsHandler,
    private val getDocumentRoundsHandler: GetDocumentRoundsHandler,
    private val postDocumentFileHandler: PostDocumentFileHandler,
    private val getDocumentFileHandler: GetDocumentFileHandler,
    private val getDocumentEditSignViewHandler: GetDocumentEditSignViewHandler,
    private val getDocumentRoundInteractionsHistoryHandler: GetDocumentRoundInteractionsHistoryHandler,
    private val getDocumentSignViewHandler: GetDocumentSignViewHandler,
    private val getSignDocumentUrlFromToken: GetSignDocumentUrlFromToken
) : Router {
    override fun setUpRoutes() {
        "/deal/{deal-id}/documents".also { path ->
            app.get(path, getDealMainDocumentsHandler())
        }

        "/deal/{deal-id}/documents/{document-type}".also { path ->
            app.post(path, postDocumentRoundInteractionHandler())
        }

        "/deal/{deal-id}/documents/{document-type}/edit-sign-view".also { path ->
            app.get(path, getDocumentEditSignViewHandler())
        }

        "/deal/{deal-id}/documents/{document-type}/sign-view".also { path ->
            app.get(path, getDocumentSignViewHandler())
        }

        "/deal/{deal-id}/documents/{document-type}/rounds/{round-id}/interactions".also { path ->
            app.get(path, getDocumentRoundInteractionsHandler())
        }

        "/deal/{deal-id}/documents/{document-type}/task/history".also { path ->
            app.get(path, getDocumentRoundInteractionsHistoryHandler())
        }

        "/deal/{deal-id}/documents/{document-type}/rounds".also { path ->
            app.get(path, getDocumentRoundsHandler())
        }

        "/deal/{deal-id}/documents/{document-type}/files".also { path ->
            app.post(path, postDocumentFileHandler())
        }

        "/deal/{deal-id}/documents/{document-type}/files/{file-id}".also { path ->
            app.get(path, getDocumentFileHandler())
        }

        "/sign-document-link".also { path ->
            app.get(path, getSignDocumentUrlFromToken())
        }
    }
}
