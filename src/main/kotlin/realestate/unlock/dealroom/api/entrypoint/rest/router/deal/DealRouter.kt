package realestate.unlock.dealroom.api.entrypoint.rest.router.deal

import io.javalin.Javalin
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.*
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.lease.GetDealLeaseByDealIdHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.lease.PatchDealLeaseByDealIdHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.tasks.GetDealPendingTasksHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.tasks.GetDealTasksHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.update.*
import realestate.unlock.dealroom.api.infrastructure.javalin.Router
import javax.inject.Inject

class DealRouter @Inject constructor(
    private val app: Javalin,
    private val getDealsHandler: GetDealsHandler,
    private val createDealAndPropertyHandler: CreateDealAndPropertyHandler,
    private val updateDealDatesHandler: UpdateDealDatesHandler,
    private val updateDealStageHandler: UpdateDealStageHandler,
    private val addDealTeamMemberHandler: AddDealTeamMemberHandler,
    private val removeDealTeamMemberHandler: RemoveDealTeamMemberHandler,
    private val getDealStagesHandler: GetDealStagesHandler,
    private val createPointOfContactHandler: CreatePointOfContactHandler,
    private val getDealPendingTasksHandler: GetDealPendingTasksHandler,
    private val getDealByIdHandler: GetDealByIdHandler,
    private val updateDealHandler: UpdateDealHandler,
    private val getDealLeaseByDealIdHandler: GetDealLeaseByDealIdHandler,
    private val patchDealLeaseByDealIdHandler: PatchDealLeaseByDealIdHandler,
    private val getDealMembersHandler: GetDealMembersHandler,
    private val updateDealStatusHandler: UpdateDealStatusHandler,
    private val getDealTasksHandler: GetDealTasksHandler,
    private val updateDealPropertyHandler: UpdateDealPropertyHandler,
    private val createDealExternalCalendar: CreateDealExternalCalendarHandler
) : Router {

    override fun setUpRoutes() {
        "/deal".also { path ->
            app.post(path, createDealAndPropertyHandler())
            app.get(path, getDealsHandler())
        }

        "/deal/{deal-id}".also { path ->
            app.get(path, getDealByIdHandler())
            app.patch(path, updateDealHandler())
        }

        "/deal/{deal-id}/dates".also { path ->
            app.put(path, updateDealDatesHandler())
        }
        "/deal/{deal-id}/stage".also { path ->
            app.put(path, updateDealStageHandler())
        }

        "/deal/{deal-id}/status".also { path ->
            app.post(path, updateDealStatusHandler())
        }

        "/deal/{deal-id}/lease".also { path ->
            app.get(path, getDealLeaseByDealIdHandler())
            app.patch(path, patchDealLeaseByDealIdHandler())
        }

        "/deal/{deal-id}/members".also { path ->
            app.get(path, getDealMembersHandler())
            app.post(path, addDealTeamMemberHandler())
        }

        "/deal/{deal-id}/members/{member-id}".also { path ->
            app.delete(path, removeDealTeamMemberHandler())
        }

        "/deal/{deal-id}/stages".also { path ->
            app.get(path, getDealStagesHandler())
        }

        "/deal/{deal-id}/point-of-contact".also { path ->
            app.post(path, createPointOfContactHandler())
        }

        "/deal/{deal-id}/pending-tasks".also { path ->
            app.get(path, getDealPendingTasksHandler())
        }

        "/deal/{deal-id}/tasks".also { path ->
            app.get(path, getDealTasksHandler())
        }

        "/deal/{deal-id}/property".also { path ->
            app.put(path, updateDealPropertyHandler())
        }

        "/deal-calendar".also { path ->
            app.post(path, createDealExternalCalendar())
        }
    }
}
