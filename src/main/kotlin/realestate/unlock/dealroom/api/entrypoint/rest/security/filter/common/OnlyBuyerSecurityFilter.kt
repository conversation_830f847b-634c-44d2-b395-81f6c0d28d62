package realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common

import io.javalin.http.Context
import realestate.unlock.dealroom.api.core.entity.member.type.MemberDealTeam
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.AuthSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ForbiddenException

class OnlyBuyerSecurityFilter : AuthSecurityFilter {
    override fun apply(ctx: Context, user: AuthenticatedUser) {
        if (!user.member.isTeam(MemberDealTeam.BUYER)) {
            throw ForbiddenException("Forbidden path: ${ctx.path()} - It's only available for buyer users")
        }
    }
}
