package realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal

import io.javalin.http.Context
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.repository.deal.DealRepository
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.ContextUtils
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.AuthSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ForbiddenException
import javax.inject.Inject

class VerifyPropertyBelongsToOrganizationSecurityFilter @Inject constructor(
    private val dealRepository: DealRepository
) : AuthSecurityFilter {

    override fun apply(ctx: Context, user: AuthenticatedUser) {
        val deal = dealRepository.findByPropertyId(ContextUtils.getPathParam(ctx, "property-id"))
            ?: throw BadRequestException("Property is not part of a deal")

        if (deal.organizationId != user.member.organizationId) {
            throw ForbiddenException("You must belong to the organization owner of the property")
        }
    }
}
