package realestate.unlock.dealroom.api.entrypoint.rest.security.filter.task

import io.javalin.http.Context
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.usecase.member.GetDealMembersByTaskId
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.AuthSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ForbiddenException
import javax.inject.Inject

class VerifyDealMembershipFromTaskIdPathParamSecurityFilter @Inject constructor(
    private val getDealMembersByTaskId: GetDealMembersByTaskId
) : AuthSecurityFilter {

    override fun apply(ctx: Context, user: AuthenticatedUser) {
        this.runCatching {
            ctx.pathParamAsClass<Long>("task-id").get()
        }
            .onFailure { error ->
                throw BadRequestException(
                    message = error.localizedMessage,
                    cause = error
                )
            }
            .getOrThrow()
            .let(getDealMembersByTaskId::get)
            .find { member ->
                member.id == user.member.id
            }
            ?: throw ForbiddenException("In order to access to the task you need to be member of the related deal")
    }
}
