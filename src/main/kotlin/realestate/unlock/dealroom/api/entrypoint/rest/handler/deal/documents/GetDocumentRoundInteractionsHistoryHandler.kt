package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.documents

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.documents.FindDocumentRoundInteractionsHistory
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.InteractionHistoryResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.documents.toHistoryResponse
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.getDocumentType
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import javax.inject.Inject

class GetDocumentRoundInteractionsHistoryHandler @Inject constructor(
    private val findDocumentRoundInteractionsHistory: FindDocumentRoundInteractionsHistory,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : DealDocumentHandler() {

    override fun getDocumentation(documentation: OpenApiDocumentation) =
        documentation
            .jsonArray(status = HttpStatus.OK.toString(), InteractionHistoryResponse::class.java)
            .operation {
                it.description("Get seller-related Document round interactions")
                it.operationId("getDocumentRoundInteractionsHistory")
                it.summary(
                    """
                    Get Document round interactions where the seller is involved
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.READ_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser, dealId: Long, documentType: String) {
        findDocumentRoundInteractionsHistory(
            dealId = dealId,
            documentType = ctx.getDocumentType()
        )
            .map { it.toHistoryResponse() }
            .let(ctx::json)
    }
}
