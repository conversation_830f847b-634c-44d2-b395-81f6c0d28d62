package realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal

import io.javalin.http.Context
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.usecase.member.GetMembersByDealId
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.AuthSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ForbiddenException
import javax.inject.Inject

class VerifyDealMembershipByDealIdPathParamSecurityFilter @Inject constructor(
    private val getMembersByDealId: GetMembersByDealId
) : AuthSecurityFilter {

    override fun apply(ctx: Context, user: AuthenticatedUser) {
        val dealId = runCatching {
            ctx.pathParamAsClass<Long>("deal-id").get()
        }
            .onFailure { error ->
                throw BadRequestException(
                    message = error.localizedMessage,
                    cause = error
                )
            }
            .getOrThrow()

        getMembersByDealId.get(dealId)
            .let { dealMembers -> dealMembers.find { it.id == user.member.id } }
            .also { member ->
                if (member == null) {
                    throw ForbiddenException("In order to access to the deal you must be a member")
                }
            }
    }
}
