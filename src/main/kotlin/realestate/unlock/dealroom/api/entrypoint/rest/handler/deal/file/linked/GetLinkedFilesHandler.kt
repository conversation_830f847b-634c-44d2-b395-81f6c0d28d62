package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.file.linked

import com.fasterxml.jackson.annotation.JsonIgnore
import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileData
import realestate.unlock.dealroom.api.core.entity.deal.file.linked.LinkedFileEntityType
import realestate.unlock.dealroom.api.core.entity.paginated.PaginatedOutput
import realestate.unlock.dealroom.api.core.entity.search.SearchFieldParam
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.extension.filterNotNullValues
import realestate.unlock.dealroom.api.core.usecase.deal.files.linked.GetLinkedFiles
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.PaginatedResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.PaginatedSearch
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.SearchFilters
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableEnum
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.filters.SearchableString
import realestate.unlock.dealroom.api.entrypoint.rest.contract.paginated.paginatedAndFilteredSearchDocumentation
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import javax.inject.Inject

class GetLinkedFilesHandler @Inject constructor(
    private val getLinkedFiles: GetLinkedFiles,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .paginatedAndFilteredSearchDocumentation<GetLinkedFilesFilters, GetLinkedFilesPaginated, GetLinkedFilesOrderBy>()
            .operation {
                it.description("Get Linked Files")
                it.operationId("getLinkedFiles")
                it.addTagsItem("deal-files")
                it.summary(
                    """
                    Returns a list with linked files for a deal.
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.READ_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()} - Params: ${ctx.queryParamMap()}")
        val dealId = ctx.pathParamAsClass<Long>("deal-id").get()
        GetLinkedFilesSearch(ctx).toPaginatedInput()
            .let { getLinkedFiles.execute(dealId, it) }
            .let(GetLinkedFilesHandler::GetLinkedFilesPaginated)
            .let(ctx::json)
    }

    enum class GetLinkedFilesOrderBy(val field: String) {
        CREATED_AT("created_at")
    }

    data class GetLinkedFilesFilters(
        val entityType: SearchableEnum<LinkedFileEntityType>?,
        val entityId: SearchableString?
    ) : SearchFilters {
        override fun getFiltersMap(): Map<String, SearchFieldParam> = mapOf(
            "entity_type" to entityType?.toSearchField(),
            "entity_id" to entityId?.toSearchField()
        ).filterNotNullValues()
    }

    class GetLinkedFilesSearch constructor(ctx: Context) : PaginatedSearch<GetLinkedFilesFilters>(ctx) {
        override fun castFilterQueryParam(queryParam: String) = JsonMapper.decode(queryParam, GetLinkedFilesFilters::class.java)

        override fun castOrderByQueryParam(queryParam: String?) =
            (queryParam.takeIf { !it.isNullOrEmpty() }?.let { GetLinkedFilesOrderBy.valueOf(it) } ?: GetLinkedFilesOrderBy.CREATED_AT).field
    }

    data class GetLinkedFilesPaginated(
        @JsonIgnore
        private val output: PaginatedOutput<LinkedFileData>
    ) : PaginatedResponse<LinkedFileData>(
        size = output.size,
        offset = output.offset,
        data = output.data,
        total = output.total
    )
}
