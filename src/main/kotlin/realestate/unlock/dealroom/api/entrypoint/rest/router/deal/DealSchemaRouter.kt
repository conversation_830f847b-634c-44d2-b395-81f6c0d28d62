package realestate.unlock.dealroom.api.entrypoint.rest.router.deal

import io.javalin.Javalin
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.schema.*
import realestate.unlock.dealroom.api.infrastructure.javalin.Router
import javax.inject.Inject

class DealSchemaRouter @Inject constructor(
    private val app: <PERSON>lin,
    private val getDealSchemasHandler: GetDealSchemasHandler,
    private val getDealSchemaByIdHandler: GetDealSchemaByIdHandler,
    private val getDealSchemaDataByIdHandler: GetDealSchemaDataByIdHandler,
    private val putDealSchemaDataByIdHandler: PutDealSchemaDataByIdHandler,
    private val patchDealSchemaDataByIdHandler: PatchDealSchemaDataByIdHandler
) : Router {

    override fun setUpRoutes() {
        "/deal-schemas".also { path ->
            app.get(path, getDealSchemasHandler())
        }

        "/deal-schemas/{schema-id}".also { path ->
            app.get(path, getDealSchemaByIdHandler())
        }

        "/deal/{deal-id}/schema-data".also { path ->
            app.get(path, getDealSchemaDataByIdHandler())
            app.put(path, putDealSchemaDataByIdHandler())
            app.patch(path, patchDealSchemaDataByIdHandler())
        }
    }
}
