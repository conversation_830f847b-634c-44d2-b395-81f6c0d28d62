package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.file

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.task.file.GetFileByKFileId
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.GetFileResponse
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.ContextUtils.getOptionalQueryParam
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.ContextUtils.getPathParam
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class GetDealFileByKFileIdHandler @Inject constructor(
    private val getFileByKFileId: GetFileByKFileId,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .queryParam<String>("fileName")
            .json(status = HttpStatus.OK.toString(), returnType = GetFileResponse::class.java)
            .operation {
                it.description("Get Files in Deal")
                it.operationId("getFilesInDeal")
                it.addTagsItem("deal-files")
                it.summary(
                    """
                    Retrieves all deal files
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.READ_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()}")
        getFileByKFileId.get(
            kFileId = getPathParam(ctx, "file-id"),
            fileName = getOptionalQueryParam(ctx, "fileName")
        )
            .let(::GetFileResponse)
            .let(ctx::json)
    }
}
