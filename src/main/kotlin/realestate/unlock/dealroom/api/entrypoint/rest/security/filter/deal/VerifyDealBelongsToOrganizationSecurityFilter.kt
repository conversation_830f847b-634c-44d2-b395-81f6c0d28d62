package realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal

import io.javalin.http.Context
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealById
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.AuthSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.ForbiddenException
import javax.inject.Inject

class VerifyDealBelongsToOrganizationSecurityFilter @Inject constructor(
    private val getDealById: GetDealById
) : AuthSecurityFilter {

    override fun apply(ctx: Context, user: AuthenticatedUser) {
        val dealId = runCatching {
            ctx.pathParamAsClass<Long>("deal-id").get()
        }
            .onFailure { error ->
                throw BadRequestException(
                    message = error.localizedMessage,
                    cause = error
                )
            }
            .getOrThrow()

        val deal = getDealById.get(dealId)
        if (deal.organizationId != user.member.organizationId) {
            throw ForbiddenException("In order to access to the deal you must belong to the organization")
        }
    }
}
