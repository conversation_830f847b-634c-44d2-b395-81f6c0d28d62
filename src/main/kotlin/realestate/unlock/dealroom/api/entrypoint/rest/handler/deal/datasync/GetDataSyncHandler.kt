package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.datasync

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.deal.datasync.DataSyncResponse
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.GetDataSync
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class GetDataSyncHandler @Inject constructor(
    private val getDataSync: GetDataSync,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .pathParam<Long>("data-sync-id")
            .result(status = HttpStatus.OK.toString(), returnType = DataSyncResponse::class.java)
            .operation {
                it.description("Get a DataSync")
                it.operationId("getDataSync")
                it.addTagsItem("data-sync")
                it.summary(
                    """
                    Get a DataSync
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.READ_ALL_DEALS),
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()}")
        Pair(
            ctx.pathParamAsClass<Long>("deal-id").get(),
            ctx.pathParamAsClass<Long>("data-sync-id").get()
        ).let { (dealId, dataSyncId) ->
            getDataSync.get(dealId, dataSyncId)
        }.let { ctx.status(HttpStatus.OK).json(it) }
    }
}
