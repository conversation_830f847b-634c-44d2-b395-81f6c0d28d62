package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.findings

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.deal.findings.Finding
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.findings.UpdateDealFinding
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.finding.UpdateDealFindingRequest
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.ContextUtils.getPathParam
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.bodyTo
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import javax.inject.Inject

class UpdateDealFindingHandler @Inject constructor(
    private val updateDealFinding: UpdateDealFinding,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .pathParam<Long>("finding-id")
            .body<UpdateDealFindingRequest>()
            .json(status = HttpStatus.OK.toString(), returnType = Finding::class.java)
            .operation {
                it.description("Updates deal finding")
                it.operationId("updateFinding")
                it.addTagsItem("findings")
                it.summary(
                    """
                    Updates the status of a given deal finding
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.UPDATE_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.UPDATE_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        getPathParam<Long>(ctx, "finding-id")
            .let { findingId ->
                ctx.bodyTo(UpdateDealFindingRequest::class.java)
                    .toUpdateFindingInput(findingId, user.member.id)
            }
            .let { input -> updateDealFinding(input) }
            .let(ctx::json)
            .status(HttpStatus.OK)
    }

    private fun UpdateDealFindingRequest.toUpdateFindingInput(findingId: Long, memberId: Long) = UpdateDealFinding.Input(
        findingId = findingId,
        status = this.status,
        memberId = memberId
    )
}
