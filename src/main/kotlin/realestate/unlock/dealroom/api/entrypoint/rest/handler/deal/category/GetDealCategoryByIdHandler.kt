package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.category

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.category.GetAndSummarizeCompleteDealCategory
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.category.GetDealCategoryResponse
import realestate.unlock.dealroom.api.entrypoint.rest.contract.task.TaskStatusQueryParam
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class GetDealCategoryByIdHandler @Inject constructor(
    private val getCompleteDealCategory: GetAndSummarizeCompleteDealCategory,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .pathParam<Long>("deal-category-id")
            .json(status = HttpStatus.OK.toString(), returnType = GetDealCategoryResponse::class.java)
            .operation {
                it.description("Get Deal Category by id")
                it.operationId("getDealCategoryById")
                it.addTagsItem("deals")
                it.deprecated(true)
                it.summary(
                    """
                    Retrieves a Deal Category with it's summary and tasks data
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.READ_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()}")
        val taskStatus = TaskStatusQueryParam.get(ctx)
        this.runCatching {
            ctx.pathParamAsClass<Long>("deal-category-id").get()
        }
            .onFailure { error ->
                throw BadRequestException(
                    message = error.localizedMessage,
                    cause = error
                )
            }
            .getOrThrow()
            .let {
                getCompleteDealCategory.get(
                    dealCategoryId = it,
                    taskStatusTranslator = taskStatus,
                    member = user.member
                )
            }
            .let(::GetDealCategoryResponse)
            .let(ctx::json)
    }
}
