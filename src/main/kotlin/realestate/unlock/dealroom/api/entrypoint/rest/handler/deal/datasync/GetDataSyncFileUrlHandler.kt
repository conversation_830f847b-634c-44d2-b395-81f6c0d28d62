package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.datasync

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.datasync.GetDataSyncFileUrl
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.datasync.DataSyncFileUrlResponse
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class GetDataSyncFileUrlHandler @Inject constructor(
    private val getDataSyncFileUrl: GetDataSyncFileUrl,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .queryParam<String>("fileName")
            .result(status = HttpStatus.OK.toString(), returnType = DataSyncFileUrlResponse::class.java)
            .operation {
                it.description("Get a url to upload a DataSync file")
                it.operationId("getDataSyncFileUrl")
                it.addTagsItem("data-sync")
                it.summary(
                    """
                    Get a url to upload a DataSync file
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.UPDATE_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.UPDATE_ALL_DEALS),
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()}")
        ctx.queryParamAsClass<String>("fileName").get()
            .let { fileName -> getDataSyncFileUrl.get(fileName) }
            .let { DataSyncFileUrlResponse(id = it.kFileId, url = it.url) }
            .let { ctx.status(HttpStatus.OK).json(it) }
    }
}
