package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.CreatePointOfContact
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.post.PostDealSupportMemberRequest
import realestate.unlock.dealroom.api.entrypoint.rest.contract.mappers.Mapper
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.MemberDto
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.bodyTo
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class CreatePointOfContactHandler @Inject constructor(
    private val createPointOfContact: CreatePointOfContact,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .body(PostDealSupportMemberRequest::class.java)
            .json(status = HttpStatus.CREATED.toString(), returnType = MemberDto::class.java)
            .operation {
                it.description("Creates a new member")
                it.operationId("postPointOfContact")
                it.addTagsItem("deals")
                it.summary(
                    """
                    Creates a new member with type seller_support and adds it to the deal team
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.UPDATE_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.UPDATE_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()} - Body: ${ctx.body()}")
        ctx.pathParamAsClass<Long>("deal-id").get()
            .let { dealId ->
                ctx.bodyTo(PostDealSupportMemberRequest::class.java)
                    .toCreateDealSupportMemberInput(dealId, user.member.organizationId)
            }
            .let(createPointOfContact::create)
            .let(Mapper.toMemberDto)
            .let(ctx::json)
            .status(HttpStatus.CREATED)
    }
}
