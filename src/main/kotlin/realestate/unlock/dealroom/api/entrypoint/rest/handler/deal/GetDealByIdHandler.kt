package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.GetDealById
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.DealDto
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class GetDealByIdHandler @Inject constructor(
    private val getDealById: GetDealById,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) :
    DealHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(documentation: OpenApiDocumentation): OpenApiDocumentation =
        documentation
            .json(status = HttpStatus.OK.toString(), returnType = DealDto::class.java)
            .operation {
                it.description("Get Deal by Id")
                it.operationId("getDealById")
                it.summary(
                    """
                    Get Deal by Id
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.READ_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser, dealId: Long) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()}")
        getDealById.get(dealId).let(::DealDto).let(ctx::json)
    }
}
