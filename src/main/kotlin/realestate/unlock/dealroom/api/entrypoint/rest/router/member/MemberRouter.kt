package realestate.unlock.dealroom.api.entrypoint.rest.router.member

import io.javalin.Javalin
import realestate.unlock.dealroom.api.entrypoint.rest.handler.member.*
import realestate.unlock.dealroom.api.entrypoint.rest.handler.member.type.GetMemberTypesHandler
import realestate.unlock.dealroom.api.infrastructure.javalin.Router
import javax.inject.Inject

class MemberRouter @Inject constructor(
    private val app: Javalin,
    private val getMemberTypesHandler: GetMemberTypesHandler,
    private val createMemberHandler: CreateMemberHandler,
    private val getMembersHandler: GetMembersHandler,
    private val getMemberDetailByIdHandler: GetMemberDetailByIdHandler,
    private val disableMemberHandler: DisableMemberHandler,
    private val getMemberDetailByEmailHandler: GetMemberDetailByEmailHandler
) : Router {

    override fun setUpRoutes() {
        "/member".also { path ->
            app.post(path, createMemberHandler())
            app.get(path, getMembersHandler())
        }

        "/member-types".also { path ->
            app.get(path, getMemberTypesHandler())
        }

        "/member/{member-id}".also { path ->
            app.get(path, getMemberDetailByIdHandler())
        }

        "/member/{member-id}/disable".also { path ->
            app.put(path, disableMemberHandler())
        }

        "/member-by-email/{member-email}".also { path ->
            app.get(path, getMemberDetailByEmailHandler())
        }
    }
}
