package realestate.unlock.dealroom.api.entrypoint.rest.handler.member

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.member.GetMemberDetail
import realestate.unlock.dealroom.api.entrypoint.rest.contract.member.get.GetMemberDetailResponse
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class GetMemberDetailByEmailHandler@Inject constructor(
    private val getMemberDetail: GetMemberDetail,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<String>("member-email")
            .json(status = HttpStatus.OK.toString(), returnType = GetMemberDetailResponse::class.java)
            .operation {
                it.description("Get Member Detail by email")
                it.operationId("getMemberByEmail")
                it.addTagsItem("members")
                it.summary(
                    """
                    Finds a member by its email and returns the details
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_MEMBERS)
    override val securityFilters = listOf(
        FilterExecutor(onlyBuyerSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()} - Params: ${ctx.queryParamMap()}")
        ctx.runCatching {
            this.pathParamAsClass<String>("member-email").get()
        }.onFailure { error ->
            throw BadRequestException(
                message = error.localizedMessage,
                cause = error
            )
        }.getOrThrow()
            .let(getMemberDetail::getByEmail)
            .let(::GetMemberDetailResponse)
            .let(ctx::json)
    }
}
