package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.file

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.files.GetDealFilesByDealId
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.GetDealFilesResponse
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class GetDealFilesHandler @Inject constructor(
    private val getDealFilesByDealId: GetDealFilesByDealId,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .jsonArray(status = HttpStatus.OK.toString(), returnType = GetDealFilesResponse::class.java)
            .operation {
                it.description("Get Deal Files")
                it.operationId("getDealFiles")
                it.addTagsItem("deal-files")
                it.summary(
                    """
                    Returns a list with all the files of a Deal
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.READ_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()}")
        this.runCatching {
            ctx.pathParamAsClass<Long>("deal-id").get()
        }
            .onFailure { error ->
                throw BadRequestException(
                    message = error.localizedMessage,
                    cause = error
                )
            }
            .getOrThrow()
            .let(getDealFilesByDealId::get)
            .map(::GetDealFilesResponse)
            .let(ctx::json)
    }
}
