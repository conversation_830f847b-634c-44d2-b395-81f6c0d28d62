package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.deal.team.RemoveDealTeamMemberInput
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.team.RemoveDealTeamMember
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class RemoveDealTeamMemberHandler @Inject constructor(
    private val removeDealTeamMember: RemoveDealTeamMember,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .pathParam<Long>("member-id")
            .result<Int>(status = HttpStatus.ACCEPTED.toString())
            .operation {
                it.description("Remove Deal Member")
                it.operationId("deleteDealMember")
                it.addTagsItem("deals")
                it.summary(
                    """
                    Removes a Deal Member
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.UPDATE_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.UPDATE_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()} - Body: ${ctx.body()}")
        this.runCatching {
            Pair(
                ctx.pathParamAsClass<Long>("deal-id").get(),
                ctx.pathParamAsClass<Long>("member-id").get()
            )
        }
            .onFailure { error ->
                throw BadRequestException(message = error.localizedMessage, cause = error)
            }
            .getOrThrow()
            .let { (dealId, memberId) ->
                removeDealTeamMember.remove(
                    RemoveDealTeamMemberInput(
                        dealId = dealId,
                        memberId = memberId
                    )
                )
            }
            .let(ctx::json)
            .status(HttpStatus.ACCEPTED)
    }
}
