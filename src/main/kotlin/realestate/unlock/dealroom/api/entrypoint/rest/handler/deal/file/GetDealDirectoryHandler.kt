package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.file

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.deal.file.DealDirectoryFilter
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.files.GetDealDirectory
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.file.DirectoryItem
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.client.rest.exception.BadRequestException
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import realestate.unlock.dealroom.api.infrastructure.utils.mapper.JsonMapper
import javax.inject.Inject
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter

class GetDealDirectoryHandler @Inject constructor(
    private val getDealDirectory: GetDealDirectory,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .queryParam<String>("path")
            .queryParam<DealDirectoryFilter>("filter")
            .jsonArray<DirectoryItem>(status = HttpStatus.OK.toString())
            .operation {
                it.description("Get Deal Files and Folders")
                it.operationId("getDealDirectory")
                it.addTagsItem("deal-files")
                it.summary(
                    """
                    Returns a list with Deal Files and Folders in path
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.READ_ALL_DEALS),
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()}")
        this.runCatching {
            Triple(
                ctx.pathParamAsClass<Long>("deal-id").get(),
                ctx.queryParam("path") ?: "",
                ctx.queryParamAsClass<String>("filter").allowNullable().get()
                    .takeUnless { it.isNullOrEmpty() }
                    ?.let { JsonMapper.decode(it, DealDirectoryFilter::class.java) }
                    ?: DealDirectoryFilter()
            )
        }
            .onFailure { error ->
                throw BadRequestException(
                    message = error.localizedMessage,
                    cause = error
                )
            }
            .getOrThrow()
            .let { (dealId, path, filters) -> getDealDirectory.invoke(dealId = dealId, path = path, filters) }
            .let(ctx::json)
    }
}
