package realestate.unlock.dealroom.api.entrypoint.rest.handler.file

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.file.gpt.SendFileToProcess
import realestate.unlock.dealroom.api.entrypoint.rest.contract.file.gpt.SendFileToProcessBody
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.ContextUtils.getPathParam
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.bodyTo
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import javax.inject.Inject

class ProcessFileByGptHandler @Inject constructor(
    private val sendFileToProcess: SendFileToProcess,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.UPDATE_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<String>("file-id")
            .body<SendFileToProcessBody>()
            .result(status = HttpStatus.CREATED.toString(), returnType = Unit::class.java)
            .operation {
                it.description("Process file by chat gpt service")
                it.operationId("sendFileToProcessByGpt")
                it.addTagsItem("chat-gpt")
                it.summary(
                    """
                    Sends a file to be processed by the chat gpt service
                    """.trimIndent()
                )
            }

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        sendFileToProcess(
            SendFileToProcess.Input(
                fileId = getPathParam(ctx, "file-id"),
                fileType = ctx.bodyTo(SendFileToProcessBody::class.java).fileType,
                dealId = getPathParam(ctx, "deal-id"),
                authToken = user.authToken
            )
        ).let(ctx::json)
            .status(HttpStatus.CREATED)
    }
}
