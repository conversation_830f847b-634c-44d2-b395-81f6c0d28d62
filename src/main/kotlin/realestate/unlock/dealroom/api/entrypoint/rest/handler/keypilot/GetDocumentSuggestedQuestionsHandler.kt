package realestate.unlock.dealroom.api.entrypoint.rest.handler.keypilot

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.file.gpt.TokenFileType
import realestate.unlock.dealroom.api.core.entity.keypilot.DocumentQuestion
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.keypilot.GetSuggestedQuestions
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.ContextUtils.getOptionalQueryParam
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.ContextUtils.getQueryParam
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import javax.inject.Inject

class GetDocumentSuggestedQuestionsHandler @Inject constructor(
    private val getSuggestedQuestions: GetSuggestedQuestions,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .queryParam<TokenFileType>("document")
            .queryParam<String>("type")
            .jsonArray(status = HttpStatus.OK.toString(), returnType = DocumentQuestion::class.java)
            .operation {
                it.description("Get suggested questions for a document")
                it.operationId("getDocumentSuggestedQuestions")
                it.addTagsItem("gpt")
                it.summary(
                    """
                    Retrieves the suggested questions for a given document
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.READ_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.READ_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        getSuggestedQuestions(
            document = TokenFileType.valueOf(getQueryParam(ctx, "document")),
            documentType = getOptionalQueryParam(ctx, "type")
        )
            .let(ctx::json)
            .status(HttpStatus.OK)
    }
}
