package realestate.unlock.dealroom.api.entrypoint.rest.router.deal

import io.javalin.Javalin
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.file.*
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.file.linked.CreateLinkedFileHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.file.linked.DeleteLinkedFileHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.deal.file.linked.GetLinkedFilesHandler
import realestate.unlock.dealroom.api.infrastructure.javalin.Router
import javax.inject.Inject

class DealFilesRouter @Inject constructor(
    private val app: <PERSON>lin,
    private val uploadDealFileHandler: UploadDealFileHandler,
    private val getDealFilesHandler: GetDealFilesHandler,
    private val getDealFileByIdHandler: GetDealFileByKFileIdHandler,
    private val getDealDirectoryHandler: GetDealDirectory<PERSON><PERSON><PERSON>,
    private val getFileDirectoryUrlHandler: GetFileDirectoryUrlHandler,
    private val deleteDirectoriesHandler: DeleteDirectoriesHandler,
    private val moveDirectoriesHandler: MoveDirectoriesHandler,
    private val folderDirectoryCreationHandler: FolderDirectoryCreationHandler,
    private val createLinkedFileHandler: CreateLinkedFileHandler,
    private val deleteLinkedFileHandler: DeleteLinkedFileHandler,
    private val getLinkedFilesHandler: GetLinkedFilesHandler
) : Router {

    override fun setUpRoutes() {
        "/deal/{deal-id}/file".also { path ->
            app.get(path, getDealFilesHandler()) // Deprecated
            app.post(path, uploadDealFileHandler()) // Deprecated
        }

        "/deal/{deal-id}/directory".also { path ->
            app.get(path, getDealDirectoryHandler())
            app.post(path, folderDirectoryCreationHandler())
            app.put(path, moveDirectoriesHandler())
            app.delete(path, deleteDirectoriesHandler())
        }

        "/deal/{deal-id}/file-urls".also { path ->
            app.get(path, getFileDirectoryUrlHandler())
        }

        "/deal/{deal-id}/files/{file-id}".also { path ->
            app.get(path, getDealFileByIdHandler())
        }

        "/deal/{deal-id}/linked-files".also { path ->
            app.get(path, getLinkedFilesHandler())
            app.post(path, createLinkedFileHandler())
        }

        "/deal/{deal-id}/linked-files/{linked-file-id}".also { path ->
            app.delete(path, deleteLinkedFileHandler())
        }
    }
}
