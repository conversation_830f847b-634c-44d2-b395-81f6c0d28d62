package realestate.unlock.dealroom.api.entrypoint.rest.handler.deal

import io.javalin.http.Context
import io.javalin.plugin.openapi.dsl.OpenApiDocumentation
import io.javalin.plugin.openapi.dsl.document
import kong.unirest.HttpStatus
import realestate.unlock.dealroom.api.core.entity.user.AuthenticatedUser
import realestate.unlock.dealroom.api.core.entity.user.auth.Permission
import realestate.unlock.dealroom.api.core.usecase.deal.AddDealTeamMembers
import realestate.unlock.dealroom.api.entrypoint.rest.contract.deal.team.AddDealTeamMemberRequest
import realestate.unlock.dealroom.api.entrypoint.rest.handler.base.AuthorizedHandler
import realestate.unlock.dealroom.api.entrypoint.rest.handler.utils.bodyTo
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.FilterExecutor
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.common.OnlyBuyerSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealBelongsToOrganizationSecurityFilter
import realestate.unlock.dealroom.api.entrypoint.rest.security.filter.deal.VerifyDealMembershipByDealIdPathParamSecurityFilter
import realestate.unlock.dealroom.api.infrastructure.utils.logger.LoggerDelegate
import javax.inject.Inject

class AddDealTeamMemberHandler @Inject constructor(
    private val addDealTeamMembers: AddDealTeamMembers,
    private val onlyBuyerSecurityFilter: OnlyBuyerSecurityFilter,
    private val verifyDealMembershipByDealIdPathParamSecurityFilter: VerifyDealMembershipByDealIdPathParamSecurityFilter,
    private val verifyDealBelongsToOrganizationSecurityFilter: VerifyDealBelongsToOrganizationSecurityFilter
) : AuthorizedHandler() {

    companion object {
        private val logger by LoggerDelegate()
    }

    override fun getDocumentation(): OpenApiDocumentation =
        document()
            .pathParam<Long>("deal-id")
            .body(AddDealTeamMemberRequest::class.java)
            .result<Int>(status = HttpStatus.CREATED.toString())
            .operation {
                it.description("Add Deal Team Member")
                it.operationId("postDealTeamMember")
                it.addTagsItem("deals")
                it.summary(
                    """
                    Adds a Member to the Team of a Deal
                    """.trimIndent()
                )
            }

    override val permissionsNeeded = listOf(Permission.UPDATE_OWN_DEALS)

    override val securityFilters = listOf(
        FilterExecutor(onlyBuyerSecurityFilter),
        FilterExecutor(verifyDealMembershipByDealIdPathParamSecurityFilter, Permission.UPDATE_ALL_DEALS),
        FilterExecutor(verifyDealBelongsToOrganizationSecurityFilter)
    )

    override fun handle(ctx: Context, user: AuthenticatedUser) {
        logger.trace("Got request ${ctx.method()}:${ctx.path()} - Body: ${ctx.body()}")
        ctx.pathParamAsClass<Long>("deal-id").get().let {
            ctx.bodyTo(AddDealTeamMemberRequest::class.java)
                .toAddDealTeamMembersInput(it)
        }
            .let(addDealTeamMembers::add)
            .let(ctx::json)
            .status(HttpStatus.CREATED)
    }
}
