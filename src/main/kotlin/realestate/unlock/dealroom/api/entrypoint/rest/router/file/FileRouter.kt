package realestate.unlock.dealroom.api.entrypoint.rest.router.file

import io.javalin.Javalin
import realestate.unlock.dealroom.api.entrypoint.rest.handler.file.*
import realestate.unlock.dealroom.api.infrastructure.javalin.Router
import javax.inject.Inject

class FileRouter @Inject constructor(
    private val app: <PERSON>lin,
    private val processFileByGptHandler: ProcessFileByGptHandler,
    private val getFileQuestionsHistoryHandler: GetFileQuestionsHistoryHandler,
    private val getFileStatusHandler: GetFileStatusHandler,
    private val getFileInfoHandler: GetFileInfoHandler
) : Router {

    override fun setUpRoutes() {
        "/deal/{deal-id}/file/{file-id}".also { path ->
            app.get(path, getFileInfoHandler())
        }

        "/deal/{deal-id}/file/{file-id}/process".also { path ->
            app.post(path, processFileByGptHandler())
        }

        "/deal/{deal-id}/file/{file-id}/questions".also { path ->
            app.get(path, getFileQuestionsHistoryHandler())
        }

        "/deal/{deal-id}/file/{file-id}/status".also { path ->
            app.get(path, getFileStatusHandler())
        }
    }
}
